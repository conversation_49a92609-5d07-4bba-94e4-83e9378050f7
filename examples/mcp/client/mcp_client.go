package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"api-gateway/pkg/mcp/protocol"
)

// MCPClient MCP 客户端
type MCPClient struct {
	baseURL    string
	httpClient *http.Client
	sessionID  string
}

// NewMCPClient 创建 MCP 客户端
func NewMCPClient(baseURL string) *MCPClient {
	return &MCPClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SendRequest 发送 MCP 请求
func (c *MCPClient) SendRequest(method string, params interface{}) (*protocol.Response, error) {
	// 创建请求
	req := protocol.NewRequest(fmt.Sprintf("req_%d", time.Now().UnixNano()), method, params)

	// 序列化请求
	reqData, err := req.ToJSON()
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 发送 HTTP 请求
	httpReq, err := http.NewRequest("POST", c.baseURL+"/mcp/message", bytes.NewReader(reqData))
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	if c.sessionID != "" {
		httpReq.Header.Set("X-Session-ID", c.sessionID)
	}

	// 发送请求
	httpResp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer httpResp.Body.Close()

	// 读取响应
	respData, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查 HTTP 状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP 错误: %d, 响应: %s", httpResp.StatusCode, string(respData))
	}

	// 解析 MCP 响应
	msg, err := protocol.ParseMessage(respData)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	resp, ok := msg.(*protocol.Response)
	if !ok {
		return nil, fmt.Errorf("响应不是 Response 类型")
	}

	return resp, nil
}

// SendHTTPWithMCPConversion 发送带 MCP 转换的 HTTP 请求
func (c *MCPClient) SendHTTPWithMCPConversion(method, path string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		bodyData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("序列化请求体失败: %w", err)
		}
		reqBody = bytes.NewReader(bodyData)
	}

	httpReq, err := http.NewRequest(method, c.baseURL+path, reqBody)
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 设置请求头，要求进行 MCP 转换
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Convert-To-MCP", "true")

	return c.httpClient.Do(httpReq)
}

func main() {
	// 创建 MCP 客户端
	client := NewMCPClient("http://localhost:8080")

	fmt.Println("🔗 MCP 客户端示例")
	fmt.Println("================")

	// 示例 1: 直接发送 MCP 请求
	fmt.Println("\n📨 示例 1: 直接发送 MCP 请求")
	testDirectMCPRequest(client)

	// 示例 2: 通过 HTTP 请求触发 MCP 转换
	fmt.Println("\n🔄 示例 2: HTTP 请求 MCP 转换")
	testHTTPToMCPConversion(client)

	// 示例 3: 测试不同的 MCP 方法
	fmt.Println("\n🛠️  示例 3: 测试不同的 MCP 方法")
	testDifferentMCPMethods(client)

	// 示例 4: 测试错误处理
	fmt.Println("\n❌ 示例 4: 测试错误处理")
	testErrorHandling(client)
}

// testDirectMCPRequest 测试直接发送 MCP 请求
func testDirectMCPRequest(client *MCPClient) {
	// 发送用户列表请求
	params := map[string]interface{}{
		"pagination": map[string]interface{}{
			"page":  1,
			"limit": 5,
		},
		"filter": map[string]interface{}{
			"search": "张",
		},
	}

	resp, err := client.SendRequest("users.list", params)
	if err != nil {
		log.Printf("发送 MCP 请求失败: %v", err)
		return
	}

	fmt.Printf("✅ MCP 请求成功\n")
	fmt.Printf("   请求 ID: %v\n", resp.ID)

	if resp.Error != nil {
		fmt.Printf("   错误: %s (代码: %d)\n", resp.Error.Message, resp.Error.Code)
	} else {
		fmt.Printf("   结果: %v\n", resp.Result)
	}
}

// testHTTPToMCPConversion 测试 HTTP 到 MCP 转换
func testHTTPToMCPConversion(client *MCPClient) {
	// 发送 HTTP 请求，要求转换为 MCP
	resp, err := client.SendHTTPWithMCPConversion("GET", "/api/v1/users?page=1&limit=3&search=李", nil)
	if err != nil {
		log.Printf("发送 HTTP 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("读取响应失败: %v", err)
		return
	}

	fmt.Printf("✅ HTTP 到 MCP 转换成功\n")
	fmt.Printf("   状态码: %d\n", resp.StatusCode)
	fmt.Printf("   响应: %s\n", string(body))
}

// testDifferentMCPMethods 测试不同的 MCP 方法
func testDifferentMCPMethods(client *MCPClient) {
	// 测试获取单个用户
	params := map[string]interface{}{
		"user": map[string]interface{}{
			"id": "1",
		},
	}

	resp, err := client.SendRequest("users.get", params)
	if err != nil {
		log.Printf("发送 users.get 请求失败: %v", err)
		return
	}

	fmt.Printf("✅ users.get 请求成功\n")
	fmt.Printf("   请求 ID: %v\n", resp.ID)
	if resp.Error != nil {
		fmt.Printf("   错误: %s\n", resp.Error.Message)
	} else {
		fmt.Printf("   结果: %v\n", resp.Result)
	}

	// 测试创建用户
	createParams := map[string]interface{}{
		"user": map[string]interface{}{
			"name":   "测试用户",
			"email":  "<EMAIL>",
			"active": true,
		},
	}

	createResp, err := client.SendRequest("users.create", createParams)
	if err != nil {
		log.Printf("发送 users.create 请求失败: %v", err)
		return
	}

	fmt.Printf("✅ users.create 请求成功\n")
	fmt.Printf("   请求 ID: %v\n", createResp.ID)
	if createResp.Error != nil {
		fmt.Printf("   错误: %s\n", createResp.Error.Message)
	} else {
		fmt.Printf("   结果: %v\n", createResp.Result)
	}
}

// testErrorHandling 测试错误处理
func testErrorHandling(client *MCPClient) {
	// 测试无效方法
	resp, err := client.SendRequest("invalid.method", nil)
	if err != nil {
		fmt.Printf("❌ 发送无效方法请求失败（预期）: %v\n", err)
	} else {
		fmt.Printf("📝 收到无效方法响应:\n")
		fmt.Printf("   请求 ID: %v\n", resp.ID)
		if resp.Error != nil {
			fmt.Printf("   错误: %s (代码: %d)\n", resp.Error.Message, resp.Error.Code)
		}
	}

	// 测试无效参数
	invalidParams := map[string]interface{}{
		"invalid": "parameter",
	}

	resp2, err := client.SendRequest("users.list", invalidParams)
	if err != nil {
		fmt.Printf("❌ 发送无效参数请求失败: %v\n", err)
	} else {
		fmt.Printf("📝 收到无效参数响应:\n")
		fmt.Printf("   请求 ID: %v\n", resp2.ID)
		if resp2.Error != nil {
			fmt.Printf("   错误: %s (代码: %d)\n", resp2.Error.Message, resp2.Error.Code)
		} else {
			fmt.Printf("   结果: %v\n", resp2.Result)
		}
	}

	// 测试不存在的用户
	notFoundParams := map[string]interface{}{
		"user": map[string]interface{}{
			"id": "999",
		},
	}

	resp3, err := client.SendRequest("users.get", notFoundParams)
	if err != nil {
		fmt.Printf("❌ 发送不存在用户请求失败: %v\n", err)
	} else {
		fmt.Printf("📝 收到不存在用户响应:\n")
		fmt.Printf("   请求 ID: %v\n", resp3.ID)
		if resp3.Error != nil {
			fmt.Printf("   错误: %s (代码: %d)\n", resp3.Error.Message, resp3.Error.Code)
		} else {
			fmt.Printf("   结果: %v\n", resp3.Result)
		}
	}
}

// 辅助函数

// prettyPrint 美化打印 JSON
func prettyPrint(data interface{}) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		fmt.Printf("序列化失败: %v\n", err)
		return
	}
	fmt.Println(string(jsonData))
}
