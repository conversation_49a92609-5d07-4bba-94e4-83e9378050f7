package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"api-gateway/pkg/mcp/config"
	"api-gateway/pkg/mcp/middleware"
	"api-gateway/pkg/telemetry"
)

// User 用户结构体
type User struct {
	ID     int    `json:"id"`
	Name   string `json:"name"`
	Email  string `json:"email"`
	Active bool   `json:"active"`
}

// 模拟用户数据
var users = []User{
	{ID: 1, Name: "张三", Email: "<EMAIL>", Active: true},
	{ID: 2, Name: "李四", Email: "<EMAIL>", Active: true},
	{ID: 3, Name: "王五", Email: "<EMAIL>", Active: false},
}

func main() {
	// 创建 Gin 路由器
	router := gin.Default()

	// 创建日志记录器
	logger := &telemetry.Logger{}

	// 创建指标收集器
	metrics := &telemetry.Metrics{}

	// 加载 MCP 配置
	mcpConfig, err := loadMCPConfig()
	if err != nil {
		log.Fatalf("加载 MCP 配置失败: %v", err)
	}

	// 创建并添加 MCP 中间件
	mcpMiddleware := middleware.NewMCPMiddleware(mcpConfig, logger, metrics)
	router.Use(mcpMiddleware.Handle())

	// 添加示例 API 路由
	setupAPIRoutes(router)

	// 添加 MCP 相关路由
	setupMCPRoutes(router, mcpMiddleware)

	// 创建 HTTP 服务器
	server := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	// 启动服务器
	go func() {
		fmt.Println("🚀 服务器启动在 http://localhost:8080")
		fmt.Println("📋 MCP 转换功能已启用")
		fmt.Println("🔗 MCP SSE 端点: http://localhost:8080/mcp/sse")
		fmt.Println("📨 MCP 消息端点: http://localhost:8080/mcp/message")
		fmt.Println()
		fmt.Println("示例请求:")
		fmt.Println("  普通 API: curl http://localhost:8080/api/v1/users")
		fmt.Println("  MCP 转换: curl -H 'X-Convert-To-MCP: true' http://localhost:8080/api/v1/users")
		fmt.Println()

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("🛑 正在关闭服务器...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭 MCP 中间件
	if err := mcpMiddleware.Shutdown(ctx); err != nil {
		log.Printf("关闭 MCP 中间件失败: %v", err)
	}

	// 关闭 HTTP 服务器
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("关闭服务器失败: %v", err)
	}

	fmt.Println("✅ 服务器已关闭")
}

// loadMCPConfig 加载 MCP 配置
func loadMCPConfig() (*config.MCPConfig, error) {
	// 尝试从配置文件加载
	configPaths := []string{
		"configs/mcp.yaml",
		"../../../configs/mcp.yaml",
		"mcp.yaml",
	}

	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			loader := config.NewConfigLoader(config.NewConfigValidator())
			return loader.LoadConfig(path)
		}
	}

	// 如果没有找到配置文件，使用默认配置
	fmt.Println("⚠️  未找到配置文件，使用默认配置")
	return createDefaultMCPConfig(), nil
}

// createDefaultMCPConfig 创建默认 MCP 配置
func createDefaultMCPConfig() *config.MCPConfig {
	cfg := config.DefaultMCPConfig()
	cfg.Enabled = true

	// 添加示例转换规则
	cfg.ConversionRules = []config.ConversionRule{
		{
			Path:        "/api/v1/users",
			Methods:     []string{"GET", "POST"},
			MCPMethod:   "users.list",
			Enabled:     true,
			Priority:    1,
			Description: "用户列表和创建用户的 API 转换",
			ParameterMapping: config.ParameterMapping{
				Query: map[string]string{
					"page":   "pagination.page",
					"limit":  "pagination.limit",
					"search": "filter.search",
				},
				Body: map[string]string{
					"name":   "user.name",
					"email":  "user.email",
					"active": "user.active",
				},
			},
		},
		{
			Path:        "/api/v1/users/{id}",
			Methods:     []string{"GET", "PUT", "DELETE"},
			MCPMethod:   "users.get",
			Enabled:     true,
			Priority:    2,
			Description: "单个用户的获取、更新和删除操作",
			ParameterMapping: config.ParameterMapping{
				Path: map[string]string{
					"id": "user.id",
				},
				Body: map[string]string{
					"name":   "user.name",
					"email":  "user.email",
					"active": "user.active",
				},
			},
		},
	}

	return cfg
}

// setupAPIRoutes 设置 API 路由
func setupAPIRoutes(router *gin.Engine) {
	api := router.Group("/api/v1")

	// 用户相关路由
	users := api.Group("/users")
	{
		users.GET("", getUserList)
		users.POST("", createUser)
		users.GET("/:id", getUser)
		users.PUT("/:id", updateUser)
		users.DELETE("/:id", deleteUser)
	}

	// 健康检查
	api.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
			"service":   "API Gateway with MCP",
		})
	})
}

// setupMCPRoutes 设置 MCP 相关路由
func setupMCPRoutes(router *gin.Engine, mcpMiddleware *middleware.MCPMiddleware) {
	mcp := router.Group("/mcp")

	// MCP 健康检查
	mcp.GET("/health", func(c *gin.Context) {
		stats := mcpMiddleware.GetStats()
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"enabled": stats.Enabled,
			"version": stats.ConfigVersion,
			"stats":   stats,
		})
	})

	// MCP 统计信息
	mcp.GET("/stats", func(c *gin.Context) {
		stats := mcpMiddleware.GetStats()
		c.JSON(http.StatusOK, stats)
	})

	// MCP 配置信息
	mcp.GET("/config", func(c *gin.Context) {
		config := mcpMiddleware.GetConfig()
		c.JSON(http.StatusOK, gin.H{
			"enabled":          config.Enabled,
			"protocol_version": config.ProtocolVersion,
			"rules_count":      len(config.ConversionRules),
			"server":           config.Server,
			"transport":        config.Transport,
		})
	})
}

// API 处理函数

// getUserList 获取用户列表
func getUserList(c *gin.Context) {
	// 解析查询参数
	page := c.DefaultQuery("page", "1")
	limit := c.DefaultQuery("limit", "10")
	search := c.Query("search")

	fmt.Printf("📋 获取用户列表 - page: %s, limit: %s, search: %s\n", page, limit, search)

	// 模拟分页和搜索
	filteredUsers := users
	if search != "" {
		var filtered []User
		for _, user := range users {
			if contains(user.Name, search) || contains(user.Email, search) {
				filtered = append(filtered, user)
			}
		}
		filteredUsers = filtered
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    filteredUsers,
		"total":   len(filteredUsers),
		"page":    page,
		"limit":   limit,
	})
}

// createUser 创建用户
func createUser(c *gin.Context) {
	var newUser User
	if err := c.ShouldBindJSON(&newUser); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的请求数据",
			"details": err.Error(),
		})
		return
	}

	// 生成新 ID
	newUser.ID = len(users) + 1
	users = append(users, newUser)

	fmt.Printf("➕ 创建用户 - ID: %d, Name: %s, Email: %s\n", newUser.ID, newUser.Name, newUser.Email)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    newUser,
		"message": "用户创建成功",
	})
}

// getUser 获取单个用户
func getUser(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("👤 获取用户 - ID: %s\n", id)

	// 查找用户
	for _, user := range users {
		if fmt.Sprintf("%d", user.ID) == id {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    user,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success": false,
		"error":   "用户未找到",
		"user_id": id,
	})
}

// updateUser 更新用户
func updateUser(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("✏️  更新用户 - ID: %s\n", id)

	var updateData User
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的请求数据",
			"details": err.Error(),
		})
		return
	}

	// 查找并更新用户
	for i, user := range users {
		if fmt.Sprintf("%d", user.ID) == id {
			updateData.ID = user.ID
			users[i] = updateData
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    updateData,
				"message": "用户更新成功",
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success": false,
		"error":   "用户未找到",
		"user_id": id,
	})
}

// deleteUser 删除用户
func deleteUser(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("🗑️  删除用户 - ID: %s\n", id)

	// 查找并删除用户
	for i, user := range users {
		if fmt.Sprintf("%d", user.ID) == id {
			users = append(users[:i], users[i+1:]...)
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "用户删除成功",
				"user_id": id,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success": false,
		"error":   "用户未找到",
		"user_id": id,
	})
}

// 辅助函数

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || containsSubstring(s, substr))
}

// containsSubstring 检查字符串是否包含子字符串
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
