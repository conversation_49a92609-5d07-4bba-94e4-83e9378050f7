package unit

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"api-gateway/pkg/api"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAPIHandlers 测试API处理器
func TestAPIHandlers(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("健康检查处理器", func(t *testing.T) {
		// 创建处理器
		handler := api.NewHealthCheckHandler()
		assert.NotNil(t, handler)

		// 创建测试路由
		router := gin.New()
		router.GET("/health", handler.GetHealth)

		// 创建测试请求
		req, err := http.NewRequest("GET", "/health", nil)
		require.NoError(t, err)

		// 执行请求
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// 验证响应
		assert.Equal(t, http.StatusOK, w.Code)

		var response api.HealthResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "healthy", response.Status)
		assert.Equal(t, "1.0.0", response.Version)
		assert.NotEmpty(t, response.Uptime)
		assert.NotEmpty(t, response.Timestamp)
		assert.Contains(t, response.Components, "auth")
		assert.Contains(t, response.Components, "security")
	})

	t.Run("管理API处理器", func(t *testing.T) {
		// 创建处理器
		handler := api.NewAdminHandler()
		assert.NotNil(t, handler)

		// 创建测试路由
		router := gin.New()
		admin := router.Group("/admin")
		{
			admin.GET("/config", handler.GetConfig)
			admin.POST("/config/reload", handler.ReloadConfig)
			admin.GET("/routes", handler.GetRoutes)
			admin.GET("/metrics", handler.GetMetrics)
		}

		// 测试获取配置
		req, err := http.NewRequest("GET", "/admin/config", nil)
		require.NoError(t, err)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var config map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &config)
		require.NoError(t, err)

		assert.Contains(t, config, "server")
		assert.Contains(t, config, "auth")

		// 测试获取路由
		req, err = http.NewRequest("GET", "/admin/routes", nil)
		require.NoError(t, err)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var routes []api.RouteInfo
		err = json.Unmarshal(w.Body.Bytes(), &routes)
		require.NoError(t, err)

		assert.Len(t, routes, 2)
		assert.Equal(t, "public-api", routes[0].Name)
		assert.Equal(t, "protected-api", routes[1].Name)

		// 测试获取指标
		req, err = http.NewRequest("GET", "/admin/metrics", nil)
		require.NoError(t, err)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Header().Get("Content-Type"), "text/plain")
		assert.Contains(t, w.Body.String(), "gateway_requests_total")
	})

	t.Run("OIDC处理器", func(t *testing.T) {
		// 创建处理器
		handler := api.NewOIDCHandler()
		assert.NotNil(t, handler)

		// 创建测试路由
		router := gin.New()
		oidc := router.Group("/auth/oidc")
		{
			oidc.GET("/authorize", handler.GetAuthURL)
			oidc.GET("/callback", handler.HandleCallback)
			oidc.POST("/refresh", handler.RefreshToken)
			oidc.POST("/revoke", handler.RevokeToken)
		}

		// 测试获取授权URL
		req, err := http.NewRequest("GET", "/auth/oidc/authorize?state=test-state", nil)
		require.NoError(t, err)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response api.OIDCAuthResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Contains(t, response.AuthorizationURL, "accounts.google.com")
		assert.Contains(t, response.AuthorizationURL, "test-state")

		// 测试正确的回调路径
		router.GET("/callback", handler.HandleCallback)
		req, err = http.NewRequest("GET", "/callback?code=test-code&state=test-state", nil)
		require.NoError(t, err)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var tokenResponse api.OIDCTokenResponse
		err = json.Unmarshal(w.Body.Bytes(), &tokenResponse)
		require.NoError(t, err)

		assert.NotEmpty(t, tokenResponse.AccessToken)
		assert.NotEmpty(t, tokenResponse.IDToken)
		assert.Equal(t, "Bearer", tokenResponse.TokenType)
		assert.Equal(t, 3600, tokenResponse.ExpiresIn)
	})
}

// TestAPIModels 测试API模型
func TestAPIModels(t *testing.T) {
	t.Run("健康检查响应模型", func(t *testing.T) {
		response := api.HealthResponse{
			Status:    "healthy",
			Version:   "1.0.0",
			Uptime:    "2小时30分钟",
			Timestamp: "2025-08-17T07:30:14.646Z",
			Components: map[string]api.ComponentStatus{
				"auth": {
					Status:      "healthy",
					Description: "认证管理器运行正常",
				},
			},
		}

		// 验证模型字段
		assert.Equal(t, "healthy", response.Status)
		assert.Equal(t, "1.0.0", response.Version)
		assert.Contains(t, response.Components, "auth")

		// 测试JSON序列化
		data, err := json.Marshal(response)
		require.NoError(t, err)
		assert.Contains(t, string(data), "healthy")
		assert.Contains(t, string(data), "认证管理器运行正常")
	})

	t.Run("错误响应模型", func(t *testing.T) {
		errorResp := api.ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数无效",
			Details:   "详细错误信息",
			Timestamp: "2025-08-17T07:30:14.646Z",
		}

		// 验证模型字段
		assert.Equal(t, "INVALID_REQUEST", errorResp.Code)
		assert.Equal(t, "请求参数无效", errorResp.Message)

		// 测试JSON序列化
		data, err := json.Marshal(errorResp)
		require.NoError(t, err)
		assert.Contains(t, string(data), "INVALID_REQUEST")
	})

	t.Run("路由信息模型", func(t *testing.T) {
		route := api.RouteInfo{
			Name:   "test-route",
			Path:   "/api/v1/test",
			Method: "GET",
			Upstream: api.UpstreamInfo{
				Type:         "static",
				LoadBalancer: "round_robin",
				Servers: []api.ServerInfo{
					{Host: "backend.example.com", Port: 8080, Status: "healthy"},
				},
			},
			Auth: &api.AuthInfo{
				Required: true,
				Methods:  []string{"jwt"},
				Roles:    []string{"user"},
			},
			Timeout: 30,
			Retries: 3,
		}

		// 验证模型字段
		assert.Equal(t, "test-route", route.Name)
		assert.Equal(t, "/api/v1/test", route.Path)
		assert.True(t, route.Auth.Required)
		assert.Contains(t, route.Auth.Methods, "jwt")
		assert.Len(t, route.Upstream.Servers, 1)

		// 测试JSON序列化
		data, err := json.Marshal(route)
		require.NoError(t, err)
		assert.Contains(t, string(data), "test-route")
		assert.Contains(t, string(data), "round_robin")
	})

	t.Run("OIDC Token响应模型", func(t *testing.T) {
		tokenResp := api.OIDCTokenResponse{
			AccessToken:  "access-token",
			IDToken:      "id-token",
			RefreshToken: "refresh-token",
			TokenType:    "Bearer",
			ExpiresIn:    3600,
		}

		// 验证模型字段
		assert.Equal(t, "access-token", tokenResp.AccessToken)
		assert.Equal(t, "Bearer", tokenResp.TokenType)
		assert.Equal(t, 3600, tokenResp.ExpiresIn)

		// 测试JSON序列化
		data, err := json.Marshal(tokenResp)
		require.NoError(t, err)
		assert.Contains(t, string(data), "Bearer")
		assert.Contains(t, string(data), "3600")
	})
}

// BenchmarkAPIHandlers API处理器性能基准测试
func BenchmarkAPIHandlers(b *testing.B) {
	gin.SetMode(gin.TestMode)

	b.Run("健康检查处理器", func(b *testing.B) {
		handler := api.NewHealthCheckHandler()
		router := gin.New()
		router.GET("/health", handler.GetHealth)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			req, _ := http.NewRequest("GET", "/health", nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
		}
	})

	b.Run("配置获取处理器", func(b *testing.B) {
		handler := api.NewAdminHandler()
		router := gin.New()
		router.GET("/admin/config", handler.GetConfig)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			req, _ := http.NewRequest("GET", "/admin/config", nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
		}
	})

	b.Run("OIDC授权URL生成", func(b *testing.B) {
		handler := api.NewOIDCHandler()
		router := gin.New()
		router.GET("/auth/oidc/authorize", handler.GetAuthURL)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			req, _ := http.NewRequest("GET", "/auth/oidc/authorize", nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
		}
	})
}
