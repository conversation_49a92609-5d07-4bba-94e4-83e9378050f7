package unit

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"math/big"
	"os"
	"testing"
	"time"

	"api-gateway/pkg/auth"
	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestMTLSAuthenticator 测试mTLS认证器
func TestMTLSAuthenticator(t *testing.T) {
	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	// 创建测试证书
	caCert, caKey, clientCert, clientKey := createTestCertificates(t)

	// 创建临时CA证书文件
	caFile := createTempCertFile(t, caCert)
	defer os.Remove(caFile)

	t.Run("mTLS认证器创建", func(t *testing.T) {
		// 测试启用的配置
		mtlsConfig := config.MTLSConfig{
			Enabled:            true,
			CAFile:             caFile,
			OCSPEnabled:        false,
			OCSPTimeoutSeconds: 10,
		}

		authenticator, err := auth.NewMTLSAuthenticator(mtlsConfig, logger)
		assert.NoError(t, err)
		assert.NotNil(t, authenticator)

		// 测试未启用的配置
		disabledConfig := config.MTLSConfig{
			Enabled: false,
		}

		_, err = auth.NewMTLSAuthenticator(disabledConfig, logger)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "未启用")
	})

	t.Run("证书验证", func(t *testing.T) {
		mtlsConfig := config.MTLSConfig{
			Enabled:            true,
			CAFile:             caFile,
			OCSPEnabled:        false,
			OCSPTimeoutSeconds: 10,
		}

		authenticator, err := auth.NewMTLSAuthenticator(mtlsConfig, logger)
		require.NoError(t, err)

		// 将客户端证书编码为PEM格式
		clientCertPEM := encodeCertToPEM(clientCert)

		// 创建认证上下文，包含客户端证书头
		authCtx := &auth.AuthContext{
			RequestID: "test-request-001",
			Headers: map[string]string{
				"X-Client-Cert": clientCertPEM,
			},
		}

		// 执行认证
		ctx := context.Background()
		result, err := authenticator.Authenticate(ctx, authCtx)
		require.NoError(t, err)

		// 验证认证结果
		assert.True(t, result.Authenticated)
		assert.Equal(t, "mtls", result.TokenType)
		assert.NotEmpty(t, result.UserID)
		assert.Contains(t, result.Attributes, "certificate_subject")
		assert.Contains(t, result.Attributes, "certificate_serial")
	})

	t.Run("无效证书处理", func(t *testing.T) {
		mtlsConfig := config.MTLSConfig{
			Enabled:            true,
			CAFile:             caFile,
			OCSPEnabled:        false,
			OCSPTimeoutSeconds: 10,
		}

		authenticator, err := auth.NewMTLSAuthenticator(mtlsConfig, logger)
		require.NoError(t, err)

		// 测试无效的PEM格式
		authCtx := &auth.AuthContext{
			RequestID: "test-request-002",
			Headers: map[string]string{
				"X-Client-Cert": "invalid-pem-data",
			},
		}

		ctx := context.Background()
		result, err := authenticator.Authenticate(ctx, authCtx)
		require.NoError(t, err)

		assert.False(t, result.Authenticated)
		assert.Contains(t, result.Error, "解析失败")
	})

	t.Run("缺少客户端证书", func(t *testing.T) {
		mtlsConfig := config.MTLSConfig{
			Enabled:            true,
			CAFile:             caFile,
			OCSPEnabled:        false,
			OCSPTimeoutSeconds: 10,
		}

		authenticator, err := auth.NewMTLSAuthenticator(mtlsConfig, logger)
		require.NoError(t, err)

		// 创建无客户端证书头的认证上下文
		authCtx := &auth.AuthContext{
			RequestID: "test-request-003",
			Headers:   map[string]string{}, // 空头部
		}

		ctx := context.Background()
		result, err := authenticator.Authenticate(ctx, authCtx)
		require.NoError(t, err)

		assert.False(t, result.Authenticated)
		assert.Contains(t, result.Error, "未提供客户端证书")
	})

	// 清理
	_ = caKey // 避免未使用变量警告
	_ = clientKey
}

// createTestCertificates 创建测试用的CA和客户端证书
func createTestCertificates(t *testing.T) (*x509.Certificate, *rsa.PrivateKey, *x509.Certificate, *rsa.PrivateKey) {
	// 创建CA私钥
	caKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)

	// 创建CA证书模板
	caTemplate := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization:  []string{"Test CA"},
			Country:       []string{"US"},
			Province:      []string{""},
			Locality:      []string{"San Francisco"},
			StreetAddress: []string{""},
			PostalCode:    []string{""},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().Add(365 * 24 * time.Hour),
		IsCA:                  true,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth, x509.ExtKeyUsageServerAuth},
		KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageCertSign,
		BasicConstraintsValid: true,
	}

	// 创建CA证书
	caCertDER, err := x509.CreateCertificate(rand.Reader, &caTemplate, &caTemplate, &caKey.PublicKey, caKey)
	require.NoError(t, err)

	caCert, err := x509.ParseCertificate(caCertDER)
	require.NoError(t, err)

	// 创建客户端私钥
	clientKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)

	// 创建客户端证书模板
	clientTemplate := x509.Certificate{
		SerialNumber: big.NewInt(2),
		Subject: pkix.Name{
			Organization:       []string{"Test Client"},
			OrganizationalUnit: []string{"Engineering"},
			Country:            []string{"US"},
			Province:           []string{""},
			Locality:           []string{"San Francisco"},
			StreetAddress:      []string{""},
			PostalCode:         []string{""},
			CommonName:         "test-client",
		},
		EmailAddresses: []string{"<EMAIL>"},
		NotBefore:      time.Now(),
		NotAfter:       time.Now().Add(365 * 24 * time.Hour),
		ExtKeyUsage:    []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth},
		KeyUsage:       x509.KeyUsageDigitalSignature,
	}

	// 创建客户端证书
	clientCertDER, err := x509.CreateCertificate(rand.Reader, &clientTemplate, caCert, &clientKey.PublicKey, caKey)
	require.NoError(t, err)

	clientCert, err := x509.ParseCertificate(clientCertDER)
	require.NoError(t, err)

	return caCert, caKey, clientCert, clientKey
}

// createTempCertFile 创建临时证书文件
func createTempCertFile(t *testing.T, cert *x509.Certificate) string {
	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "ca-cert-*.pem")
	require.NoError(t, err)
	defer tmpFile.Close()

	// 编码证书为PEM格式
	err = pem.Encode(tmpFile, &pem.Block{
		Type:  "CERTIFICATE",
		Bytes: cert.Raw,
	})
	require.NoError(t, err)

	return tmpFile.Name()
}

// encodeCertToPEM 将证书编码为PEM格式字符串
func encodeCertToPEM(cert *x509.Certificate) string {
	certPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "CERTIFICATE",
		Bytes: cert.Raw,
	})
	return string(certPEM)
}

// createTestCertificatesForBench 为基准测试创建测试证书
func createTestCertificatesForBench(b *testing.B) (*x509.Certificate, *rsa.PrivateKey, *x509.Certificate, *rsa.PrivateKey) {
	// 创建CA私钥
	caKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(b, err)

	// 创建CA证书模板
	caTemplate := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization: []string{"Test CA"},
			Country:      []string{"US"},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().Add(365 * 24 * time.Hour),
		IsCA:                  true,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth, x509.ExtKeyUsageServerAuth},
		KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageCertSign,
		BasicConstraintsValid: true,
	}

	// 创建CA证书
	caCertDER, err := x509.CreateCertificate(rand.Reader, &caTemplate, &caTemplate, &caKey.PublicKey, caKey)
	require.NoError(b, err)

	caCert, err := x509.ParseCertificate(caCertDER)
	require.NoError(b, err)

	// 创建客户端私钥
	clientKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(b, err)

	// 创建客户端证书模板
	clientTemplate := x509.Certificate{
		SerialNumber: big.NewInt(2),
		Subject: pkix.Name{
			Organization:       []string{"Test Client"},
			OrganizationalUnit: []string{"Engineering"},
			Country:            []string{"US"},
			CommonName:         "test-client",
		},
		EmailAddresses: []string{"<EMAIL>"},
		NotBefore:      time.Now(),
		NotAfter:       time.Now().Add(365 * 24 * time.Hour),
		ExtKeyUsage:    []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth},
		KeyUsage:       x509.KeyUsageDigitalSignature,
	}

	// 创建客户端证书
	clientCertDER, err := x509.CreateCertificate(rand.Reader, &clientTemplate, caCert, &clientKey.PublicKey, caKey)
	require.NoError(b, err)

	clientCert, err := x509.ParseCertificate(clientCertDER)
	require.NoError(b, err)

	return caCert, caKey, clientCert, clientKey
}

// createTempCertFileForBench 为基准测试创建临时证书文件
func createTempCertFileForBench(b *testing.B, cert *x509.Certificate) string {
	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "ca-cert-*.pem")
	require.NoError(b, err)
	defer tmpFile.Close()

	// 编码证书为PEM格式
	err = pem.Encode(tmpFile, &pem.Block{
		Type:  "CERTIFICATE",
		Bytes: cert.Raw,
	})
	require.NoError(b, err)

	return tmpFile.Name()
}

// TestMTLSConfig 测试mTLS配置
func TestMTLSConfig(t *testing.T) {
	t.Run("默认配置", func(t *testing.T) {
		mtlsConfig := config.MTLSConfig{}

		assert.False(t, mtlsConfig.Enabled)
		assert.Empty(t, mtlsConfig.CAFile)
		assert.Empty(t, mtlsConfig.CRLFile)
		assert.False(t, mtlsConfig.OCSPEnabled)
		assert.Equal(t, 0, mtlsConfig.OCSPTimeoutSeconds)
	})

	t.Run("完整配置", func(t *testing.T) {
		mtlsConfig := config.MTLSConfig{
			Enabled:                true,
			CAFile:                 "/path/to/ca.pem",
			CRLFile:                "/path/to/crl.pem",
			OCSPEnabled:            true,
			OCSPTimeoutSeconds:     30,
			VerifyClientCertCN:     true,
			AllowedCNs:             []string{"client1", "client2"},
			VerifyOrganization:     true,
			AllowedOrganizations:   []string{"Example Corp", "Test Org"},
		}

		assert.True(t, mtlsConfig.Enabled)
		assert.Equal(t, "/path/to/ca.pem", mtlsConfig.CAFile)
		assert.Equal(t, "/path/to/crl.pem", mtlsConfig.CRLFile)
		assert.True(t, mtlsConfig.OCSPEnabled)
		assert.Equal(t, 30, mtlsConfig.OCSPTimeoutSeconds)
		assert.True(t, mtlsConfig.VerifyClientCertCN)
		assert.Len(t, mtlsConfig.AllowedCNs, 2)
		assert.True(t, mtlsConfig.VerifyOrganization)
		assert.Len(t, mtlsConfig.AllowedOrganizations, 2)
	})
}

// BenchmarkMTLSAuthentication mTLS认证性能基准测试
func BenchmarkMTLSAuthentication(b *testing.B) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})

	// 创建测试证书
	caCert, _, clientCert, _ := createTestCertificatesForBench(b)

	// 创建临时CA证书文件
	caFile := createTempCertFileForBench(b, caCert)
	defer os.Remove(caFile)

	mtlsConfig := config.MTLSConfig{
		Enabled:            true,
		CAFile:             caFile,
		OCSPEnabled:        false,
		OCSPTimeoutSeconds: 10,
	}

	authenticator, err := auth.NewMTLSAuthenticator(mtlsConfig, logger)
	require.NoError(b, err)

	// 创建模拟请求
	clientCertPEM := encodeCertToPEM(clientCert)

	authCtx := &auth.AuthContext{
		RequestID: "bench-request",
		Headers: map[string]string{
			"X-Client-Cert": clientCertPEM,
		},
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, err := authenticator.Authenticate(ctx, authCtx)
		if err != nil {
			b.Error(err)
		}
		if !result.Authenticated {
			b.Error("认证失败")
		}
	}
}
