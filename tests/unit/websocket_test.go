package unit

import (
	"testing"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
	"api-gateway/pkg/websocket"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockConnection 模拟连接
type MockConnection struct {
	id           string
	remoteAddr   string
	userID       string
	connectedAt  time.Time
	active       bool
	metadata     map[string]interface{}
	stats        *websocket.ConnectionStats
	sendChan     chan *websocket.Message
	receiveChan  chan *websocket.Message
}

// NewMockConnection 创建模拟连接
func NewMockConnection(id, userID string) *MockConnection {
	return &MockConnection{
		id:          id,
		remoteAddr:  "127.0.0.1:12345",
		userID:      userID,
		connectedAt: time.Now(),
		active:      true,
		metadata:    make(map[string]interface{}),
		stats: &websocket.ConnectionStats{
			ConnectionID: id,
			ConnectedAt:  time.Now(),
		},
		sendChan:    make(chan *websocket.Message, 10),
		receiveChan: make(chan *websocket.Message, 10),
	}
}

func (m *MockConnection) ID() string                                    { return m.id }
func (m *MockConnection) RemoteAddr() string                            { return m.remoteAddr }
func (m *MockConnection) UserID() string                                { return m.userID }
func (m *MockConnection) ConnectedAt() time.Time                        { return m.connectedAt }
func (m *MockConnection) IsActive() bool                                { return m.active }
func (m *MockConnection) Metadata() map[string]interface{}              { return m.metadata }
func (m *MockConnection) SetMetadata(key string, value interface{})     { m.metadata[key] = value }
func (m *MockConnection) Stats() *websocket.ConnectionStats             { return m.stats }
func (m *MockConnection) Ping() error                                   { return nil }
func (m *MockConnection) Close(reason string) error                     { m.active = false; return nil }

func (m *MockConnection) SendMessage(message *websocket.Message) error {
	select {
	case m.sendChan <- message:
		return nil
	default:
		return nil
	}
}

func (m *MockConnection) ReceiveMessage() (*websocket.Message, error) {
	select {
	case message := <-m.receiveChan:
		return message, nil
	case <-time.After(100 * time.Millisecond):
		return nil, nil
	}
}

// TestWebSocketRouter 测试WebSocket路由器
func TestWebSocketRouter(t *testing.T) {
	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("路由器创建", func(t *testing.T) {
		routeConfigs := []config.WebSocketRouteConfig{
			{
				Name:    "test-route",
				Path:    "/ws/test",
				Enabled: true,
				Upstream: config.WebSocketUpstreamConfig{
					Type: "static",
					URL:  "ws://localhost:8081/ws",
				},
			},
		}

		router, err := websocket.NewRouter(routeConfigs, logger)
		require.NoError(t, err)
		assert.NotNil(t, router)

		routes := router.GetRoutes()
		assert.Len(t, routes, 1)
		assert.Equal(t, "test-route", routes[0].Name)
		assert.Equal(t, "/ws/test", routes[0].Path)
	})

	t.Run("路由匹配", func(t *testing.T) {
		routeConfigs := []config.WebSocketRouteConfig{
			{
				Name:    "exact-route",
				Path:    "/ws/exact",
				Enabled: true,
			},
			{
				Name:    "wildcard-route",
				Path:    "/ws/api/*",
				Enabled: true,
			},
			{
				Name:    "param-route",
				Path:    "/ws/users/{id}",
				Enabled: true,
			},
		}

		router, err := websocket.NewRouter(routeConfigs, logger)
		require.NoError(t, err)

		// 精确匹配
		route, err := router.MatchRoute("/ws/exact")
		assert.NoError(t, err)
		assert.Equal(t, "exact-route", route.Name)

		// 通配符匹配
		route, err = router.MatchRoute("/ws/api/v1/test")
		assert.NoError(t, err)
		assert.Equal(t, "wildcard-route", route.Name)

		// 参数匹配
		route, err = router.MatchRoute("/ws/users/123")
		assert.NoError(t, err)
		assert.Equal(t, "param-route", route.Name)

		// 无匹配
		_, err = router.MatchRoute("/ws/unknown")
		assert.Error(t, err)
	})

	t.Run("路由管理", func(t *testing.T) {
		router, err := websocket.NewRouter([]config.WebSocketRouteConfig{}, logger)
		require.NoError(t, err)

		// 添加路由
		route := &websocket.Route{
			Name:    "dynamic-route",
			Path:    "/ws/dynamic",
			Enabled: true,
		}

		err = router.AddRoute(route)
		assert.NoError(t, err)

		// 验证添加
		routes := router.GetRoutes()
		assert.Len(t, routes, 1)

		// 重复添加应该失败
		err = router.AddRoute(route)
		assert.Error(t, err)

		// 删除路由
		err = router.RemoveRoute("dynamic-route")
		assert.NoError(t, err)

		// 验证删除
		routes = router.GetRoutes()
		assert.Len(t, routes, 0)

		// 删除不存在的路由应该失败
		err = router.RemoveRoute("non-existent")
		assert.Error(t, err)
	})
}

// TestWebSocketManager 测试WebSocket管理器
func TestWebSocketManager(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("管理器创建", func(t *testing.T) {
		config := config.WebSocketConfig{
			Enabled: true,
			Connection: config.WebSocketConnectionConfig{
				ReadBufferSize:  1024,
				WriteBufferSize: 1024,
				CheckOrigin:     false,
			},
			Proxy: config.WebSocketProxyConfig{
				Enabled: false,
			},
			Routes: []config.WebSocketRouteConfig{
				{
					Name:    "test-route",
					Path:    "/ws/test",
					Enabled: true,
				},
			},
		}

		manager, err := websocket.NewManager(config, logger)
		require.NoError(t, err)
		assert.NotNil(t, manager)

		// 启动管理器
		err = manager.Start()
		assert.NoError(t, err)

		// 健康检查
		err = manager.HealthCheck()
		assert.NoError(t, err)

		// 停止管理器
		err = manager.Stop()
		assert.NoError(t, err)
	})

	t.Run("连接管理", func(t *testing.T) {
		config := config.WebSocketConfig{
			Enabled: true,
			Routes: []config.WebSocketRouteConfig{
				{
					Name:    "test-route",
					Path:    "/ws/test",
					Enabled: true,
				},
			},
		}

		manager, err := websocket.NewManager(config, logger)
		require.NoError(t, err)

		err = manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		// 获取初始统计
		stats, err := manager.GetStats()
		assert.NoError(t, err)
		assert.Equal(t, int64(0), stats.ActiveConnections)

		// 获取活跃连接
		connections := manager.GetActiveConnections()
		assert.Len(t, connections, 0)
	})

	t.Run("消息广播", func(t *testing.T) {
		config := config.WebSocketConfig{
			Enabled: true,
		}

		manager, err := websocket.NewManager(config, logger)
		require.NoError(t, err)

		err = manager.Start()
		require.NoError(t, err)
		defer manager.Stop()

		// 创建测试消息
		message := &websocket.Message{
			ID:        "test-msg-1",
			Type:      websocket.MessageTypeText,
			Data:      []byte("Hello, World!"),
			Timestamp: time.Now(),
		}

		// 广播消息（没有连接时应该成功）
		err = manager.Broadcast(message, nil)
		assert.NoError(t, err)
	})

	t.Run("禁用WebSocket", func(t *testing.T) {
		config := config.WebSocketConfig{
			Enabled: false,
		}

		manager, err := websocket.NewManager(config, logger)
		require.NoError(t, err)

		// 启动应该成功但不做任何事
		err = manager.Start()
		assert.NoError(t, err)

		err = manager.Stop()
		assert.NoError(t, err)
	})
}

// TestWebSocketProxy 测试WebSocket代理
func TestWebSocketProxy(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("代理创建", func(t *testing.T) {
		config := config.WebSocketProxyConfig{
			Enabled:    true,
			Mode:       "transparent",
			BufferSize: 4096,
			KeepAlive:  true,
		}

		proxy, err := websocket.NewProxy(config, logger)
		require.NoError(t, err)
		assert.NotNil(t, proxy)

		// 启动代理
		err = proxy.Start()
		assert.NoError(t, err)

		// 健康检查
		err = proxy.HealthCheck()
		assert.NoError(t, err)

		// 获取统计
		stats, err := proxy.GetProxyStats()
		assert.NoError(t, err)
		assert.Equal(t, int64(0), stats.ActiveProxies)

		// 停止代理
		err = proxy.Stop()
		assert.NoError(t, err)
	})

	t.Run("禁用代理", func(t *testing.T) {
		config := config.WebSocketProxyConfig{
			Enabled: false,
		}

		proxy, err := websocket.NewProxy(config, logger)
		require.NoError(t, err)

		// 代理连接应该失败
		mockConn := NewMockConnection("test-conn", "test-user")
		err = proxy.ProxyConnection(mockConn, "ws://localhost:8081/ws")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "代理未启用")
	})
}

// TestWebSocketMessage 测试WebSocket消息
func TestWebSocketMessage(t *testing.T) {
	t.Run("消息创建", func(t *testing.T) {
		message := &websocket.Message{
			ID:        "test-msg-1",
			Type:      websocket.MessageTypeText,
			Data:      []byte("Hello, World!"),
			SenderID:  "user123",
			Timestamp: time.Now(),
		}

		assert.Equal(t, "test-msg-1", message.ID)
		assert.Equal(t, websocket.MessageTypeText, message.Type)
		assert.Equal(t, []byte("Hello, World!"), message.Data)
		assert.Equal(t, "user123", message.SenderID)
	})

	t.Run("消息类型", func(t *testing.T) {
		assert.Equal(t, websocket.MessageType(1), websocket.MessageTypeText)
		assert.Equal(t, websocket.MessageType(2), websocket.MessageTypeBinary)
		assert.Equal(t, websocket.MessageType(8), websocket.MessageTypeClose)
		assert.Equal(t, websocket.MessageType(9), websocket.MessageTypePing)
		assert.Equal(t, websocket.MessageType(10), websocket.MessageTypePong)
	})
}

// BenchmarkWebSocketMessage WebSocket消息性能基准测试
func BenchmarkWebSocketMessage(b *testing.B) {
	message := &websocket.Message{
		ID:        "bench-msg",
		Type:      websocket.MessageTypeText,
		Data:      []byte("Benchmark message data"),
		Timestamp: time.Now(),
	}

	b.Run("MessageCreation", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = &websocket.Message{
				ID:        "bench-msg",
				Type:      websocket.MessageTypeText,
				Data:      []byte("Benchmark message data"),
				Timestamp: time.Now(),
			}
		}
	})

	b.Run("MessageCopy", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			newMessage := *message
			_ = newMessage
		}
	})
}
