package unit

import (
	"testing"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/security"
	"api-gateway/pkg/telemetry"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRateLimiter 测试限流器
func TestRateLimiter(t *testing.T) {
	// 创建测试配置
	cfg := config.RateLimitConfig{
		Enabled:   true,
		Algorithm: "token_bucket",
		Rules: []config.RateLimitRule{
			{
				Path:   "/api/*",
				Method: "*",
				Rate:   10,  // 每秒10个请求
				Burst:  20,  // 突发20个请求
				Window: time.Second,
				KeyBy:  "ip",
			},
		},
	}

	// 创建测试日志器和指标收集器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	metrics, err := telemetry.NewMetrics(config.MetricsConfig{
		Enabled: false,
	})
	require.NoError(t, err)

	// 创建限流器
	rateLimiter, err := security.NewRateLimiter(cfg, logger, metrics)
	require.NoError(t, err)

	t.Run("正常请求通过", func(t *testing.T) {
		// 测试正常请求
		for i := 0; i < 5; i++ {
			allowed, err := rateLimiter.IsAllowed("***********", "/api/users", "GET", "ip")
			require.NoError(t, err)
			assert.True(t, allowed, "第%d个请求应该被允许", i+1)
		}
	})

	t.Run("超过限制的请求被阻止", func(t *testing.T) {
		clientIP := "***********"
		
		// 快速发送超过限制的请求
		allowedCount := 0
		blockedCount := 0
		
		for i := 0; i < 30; i++ {
			allowed, err := rateLimiter.IsAllowed(clientIP, "/api/users", "GET", "ip")
			require.NoError(t, err)
			
			if allowed {
				allowedCount++
			} else {
				blockedCount++
			}
		}

		// 应该有一些请求被阻止
		assert.Greater(t, blockedCount, 0, "应该有请求被限流")
		assert.LessOrEqual(t, allowedCount, 20, "允许的请求数不应超过突发限制")
	})

	t.Run("不同IP独立限流", func(t *testing.T) {
		ip1 := "***********"
		ip2 := "***********"

		// 两个不同IP的请求应该独立计算
		allowed1, err := rateLimiter.IsAllowed(ip1, "/api/users", "GET", "ip")
		require.NoError(t, err)
		assert.True(t, allowed1)

		allowed2, err := rateLimiter.IsAllowed(ip2, "/api/users", "GET", "ip")
		require.NoError(t, err)
		assert.True(t, allowed2)
	})
}

// TestWAFEngine 测试WAF引擎
func TestWAFEngine(t *testing.T) {
	// 创建测试配置
	cfg := config.WAFConfig{
		Enabled: true,
		Rules: []config.WAFRule{
			{
				Name:        "sql_injection",
				Pattern:     "(?i)(union|select|insert|delete|update|drop|create|alter|exec|script)",
				Action:      "block",
				Description: "阻止SQL注入攻击",
			},
			{
				Name:        "xss_attack",
				Pattern:     "(?i)(<script|javascript:|on\\w+\\s*=)",
				Action:      "block",
				Description: "阻止XSS攻击",
			},
		},
	}

	// 创建测试日志器和指标收集器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	metrics, err := telemetry.NewMetrics(config.MetricsConfig{
		Enabled: false,
	})
	require.NoError(t, err)

	// 创建WAF引擎
	wafEngine, err := security.NewWAFEngine(cfg, logger, metrics)
	require.NoError(t, err)

	t.Run("正常请求通过", func(t *testing.T) {
		request := &security.WAFRequest{
			Method:      "GET",
			Path:        "/api/users",
			Query:       "page=1&limit=10",
			Headers:     map[string]string{"Content-Type": "application/json"},
			Body:        `{"name": "test user"}`,
			ClientIP:    "***********",
			UserAgent:   "Mozilla/5.0",
			ContentType: "application/json",
		}

		result, err := wafEngine.Evaluate(request)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, "allow", result.Action)
	})

	t.Run("SQL注入攻击被阻止", func(t *testing.T) {
		request := &security.WAFRequest{
			Method:      "POST",
			Path:        "/api/users",
			Query:       "id=1 UNION SELECT * FROM users",
			Headers:     map[string]string{"Content-Type": "application/json"},
			Body:        `{"name": "test"}`,
			ClientIP:    "***********",
			UserAgent:   "Mozilla/5.0",
			ContentType: "application/json",
		}

		result, err := wafEngine.Evaluate(request)
		require.NoError(t, err)
		assert.False(t, result.Allowed)
		assert.Equal(t, "block", result.Action)
		assert.Equal(t, "sql_injection", result.RuleName)
	})

	t.Run("XSS攻击被阻止", func(t *testing.T) {
		request := &security.WAFRequest{
			Method:      "POST",
			Path:        "/api/comments",
			Query:       "",
			Headers:     map[string]string{"Content-Type": "application/json"},
			Body:        `{"comment": "<script>alert('xss')</script>"}`,
			ClientIP:    "***********",
			UserAgent:   "Mozilla/5.0",
			ContentType: "application/json",
		}

		result, err := wafEngine.Evaluate(request)
		require.NoError(t, err)
		assert.False(t, result.Allowed)
		assert.Equal(t, "block", result.Action)
		assert.Equal(t, "xss_attack", result.RuleName)
	})
}

// TestIPFilter 测试IP过滤器
func TestIPFilter(t *testing.T) {
	// 创建测试配置
	cfg := config.IPFilterConfig{
		Enabled:   true,
		Whitelist: []string{"***********/24", "********"},
		Blacklist: []string{"***********00", "10.0.0.0/8"},
	}

	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	// 创建IP过滤器
	ipFilter, err := security.NewIPFilter(cfg, logger)
	require.NoError(t, err)

	t.Run("白名单IP允许访问", func(t *testing.T) {
		allowed, err := ipFilter.IsAllowed("************")
		require.NoError(t, err)
		assert.True(t, allowed)

		allowed, err = ipFilter.IsAllowed("********")
		require.NoError(t, err)
		assert.True(t, allowed)
	})

	t.Run("黑名单IP被阻止", func(t *testing.T) {
		allowed, err := ipFilter.IsAllowed("***********00")
		require.NoError(t, err)
		assert.False(t, allowed)

		allowed, err = ipFilter.IsAllowed("*********")
		require.NoError(t, err)
		assert.False(t, allowed)
	})

	t.Run("不在白名单的IP被阻止", func(t *testing.T) {
		allowed, err := ipFilter.IsAllowed("**********")
		require.NoError(t, err)
		assert.False(t, allowed)
	})
}

// TestCORSHandler 测试CORS处理器
func TestCORSHandler(t *testing.T) {
	// 创建测试配置
	cfg := config.CORSConfig{
		Enabled:          true,
		AllowedOrigins:   []string{"https://example.com", "https://app.example.com"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Content-Type", "Authorization", "X-API-Key"},
		ExposedHeaders:   []string{"X-Request-ID"},
		AllowCredentials: true,
		MaxAge:           86400,
	}

	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	// 创建CORS处理器
	corsHandler, err := security.NewCORSHandler(cfg, logger)
	require.NoError(t, err)

	t.Run("允许的Origin通过", func(t *testing.T) {
		result, err := corsHandler.Handle("https://example.com", "GET", []string{"Content-Type"})
		require.NoError(t, err)
		assert.True(t, result.Allowed)

		result, err = corsHandler.Handle("https://app.example.com", "GET", []string{"Content-Type"})
		require.NoError(t, err)
		assert.True(t, result.Allowed)
	})

	t.Run("不允许的Origin被阻止", func(t *testing.T) {
		result, err := corsHandler.Handle("https://malicious.com", "GET", []string{"Content-Type"})
		require.NoError(t, err)
		assert.False(t, result.Allowed)

		result, err = corsHandler.Handle("http://example.com", "GET", []string{"Content-Type"}) // 协议不匹配
		require.NoError(t, err)
		assert.False(t, result.Allowed)
	})

	t.Run("允许的方法通过", func(t *testing.T) {
		result, err := corsHandler.Handle("https://example.com", "GET", []string{"Content-Type"})
		require.NoError(t, err)
		assert.True(t, result.Allowed)

		result, err = corsHandler.Handle("https://example.com", "POST", []string{"Content-Type"})
		require.NoError(t, err)
		assert.True(t, result.Allowed)

		result, err = corsHandler.Handle("https://example.com", "OPTIONS", []string{"Content-Type"})
		require.NoError(t, err)
		assert.True(t, result.Allowed)
	})

	t.Run("不允许的方法被阻止", func(t *testing.T) {
		result, err := corsHandler.Handle("https://example.com", "PATCH", []string{"Content-Type"})
		require.NoError(t, err)
		assert.False(t, result.Allowed)

		result, err = corsHandler.Handle("https://example.com", "TRACE", []string{"Content-Type"})
		require.NoError(t, err)
		assert.False(t, result.Allowed)
	})
}

// BenchmarkRateLimiter 限流器性能基准测试
func BenchmarkRateLimiter(b *testing.B) {
	cfg := config.RateLimitConfig{
		Enabled:   true,
		Algorithm: "token_bucket",
		Rules: []config.RateLimitRule{
			{
				Path:   "/api/*",
				Method: "*",
				Rate:   1000,
				Burst:  2000,
				Window: time.Second,
				KeyBy:  "ip",
			},
		},
	}

	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})

	metrics, _ := telemetry.NewMetrics(config.MetricsConfig{
		Enabled: false,
	})

	rateLimiter, _ := security.NewRateLimiter(cfg, logger, metrics)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, _ = rateLimiter.IsAllowed("***********", "/api/users", "GET", "ip")
		}
	})
}

// BenchmarkWAFEngine WAF引擎性能基准测试
func BenchmarkWAFEngine(b *testing.B) {
	cfg := config.WAFConfig{
		Enabled: true,
		Rules: []config.WAFRule{
			{
				Name:    "sql_injection",
				Pattern: "(?i)(union|select|insert|delete|update|drop|create|alter|exec|script)",
				Action:  "block",
			},
			{
				Name:    "xss_attack",
				Pattern: "(?i)(<script|javascript:|on\\w+\\s*=)",
				Action:  "block",
			},
		},
	}

	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})

	metrics, _ := telemetry.NewMetrics(config.MetricsConfig{
		Enabled: false,
	})

	wafEngine, _ := security.NewWAFEngine(cfg, logger, metrics)

	request := &security.WAFRequest{
		Method:      "GET",
		Path:        "/api/users",
		Query:       "page=1&limit=10",
		Headers:     map[string]string{"Content-Type": "application/json"},
		Body:        `{"name": "test user"}`,
		ClientIP:    "***********",
		UserAgent:   "Mozilla/5.0",
		ContentType: "application/json",
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, _ = wafEngine.Evaluate(request)
		}
	})
}
