package unit

import (
	"context"
	"testing"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/serverless"
	"api-gateway/pkg/telemetry"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAWSLambdaProvider 测试AWS Lambda提供者
func TestAWSLambdaProvider(t *testing.T) {
	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("提供者创建", func(t *testing.T) {
		config := serverless.AWSLambdaConfig{
			Region:           "us-east-1",
			AccessKeyID:      "test-access-key",
			SecretAccessKey:  "test-secret-key",
			DefaultTimeout:   30 * time.Second,
			MaxRetries:       3,
			FunctionCacheTTL: 5 * time.Minute,
		}

		provider, err := serverless.NewAWSLambdaProvider(config, logger)
		require.NoError(t, err)
		assert.NotNil(t, provider)
		assert.Equal(t, "aws_lambda", provider.Name())
	})

	t.Run("函数调用", func(t *testing.T) {
		config := serverless.AWSLambdaConfig{
			Region:         "us-east-1",
			DefaultTimeout: 30 * time.Second,
			MaxRetries:     3,
		}

		provider, err := serverless.NewAWSLambdaProvider(config, logger)
		require.NoError(t, err)

		// 创建调用请求
		request := &serverless.InvocationRequest{
			FunctionName:   "test-function",
			InvocationType: "sync",
			Payload:        []byte(`{"message": "Hello, World!"}`),
			Headers:        map[string]string{"Content-Type": "application/json"},
			Timeout:        10 * time.Second,
		}

		// 调用函数
		ctx := context.Background()
		response, err := provider.InvokeFunction(ctx, request)
		require.NoError(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, 200, response.StatusCode)
		assert.NotEmpty(t, response.Payload)
		assert.NotEmpty(t, response.RequestID)
		assert.Greater(t, response.Duration, time.Duration(0))
	})

	t.Run("获取函数信息", func(t *testing.T) {
		config := serverless.AWSLambdaConfig{
			Region:         "us-east-1",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewAWSLambdaProvider(config, logger)
		require.NoError(t, err)

		// 获取函数信息
		ctx := context.Background()
		functionInfo, err := provider.GetFunction(ctx, "test-function")
		require.NoError(t, err)
		assert.NotNil(t, functionInfo)
		assert.Equal(t, "test-function", functionInfo.Name)
		assert.NotEmpty(t, functionInfo.ARN)
		assert.NotEmpty(t, functionInfo.Runtime)
	})

	t.Run("列出函数", func(t *testing.T) {
		config := serverless.AWSLambdaConfig{
			Region:         "us-east-1",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewAWSLambdaProvider(config, logger)
		require.NoError(t, err)

		// 列出函数
		ctx := context.Background()
		functions, err := provider.ListFunctions(ctx)
		require.NoError(t, err)
		assert.NotEmpty(t, functions)
		assert.Greater(t, len(functions), 0)

		// 验证函数信息
		for _, function := range functions {
			assert.NotEmpty(t, function.Name)
			assert.NotEmpty(t, function.ARN)
			assert.NotEmpty(t, function.Runtime)
		}
	})

	t.Run("健康检查", func(t *testing.T) {
		config := serverless.AWSLambdaConfig{
			Region:         "us-east-1",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewAWSLambdaProvider(config, logger)
		require.NoError(t, err)

		// 健康检查
		ctx := context.Background()
		err = provider.HealthCheck(ctx)
		assert.NoError(t, err)
	})

	t.Run("获取统计信息", func(t *testing.T) {
		config := serverless.AWSLambdaConfig{
			Region:         "us-east-1",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewAWSLambdaProvider(config, logger)
		require.NoError(t, err)

		// 先调用一个函数来生成统计数据
		request := &serverless.InvocationRequest{
			FunctionName:   "test-function",
			InvocationType: "sync",
			Payload:        []byte(`{"test": true}`),
		}

		ctx := context.Background()
		_, err = provider.InvokeFunction(ctx, request)
		require.NoError(t, err)

		// 获取统计信息
		stats := provider.GetStats()
		assert.NotNil(t, stats)
		assert.Equal(t, "aws_lambda", stats.ProviderName)
		assert.Greater(t, stats.TotalInvocations, int64(0))
		assert.Greater(t, stats.SuccessfulInvocations, int64(0))
		assert.Equal(t, int64(0), stats.FailedInvocations)
		assert.Greater(t, stats.AvgDuration, time.Duration(0))
	})
}

// TestServerlessManager 测试Serverless管理器
func TestServerlessManager(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("管理器创建", func(t *testing.T) {
		config := config.ServerlessConfig{
			Enabled: true,
			AWSLambda: config.ServerlessAWSLambdaConfig{
				Enabled:        true,
				Region:         "us-east-1",
				DefaultTimeout: 30 * time.Second,
				MaxRetries:     3,
			},
			Routes: []config.ServerlessRouteConfig{
				{
					Name:           "test-route",
					Path:           "/api/v1/test",
					Methods:        []string{"GET", "POST"},
					Provider:       "aws_lambda",
					FunctionName:   "test-function",
					InvocationType: "sync",
					Enabled:        true,
				},
			},
		}

		manager, err := serverless.NewManager(config, logger)
		require.NoError(t, err)
		assert.NotNil(t, manager)

		// 启动管理器
		err = manager.Start()
		assert.NoError(t, err)

		// 停止管理器
		err = manager.Stop()
		assert.NoError(t, err)
	})

	t.Run("提供者管理", func(t *testing.T) {
		config := config.ServerlessConfig{
			Enabled: true,
		}

		manager, err := serverless.NewManager(config, logger)
		require.NoError(t, err)

		// 创建测试提供者
		providerConfig := serverless.AWSLambdaConfig{
			Region:         "us-east-1",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewAWSLambdaProvider(providerConfig, logger)
		require.NoError(t, err)

		// 注册提供者
		err = manager.RegisterProvider(provider)
		assert.NoError(t, err)

		// 获取提供者
		retrievedProvider, err := manager.GetProvider("aws_lambda")
		assert.NoError(t, err)
		assert.Equal(t, provider, retrievedProvider)

		// 获取所有提供者
		providers := manager.GetProviders()
		assert.Len(t, providers, 1)
		assert.Equal(t, provider, providers[0])

		// 重复注册应该失败
		err = manager.RegisterProvider(provider)
		assert.Error(t, err)

		// 获取不存在的提供者应该失败
		_, err = manager.GetProvider("non-existent")
		assert.Error(t, err)
	})

	t.Run("函数调用", func(t *testing.T) {
		config := config.ServerlessConfig{
			Enabled: true,
		}

		manager, err := serverless.NewManager(config, logger)
		require.NoError(t, err)

		// 注册AWS Lambda提供者
		providerConfig := serverless.AWSLambdaConfig{
			Region:         "us-east-1",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewAWSLambdaProvider(providerConfig, logger)
		require.NoError(t, err)

		err = manager.RegisterProvider(provider)
		require.NoError(t, err)

		// 创建调用请求
		request := &serverless.InvocationRequest{
			InvocationType: "sync",
			Payload:        []byte(`{"message": "Hello from manager!"}`),
			Headers:        map[string]string{"Content-Type": "application/json"},
		}

		// 调用函数
		ctx := context.Background()
		response, err := manager.InvokeFunction(ctx, "aws_lambda", "test-function", request)
		require.NoError(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, 200, response.StatusCode)
		assert.NotEmpty(t, response.RequestID)
	})

	t.Run("路由到函数", func(t *testing.T) {
		config := config.ServerlessConfig{
			Enabled: true,
		}

		manager, err := serverless.NewManager(config, logger)
		require.NoError(t, err)

		// 注册提供者
		providerConfig := serverless.AWSLambdaConfig{
			Region:         "us-east-1",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewAWSLambdaProvider(providerConfig, logger)
		require.NoError(t, err)

		err = manager.RegisterProvider(provider)
		require.NoError(t, err)

		// 创建路由
		route := &serverless.ServerlessRoute{
			Name:           "test-route",
			Path:           "/api/v1/test",
			Methods:        []string{"POST"},
			Provider:       "aws_lambda",
			FunctionName:   "test-function",
			InvocationType: "sync",
			Timeout:        15 * time.Second,
			Enabled:        true,
		}

		// 创建请求
		request := &serverless.InvocationRequest{
			Payload: []byte(`{"data": "test"}`),
			Headers: map[string]string{"Content-Type": "application/json"},
		}

		// 路由到函数
		ctx := context.Background()
		response, err := manager.RouteToFunction(ctx, route, request)
		require.NoError(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, 200, response.StatusCode)
	})

	t.Run("禁用的路由", func(t *testing.T) {
		config := config.ServerlessConfig{
			Enabled: true,
		}

		manager, err := serverless.NewManager(config, logger)
		require.NoError(t, err)

		// 创建禁用的路由
		route := &serverless.ServerlessRoute{
			Name:         "disabled-route",
			Path:         "/api/v1/disabled",
			Provider:     "aws_lambda",
			FunctionName: "test-function",
			Enabled:      false, // 禁用
		}

		request := &serverless.InvocationRequest{
			Payload: []byte(`{"test": true}`),
		}

		// 路由到禁用的函数应该失败
		ctx := context.Background()
		_, err = manager.RouteToFunction(ctx, route, request)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "已禁用")
	})
}

// TestServerlessInvocationRequest 测试调用请求
func TestServerlessInvocationRequest(t *testing.T) {
	t.Run("请求创建", func(t *testing.T) {
		request := &serverless.InvocationRequest{
			FunctionName:   "test-function",
			InvocationType: "sync",
			Payload:        []byte(`{"message": "test"}`),
			Headers: map[string]string{
				"Content-Type": "application/json",
				"X-Custom":     "value",
			},
			QueryParams: map[string]string{
				"param1": "value1",
				"param2": "value2",
			},
			PathParams: map[string]string{
				"id": "123",
			},
			Context: map[string]interface{}{
				"user_id": "user123",
				"role":    "admin",
			},
			Timeout:    30 * time.Second,
			RetryCount: 3,
		}

		assert.Equal(t, "test-function", request.FunctionName)
		assert.Equal(t, "sync", request.InvocationType)
		assert.Equal(t, []byte(`{"message": "test"}`), request.Payload)
		assert.Len(t, request.Headers, 2)
		assert.Len(t, request.QueryParams, 2)
		assert.Len(t, request.PathParams, 1)
		assert.Len(t, request.Context, 2)
		assert.Equal(t, 30*time.Second, request.Timeout)
		assert.Equal(t, 3, request.RetryCount)
	})
}

// TestServerlessInvocationResponse 测试调用响应
func TestServerlessInvocationResponse(t *testing.T) {
	t.Run("响应创建", func(t *testing.T) {
		response := &serverless.InvocationResponse{
			StatusCode:      200,
			Payload:         []byte(`{"result": "success"}`),
			Headers:         map[string]string{"Content-Type": "application/json"},
			Duration:        100 * time.Millisecond,
			BilledDuration:  100 * time.Millisecond,
			MemoryUsed:      128 * 1024 * 1024, // 128MB
			LogOutput:       "Function executed successfully",
			RequestID:       "req-12345",
			ExecutionEnv:    map[string]interface{}{"runtime": "nodejs18.x"},
		}

		assert.Equal(t, 200, response.StatusCode)
		assert.Equal(t, []byte(`{"result": "success"}`), response.Payload)
		assert.Equal(t, "application/json", response.Headers["Content-Type"])
		assert.Equal(t, 100*time.Millisecond, response.Duration)
		assert.Equal(t, int64(128*1024*1024), response.MemoryUsed)
		assert.Equal(t, "req-12345", response.RequestID)
		assert.Equal(t, "nodejs18.x", response.ExecutionEnv["runtime"])
	})
}

// TestLightweightProviders 测试轻量级Serverless提供者
func TestLightweightProviders(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("OpenFaaS提供者", func(t *testing.T) {
		config := serverless.OpenFaaSConfig{
			GatewayURL:     "http://localhost:8080",
			Username:       "admin",
			Password:       "password",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewOpenFaaSProvider(config, logger)
		require.NoError(t, err)
		assert.Equal(t, "openfaas", provider.Name())

		// 测试函数调用
		request := &serverless.InvocationRequest{
			FunctionName:   "hello-openfaas",
			InvocationType: "sync",
			Payload:        []byte(`{"message": "test"}`),
		}

		ctx := context.Background()
		response, err := provider.InvokeFunction(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 200, response.StatusCode)
		assert.Contains(t, string(response.Payload), "OpenFaaS")
	})

	t.Run("Fn Project提供者", func(t *testing.T) {
		config := serverless.FnProjectConfig{
			APIURL:         "http://localhost:8080",
			AppName:        "myapp",
			Token:          "test-token",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewFnProjectProvider(config, logger)
		require.NoError(t, err)
		assert.Equal(t, "fn_project", provider.Name())

		// 测试函数调用
		request := &serverless.InvocationRequest{
			FunctionName:   "hello-fn",
			InvocationType: "sync",
			Payload:        []byte(`{"message": "test"}`),
		}

		ctx := context.Background()
		response, err := provider.InvokeFunction(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 200, response.StatusCode)
		assert.Contains(t, string(response.Payload), "Fn Project")
	})

	t.Run("WebAssembly提供者", func(t *testing.T) {
		config := serverless.WebAssemblyConfig{
			RuntimePath:    "/usr/local/bin/wasmtime",
			ModulesPath:    "/app/wasm-modules",
			MaxInstances:   100,
			DefaultTimeout: 10 * time.Second,
		}

		provider, err := serverless.NewWebAssemblyProvider(config, logger)
		require.NoError(t, err)
		assert.Equal(t, "webassembly", provider.Name())

		// 测试函数调用
		request := &serverless.InvocationRequest{
			FunctionName:   "hello-wasm",
			InvocationType: "sync",
			Payload:        []byte(`{"message": "test"}`),
		}

		ctx := context.Background()
		response, err := provider.InvokeFunction(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 200, response.StatusCode)
		assert.Contains(t, string(response.Payload), "WebAssembly")

		// WebAssembly应该使用更少的内存
		assert.Equal(t, int64(16*1024*1024), response.MemoryUsed)
	})
}

// TestLocalProviders 测试本地/自托管提供者
func TestLocalProviders(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("LocalStack提供者", func(t *testing.T) {
		config := serverless.LocalStackConfig{
			EndpointURL:    "http://localhost:4566",
			Region:         "us-east-1",
			AccessKeyID:    "test",
			SecretKey:      "test",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewLocalStackProvider(config, logger)
		require.NoError(t, err)
		assert.Equal(t, "localstack", provider.Name())

		// 测试函数调用
		request := &serverless.InvocationRequest{
			FunctionName:   "hello-localstack",
			InvocationType: "sync",
			Payload:        []byte(`{"message": "test"}`),
		}

		ctx := context.Background()
		response, err := provider.InvokeFunction(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 200, response.StatusCode)
		assert.Contains(t, string(response.Payload), "LocalStack")
	})

	t.Run("Supabase Functions提供者", func(t *testing.T) {
		config := serverless.SupabaseFunctionsConfig{
			ProjectURL:     "https://test.supabase.co",
			AnonKey:        "test-anon-key",
			ServiceKey:     "test-service-key",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewSupabaseFunctionsProvider(config, logger)
		require.NoError(t, err)
		assert.Equal(t, "supabase_functions", provider.Name())

		// 测试函数调用
		request := &serverless.InvocationRequest{
			FunctionName:   "hello-supabase",
			InvocationType: "sync",
			Payload:        []byte(`{"message": "test"}`),
		}

		ctx := context.Background()
		response, err := provider.InvokeFunction(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 200, response.StatusCode)
		assert.Contains(t, string(response.Payload), "Supabase")

		// Supabase使用Deno运行时
		assert.Equal(t, "deno", response.ExecutionEnv["runtime"])
	})

	t.Run("Appwrite Functions提供者", func(t *testing.T) {
		config := serverless.AppwriteFunctionsConfig{
			Endpoint:       "https://appwrite.example.com/v1",
			ProjectID:      "test-project",
			APIKey:         "test-api-key",
			DefaultTimeout: 30 * time.Second,
		}

		provider, err := serverless.NewAppwriteFunctionsProvider(config, logger)
		require.NoError(t, err)
		assert.Equal(t, "appwrite_functions", provider.Name())

		// 测试函数调用
		request := &serverless.InvocationRequest{
			FunctionName:   "hello-appwrite",
			InvocationType: "sync",
			Payload:        []byte(`{"message": "test"}`),
		}

		ctx := context.Background()
		response, err := provider.InvokeFunction(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 200, response.StatusCode)
		assert.Contains(t, string(response.Payload), "Appwrite")
	})
}

// TestMultiProviderManager 测试多提供者管理器
func TestMultiProviderManager(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("多提供者注册", func(t *testing.T) {
		config := config.ServerlessConfig{
			Enabled: true,
		}

		manager, err := serverless.NewManager(config, logger)
		require.NoError(t, err)

		// 注册多个提供者
		providers := []serverless.ServerlessProvider{
			mustCreateOpenFaaSProvider(logger),
			mustCreateFnProjectProvider(logger),
			mustCreateWebAssemblyProvider(logger),
			mustCreateLocalStackProvider(logger),
		}

		for _, provider := range providers {
			err := manager.RegisterProvider(provider)
			assert.NoError(t, err)
		}

		// 验证所有提供者都已注册
		registeredProviders := manager.GetProviders()
		assert.Len(t, registeredProviders, 4)

		// 验证可以获取每个提供者
		providerNames := []string{"openfaas", "fn_project", "webassembly", "localstack"}
		for _, name := range providerNames {
			provider, err := manager.GetProvider(name)
			assert.NoError(t, err)
			assert.Equal(t, name, provider.Name())
		}
	})

	t.Run("跨提供者函数调用", func(t *testing.T) {
		config := config.ServerlessConfig{
			Enabled: true,
		}

		manager, err := serverless.NewManager(config, logger)
		require.NoError(t, err)

		// 注册提供者
		providers := []serverless.ServerlessProvider{
			mustCreateOpenFaaSProvider(logger),
			mustCreateWebAssemblyProvider(logger),
		}

		for _, provider := range providers {
			err := manager.RegisterProvider(provider)
			require.NoError(t, err)
		}

		// 测试不同提供者的函数调用
		testCases := []struct {
			provider     string
			functionName string
		}{
			{"openfaas", "hello-openfaas"},
			{"webassembly", "hello-wasm"},
		}

		for _, tc := range testCases {
			request := &serverless.InvocationRequest{
				InvocationType: "sync",
				Payload:        []byte(`{"test": true}`),
			}

			ctx := context.Background()
			response, err := manager.InvokeFunction(ctx, tc.provider, tc.functionName, request)
			require.NoError(t, err)
			assert.Equal(t, 200, response.StatusCode)
			assert.NotEmpty(t, response.RequestID)
		}
	})
}

// 辅助函数
func mustCreateOpenFaaSProvider(logger *telemetry.Logger) serverless.ServerlessProvider {
	provider, err := serverless.NewOpenFaaSProvider(serverless.OpenFaaSConfig{
		GatewayURL:     "http://localhost:8080",
		DefaultTimeout: 30 * time.Second,
	}, logger)
	if err != nil {
		panic(err)
	}
	return provider
}

func mustCreateFnProjectProvider(logger *telemetry.Logger) serverless.ServerlessProvider {
	provider, err := serverless.NewFnProjectProvider(serverless.FnProjectConfig{
		APIURL:         "http://localhost:8080",
		AppName:        "test",
		DefaultTimeout: 30 * time.Second,
	}, logger)
	if err != nil {
		panic(err)
	}
	return provider
}

func mustCreateWebAssemblyProvider(logger *telemetry.Logger) serverless.ServerlessProvider {
	provider, err := serverless.NewWebAssemblyProvider(serverless.WebAssemblyConfig{
		RuntimePath:    "/usr/local/bin/wasmtime",
		ModulesPath:    "/app/wasm",
		MaxInstances:   100,
		DefaultTimeout: 10 * time.Second,
	}, logger)
	if err != nil {
		panic(err)
	}
	return provider
}

func mustCreateLocalStackProvider(logger *telemetry.Logger) serverless.ServerlessProvider {
	provider, err := serverless.NewLocalStackProvider(serverless.LocalStackConfig{
		EndpointURL:    "http://localhost:4566",
		Region:         "us-east-1",
		AccessKeyID:    "test",
		SecretKey:      "test",
		DefaultTimeout: 30 * time.Second,
	}, logger)
	if err != nil {
		panic(err)
	}
	return provider
}

// BenchmarkServerlessInvocation Serverless调用性能基准测试
func BenchmarkServerlessInvocation(b *testing.B) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(b, err)

	config := serverless.AWSLambdaConfig{
		Region:         "us-east-1",
		DefaultTimeout: 30 * time.Second,
	}

	provider, err := serverless.NewAWSLambdaProvider(config, logger)
	require.NoError(b, err)

	request := &serverless.InvocationRequest{
		FunctionName:   "benchmark-function",
		InvocationType: "sync",
		Payload:        []byte(`{"benchmark": true}`),
	}

	ctx := context.Background()

	b.Run("FunctionInvocation", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := provider.InvokeFunction(ctx, request)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("GetStats", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			stats := provider.GetStats()
			_ = stats
		}
	})
}
