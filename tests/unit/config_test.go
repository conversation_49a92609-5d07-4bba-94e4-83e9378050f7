package unit

import (
	"context"
	"fmt"
	"testing"
	"time"

	"api-gateway/pkg/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestConfigValidation 测试配置验证
func TestConfigValidation(t *testing.T) {
	validator := config.NewDefaultConfigValidator()

	t.Run("有效配置验证", func(t *testing.T) {
		cfg := &config.Config{
			Server: config.ServerConfig{
				Address:      ":8080",
				ReadTimeout:  30,
				WriteTimeout: 30,
				IdleTimeout:  120,
			},
			Logging: config.LoggingConfig{
				Level:  "info",
				Format: "json",
				Output: "stdout",
			},
			Plugins: config.PluginConfig{
				Directory: "plugins",
				Plugins:   make(map[string]interface{}),
			},
			Routes: []config.RouteConfig{
				{
					Name:   "test-route",
					Path:   "/api/v1/test",
					Method: "GET",
					Upstream: config.UpstreamConfig{
						Type:         "static",
						LoadBalancer: "round_robin",
						Servers: []config.ServerTarget{
							{
								Host: "localhost",
								Port: 8081,
							},
						},
					},
					Timeout: 30 * time.Second,
					Retries: 3,
				},
			},
		}

		err := validator.ValidateConfig(cfg)
		assert.NoError(t, err)
	})

	t.Run("无效服务器配置", func(t *testing.T) {
		cfg := &config.Config{
			Server: config.ServerConfig{
				Address:      "", // 无效地址
				ReadTimeout:  30,
				WriteTimeout: 30,
			},
		}

		err := validator.ValidateConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "server configuration error")
	})

	t.Run("无效路由配置", func(t *testing.T) {
		cfg := &config.Config{
			Server: config.ServerConfig{
				Address:      ":8080",
				ReadTimeout:  30,
				WriteTimeout: 30,
			},
			Routes: []config.RouteConfig{
				{
					Name:   "", // 无效名称
					Path:   "/api/v1/test",
					Method: "GET",
				},
			},
		}

		err := validator.ValidateConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "route 0 configuration error")
	})
}

// TestConfigLoader 测试配置加载器
func TestConfigLoader(t *testing.T) {
	t.Run("加载不存在的配置文件", func(t *testing.T) {
		_, err := config.LoadConfig("nonexistent.yaml")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "configuration file not found")
	})

	t.Run("创建默认配置", func(t *testing.T) {
		// 测试默认配置创建
		cfg := &config.Config{
			Server: config.ServerConfig{
				Address:      ":8080",
				ReadTimeout:  30,
				WriteTimeout: 30,
				IdleTimeout:  120,
			},
			Logging: config.LoggingConfig{
				Level:  "info",
				Format: "json",
				Output: "stdout",
			},
		}

		assert.Equal(t, ":8080", cfg.Server.Address)
		assert.Equal(t, 30, cfg.Server.ReadTimeout)
		assert.Equal(t, "info", cfg.Logging.Level)
		assert.Equal(t, "json", cfg.Logging.Format)
	})
}

// TestDynamicConfig 测试动态配置
func TestDynamicConfig(t *testing.T) {
	t.Run("内存存储基本操作", func(t *testing.T) {
		storage := config.NewMemoryStorage()
		defer storage.Close()

		// 测试存储
		testData := []byte(`{"server":{"address":":9000"}}`)

		err := storage.Set("test-key", testData)
		require.NoError(t, err)

		// 测试获取
		retrievedData, err := storage.Get("test-key")
		require.NoError(t, err)
		assert.Equal(t, testData, retrievedData)

		// 测试删除
		err = storage.Delete("test-key")
		require.NoError(t, err)

		// 验证删除
		_, err = storage.Get("test-key")
		assert.Error(t, err)
	})

	t.Run("配置监听器", func(t *testing.T) {
		// 简化的监听器测试
		storage := config.NewMemoryStorage()
		defer storage.Close()

		// 测试监听功能
		eventCh, err := storage.Watch("test-key")
		require.NoError(t, err)

		// 在另一个goroutine中设置值
		go func() {
			time.Sleep(100 * time.Millisecond)
			_ = storage.Set("test-key", []byte("test-value"))
		}()

		// 等待事件
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		defer cancel()

		select {
		case event := <-eventCh:
			assert.Equal(t, "test-key", event.Key)
			assert.Equal(t, "set", event.Type)
		case <-ctx.Done():
			t.Fatal("超时等待配置变更事件")
		}
	})
}

// TestConfigTypes 测试配置类型
func TestConfigTypes(t *testing.T) {
	t.Run("服务器配置", func(t *testing.T) {
		cfg := config.ServerConfig{
			Address:      ":8080",
			ReadTimeout:  30,
			WriteTimeout: 30,
			IdleTimeout:  120,
			TLS: config.TLSConfig{
				Enabled:  true,
				CertFile: "cert.pem",
				KeyFile:  "key.pem",
			},
		}

		assert.Equal(t, ":8080", cfg.Address)
		assert.True(t, cfg.TLS.Enabled)
	})

	t.Run("认证配置", func(t *testing.T) {
		cfg := config.AuthConfig{
			JWT: config.JWTConfig{
				Enabled:    true,
				Secret:     "test-secret",
				Algorithm:  "HS256",
				Expiration: 3600,
			},
			APIKey: config.APIKeyConfig{
				Enabled:     true,
				HeaderName:  "X-API-Key",
				QueryParam:  "api_key",
			},
		}

		assert.True(t, cfg.JWT.Enabled)
		assert.Equal(t, "HS256", cfg.JWT.Algorithm)
		assert.Equal(t, "X-API-Key", cfg.APIKey.HeaderName)
	})

	t.Run("安全配置", func(t *testing.T) {
		cfg := config.SecurityConfig{
			RateLimit: config.RateLimitConfig{
				Enabled:   true,
				Algorithm: "token_bucket",
				Rules: []config.RateLimitRule{
					{
						Path:   "/api/*",
						Method: "*",
						Rate:   100,
						Burst:  200,
						Window: time.Minute,
						KeyBy:  "ip",
					},
				},
			},
			CORS: config.CORSConfig{
				Enabled:          true,
				AllowedOrigins:   []string{"*"},
				AllowedMethods:   []string{"GET", "POST"},
				AllowCredentials: true,
			},
		}

		assert.True(t, cfg.RateLimit.Enabled)
		assert.Equal(t, "token_bucket", cfg.RateLimit.Algorithm)
		assert.Len(t, cfg.RateLimit.Rules, 1)
		assert.True(t, cfg.CORS.Enabled)
	})
}

// BenchmarkConfigValidation 配置验证性能基准测试
func BenchmarkConfigValidation(b *testing.B) {
	validator := config.NewDefaultConfigValidator()
	
	cfg := &config.Config{
		Server: config.ServerConfig{
			Address:      ":8080",
			ReadTimeout:  30,
			WriteTimeout: 30,
		},
		Routes: []config.RouteConfig{
			{
				Name:   "test-route",
				Path:   "/api/v1/test",
				Method: "GET",
				Upstream: config.UpstreamConfig{
					Type:         "static",
					LoadBalancer: "round_robin",
					Servers: []config.ServerTarget{
						{Host: "localhost", Port: 8081},
					},
				},
			},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = validator.ValidateConfig(cfg)
	}
}

// BenchmarkMemoryStorage 内存存储性能基准测试
func BenchmarkMemoryStorage(b *testing.B) {
	storage := config.NewMemoryStorage()
	defer storage.Close()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			key := fmt.Sprintf("test-key-%d", i)
			_ = storage.Set(key, []byte("test-data"))
			_, _ = storage.Get(key)
			_ = storage.Delete(key)
			i++
		}
	})
}
