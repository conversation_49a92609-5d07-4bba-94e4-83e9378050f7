package unit

import (
	"context"
	"testing"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/monitoring"
	"api-gateway/pkg/performance"
	"api-gateway/pkg/telemetry"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestPerformanceOptimizer 测试性能优化器
func TestPerformanceOptimizer(t *testing.T) {
	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	// 创建测试指标系统
	metrics, err := telemetry.NewMetrics(config.MetricsConfig{
		Enabled: false, // 禁用以简化测试
	})
	require.NoError(t, err)

	t.Run("优化器创建和配置", func(t *testing.T) {
		config := performance.DefaultOptimizerConfig()
		optimizer := performance.NewOptimizer(config, logger, metrics)

		assert.NotNil(t, optimizer)

		// 测试默认配置
		assert.True(t, config.Enabled)
		assert.Equal(t, 30*time.Second, config.MonitorInterval)
		assert.True(t, config.GCConfig.Enabled)
		assert.True(t, config.MemoryConfig.Enabled)
		assert.Equal(t, int64(512), config.MemoryConfig.ThresholdMB)
	})

	t.Run("优化器启动和停止", func(t *testing.T) {
		config := performance.OptimizerConfig{
			Enabled:         true,
			MonitorInterval: 100 * time.Millisecond, // 快速测试
			GCConfig: performance.GCConfig{
				Enabled:       false, // 禁用GC优化以简化测试
				TargetPercent: 100,
			},
			MemoryConfig: performance.MemoryConfig{
				Enabled:         false, // 禁用内存优化以简化测试
				ThresholdMB:     512,
				CleanupInterval: 1 * time.Second,
			},
		}

		optimizer := performance.NewOptimizer(config, logger, metrics)

		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		defer cancel()

		// 启动优化器
		err := optimizer.Start(ctx)
		assert.NoError(t, err)

		// 等待一段时间让优化器运行
		time.Sleep(200 * time.Millisecond)

		// 获取性能统计
		stats := optimizer.GetStats()
		assert.NotNil(t, stats)
		assert.Greater(t, stats.MemoryUsage, int64(0))
		assert.Greater(t, stats.GoroutineCount, 0)

		// 停止优化器
		err = optimizer.Stop()
		assert.NoError(t, err)
	})

	t.Run("禁用优化器", func(t *testing.T) {
		config := performance.OptimizerConfig{
			Enabled: false,
		}

		optimizer := performance.NewOptimizer(config, logger, metrics)

		ctx := context.Background()
		err := optimizer.Start(ctx)
		assert.NoError(t, err)

		err = optimizer.Stop()
		assert.NoError(t, err)
	})

	t.Run("性能统计", func(t *testing.T) {
		config := performance.DefaultOptimizerConfig()
		config.MonitorInterval = 50 * time.Millisecond
		config.GCConfig.Enabled = false
		config.MemoryConfig.Enabled = false

		optimizer := performance.NewOptimizer(config, logger, metrics)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()

		err := optimizer.Start(ctx)
		require.NoError(t, err)

		// 等待收集统计数据
		time.Sleep(100 * time.Millisecond)

		stats := optimizer.GetStats()
		assert.NotNil(t, stats)
		assert.Greater(t, stats.MemoryUsage, int64(0))
		assert.Greater(t, stats.GoroutineCount, 0)
		assert.GreaterOrEqual(t, stats.GCCount, uint32(0))
		assert.False(t, stats.Timestamp.IsZero())

		optimizer.Stop()
	})
}

// TestAlertManager 测试告警管理器
func TestAlertManager(t *testing.T) {
	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	// 创建测试指标系统
	metrics, err := telemetry.NewMetrics(config.MetricsConfig{
		Enabled: false,
	})
	require.NoError(t, err)

	t.Run("告警管理器创建和配置", func(t *testing.T) {
		config := monitoring.DefaultAlertConfig()
		alertManager := monitoring.NewAlertManager(config, logger, metrics)

		assert.NotNil(t, alertManager)
		assert.True(t, config.Enabled)
		assert.Equal(t, 30*time.Second, config.CheckInterval)
	})

	t.Run("告警管理器启动和停止", func(t *testing.T) {
		config := monitoring.AlertConfig{
			Enabled:       true,
			CheckInterval: 100 * time.Millisecond,
			Notifications: []monitoring.NotificationConfig{
				{
					Type:    "log",
					Enabled: true,
					Config:  map[string]interface{}{},
				},
			},
		}

		alertManager := monitoring.NewAlertManager(config, logger, metrics)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()

		// 启动告警管理器
		err := alertManager.Start(ctx)
		assert.NoError(t, err)

		// 等待一段时间让告警管理器运行
		time.Sleep(200 * time.Millisecond)

		// 停止告警管理器
		err = alertManager.Stop()
		assert.NoError(t, err)
	})

	t.Run("添加告警规则", func(t *testing.T) {
		config := monitoring.DefaultAlertConfig()
		config.Enabled = false // 禁用自动启动

		alertManager := monitoring.NewAlertManager(config, logger, metrics)

		rule := monitoring.AlertRule{
			Name:        "测试规则",
			Description: "测试告警规则",
			Metric:      "test_metric",
			Condition:   ">",
			Threshold:   100,
			Duration:    1 * time.Minute,
			Severity:    "warning",
			Enabled:     true,
		}

		alertManager.AddRule(rule)

		// 验证规则已添加（这里需要访问内部状态，实际实现中可能需要提供公共方法）
		// 由于当前实现没有GetRules方法，我们只能验证AddRule不会panic
	})

	t.Run("获取告警列表", func(t *testing.T) {
		config := monitoring.DefaultAlertConfig()
		config.Enabled = false

		alertManager := monitoring.NewAlertManager(config, logger, metrics)

		alerts := alertManager.GetAlerts()
		assert.NotNil(t, alerts)
		assert.Len(t, alerts, 0) // 初始状态应该没有告警
	})

	t.Run("禁用告警管理器", func(t *testing.T) {
		config := monitoring.AlertConfig{
			Enabled: false,
		}

		alertManager := monitoring.NewAlertManager(config, logger, metrics)

		ctx := context.Background()
		err := alertManager.Start(ctx)
		assert.NoError(t, err)

		err = alertManager.Stop()
		assert.NoError(t, err)
	})
}

// TestNotifiers 测试通知器
func TestNotifiers(t *testing.T) {
	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	// 创建测试告警
	alert := &monitoring.Alert{
		ID:        "test-alert-001",
		RuleName:  "测试规则",
		Message:   "这是一个测试告警",
		Severity:  "warning",
		Value:     150.0,
		Threshold: 100.0,
		Labels:    map[string]string{"service": "api-gateway"},
		Status:    "firing",
		StartsAt:  time.Now(),
		UpdatedAt: time.Now(),
	}

	t.Run("日志通知器", func(t *testing.T) {
		notifier := monitoring.NewLogNotifier(logger)

		assert.Equal(t, "log", notifier.Type())

		ctx := context.Background()
		err := notifier.Send(ctx, alert)
		assert.NoError(t, err)
	})

	t.Run("Webhook通知器", func(t *testing.T) {
		config := map[string]interface{}{
			"url": "http://example.com/webhook",
			"headers": map[string]interface{}{
				"Authorization": "Bearer token",
			},
			"timeout": "5s",
		}

		notifier := monitoring.NewWebhookNotifier(config, logger)

		assert.Equal(t, "webhook", notifier.Type())

		// 注意：这个测试会失败，因为URL不存在
		// 在实际测试中，应该使用模拟的HTTP服务器
		ctx := context.Background()
		err := notifier.Send(ctx, alert)
		assert.Error(t, err) // 预期会失败
	})

	t.Run("Slack通知器", func(t *testing.T) {
		config := map[string]interface{}{
			"webhook_url": "https://hooks.slack.com/services/test",
			"channel":     "#alerts",
			"username":    "API Gateway Bot",
			"icon_emoji":  ":warning:",
		}

		notifier := monitoring.NewSlackNotifier(config, logger)

		assert.Equal(t, "slack", notifier.Type())

		// 注意：这个测试可能会失败，因为URL不存在
		ctx := context.Background()
		err := notifier.Send(ctx, alert)
		// Slack通知器可能会成功（如果URL格式正确）或失败（如果网络不可达）
		// 我们不强制要求特定的结果
		_ = err
	})

	t.Run("邮件通知器", func(t *testing.T) {
		config := map[string]interface{}{
			"smtp_host": "smtp.example.com",
			"smtp_port": 587,
			"username":  "<EMAIL>",
			"password":  "password",
			"from":      "<EMAIL>",
			"to":        []interface{}{"<EMAIL>"},
		}

		notifier := monitoring.NewEmailNotifier(config, logger)

		assert.Equal(t, "email", notifier.Type())

		ctx := context.Background()
		err := notifier.Send(ctx, alert)
		assert.NoError(t, err) // 邮件通知器目前只记录日志，不会失败
	})
}

// BenchmarkPerformanceOptimizer 性能优化器基准测试
func BenchmarkPerformanceOptimizer(b *testing.B) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})

	metrics, _ := telemetry.NewMetrics(config.MetricsConfig{
		Enabled: false,
	})

	config := performance.DefaultOptimizerConfig()
	config.MonitorInterval = 1 * time.Second // 减少监控频率

	b.Run("优化器创建", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			optimizer := performance.NewOptimizer(config, logger, metrics)
			_ = optimizer
		}
	})

	b.Run("性能统计获取", func(b *testing.B) {
		optimizer := performance.NewOptimizer(config, logger, metrics)
		ctx := context.Background()
		optimizer.Start(ctx)
		defer optimizer.Stop()

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			stats := optimizer.GetStats()
			_ = stats
		}
	})
}

// BenchmarkAlertManager 告警管理器基准测试
func BenchmarkAlertManager(b *testing.B) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})

	metrics, _ := telemetry.NewMetrics(config.MetricsConfig{
		Enabled: false,
	})

	config := monitoring.DefaultAlertConfig()
	config.CheckInterval = 1 * time.Second

	b.Run("告警管理器创建", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			alertManager := monitoring.NewAlertManager(config, logger, metrics)
			_ = alertManager
		}
	})

	b.Run("告警列表获取", func(b *testing.B) {
		alertManager := monitoring.NewAlertManager(config, logger, metrics)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			alerts := alertManager.GetAlerts()
			_ = alerts
		}
	})
}

// BenchmarkNotifiers 通知器基准测试
func BenchmarkNotifiers(b *testing.B) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})

	alert := &monitoring.Alert{
		ID:        "bench-alert",
		RuleName:  "基准测试规则",
		Message:   "基准测试告警消息",
		Severity:  "info",
		Value:     100.0,
		Threshold: 50.0,
		Status:    "firing",
		StartsAt:  time.Now(),
		UpdatedAt: time.Now(),
	}

	ctx := context.Background()

	b.Run("日志通知器", func(b *testing.B) {
		notifier := monitoring.NewLogNotifier(logger)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			notifier.Send(ctx, alert)
		}
	})
}
