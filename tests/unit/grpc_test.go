package unit

import (
	"context"
	"net"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/test/bufconn"

	"api-gateway/pkg/config"
	grpcpkg "api-gateway/pkg/grpc"
	"api-gateway/pkg/telemetry"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestGRPCClientManager 测试gRPC客户端管理器
func TestGRPCClientManager(t *testing.T) {
	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("客户端管理器创建", func(t *testing.T) {
		config := config.GRPCClientConfig{
			DefaultTimeout: 30,
			MaxRecvMsgSize: 1024 * 1024,
			MaxSendMsgSize: 1024 * 1024,
			LoadBalancer:   "round_robin",
			KeepAlive: config.GRPCKeepAliveConfig{
				Time:                30,
				Timeout:             5,
				PermitWithoutStream: true,
			},
		}

		manager := grpcpkg.NewClientManager(config, logger)
		assert.NotNil(t, manager)

		// 测试统计信息
		stats := manager.GetStats()
		assert.Equal(t, 0, stats["total_connections"])
	})

	t.Run("连接管理", func(t *testing.T) {
		config := config.GRPCClientConfig{
			DefaultTimeout: 5,
		}

		manager := grpcpkg.NewClientManager(config, logger)

		// 测试无效地址
		_, err := manager.GetConnection("invalid-address")
		assert.Error(t, err)

		// 测试连接状态（对于不存在的连接应该是SHUTDOWN）
		state := manager.GetConnectionState("non-existent-address")
		assert.Equal(t, "SHUTDOWN", state.String())

		// 测试关闭连接
		err = manager.CloseConnection("invalid-address")
		assert.NoError(t, err)

		// 测试关闭所有连接
		err = manager.CloseAll()
		assert.NoError(t, err)
	})
}

// TestGRPCHealthChecker 测试gRPC健康检查器
func TestGRPCHealthChecker(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("健康检查器创建", func(t *testing.T) {
		healthConfig := config.GRPCHealthConfig{
			Enabled:            true,
			Interval:           30,
			Timeout:            5,
			UnhealthyThreshold: 3,
			HealthyThreshold:   2,
		}

		clientConfig := config.GRPCClientConfig{
			DefaultTimeout: 5,
		}
		clientManager := grpcpkg.NewClientManager(clientConfig, logger)

		checker := grpcpkg.NewHealthChecker(healthConfig, clientManager, logger)
		assert.NotNil(t, checker)

		// 测试统计信息
		stats := checker.GetHealthStats()
		assert.True(t, stats["health_check_enabled"].(bool))
		assert.Equal(t, 30, stats["check_interval"])
		assert.Equal(t, 5, stats["check_timeout"])
	})

	t.Run("健康检查功能", func(t *testing.T) {
		healthConfig := config.GRPCHealthConfig{
			Enabled:  true,
			Interval: 1,
			Timeout:  5,
		}

		clientConfig := config.GRPCClientConfig{
			DefaultTimeout: 5,
		}
		clientManager := grpcpkg.NewClientManager(clientConfig, logger)

		checker := grpcpkg.NewHealthChecker(healthConfig, clientManager, logger)

		// 测试检查不存在的服务
		health := checker.CheckService("localhost:9999", "test-service")
		assert.NotNil(t, health)
		assert.Equal(t, "localhost:9999", health.Address)
		assert.Equal(t, "test-service", health.ServiceName)

		// 测试获取服务健康状态
		retrievedHealth := checker.GetServiceHealth("localhost:9999", "test-service")
		assert.NotNil(t, retrievedHealth)
		assert.Equal(t, health.Address, retrievedHealth.Address)

		// 测试获取所有服务健康状态
		allHealth := checker.GetAllServiceHealth()
		assert.Len(t, allHealth, 1)

		// 测试服务是否健康
		isHealthy := checker.IsServiceHealthy("localhost:9999", "test-service")
		assert.False(t, isHealthy) // 应该不健康，因为服务不存在

		// 测试移除服务
		checker.RemoveService("localhost:9999", "test-service")
		retrievedHealth = checker.GetServiceHealth("localhost:9999", "test-service")
		assert.Nil(t, retrievedHealth)
	})

	t.Run("健康检查启动停止", func(t *testing.T) {
		healthConfig := config.GRPCHealthConfig{
			Enabled:  true,
			Interval: 1,
			Timeout:  5,
		}

		clientConfig := config.GRPCClientConfig{
			DefaultTimeout: 5,
		}
		clientManager := grpcpkg.NewClientManager(clientConfig, logger)

		checker := grpcpkg.NewHealthChecker(healthConfig, clientManager, logger)

		// 启动健康检查器
		checker.Start()

		// 等待一段时间
		time.Sleep(100 * time.Millisecond)

		// 停止健康检查器
		checker.Stop()
	})
}

// TestGRPCDiscoveryManager 测试gRPC服务发现管理器
func TestGRPCDiscoveryManager(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("服务发现管理器创建", func(t *testing.T) {
		config := config.DiscoveryConfig{
			Type: "static",
		}

		manager := grpcpkg.NewDiscoveryManager(config, logger)
		assert.NotNil(t, manager)
	})

	t.Run("静态服务发现", func(t *testing.T) {
		config := config.DiscoveryConfig{
			Type: "static",
		}

		manager := grpcpkg.NewDiscoveryManager(config, logger)

		// 启动管理器
		err := manager.Start()
		assert.NoError(t, err)

		// 测试发现不存在的服务
		instances, err := manager.DiscoverService("test-service")
		assert.NoError(t, err)
		assert.Len(t, instances, 0)

		// 测试获取服务地址
		_, err = manager.GetServiceAddress("test-service")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "没有可用实例")

		// 停止管理器
		err = manager.Stop()
		assert.NoError(t, err)
	})
}

// TestGRPCManager 测试gRPC管理器
func TestGRPCManager(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("gRPC管理器创建", func(t *testing.T) {
		grpcConfig := config.GRPCConfig{
			Enabled: true,
			Server: config.GRPCServerConfig{
				Address:           ":0", // 使用随机端口
				Reflection:        true,
				MaxRecvMsgSize:    1024 * 1024,
				MaxSendMsgSize:    1024 * 1024,
				ConnectionTimeout: 30,
			},
			Client: config.GRPCClientConfig{
				DefaultTimeout: 30,
				MaxRecvMsgSize: 1024 * 1024,
				MaxSendMsgSize: 1024 * 1024,
			},
			Gateway: config.GRPCGatewayConfig{
				Enabled:    true,
				PathPrefix: "/grpc",
			},
			HealthCheck: config.GRPCHealthConfig{
				Enabled:  true,
				Interval: 30,
				Timeout:  5,
			},
		}

		discoveryConfig := config.DiscoveryConfig{
			Type: "static",
		}

		manager := grpcpkg.NewManager(grpcConfig, discoveryConfig, logger)
		assert.NotNil(t, manager)

		// 测试获取组件
		assert.NotNil(t, manager.GetClientManager())
		assert.NotNil(t, manager.GetHealthChecker())
		assert.NotNil(t, manager.GetDiscoveryManager())
		assert.NotNil(t, manager.GetProxyHandler())

		// 测试运行状态
		assert.False(t, manager.IsRunning())
	})

	t.Run("gRPC管理器启动停止", func(t *testing.T) {
		grpcConfig := config.GRPCConfig{
			Enabled: true,
			Gateway: config.GRPCGatewayConfig{
				Enabled: false, // 禁用网关以避免端口冲突
			},
			HealthCheck: config.GRPCHealthConfig{
				Enabled: false, // 禁用健康检查以加快测试
			},
		}

		discoveryConfig := config.DiscoveryConfig{
			Type: "static",
		}

		manager := grpcpkg.NewManager(grpcConfig, discoveryConfig, logger)

		// 启动管理器
		err := manager.Start()
		assert.NoError(t, err)
		assert.True(t, manager.IsRunning())

		// 停止管理器
		err = manager.Stop()
		assert.NoError(t, err)
		assert.False(t, manager.IsRunning())
	})

	t.Run("禁用gRPC", func(t *testing.T) {
		grpcConfig := config.GRPCConfig{
			Enabled: false,
		}

		discoveryConfig := config.DiscoveryConfig{
			Type: "static",
		}

		manager := grpcpkg.NewManager(grpcConfig, discoveryConfig, logger)

		// 启动管理器（应该跳过）
		err := manager.Start()
		assert.NoError(t, err)
		assert.False(t, manager.IsRunning())
	})
}

// BenchmarkGRPCClientManager gRPC客户端管理器性能基准测试
func BenchmarkGRPCClientManager(b *testing.B) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})

	config := config.GRPCClientConfig{
		DefaultTimeout: 5,
	}

	manager := grpcpkg.NewClientManager(config, logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 测试连接状态检查的性能
		_ = manager.GetConnectionState("test-address")
	}
}

// mockHealthServer 模拟健康检查服务器
type mockHealthServer struct {
	grpc_health_v1.UnimplementedHealthServer
}

func (m *mockHealthServer) Check(ctx context.Context, req *grpc_health_v1.HealthCheckRequest) (*grpc_health_v1.HealthCheckResponse, error) {
	return &grpc_health_v1.HealthCheckResponse{
		Status: grpc_health_v1.HealthCheckResponse_SERVING,
	}, nil
}

// createMockGRPCServer 创建模拟gRPC服务器
func createMockGRPCServer() (*grpc.Server, *bufconn.Listener) {
	listener := bufconn.Listen(1024 * 1024)
	server := grpc.NewServer()
	
	// 注册健康检查服务
	grpc_health_v1.RegisterHealthServer(server, &mockHealthServer{})
	
	go func() {
		server.Serve(listener)
	}()
	
	return server, listener
}

// bufDialer 用于连接到bufconn监听器
func bufDialer(listener *bufconn.Listener) func(context.Context, string) (net.Conn, error) {
	return func(ctx context.Context, url string) (net.Conn, error) {
		return listener.Dial()
	}
}
