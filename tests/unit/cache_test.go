package unit

import (
	"context"
	"testing"
	"time"

	"api-gateway/pkg/cache"
	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestMemoryCache 测试内存缓存
func TestMemoryCache(t *testing.T) {
	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("内存缓存创建", func(t *testing.T) {
		config := config.CacheMemoryConfig{
			MaxSize:         1024 * 1024, // 1MB
			MaxItems:        1000,
			CleanupInterval: "1m",
			EvictionPolicy:  "lru",
		}

		memCache, err := cache.NewMemoryCache(config, logger)
		require.NoError(t, err)
		assert.NotNil(t, memCache)

		// 测试Ping
		err = memCache.Ping(context.Background())
		assert.NoError(t, err)

		// 关闭缓存
		err = memCache.Close()
		assert.NoError(t, err)
	})

	t.Run("基本操作", func(t *testing.T) {
		config := config.CacheMemoryConfig{
			MaxItems: 100,
		}

		memCache, err := cache.NewMemoryCache(config, logger)
		require.NoError(t, err)
		defer memCache.Close()

		ctx := context.Background()

		// 测试Set和Get
		key := "test-key"
		value := []byte("test-value")
		ttl := 5 * time.Minute

		err = memCache.Set(ctx, key, value, ttl)
		assert.NoError(t, err)

		retrievedValue, err := memCache.Get(ctx, key)
		assert.NoError(t, err)
		assert.Equal(t, value, retrievedValue)

		// 测试Exists
		exists, err := memCache.Exists(ctx, key)
		assert.NoError(t, err)
		assert.True(t, exists)

		// 测试Delete
		err = memCache.Delete(ctx, key)
		assert.NoError(t, err)

		// 验证删除
		exists, err = memCache.Exists(ctx, key)
		assert.NoError(t, err)
		assert.False(t, exists)

		// 测试不存在的键
		_, err = memCache.Get(ctx, "non-existent")
		assert.Error(t, err)
		assert.True(t, cache.IsKeyNotFoundError(err))
	})

	t.Run("带标签操作", func(t *testing.T) {
		config := config.CacheMemoryConfig{
			MaxItems: 100,
		}

		memCache, err := cache.NewMemoryCache(config, logger)
		require.NoError(t, err)
		defer memCache.Close()

		ctx := context.Background()

		// 设置带标签的缓存
		key := "tagged-key"
		value := []byte("tagged-value")
		tags := []string{"tag1", "tag2"}

		err = memCache.SetWithTags(ctx, key, value, 5*time.Minute, tags)
		assert.NoError(t, err)

		// 获取值
		retrievedValue, err := memCache.Get(ctx, key)
		assert.NoError(t, err)
		assert.Equal(t, value, retrievedValue)

		// 按标签失效
		err = memCache.InvalidateByTag(ctx, "tag1")
		assert.NoError(t, err)

		// 验证失效
		exists, err := memCache.Exists(ctx, key)
		assert.NoError(t, err)
		assert.False(t, exists)
	})

	t.Run("批量操作", func(t *testing.T) {
		config := config.CacheMemoryConfig{
			MaxItems: 100,
		}

		memCache, err := cache.NewMemoryCache(config, logger)
		require.NoError(t, err)
		defer memCache.Close()

		ctx := context.Background()

		// 批量设置
		items := map[string]cache.CacheItem{
			"key1": {Value: []byte("value1"), TTL: 5 * time.Minute},
			"key2": {Value: []byte("value2"), TTL: 5 * time.Minute},
			"key3": {Value: []byte("value3"), TTL: 5 * time.Minute},
		}

		err = memCache.SetMulti(ctx, items)
		assert.NoError(t, err)

		// 批量获取
		keys := []string{"key1", "key2", "key3", "non-existent"}
		results, err := memCache.GetMulti(ctx, keys)
		assert.NoError(t, err)
		assert.Len(t, results, 3) // 只有3个存在的键

		assert.Equal(t, []byte("value1"), results["key1"])
		assert.Equal(t, []byte("value2"), results["key2"])
		assert.Equal(t, []byte("value3"), results["key3"])

		// 批量删除
		err = memCache.DeleteMulti(ctx, []string{"key1", "key2"})
		assert.NoError(t, err)

		// 验证删除
		exists, _ := memCache.Exists(ctx, "key1")
		assert.False(t, exists)
		exists, _ = memCache.Exists(ctx, "key2")
		assert.False(t, exists)
		exists, _ = memCache.Exists(ctx, "key3")
		assert.True(t, exists)
	})

	t.Run("TTL操作", func(t *testing.T) {
		config := config.CacheMemoryConfig{
			MaxItems: 100,
		}

		memCache, err := cache.NewMemoryCache(config, logger)
		require.NoError(t, err)
		defer memCache.Close()

		ctx := context.Background()

		// 设置短TTL
		key := "ttl-key"
		value := []byte("ttl-value")
		ttl := 100 * time.Millisecond

		err = memCache.Set(ctx, key, value, ttl)
		assert.NoError(t, err)

		// 立即获取应该成功
		retrievedValue, remainingTTL, err := memCache.GetWithTTL(ctx, key)
		assert.NoError(t, err)
		assert.Equal(t, value, retrievedValue)
		assert.True(t, remainingTTL > 0)

		// 等待过期
		time.Sleep(150 * time.Millisecond)

		// 应该已过期
		_, err = memCache.Get(ctx, key)
		assert.Error(t, err)
		assert.True(t, cache.IsKeyNotFoundError(err))
	})

	t.Run("统计信息", func(t *testing.T) {
		config := config.CacheMemoryConfig{
			MaxItems: 100,
		}

		memCache, err := cache.NewMemoryCache(config, logger)
		require.NoError(t, err)
		defer memCache.Close()

		ctx := context.Background()

		// 执行一些操作
		memCache.Set(ctx, "key1", []byte("value1"), 5*time.Minute)
		memCache.Get(ctx, "key1")     // 命中
		memCache.Get(ctx, "key2")     // 未命中
		memCache.Delete(ctx, "key1")

		// 获取统计信息
		stats, err := memCache.Stats(ctx)
		assert.NoError(t, err)
		assert.NotNil(t, stats)

		assert.Equal(t, int64(1), stats.Hits)
		assert.Equal(t, int64(1), stats.Misses)
		assert.Equal(t, int64(1), stats.Sets)
		assert.Equal(t, int64(1), stats.Deletes)
		assert.Equal(t, 0.5, stats.HitRatio)
	})
}

// TestCacheKeyGenerator 测试缓存键生成器
func TestCacheKeyGenerator(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("键生成", func(t *testing.T) {
		config := config.CacheKeyConfig{
			IncludeQueryParams: true,
			IncludeHeaders:     []string{"Authorization", "User-Agent"},
			HashAlgorithm:      "sha256",
		}

		generator := cache.NewDefaultKeyGenerator(config, logger)

		request := &cache.CacheKeyRequest{
			Method:      "GET",
			Path:        "/api/v1/users",
			Query:       map[string]string{"page": "1", "size": "10"},
			Headers:     map[string]string{"Authorization": "Bearer token", "User-Agent": "test"},
			UserID:      "user123",
			ServiceName: "user-service",
		}

		key, err := generator.GenerateKey(context.Background(), request)
		assert.NoError(t, err)
		assert.NotEmpty(t, key)

		// 验证键
		err = generator.ValidateKey(key)
		assert.NoError(t, err)

		// 生成标签
		tags, err := generator.GenerateTags(context.Background(), request)
		assert.NoError(t, err)
		assert.Contains(t, tags, "path:api")
		assert.Contains(t, tags, "method:GET")
		assert.Contains(t, tags, "service:user-service")
		assert.Contains(t, tags, "user:user123")
	})

	t.Run("键验证", func(t *testing.T) {
		config := config.CacheKeyConfig{}
		generator := cache.NewDefaultKeyGenerator(config, logger)

		// 有效键
		err := generator.ValidateKey("valid-key")
		assert.NoError(t, err)

		// 空键
		err = generator.ValidateKey("")
		assert.Error(t, err)

		// 过长键
		longKey := string(make([]byte, 300))
		err = generator.ValidateKey(longKey)
		assert.Error(t, err)

		// 包含非法字符的键
		err = generator.ValidateKey("key with spaces")
		assert.Error(t, err)
	})
}

// TestCacheManager 测试缓存管理器
func TestCacheManager(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("管理器创建", func(t *testing.T) {
		config := config.CacheConfig{
			Enabled:    true,
			Type:       "memory",
			DefaultTTL: "5m",
			Memory: config.CacheMemoryConfig{
				MaxItems: 1000,
			},
		}

		manager, err := cache.NewManager(config, logger)
		require.NoError(t, err)
		assert.NotNil(t, manager)

		// 启动管理器
		err = manager.Start()
		assert.NoError(t, err)
		assert.True(t, manager.IsRunning())

		// 获取默认缓存
		defaultCache, err := manager.GetCache("default")
		assert.NoError(t, err)
		assert.NotNil(t, defaultCache)

		// 列出缓存
		caches := manager.ListCaches()
		assert.Contains(t, caches, "default")

		// 获取全局统计
		stats, err := manager.GlobalStats()
		assert.NoError(t, err)
		assert.NotNil(t, stats)
		assert.Equal(t, 1, stats.TotalCaches)

		// 停止管理器
		err = manager.Stop()
		assert.NoError(t, err)
		assert.False(t, manager.IsRunning())
	})

	t.Run("禁用缓存", func(t *testing.T) {
		config := config.CacheConfig{
			Enabled: false,
		}

		manager, err := cache.NewManager(config, logger)
		require.NoError(t, err)

		err = manager.Start()
		assert.NoError(t, err)
		assert.False(t, manager.IsRunning())

		_, err = manager.GetCache("default")
		assert.Error(t, err)
	})
}

// BenchmarkMemoryCache 内存缓存性能基准测试
func BenchmarkMemoryCache(b *testing.B) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})

	config := config.CacheMemoryConfig{
		MaxItems: 10000,
	}

	memCache, _ := cache.NewMemoryCache(config, logger)
	defer memCache.Close()

	ctx := context.Background()
	key := "benchmark-key"
	value := []byte("benchmark-value")

	b.Run("Set", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			memCache.Set(ctx, key, value, 5*time.Minute)
		}
	})

	b.Run("Get", func(b *testing.B) {
		// 先设置一个值
		memCache.Set(ctx, key, value, 5*time.Minute)
		
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			memCache.Get(ctx, key)
		}
	})

	b.Run("Delete", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			b.StopTimer()
			memCache.Set(ctx, key, value, 5*time.Minute)
			b.StartTimer()
			
			memCache.Delete(ctx, key)
		}
	})
}

// BenchmarkCacheKeyGenerator 键生成器性能基准测试
func BenchmarkCacheKeyGenerator(b *testing.B) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})

	config := config.CacheKeyConfig{
		IncludeQueryParams: true,
		IncludeHeaders:     []string{"Authorization"},
		HashAlgorithm:      "sha256",
	}

	generator := cache.NewDefaultKeyGenerator(config, logger)

	request := &cache.CacheKeyRequest{
		Method:      "GET",
		Path:        "/api/v1/users",
		Query:       map[string]string{"page": "1"},
		Headers:     map[string]string{"Authorization": "Bearer token"},
		UserID:      "user123",
		ServiceName: "user-service",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		generator.GenerateKey(context.Background(), request)
	}
}
