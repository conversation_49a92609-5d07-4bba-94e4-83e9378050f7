package unit

import (
	"testing"

	"api-gateway/pkg/auth"
	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestOIDCAuthenticator_BasicFunctionality 测试OIDC认证器基本功能
func TestOIDCAuthenticator_BasicFunctionality(t *testing.T) {
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("配置验证", func(t *testing.T) {
		// 测试未启用的配置
		cfg := config.OIDCConfig{
			Enabled: false,
		}
		_, err := auth.NewOIDCAuthenticator(cfg, logger)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "未启用")

		// 测试缺少必需参数
		cfg = config.OIDCConfig{
			Enabled: true,
			// 缺少其他必需参数
		}
		_, err = auth.NewOIDCAuthenticator(cfg, logger)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "不能为空")

		// 测试缺少Client ID
		cfg = config.OIDCConfig{
			Enabled: true,
			Issuer:  "https://example.com",
			// 缺少ClientID
		}
		_, err = auth.NewOIDCAuthenticator(cfg, logger)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "Client ID不能为空")

		// 测试缺少Client Secret
		cfg = config.OIDCConfig{
			Enabled:  true,
			Issuer:   "https://example.com",
			ClientID: "test-client",
			// 缺少ClientSecret
		}
		_, err = auth.NewOIDCAuthenticator(cfg, logger)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "Client Secret不能为空")

		// 测试缺少Redirect URL
		cfg = config.OIDCConfig{
			Enabled:      true,
			Issuer:       "https://example.com",
			ClientID:     "test-client",
			ClientSecret: "test-secret",
			// 缺少RedirectURL
		}
		_, err = auth.NewOIDCAuthenticator(cfg, logger)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "Redirect URL不能为空")
	})

	t.Run("Token提取功能", func(t *testing.T) {
		// 由于无法连接到真实的OIDC提供者，我们只测试配置验证
		// 在实际环境中，这些测试需要模拟OIDC服务器
		
		// 创建一个基本的认证上下文来测试Token提取逻辑
		authCtx := &auth.AuthContext{
			RequestID: "test-request-001",
			Method:    "GET",
			Path:      "/api/protected",
			Headers: map[string]string{
				"Authorization": "Bearer test-token",
			},
		}

		// 验证认证上下文结构
		assert.Equal(t, "test-request-001", authCtx.RequestID)
		assert.Equal(t, "GET", authCtx.Method)
		assert.Equal(t, "/api/protected", authCtx.Path)
		assert.Equal(t, "Bearer test-token", authCtx.Headers["Authorization"])
	})

	t.Run("认证器属性", func(t *testing.T) {
		// 测试认证器的基本属性，不需要实际初始化
		// 这些是静态方法，可以直接测试

		// 由于我们无法创建真实的认证器实例（需要OIDC提供者），
		// 我们测试配置结构本身
		cfg := config.OIDCConfig{
			Enabled:      true,
			Issuer:       "https://accounts.google.com",
			ClientID:     "test-client-id",
			ClientSecret: "test-client-secret",
			RedirectURL:  "http://localhost:8080/callback",
			Scopes:       []string{"openid", "profile", "email"},
			SkipVerify:   false,
		}

		// 验证配置结构
		assert.True(t, cfg.Enabled)
		assert.Equal(t, "https://accounts.google.com", cfg.Issuer)
		assert.Equal(t, "test-client-id", cfg.ClientID)
		assert.Equal(t, "test-client-secret", cfg.ClientSecret)
		assert.Equal(t, "http://localhost:8080/callback", cfg.RedirectURL)
		assert.Equal(t, []string{"openid", "profile", "email"}, cfg.Scopes)
		assert.False(t, cfg.SkipVerify)
	})

	t.Run("认证结果结构", func(t *testing.T) {
		// 测试认证结果结构
		result := &auth.AuthResult{
			Authenticated: true,
			UserID:        "test-user-123",
			Username:      "Test User",
			TokenType:     "oidc",
			Roles:         []string{"user", "admin"},
			Attributes: map[string]interface{}{
				"email":          "<EMAIL>",
				"email_verified": true,
				"picture":        "https://example.com/avatar.jpg",
				"groups":         []string{"developers", "admins"},
			},
		}

		// 验证结果结构
		assert.True(t, result.Authenticated)
		assert.Equal(t, "test-user-123", result.UserID)
		assert.Equal(t, "Test User", result.Username)
		assert.Equal(t, "oidc", result.TokenType)
		assert.Contains(t, result.Roles, "user")
		assert.Contains(t, result.Roles, "admin")
		assert.Equal(t, "<EMAIL>", result.Attributes["email"])
		assert.Equal(t, true, result.Attributes["email_verified"])
	})
}

// TestOIDCConfig_Validation 测试OIDC配置验证
func TestOIDCConfig_Validation(t *testing.T) {
	t.Run("默认配置", func(t *testing.T) {
		cfg := config.OIDCConfig{}
		
		// 默认情况下应该是未启用的
		assert.False(t, cfg.Enabled)
		assert.Empty(t, cfg.Issuer)
		assert.Empty(t, cfg.ClientID)
		assert.Empty(t, cfg.ClientSecret)
		assert.Empty(t, cfg.RedirectURL)
		assert.Empty(t, cfg.Scopes)
		assert.False(t, cfg.SkipVerify)
	})

	t.Run("完整配置", func(t *testing.T) {
		cfg := config.OIDCConfig{
			Enabled:      true,
			Issuer:       "https://auth.example.com",
			ClientID:     "my-client-id",
			ClientSecret: "my-client-secret",
			RedirectURL:  "https://myapp.com/callback",
			Scopes:       []string{"openid", "profile", "email", "groups"},
			SkipVerify:   false,
		}

		// 验证所有字段都正确设置
		assert.True(t, cfg.Enabled)
		assert.Equal(t, "https://auth.example.com", cfg.Issuer)
		assert.Equal(t, "my-client-id", cfg.ClientID)
		assert.Equal(t, "my-client-secret", cfg.ClientSecret)
		assert.Equal(t, "https://myapp.com/callback", cfg.RedirectURL)
		assert.Len(t, cfg.Scopes, 4)
		assert.Contains(t, cfg.Scopes, "openid")
		assert.Contains(t, cfg.Scopes, "profile")
		assert.Contains(t, cfg.Scopes, "email")
		assert.Contains(t, cfg.Scopes, "groups")
		assert.False(t, cfg.SkipVerify)
	})

	t.Run("安全配置", func(t *testing.T) {
		// 测试跳过TLS验证的配置（不推荐用于生产环境）
		cfg := config.OIDCConfig{
			Enabled:      true,
			Issuer:       "http://localhost:8080", // HTTP而不是HTTPS
			ClientID:     "test-client",
			ClientSecret: "test-secret",
			RedirectURL:  "http://localhost:3000/callback",
			SkipVerify:   true, // 跳过TLS验证
		}

		assert.True(t, cfg.SkipVerify)
		assert.Contains(t, cfg.Issuer, "http://") // 非安全连接
	})
}

// TestOIDCAuthContext 测试OIDC认证上下文
func TestOIDCAuthContext(t *testing.T) {
	t.Run("基本认证上下文", func(t *testing.T) {
		ctx := &auth.AuthContext{
			RequestID: "req-123",
			ClientIP:  "*************",
			UserAgent: "Mozilla/5.0",
			Path:      "/api/v1/protected",
			Headers: map[string]string{
				"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
				"Content-Type":  "application/json",
			},
		}

		assert.Equal(t, "req-123", ctx.RequestID)
		assert.Equal(t, "*************", ctx.ClientIP)
		assert.Equal(t, "Mozilla/5.0", ctx.UserAgent)
		assert.Equal(t, "/api/v1/protected", ctx.Path)
		assert.Contains(t, ctx.Headers["Authorization"], "Bearer")
		assert.Equal(t, "application/json", ctx.Headers["Content-Type"])
	})

	t.Run("无认证头的上下文", func(t *testing.T) {
		ctx := &auth.AuthContext{
			RequestID: "req-124",
			Path:      "/api/v1/public",
			Headers:   map[string]string{},
		}

		assert.Equal(t, "req-124", ctx.RequestID)
		assert.Equal(t, "/api/v1/public", ctx.Path)
		assert.Empty(t, ctx.Headers)
	})

	t.Run("错误格式的认证头", func(t *testing.T) {
		ctx := &auth.AuthContext{
			RequestID: "req-125",
			Path:      "/api/v1/protected",
			Headers: map[string]string{
				"Authorization": "Basic dXNlcjpwYXNz", // Basic auth而不是Bearer
			},
		}

		assert.Equal(t, "req-125", ctx.RequestID)
		assert.Contains(t, ctx.Headers["Authorization"], "Basic")
		assert.NotContains(t, ctx.Headers["Authorization"], "Bearer")
	})
}

// BenchmarkOIDCConfig OIDC配置性能基准测试
func BenchmarkOIDCConfig(b *testing.B) {
	b.Run("配置创建", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			cfg := config.OIDCConfig{
				Enabled:      true,
				Issuer:       "https://auth.example.com",
				ClientID:     "client-id",
				ClientSecret: "client-secret",
				RedirectURL:  "https://app.com/callback",
				Scopes:       []string{"openid", "profile", "email"},
			}
			_ = cfg
		}
	})

	b.Run("认证上下文创建", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			ctx := &auth.AuthContext{
				RequestID: "req-123",
				Path:      "/api/protected",
				Headers: map[string]string{
					"Authorization": "Bearer token",
				},
			}
			_ = ctx
		}
	})
}
