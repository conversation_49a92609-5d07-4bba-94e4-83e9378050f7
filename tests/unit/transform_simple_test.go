package unit

import (
	"testing"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
	"api-gateway/pkg/transform"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestTransformBasic 基本转换测试
func TestTransformBasic(t *testing.T) {
	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	t.Run("JSON转换器基本功能", func(t *testing.T) {
		// 创建JSON转换配置
		jsonConfig := config.JSONTransformConfig{
			Add: map[string]interface{}{
				"timestamp": "2023-01-01T00:00:00Z",
			},
		}

		// 创建JSON转换器
		transformer := transform.NewJSONTransformer(jsonConfig, logger)
		require.NotNil(t, transformer)

		// 验证基本属性
		assert.Equal(t, "json", transformer.Name())
		assert.Equal(t, "1.0.0", transformer.Version())
		assert.Contains(t, transformer.SupportedContentTypes(), "application/json")
	})

	t.Run("格式转换器基本功能", func(t *testing.T) {
		// 创建格式转换配置
		formatConfig := config.FormatTransformConfig{
			Enabled: true,
		}

		// 创建格式转换器
		transformer := transform.NewFormatTransformer(formatConfig, logger)
		require.NotNil(t, transformer)

		// 验证基本属性
		assert.Equal(t, "format", transformer.Name())
		assert.Equal(t, "1.0.0", transformer.Version())
		assert.Contains(t, transformer.SupportedContentTypes(), "application/json")
	})

	t.Run("转换管理器基本功能", func(t *testing.T) {
		// 创建转换配置
		transformConfig := config.TransformConfig{
			Enabled: true,
		}

		// 创建转换管理器
		manager, err := transform.NewManager(transformConfig, logger)
		require.NoError(t, err)
		assert.NotNil(t, manager)

		// 启动管理器
		err = manager.Start()
		assert.NoError(t, err)

		// 健康检查
		err = manager.HealthCheck()
		assert.NoError(t, err)

		// 停止管理器
		err = manager.Stop()
		assert.NoError(t, err)
	})
}
