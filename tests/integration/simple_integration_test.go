package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockBackendServer 模拟后端服务器
type MockBackendServer struct {
	server *httptest.Server
}

// NewMockBackendServer 创建模拟后端服务器
func NewMockBackendServer() *MockBackendServer {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 公开端点
	router.GET("/public/info", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "这是公开API",
			"service": "mock-backend",
			"timestamp": time.Now().Unix(),
		})
	})

	// 受保护端点
	router.GET("/protected/data", func(c *gin.Context) {
		// 检查是否有认证信息传递
		userID := c.GetHeader("X-User-ID")
		if userID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权访问"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "这是受保护的数据",
			"user_id": userID,
			"data":    []string{"item1", "item2", "item3"},
		})
	})

	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
			"service": "mock-backend",
		})
	})

	server := httptest.NewServer(router)
	return &MockBackendServer{server: server}
}

// Close 关闭模拟服务器
func (m *MockBackendServer) Close() {
	m.server.Close()
}

// URL 返回服务器URL
func (m *MockBackendServer) URL() string {
	return m.server.URL
}

// TestBasicGatewayFunctionality 测试基本网关功能
func TestBasicGatewayFunctionality(t *testing.T) {
	// 创建模拟后端服务器
	backend := NewMockBackendServer()
	defer backend.Close()

	t.Run("配置加载和验证", func(t *testing.T) {
		// 测试基本配置创建
		cfg := &config.Config{
			Server: config.ServerConfig{
				Address:      ":8080",
				ReadTimeout:  30,
				WriteTimeout: 30,
				IdleTimeout:  120,
			},
			Logging: config.LoggingConfig{
				Level:  "info",
				Format: "json",
				Output: "stdout",
			},
			Metrics: config.MetricsConfig{
				Enabled: true,
				Path:    "/metrics",
				Port:    9090,
			},
			Routes: []config.RouteConfig{
				{
					Name:   "test-route",
					Path:   "/api/v1/test",
					Method: "GET",
					Upstream: config.UpstreamConfig{
						Type:         "static",
						LoadBalancer: "round_robin",
						Servers: []config.ServerTarget{
							{Host: "localhost", Port: 8081},
						},
					},
					Timeout: 30 * time.Second,
					Retries: 3,
				},
			},
			Plugins: config.PluginConfig{
				Directory: "plugins",
				Plugins:   make(map[string]interface{}),
			},
		}

		// 验证配置结构
		assert.Equal(t, ":8080", cfg.Server.Address)
		assert.Equal(t, 30, cfg.Server.ReadTimeout)
		assert.Equal(t, "info", cfg.Logging.Level)
		assert.True(t, cfg.Metrics.Enabled)
		assert.Len(t, cfg.Routes, 1)
		assert.Equal(t, "test-route", cfg.Routes[0].Name)
	})

	t.Run("日志系统初始化", func(t *testing.T) {
		// 测试日志系统创建
		logConfig := telemetry.LoggingConfig{
			Level:  "info",
			Format: "json",
			Output: "stdout",
		}

		logger, err := telemetry.NewLogger(logConfig)
		require.NoError(t, err)
		assert.NotNil(t, logger)

		// 测试日志记录
		logger.Info("集成测试日志记录", "component", "integration-test")
		logger.Debug("调试信息", "test", "logging")
		logger.Error("错误信息", "error", "test-error")
	})

	t.Run("指标系统初始化", func(t *testing.T) {
		// 测试指标系统创建
		metricsConfig := config.MetricsConfig{
			Enabled: true,
			Path:    "/metrics",
			Port:    9090,
		}

		metrics, err := telemetry.NewMetrics(metricsConfig)
		require.NoError(t, err)
		assert.NotNil(t, metrics)

		// 测试指标记录（简化测试，只验证指标系统创建成功）
		// 注意：实际的指标记录方法可能不同，这里只是验证系统初始化
		assert.NotNil(t, metrics)
	})

	t.Run("HTTP客户端功能", func(t *testing.T) {
		// 测试HTTP客户端基本功能
		client := &http.Client{
			Timeout: 10 * time.Second,
		}

		// 测试GET请求
		resp, err := client.Get(backend.URL() + "/public/info")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, "这是公开API", response["message"])
		assert.Equal(t, "mock-backend", response["service"])
		assert.Contains(t, response, "timestamp")
	})

	t.Run("认证头传递", func(t *testing.T) {
		// 测试认证头传递功能
		client := &http.Client{
			Timeout: 10 * time.Second,
		}

		// 创建带认证头的请求
		req, err := http.NewRequest("GET", backend.URL()+"/protected/data", nil)
		require.NoError(t, err)
		req.Header.Set("X-User-ID", "test-user-123")

		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, "这是受保护的数据", response["message"])
		assert.Equal(t, "test-user-123", response["user_id"])
		assert.Contains(t, response, "data")
	})

	t.Run("错误处理", func(t *testing.T) {
		// 测试无认证访问受保护资源
		resp, err := http.Get(backend.URL() + "/protected/data")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, "未授权访问", response["error"])
	})
}

// TestConfigurationValidation 测试配置验证
func TestConfigurationValidation(t *testing.T) {
	t.Run("有效配置", func(t *testing.T) {
		cfg := &config.Config{
			Server: config.ServerConfig{
				Address:      ":8080",
				ReadTimeout:  30,
				WriteTimeout: 30,
			},
			Logging: config.LoggingConfig{
				Level:  "info",
				Format: "json",
				Output: "stdout",
			},
			Routes: []config.RouteConfig{
				{
					Name:   "valid-route",
					Path:   "/api/v1/test",
					Method: "GET",
					Upstream: config.UpstreamConfig{
						Type:         "static",
						LoadBalancer: "round_robin",
						Servers: []config.ServerTarget{
							{Host: "localhost", Port: 8081},
						},
					},
				},
			},
			Plugins: config.PluginConfig{
				Directory: "plugins",
				Plugins:   make(map[string]interface{}),
			},
		}

		validator := config.NewDefaultConfigValidator()
		err := validator.ValidateConfig(cfg)
		assert.NoError(t, err)
	})

	t.Run("无效服务器配置", func(t *testing.T) {
		cfg := &config.Config{
			Server: config.ServerConfig{
				Address: "", // 无效地址
			},
			Plugins: config.PluginConfig{
				Directory: "plugins",
				Plugins:   make(map[string]interface{}),
			},
		}

		validator := config.NewDefaultConfigValidator()
		err := validator.ValidateConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "server configuration error")
	})
}

// TestConcurrentRequests 测试并发请求处理
func TestConcurrentRequests(t *testing.T) {
	backend := NewMockBackendServer()
	defer backend.Close()

	// 并发发送请求
	concurrency := 10
	requests := 100

	results := make(chan error, concurrency*requests)
	
	for i := 0; i < concurrency; i++ {
		go func() {
			client := &http.Client{Timeout: 5 * time.Second}
			for j := 0; j < requests; j++ {
				resp, err := client.Get(backend.URL() + "/public/info")
				if err != nil {
					results <- err
					continue
				}
				resp.Body.Close()
				
				if resp.StatusCode != http.StatusOK {
					results <- fmt.Errorf("unexpected status code: %d", resp.StatusCode)
					continue
				}
				results <- nil
			}
		}()
	}

	// 收集结果
	successCount := 0
	errorCount := 0
	
	for i := 0; i < concurrency*requests; i++ {
		err := <-results
		if err != nil {
			errorCount++
			t.Logf("请求错误: %v", err)
		} else {
			successCount++
		}
	}

	// 验证结果
	assert.Greater(t, successCount, concurrency*requests*8/10) // 至少80%成功
	t.Logf("并发测试结果: 成功 %d, 失败 %d", successCount, errorCount)
}

// BenchmarkSimpleRequest 简单请求基准测试
func BenchmarkSimpleRequest(b *testing.B) {
	backend := NewMockBackendServer()
	defer backend.Close()

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			resp, err := client.Get(backend.URL() + "/public/info")
			if err != nil {
				b.Error(err)
				continue
			}
			resp.Body.Close()
		}
	})
}

// TestContextTimeout 测试上下文超时
func TestContextTimeout(t *testing.T) {
	// 测试上下文超时处理
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	// 模拟长时间运行的操作
	select {
	case <-time.After(200 * time.Millisecond):
		t.Error("操作应该已经超时")
	case <-ctx.Done():
		assert.Equal(t, context.DeadlineExceeded, ctx.Err())
		t.Log("上下文超时处理正常")
	}
}
