# OIDC 认证指南

## 概述

本 API 网关支持 OpenID Connect (OIDC) 认证，这是一个基于 OAuth 2.0 的身份认证协议。OIDC 允许客户端验证用户身份并获取基本的用户信息。

## 功能特性

### ✅ 已实现功能

- **OIDC 提供者发现**: 自动发现 OIDC 提供者的配置信息
- **ID Token 验证**: 验证 OIDC ID Token 的签名和声明
- **授权码流程**: 支持标准的 OAuth 2.0 授权码流程
- **Token 刷新**: 支持使用 refresh token 刷新访问令牌
- **用户信息获取**: 从 OIDC 提供者获取用户详细信息
- **Token 撤销**: 支持撤销访问令牌和刷新令牌
- **多 Scope 支持**: 支持自定义权限范围
- **角色映射**: 支持从 OIDC 声明中提取角色和组信息

### 🔧 配置选项

- **灵活的提供者配置**: 支持任何兼容 OIDC 的身份提供者
- **自定义 Scope**: 可配置请求的权限范围
- **TLS 配置**: 支持跳过 TLS 验证（仅用于开发环境）
- **超时设置**: 可配置的 HTTP 客户端超时

## 配置说明

### 基本配置

```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://accounts.google.com"
    client_id: "your-client-id"
    client_secret: "your-client-secret"
    redirect_url: "http://localhost:8080/auth/callback"
```

### 完整配置

```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://your-oidc-provider.com"
    client_id: "your-application-client-id"
    client_secret: "your-application-client-secret"
    redirect_url: "https://your-app.com/auth/callback"
    scopes:
      - "openid"      # 必需
      - "profile"     # 用户基本信息
      - "email"       # 用户邮箱
      - "groups"      # 用户组信息（如果提供者支持）
    skip_verify: false  # 生产环境应设为 false
```

### 配置参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `enabled` | bool | 是 | 是否启用 OIDC 认证 |
| `issuer` | string | 是 | OIDC 提供者的 Issuer URL |
| `client_id` | string | 是 | 在 OIDC 提供者注册的客户端 ID |
| `client_secret` | string | 是 | 客户端密钥 |
| `redirect_url` | string | 是 | 授权后的重定向 URL |
| `scopes` | []string | 否 | 请求的权限范围，默认为 `["openid", "profile", "email"]` |
| `skip_verify` | bool | 否 | 是否跳过 TLS 证书验证，默认为 `false` |

## 支持的 OIDC 提供者

### Google

```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://accounts.google.com"
    client_id: "your-google-client-id.apps.googleusercontent.com"
    client_secret: "your-google-client-secret"
    redirect_url: "https://your-app.com/auth/callback"
    scopes: ["openid", "profile", "email"]
```

### Microsoft Azure AD

```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://login.microsoftonline.com/{tenant-id}/v2.0"
    client_id: "your-azure-client-id"
    client_secret: "your-azure-client-secret"
    redirect_url: "https://your-app.com/auth/callback"
    scopes: ["openid", "profile", "email"]
```

### Keycloak

```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://your-keycloak.com/auth/realms/your-realm"
    client_id: "your-keycloak-client"
    client_secret: "your-keycloak-secret"
    redirect_url: "https://your-app.com/auth/callback"
    scopes: ["openid", "profile", "email", "roles"]
```

### Auth0

```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://your-domain.auth0.com/"
    client_id: "your-auth0-client-id"
    client_secret: "your-auth0-client-secret"
    redirect_url: "https://your-app.com/auth/callback"
    scopes: ["openid", "profile", "email"]
```

## 认证流程

### 1. 授权码流程

```
用户 -> 网关 -> OIDC 提供者 -> 用户授权 -> 回调 -> 网关 -> Token 验证 -> 访问资源
```

1. **发起认证**: 用户访问受保护资源
2. **重定向到提供者**: 网关重定向用户到 OIDC 提供者
3. **用户授权**: 用户在提供者处登录并授权
4. **授权码回调**: 提供者重定向回网关并携带授权码
5. **交换 Token**: 网关使用授权码交换访问令牌和 ID Token
6. **验证 Token**: 网关验证 ID Token 的有效性
7. **访问资源**: 用户可以访问受保护的资源

### 2. Token 验证流程

```
客户端 -> 网关 (Bearer Token) -> Token 验证 -> 访问资源
```

1. **携带 Token**: 客户端在请求头中携带 ID Token
2. **提取 Token**: 网关从 `Authorization: Bearer <token>` 头中提取 Token
3. **验证 Token**: 验证 Token 签名、过期时间、发行者等
4. **提取用户信息**: 从 Token 中提取用户 ID、角色等信息
5. **授权检查**: 根据路由配置检查用户权限
6. **转发请求**: 将请求转发到后端服务

## API 端点

### 获取授权 URL

```http
GET /auth/oidc/authorize?state=random-state
```

响应：
```json
{
  "authorization_url": "https://provider.com/auth?client_id=...&redirect_uri=...&state=..."
}
```

### 处理回调

```http
GET /auth/callback?code=auth-code&state=random-state
```

响应：
```json
{
  "access_token": "...",
  "id_token": "...",
  "refresh_token": "...",
  "expires_in": 3600
}
```

### 刷新 Token

```http
POST /auth/oidc/refresh
Content-Type: application/json

{
  "refresh_token": "your-refresh-token"
}
```

### 撤销 Token

```http
POST /auth/oidc/revoke
Content-Type: application/json

{
  "token": "token-to-revoke"
}
```

## 使用示例

### 1. 前端应用集成

```javascript
// 发起 OIDC 认证
async function initiateOIDCAuth() {
  const response = await fetch('/auth/oidc/authorize?state=' + generateRandomState());
  const data = await response.json();
  window.location.href = data.authorization_url;
}

// 处理回调
async function handleCallback() {
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const state = urlParams.get('state');
  
  // 验证 state 参数
  if (state !== getStoredState()) {
    throw new Error('Invalid state parameter');
  }
  
  // 交换 token
  const response = await fetch('/auth/callback?code=' + code + '&state=' + state);
  const tokens = await response.json();
  
  // 存储 token
  localStorage.setItem('id_token', tokens.id_token);
  localStorage.setItem('access_token', tokens.access_token);
  localStorage.setItem('refresh_token', tokens.refresh_token);
}

// 使用 token 访问 API
async function callProtectedAPI() {
  const idToken = localStorage.getItem('id_token');
  
  const response = await fetch('/api/v1/protected/data', {
    headers: {
      'Authorization': 'Bearer ' + idToken,
      'Content-Type': 'application/json'
    }
  });
  
  return response.json();
}
```

### 2. 路由配置

```yaml
routes:
  - name: "protected-api"
    path: "/api/v1/protected/*"
    method: "*"
    upstream:
      type: "static"
      servers:
        - host: "backend.example.com"
          port: 8080
    auth:
      required: true
      methods: ["oidc", "jwt"]  # 支持 OIDC 或 JWT 认证
      roles: ["user", "admin"]   # 需要的角色
```

## 故障排查

### 常见问题

1. **OIDC 提供者发现失败**
   - 检查 `issuer` URL 是否正确
   - 确保网络可以访问 OIDC 提供者
   - 验证 OIDC 提供者的 `.well-known/openid_configuration` 端点

2. **Token 验证失败**
   - 检查客户端 ID 是否正确
   - 确保 Token 未过期
   - 验证 Token 的 audience 声明

3. **授权码交换失败**
   - 检查客户端密钥是否正确
   - 确保重定向 URL 与配置一致
   - 验证授权码是否已使用或过期

### 调试日志

启用调试日志：

```yaml
logging:
  level: "debug"
  format: "json"
```

关键日志信息：
- `正在发现OIDC提供者配置`
- `OIDC提供者初始化成功`
- `开始验证OIDC ID Token`
- `OIDC认证成功`

## 安全建议

1. **使用 HTTPS**: 生产环境必须使用 HTTPS
2. **验证 State 参数**: 防止 CSRF 攻击
3. **Token 存储**: 安全存储访问令牌和刷新令牌
4. **Token 过期**: 及时刷新过期的令牌
5. **撤销 Token**: 用户登出时撤销令牌
6. **最小权限原则**: 只请求必要的 scope

## 性能优化

1. **Token 缓存**: 缓存已验证的 Token
2. **连接池**: 复用 HTTP 连接
3. **超时设置**: 合理设置超时时间
4. **并发控制**: 限制并发请求数量

## 监控指标

- `oidc_auth_requests_total`: OIDC 认证请求总数
- `oidc_auth_success_total`: OIDC 认证成功总数
- `oidc_auth_failures_total`: OIDC 认证失败总数
- `oidc_token_validation_duration`: Token 验证耗时
- `oidc_provider_discovery_duration`: 提供者发现耗时
