basePath: /
definitions:
  api.AuthInfo:
    properties:
      methods:
        description: |-
          支持的认证方法
          example: ["jwt", "api_key"]
        example:
        - jwt
        - api_key
        items:
          type: string
        type: array
      required:
        description: |-
          是否需要认证
          example: true
        example: true
        type: boolean
      roles:
        description: |-
          需要的角色
          example: ["user", "admin"]
        example:
        - user
        - admin
        items:
          type: string
        type: array
    type: object
  api.ComponentStatus:
    properties:
      description:
        description: |-
          状态描述
          example: 认证管理器运行正常
        example: 认证管理器运行正常
        type: string
      status:
        description: |-
          组件状态
          example: healthy
        example: healthy
        type: string
    type: object
  api.ConfigReloadRequest:
    properties:
      config_file:
        description: |-
          配置文件路径
          example: configs/gateway.yaml
        example: configs/gateway.yaml
        type: string
    type: object
  api.ConfigReloadResponse:
    properties:
      config_file:
        description: |-
          配置文件路径
          example: configs/gateway.yaml
        example: configs/gateway.yaml
        type: string
      message:
        description: |-
          响应消息
          example: 配置重载成功
        example: 配置重载成功
        type: string
      timestamp:
        description: |-
          时间戳
          example: 2025-08-17T07:30:14.646Z
        example: "2025-08-17T07:30:14.646Z"
        type: string
    type: object
  api.ErrorResponse:
    properties:
      code:
        description: |-
          错误代码
          example: INVALID_REQUEST
        example: INVALID_REQUEST
        type: string
      details:
        description: 详细信息
      message:
        description: |-
          错误消息
          example: 请求参数无效
        example: 请求参数无效
        type: string
      timestamp:
        description: |-
          时间戳
          example: 2025-08-17T07:30:14.646Z
        example: "2025-08-17T07:30:14.646Z"
        type: string
    type: object
  api.HealthResponse:
    properties:
      components:
        additionalProperties:
          $ref: '#/definitions/api.ComponentStatus'
        description: 组件状态
        type: object
      status:
        description: |-
          服务状态
          example: healthy
        example: healthy
        type: string
      timestamp:
        description: |-
          时间戳
          example: 2025-08-17T07:30:14.646Z
        example: "2025-08-17T07:30:14.646Z"
        type: string
      uptime:
        description: |-
          运行时间
          example: 2小时30分钟15秒
        example: 2小时30分钟15秒
        type: string
      version:
        description: |-
          服务版本
          example: 1.0.0
        example: 1.0.0
        type: string
    type: object
  api.OIDCAuthResponse:
    properties:
      authorization_url:
        description: |-
          授权URL
          example: https://accounts.google.com/oauth/authorize?client_id=...
        example: https://accounts.google.com/oauth/authorize?client_id=...
        type: string
    type: object
  api.OIDCTokenResponse:
    properties:
      access_token:
        description: |-
          访问令牌
          example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
        example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      expires_in:
        description: |-
          过期时间(秒)
          example: 3600
        example: 3600
        type: integer
      id_token:
        description: |-
          ID令牌
          example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
        example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      refresh_token:
        description: |-
          刷新令牌
          example: refresh-token-string
        example: refresh-token-string
        type: string
      token_type:
        description: |-
          令牌类型
          example: Bearer
        example: Bearer
        type: string
    type: object
  api.RefreshTokenRequest:
    properties:
      refresh_token:
        description: |-
          刷新令牌
          example: refresh-token-string
        example: refresh-token-string
        type: string
    type: object
  api.RevokeTokenRequest:
    properties:
      token:
        description: |-
          要撤销的令牌
          example: token-to-revoke
        example: token-to-revoke
        type: string
    type: object
  api.RouteInfo:
    properties:
      auth:
        allOf:
        - $ref: '#/definitions/api.AuthInfo'
        description: 认证配置
      method:
        description: |-
          HTTP方法
          example: GET
        example: GET
        type: string
      name:
        description: |-
          路由名称
          example: protected-api
        example: protected-api
        type: string
      path:
        description: |-
          路径模式
          example: /api/v1/protected/*
        example: /api/v1/protected/*
        type: string
      retries:
        description: |-
          重试次数
          example: 3
        example: 3
        type: integer
      timeout:
        description: |-
          超时时间(秒)
          example: 30
        example: 30
        type: integer
      upstream:
        allOf:
        - $ref: '#/definitions/api.UpstreamInfo'
        description: 上游配置
    type: object
  api.ServerInfo:
    properties:
      host:
        description: |-
          主机地址
          example: backend1.example.com
        example: backend1.example.com
        type: string
      port:
        description: |-
          端口
          example: 8081
        example: 8081
        type: integer
      status:
        description: |-
          健康状态
          example: healthy
        example: healthy
        type: string
      weight:
        description: |-
          权重
          example: 100
        example: 100
        type: integer
    type: object
  api.SuccessResponse:
    properties:
      message:
        description: |-
          响应消息
          example: 操作成功
        example: 操作成功
        type: string
      timestamp:
        description: |-
          时间戳
          example: 2025-08-17T07:30:14.646Z
        example: "2025-08-17T07:30:14.646Z"
        type: string
    type: object
  api.UpstreamInfo:
    properties:
      load_balancer:
        description: |-
          负载均衡算法
          example: round_robin
        example: round_robin
        type: string
      servers:
        description: 服务器列表
        items:
          $ref: '#/definitions/api.ServerInfo'
        type: array
      type:
        description: |-
          类型
          example: static
        example: static
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Gateway Team
    url: https://github.com/your-org/api-gateway
  description: 企业级API网关系统，提供认证、授权、限流、负载均衡等功能
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://github.com/your-org/api-gateway
  title: API Gateway
  version: 1.0.0
paths:
  /admin/config:
    get:
      consumes:
      - application/json
      description: 返回API网关的当前配置信息
      produces:
      - application/json
      responses:
        "200":
          description: 配置信息
          schema:
            additionalProperties: true
            type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - Bearer: []
      summary: 获取当前配置
      tags:
      - 管理API
  /admin/config/reload:
    post:
      consumes:
      - application/json
      description: 重新加载API网关配置，支持热更新
      parameters:
      - description: 配置重载请求
        in: body
        name: config
        schema:
          $ref: '#/definitions/api.ConfigReloadRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 重载成功
          schema:
            $ref: '#/definitions/api.ConfigReloadResponse'
        "400":
          description: 请求无效
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - Bearer: []
      summary: 重载配置
      tags:
      - 管理API
  /admin/routes:
    get:
      consumes:
      - application/json
      description: 返回当前配置的所有路由信息
      produces:
      - application/json
      responses:
        "200":
          description: 路由列表
          schema:
            items:
              $ref: '#/definitions/api.RouteInfo'
            type: array
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - Bearer: []
      summary: 获取路由信息
      tags:
      - 管理API
  /auth/callback:
    get:
      consumes:
      - application/json
      description: 处理身份提供者的回调，交换授权码获取令牌
      parameters:
      - description: 授权码
        in: query
        name: code
        required: true
        type: string
      - description: 状态参数
        in: query
        name: state
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 令牌信息
          schema:
            $ref: '#/definitions/api.OIDCTokenResponse'
        "400":
          description: 请求无效
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 处理OIDC回调
      tags:
      - OIDC认证
  /auth/oidc/authorize:
    get:
      consumes:
      - application/json
      description: 生成OIDC认证的授权URL，用于重定向用户到身份提供者
      parameters:
      - description: 状态参数(CSRF保护)
        in: query
        name: state
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 授权URL
          schema:
            $ref: '#/definitions/api.OIDCAuthResponse'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 获取OIDC授权URL
      tags:
      - OIDC认证
  /auth/oidc/refresh:
    post:
      consumes:
      - application/json
      description: 使用刷新令牌获取新的访问令牌
      parameters:
      - description: 刷新令牌请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 新令牌信息
          schema:
            $ref: '#/definitions/api.OIDCTokenResponse'
        "400":
          description: 请求无效
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 刷新令牌
      tags:
      - OIDC认证
  /auth/oidc/revoke:
    post:
      consumes:
      - application/json
      description: 撤销访问令牌或刷新令牌
      parameters:
      - description: 撤销令牌请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.RevokeTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 撤销成功
          schema:
            $ref: '#/definitions/api.SuccessResponse'
        "400":
          description: 请求无效
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 撤销令牌
      tags:
      - OIDC认证
  /health:
    get:
      consumes:
      - application/json
      description: 返回API网关的健康状态，包括各组件的运行状态
      produces:
      - application/json
      responses:
        "200":
          description: 服务健康
          schema:
            $ref: '#/definitions/api.HealthResponse'
        "503":
          description: 服务降级
          schema:
            $ref: '#/definitions/api.HealthResponse'
      summary: 获取服务健康状态
      tags:
      - 健康检查
  /metrics:
    get:
      consumes:
      - application/json
      description: 返回Prometheus格式的指标数据
      produces:
      - text/plain
      responses:
        "200":
          description: Prometheus指标数据
          schema:
            type: string
      summary: 获取指标数据
      tags:
      - 监控
securityDefinitions:
  ApiKeyAuth:
    description: API Key认证
    in: header
    name: X-API-Key
    type: apiKey
  Bearer:
    description: 'JWT Token认证，格式: Bearer {token}'
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
