{"swagger": "2.0", "info": {"description": "企业级API网关系统，提供认证、授权、限流、负载均衡等功能", "title": "API Gateway", "termsOfService": "https://github.com/your-org/api-gateway", "contact": {"name": "API Gateway Team", "url": "https://github.com/your-org/api-gateway", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "host": "localhost:8080", "basePath": "/", "paths": {"/admin/config": {"get": {"security": [{"Bearer": []}], "description": "返回API网关的当前配置信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理API"], "summary": "获取当前配置", "responses": {"200": {"description": "配置信息", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/admin/config/reload": {"post": {"security": [{"Bearer": []}], "description": "重新加载API网关配置，支持热更新", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理API"], "summary": "重载配置", "parameters": [{"description": "配置重载请求", "name": "config", "in": "body", "schema": {"$ref": "#/definitions/api.ConfigReloadRequest"}}], "responses": {"200": {"description": "重载成功", "schema": {"$ref": "#/definitions/api.ConfigReloadResponse"}}, "400": {"description": "请求无效", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/admin/routes": {"get": {"security": [{"Bearer": []}], "description": "返回当前配置的所有路由信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理API"], "summary": "获取路由信息", "responses": {"200": {"description": "路由列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/api.RouteInfo"}}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/auth/callback": {"get": {"description": "处理身份提供者的回调，交换授权码获取令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["OIDC认证"], "summary": "处理OIDC回调", "parameters": [{"type": "string", "description": "授权码", "name": "code", "in": "query", "required": true}, {"type": "string", "description": "状态参数", "name": "state", "in": "query", "required": true}], "responses": {"200": {"description": "令牌信息", "schema": {"$ref": "#/definitions/api.OIDCTokenResponse"}}, "400": {"description": "请求无效", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/auth/oidc/authorize": {"get": {"description": "生成OIDC认证的授权URL，用于重定向用户到身份提供者", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["OIDC认证"], "summary": "获取OIDC授权URL", "parameters": [{"type": "string", "description": "状态参数(CSRF保护)", "name": "state", "in": "query"}], "responses": {"200": {"description": "授权URL", "schema": {"$ref": "#/definitions/api.OIDCAuthResponse"}}, "500": {"description": "内部错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/auth/oidc/refresh": {"post": {"description": "使用刷新令牌获取新的访问令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["OIDC认证"], "summary": "刷新令牌", "parameters": [{"description": "刷新令牌请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.RefreshTokenRequest"}}], "responses": {"200": {"description": "新令牌信息", "schema": {"$ref": "#/definitions/api.OIDCTokenResponse"}}, "400": {"description": "请求无效", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/auth/oidc/revoke": {"post": {"description": "撤销访问令牌或刷新令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["OIDC认证"], "summary": "撤销令牌", "parameters": [{"description": "撤销令牌请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.RevokeTokenRequest"}}], "responses": {"200": {"description": "撤销成功", "schema": {"$ref": "#/definitions/api.SuccessResponse"}}, "400": {"description": "请求无效", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/health": {"get": {"description": "返回API网关的健康状态，包括各组件的运行状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["健康检查"], "summary": "获取服务健康状态", "responses": {"200": {"description": "服务健康", "schema": {"$ref": "#/definitions/api.HealthResponse"}}, "503": {"description": "服务降级", "schema": {"$ref": "#/definitions/api.HealthResponse"}}}}}, "/metrics": {"get": {"description": "返回Prometheus格式的指标数据", "consumes": ["application/json"], "produces": ["text/plain"], "tags": ["监控"], "summary": "获取指标数据", "responses": {"200": {"description": "Prometheus指标数据", "schema": {"type": "string"}}}}}}, "definitions": {"api.AuthInfo": {"type": "object", "properties": {"methods": {"description": "支持的认证方法\nexample: [\"jwt\", \"api_key\"]", "type": "array", "items": {"type": "string"}, "example": ["jwt", "api_key"]}, "required": {"description": "是否需要认证\nexample: true", "type": "boolean", "example": true}, "roles": {"description": "需要的角色\nexample: [\"user\", \"admin\"]", "type": "array", "items": {"type": "string"}, "example": ["user", "admin"]}}}, "api.ComponentStatus": {"type": "object", "properties": {"description": {"description": "状态描述\nexample: 认证管理器运行正常", "type": "string", "example": "认证管理器运行正常"}, "status": {"description": "组件状态\nexample: healthy", "type": "string", "example": "healthy"}}}, "api.ConfigReloadRequest": {"type": "object", "properties": {"config_file": {"description": "配置文件路径\nexample: configs/gateway.yaml", "type": "string", "example": "configs/gateway.yaml"}}}, "api.ConfigReloadResponse": {"type": "object", "properties": {"config_file": {"description": "配置文件路径\nexample: configs/gateway.yaml", "type": "string", "example": "configs/gateway.yaml"}, "message": {"description": "响应消息\nexample: 配置重载成功", "type": "string", "example": "配置重载成功"}, "timestamp": {"description": "时间戳\nexample: 2025-08-17T07:30:14.646Z", "type": "string", "example": "2025-08-17T07:30:14.646Z"}}}, "api.ErrorResponse": {"type": "object", "properties": {"code": {"description": "错误代码\nexample: INVALID_REQUEST", "type": "string", "example": "INVALID_REQUEST"}, "details": {"description": "详细信息"}, "message": {"description": "错误消息\nexample: 请求参数无效", "type": "string", "example": "请求参数无效"}, "timestamp": {"description": "时间戳\nexample: 2025-08-17T07:30:14.646Z", "type": "string", "example": "2025-08-17T07:30:14.646Z"}}}, "api.HealthResponse": {"type": "object", "properties": {"components": {"description": "组件状态", "type": "object", "additionalProperties": {"$ref": "#/definitions/api.ComponentStatus"}}, "status": {"description": "服务状态\nexample: healthy", "type": "string", "example": "healthy"}, "timestamp": {"description": "时间戳\nexample: 2025-08-17T07:30:14.646Z", "type": "string", "example": "2025-08-17T07:30:14.646Z"}, "uptime": {"description": "运行时间\nexample: 2小时30分钟15秒", "type": "string", "example": "2小时30分钟15秒"}, "version": {"description": "服务版本\nexample: 1.0.0", "type": "string", "example": "1.0.0"}}}, "api.OIDCAuthResponse": {"type": "object", "properties": {"authorization_url": {"description": "授权URL\nexample: https://accounts.google.com/oauth/authorize?client_id=...", "type": "string", "example": "https://accounts.google.com/oauth/authorize?client_id=..."}}}, "api.OIDCTokenResponse": {"type": "object", "properties": {"access_token": {"description": "访问令牌\nexample: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "type": "string", "example": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."}, "expires_in": {"description": "过期时间(秒)\nexample: 3600", "type": "integer", "example": 3600}, "id_token": {"description": "ID令牌\nexample: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "type": "string", "example": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."}, "refresh_token": {"description": "刷新令牌\nexample: refresh-token-string", "type": "string", "example": "refresh-token-string"}, "token_type": {"description": "令牌类型\nexample: Bearer", "type": "string", "example": "Bearer"}}}, "api.RefreshTokenRequest": {"type": "object", "properties": {"refresh_token": {"description": "刷新令牌\nexample: refresh-token-string", "type": "string", "example": "refresh-token-string"}}}, "api.RevokeTokenRequest": {"type": "object", "properties": {"token": {"description": "要撤销的令牌\nexample: token-to-revoke", "type": "string", "example": "token-to-revoke"}}}, "api.RouteInfo": {"type": "object", "properties": {"auth": {"description": "认证配置", "allOf": [{"$ref": "#/definitions/api.AuthInfo"}]}, "method": {"description": "HTTP方法\nexample: GET", "type": "string", "example": "GET"}, "name": {"description": "路由名称\nexample: protected-api", "type": "string", "example": "protected-api"}, "path": {"description": "路径模式\nexample: /api/v1/protected/*", "type": "string", "example": "/api/v1/protected/*"}, "retries": {"description": "重试次数\nexample: 3", "type": "integer", "example": 3}, "timeout": {"description": "超时时间(秒)\nexample: 30", "type": "integer", "example": 30}, "upstream": {"description": "上游配置", "allOf": [{"$ref": "#/definitions/api.UpstreamInfo"}]}}}, "api.ServerInfo": {"type": "object", "properties": {"host": {"description": "主机地址\nexample: backend1.example.com", "type": "string", "example": "backend1.example.com"}, "port": {"description": "端口\nexample: 8081", "type": "integer", "example": 8081}, "status": {"description": "健康状态\nexample: healthy", "type": "string", "example": "healthy"}, "weight": {"description": "权重\nexample: 100", "type": "integer", "example": 100}}}, "api.SuccessResponse": {"type": "object", "properties": {"message": {"description": "响应消息\nexample: 操作成功", "type": "string", "example": "操作成功"}, "timestamp": {"description": "时间戳\nexample: 2025-08-17T07:30:14.646Z", "type": "string", "example": "2025-08-17T07:30:14.646Z"}}}, "api.UpstreamInfo": {"type": "object", "properties": {"load_balancer": {"description": "负载均衡算法\nexample: round_robin", "type": "string", "example": "round_robin"}, "servers": {"description": "服务器列表", "type": "array", "items": {"$ref": "#/definitions/api.ServerInfo"}}, "type": {"description": "类型\nexample: static", "type": "string", "example": "static"}}}}, "securityDefinitions": {"ApiKeyAuth": {"description": "API Key认证", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-API-Key", "in": "header"}, "Bearer": {"description": "JWT Token认证，格式: Bearer {token}", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}