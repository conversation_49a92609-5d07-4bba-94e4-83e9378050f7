# API 文档使用指南

## 概述

本 API 网关提供了完整的 OpenAPI 3.0 规范文档，包括交互式 Swagger UI 界面，方便开发者理解和测试 API 接口。

## 🚀 快速开始

### 1. 生成 API 文档

```bash
# 生成 Swagger 文档
make swagger

# 或者直接使用 swag 命令
swag init --dir ./ --generalInfo cmd/swagger/main.go --output docs/swagger
```

### 2. 启动网关服务

```bash
# 构建并启动网关
make run

# 或者开发模式启动
make dev
```

### 3. 访问 API 文档

打开浏览器访问：
- **Swagger UI**: http://localhost:8080/swagger/index.html
- **JSON 格式**: http://localhost:8080/swagger/doc.json
- **YAML 格式**: docs/swagger/swagger.yaml

## 📚 API 文档结构

### 文档组织

API 文档按功能模块组织，包含以下主要部分：

#### 1. 健康检查 API
- `GET /health` - 获取服务健康状态
- 返回服务状态、版本信息、运行时间和组件状态

#### 2. 管理 API
- `GET /admin/config` - 获取当前配置
- `POST /admin/config/reload` - 重载配置
- `GET /admin/routes` - 获取路由信息
- `GET /admin/metrics` - 获取监控指标

#### 3. OIDC 认证 API
- `GET /auth/oidc/authorize` - 获取授权URL
- `GET /auth/callback` - 处理认证回调
- `POST /auth/oidc/refresh` - 刷新令牌
- `POST /auth/oidc/revoke` - 撤销令牌

#### 4. 监控 API
- `GET /metrics` - Prometheus 格式指标数据

### 数据模型

文档包含完整的数据模型定义：

#### 响应模型
- `HealthResponse` - 健康检查响应
- `ErrorResponse` - 错误响应
- `ConfigReloadResponse` - 配置重载响应
- `OIDCTokenResponse` - OIDC 令牌响应

#### 请求模型
- `ConfigReloadRequest` - 配置重载请求
- `RefreshTokenRequest` - 刷新令牌请求
- `RevokeTokenRequest` - 撤销令牌请求

#### 信息模型
- `RouteInfo` - 路由信息
- `UpstreamInfo` - 上游服务信息
- `ServerInfo` - 服务器信息
- `AuthInfo` - 认证信息

## 🔐 认证方式

API 文档支持两种认证方式：

### 1. JWT Bearer Token
```http
Authorization: Bearer <jwt-token>
```

### 2. API Key
```http
X-API-Key: <api-key>
```

在 Swagger UI 中，点击 "Authorize" 按钮可以设置认证信息。

## 📖 使用示例

### 1. 健康检查

```bash
curl -X GET "http://localhost:8080/health" \
  -H "accept: application/json"
```

响应示例：
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": "2小时30分钟15秒",
  "timestamp": "2025-08-17T07:30:14.646Z",
  "components": {
    "auth": {
      "status": "healthy",
      "description": "认证管理器运行正常"
    },
    "security": {
      "status": "healthy",
      "description": "安全管理器运行正常"
    }
  }
}
```

### 2. 获取配置信息

```bash
curl -X GET "http://localhost:8080/admin/config" \
  -H "accept: application/json" \
  -H "Authorization: Bearer <jwt-token>"
```

### 3. OIDC 认证流程

#### 步骤 1: 获取授权 URL
```bash
curl -X GET "http://localhost:8080/auth/oidc/authorize?state=random-state" \
  -H "accept: application/json"
```

#### 步骤 2: 处理回调
```bash
curl -X GET "http://localhost:8080/auth/callback?code=auth-code&state=random-state" \
  -H "accept: application/json"
```

#### 步骤 3: 刷新令牌
```bash
curl -X POST "http://localhost:8080/auth/oidc/refresh" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "your-refresh-token"
  }'
```

## 🛠️ 开发指南

### 添加新的 API 端点

1. **定义数据模型** (pkg/api/models.go)
```go
// swagger:model NewAPIResponse
type NewAPIResponse struct {
    // 响应消息
    // example: 操作成功
    Message string `json:"message" example:"操作成功"`
    
    // 数据内容
    Data interface{} `json:"data,omitempty"`
}
```

2. **实现处理器** (pkg/api/handlers.go)
```go
// NewAPIHandler 新API处理器
// @Summary 新API接口
// @Description 新API接口的详细描述
// @Tags 新功能
// @Accept json
// @Produce json
// @Param request body NewAPIRequest true "请求参数"
// @Success 200 {object} NewAPIResponse "成功响应"
// @Failure 400 {object} ErrorResponse "请求错误"
// @Router /api/new [post]
func (h *Handler) NewAPIHandler(c *gin.Context) {
    // 实现逻辑
}
```

3. **注册路由** (internal/core/gateway.go)
```go
// 在 setupSwaggerRoutes 方法中添加
docs.POST("/api/new", handler.NewAPIHandler)
```

4. **重新生成文档**
```bash
make swagger
```

### Swagger 注解说明

#### 基本注解
- `@Summary` - 接口简要描述
- `@Description` - 接口详细描述
- `@Tags` - 接口分组标签
- `@Accept` - 接受的内容类型
- `@Produce` - 返回的内容类型

#### 参数注解
- `@Param` - 参数定义
  - 格式：`@Param name type dataType required "description"`
  - 类型：query, path, header, body, formData

#### 响应注解
- `@Success` - 成功响应
- `@Failure` - 错误响应
- 格式：`@Success code {type} dataType "description"`

#### 安全注解
- `@Security` - 安全认证要求
- 示例：`@Security Bearer` 或 `@Security ApiKeyAuth`

### 模型注解

```go
// swagger:model ExampleModel
type ExampleModel struct {
    // 字段描述
    // example: 示例值
    Field string `json:"field" example:"示例值"`
    
    // 必需字段
    // required: true
    RequiredField string `json:"required_field" binding:"required"`
    
    // 可选字段
    OptionalField *string `json:"optional_field,omitempty"`
}
```

## 📊 性能指标

### API 文档性能基准

| 接口 | 响应时间 | 内存使用 | 分配次数 |
|------|----------|----------|----------|
| 健康检查 | 2.4μs | 2,779 B | 22 allocs |
| 配置获取 | 2.9μs | 3,410 B | 37 allocs |
| OIDC授权 | 1.5μs | 2,018 B | 16 allocs |

### 文档生成性能
- 文档生成时间: < 1秒
- 生成文件大小: ~50KB (JSON)
- 支持的模型数量: 14个

## 🔧 配置选项

### Swagger 配置

在 `cmd/swagger/main.go` 中可以配置：

```go
//	@title			API Gateway
//	@version		1.0.0
//	@description	企业级API网关系统
//	@host			localhost:8080
//	@BasePath		/
//	@schemes		http https
```

### 生成选项

```bash
swag init \
  --dir ./ \
  --generalInfo cmd/swagger/main.go \
  --output docs/swagger \
  --outputTypes go,json,yaml \
  --parseVendor \
  --parseDependency \
  --parseInternal
```

参数说明：
- `--dir` - 扫描目录
- `--generalInfo` - 主文档文件
- `--output` - 输出目录
- `--outputTypes` - 输出格式
- `--parseVendor` - 解析vendor目录
- `--parseDependency` - 解析依赖
- `--parseInternal` - 解析内部包

## 🚀 部署建议

### 生产环境

1. **禁用 Swagger UI**（可选）
```go
if os.Getenv("ENVIRONMENT") != "production" {
    router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
}
```

2. **静态文件服务**
```bash
# 将生成的文档部署到静态文件服务器
cp docs/swagger/* /var/www/api-docs/
```

3. **CDN 加速**
```html
<!-- 使用CDN加载Swagger UI -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@3.52.5/swagger-ui.css" />
```

### 开发环境

1. **热重载**
```bash
# 监听文件变化自动重新生成文档
make dev
```

2. **调试模式**
```bash
# 启用详细日志
export GIN_MODE=debug
make swagger-serve
```

## 🔍 故障排查

### 常见问题

1. **文档生成失败**
   - 检查 Go 语法错误
   - 确保所有导入包可用
   - 验证 Swagger 注解格式

2. **模型未显示**
   - 确保模型有 `swagger:model` 注解
   - 检查模型是否被引用
   - 验证 JSON 标签格式

3. **接口未显示**
   - 确保处理器有完整的 Swagger 注解
   - 检查路由是否正确注册
   - 验证包导入路径

### 调试技巧

1. **查看生成日志**
```bash
swag init --debug
```

2. **验证 JSON 格式**
```bash
cat docs/swagger/swagger.json | jq .
```

3. **检查模型定义**
```bash
grep -r "swagger:model" pkg/
```

## 📝 最佳实践

1. **文档维护**
   - 及时更新 API 注解
   - 保持示例数据的准确性
   - 定期检查文档完整性

2. **版本管理**
   - 使用语义化版本号
   - 记录 API 变更历史
   - 提供迁移指南

3. **用户体验**
   - 提供清晰的错误信息
   - 包含完整的使用示例
   - 添加交互式测试功能

4. **安全考虑**
   - 不在文档中暴露敏感信息
   - 提供安全的测试环境
   - 限制生产环境访问
