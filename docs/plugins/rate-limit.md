# 速率限制插件 (Rate Limit Plugin)

## 概述

速率限制插件基于令牌桶算法实现请求速率控制，可以防止API被恶意调用或过度使用，保护后端服务的稳定性。

## 特性

- **令牌桶算法**：平滑的速率控制，支持突发流量
- **多维度限制**：支持基于IP、用户、路径等多种维度的限制
- **灵活规则**：支持正则表达式匹配和优先级控制
- **内存存储**：高性能的内存存储，支持并发访问
- **实时监控**：提供详细的限流统计信息

## 配置参数

### 基础配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | true | 是否启用插件 |
| `default_rate` | int | 100 | 默认每秒允许的请求数 |
| `default_burst` | int | 10 | 默认突发请求数 |
| `cleanup_interval` | string | "5m" | 清理过期限制器的间隔 |

### 限制规则

每个规则包含以下参数：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `name` | string | 是 | 规则名称 |
| `pattern` | string | 否 | 路径匹配正则表达式 |
| `rate` | int | 是 | 每秒允许的请求数 |
| `burst` | int | 是 | 突发请求数 |
| `key_type` | string | 否 | 限制键类型：ip, user, path, custom |
| `custom_key` | string | 否 | 自定义键模板 |
| `methods` | []string | 否 | 限制的HTTP方法 |
| `enabled` | boolean | 否 | 是否启用此规则 |

## 配置示例

### 基础配置

```yaml
plugins:
  rate_limit:
    enabled: true
    default_rate: 100
    default_burst: 10
    cleanup_interval: "5m"
    rules: []
```

### 基于IP的限制

```yaml
plugins:
  rate_limit:
    enabled: true
    default_rate: 100
    default_burst: 10
    rules:
      - name: "ip_limit"
        pattern: ".*"
        rate: 50
        burst: 5
        key_type: "ip"
        enabled: true
```

### 基于用户的限制

```yaml
plugins:
  rate_limit:
    enabled: true
    rules:
      - name: "user_api_limit"
        pattern: "/api/.*"
        rate: 1000
        burst: 100
        key_type: "user"
        enabled: true
```

### 基于路径的限制

```yaml
plugins:
  rate_limit:
    enabled: true
    rules:
      - name: "upload_limit"
        pattern: "/upload/.*"
        rate: 10
        burst: 2
        key_type: "path"
        methods: ["POST", "PUT"]
        enabled: true
```

### 自定义键限制

```yaml
plugins:
  rate_limit:
    enabled: true
    rules:
      - name: "custom_limit"
        pattern: "/api/.*"
        rate: 200
        burst: 20
        key_type: "custom"
        custom_key: "${client_ip}:${user_id}"
        enabled: true
```

### 复合规则配置

```yaml
plugins:
  rate_limit:
    enabled: true
    default_rate: 100
    default_burst: 10
    cleanup_interval: "10m"
    rules:
      # 严格限制登录接口
      - name: "login_limit"
        pattern: "^/auth/login$"
        rate: 5
        burst: 1
        key_type: "ip"
        methods: ["POST"]
        enabled: true
      
      # 限制API接口
      - name: "api_limit"
        pattern: "^/api/.*"
        rate: 1000
        burst: 100
        key_type: "user"
        enabled: true
      
      # 限制上传接口
      - name: "upload_limit"
        pattern: "^/upload/.*"
        rate: 10
        burst: 2
        key_type: "ip"
        methods: ["POST", "PUT"]
        enabled: true
      
      # 公共接口较宽松限制
      - name: "public_limit"
        pattern: "^/public/.*"
        rate: 200
        burst: 50
        key_type: "ip"
        enabled: true
```

## 限制键类型说明

### IP限制 (ip)
基于客户端IP地址进行限制，适用于防止单个IP的恶意请求。

```yaml
key_type: "ip"
```

### 用户限制 (user)
基于用户ID进行限制，需要在认证后获取用户信息。

```yaml
key_type: "user"
```

### 路径限制 (path)
基于请求路径进行限制，所有访问相同路径的请求共享限制。

```yaml
key_type: "path"
```

### 自定义限制 (custom)
基于自定义键模板进行限制，支持变量替换。

```yaml
key_type: "custom"
custom_key: "${client_ip}:${user_id}:${path}"
```

## 支持的变量

在自定义键模板中可以使用以下变量：

| 变量 | 说明 |
|------|------|
| `${request_id}` | 请求ID |
| `${client_ip}` | 客户端IP |
| `${method}` | HTTP方法 |
| `${path}` | 请求路径 |
| `${user_id}` | 用户ID |
| `${query.参数名}` | 查询参数 |
| `${header.头部名}` | 请求头 |
| `${meta.元数据名}` | 元数据 |

## 响应头

当请求被限制时，插件会添加以下响应头：

| 头部 | 说明 |
|------|------|
| `X-RateLimit-Limit` | 速率限制值 |
| `X-RateLimit-Remaining` | 剩余请求数 |
| `X-RateLimit-Reset` | 重置时间戳 |
| `Retry-After` | 建议重试间隔（秒） |

## 错误响应

当请求超过限制时，返回HTTP 429状态码：

```json
{
  "error": "请求频率过高，请稍后重试",
  "code": 429
}
```

## 监控指标

插件提供以下监控指标：

- `rate_limit_requests_total`：总请求数
- `rate_limit_blocked_total`：被阻止的请求数
- `rate_limit_active_limiters`：活跃限制器数量
- `rate_limit_rule_matches`：规则匹配次数

## 性能考虑

1. **内存使用**：每个唯一键会创建一个限制器，注意内存使用
2. **清理机制**：定期清理不活跃的限制器，避免内存泄漏
3. **并发安全**：使用读写锁保证并发安全
4. **规则优化**：将匹配频率高的规则放在前面

## 故障排查

### 常见问题

1. **限制不生效**
   - 检查规则是否启用
   - 验证正则表达式是否正确
   - 确认键类型配置是否合适

2. **误限制正常请求**
   - 检查限制值是否过低
   - 验证键生成逻辑是否正确
   - 考虑调整突发值

3. **内存使用过高**
   - 减少清理间隔
   - 优化键生成策略
   - 考虑使用外部存储

### 调试命令

```bash
# 查看插件状态
curl http://localhost:8080/admin/plugins/rate_limit/status

# 查看限制器统计
curl http://localhost:8080/admin/plugins/rate_limit/stats

# 重置特定键的限制
curl -X POST http://localhost:8080/admin/plugins/rate_limit/reset \
  -d '{"key": "*************"}'
```

## 最佳实践

1. **分层限制**：结合全局限制和特定接口限制
2. **合理设置突发值**：允许短时间内的流量突发
3. **监控告警**：设置限流触发的告警机制
4. **渐进式限制**：根据业务增长逐步调整限制值
5. **白名单机制**：为可信客户端设置更高的限制

## 与其他插件的配合

- **IP过滤插件**：先过滤恶意IP，再进行速率限制
- **JWT插件**：获取用户信息后进行用户级限制
- **熔断器插件**：在后端服务异常时配合熔断保护
