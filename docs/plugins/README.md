# API网关插件系统文档

本文档介绍API网关的插件系统及其内置插件的使用方法。

## 插件系统概述

API网关采用模块化的插件架构，支持在请求处理的不同阶段执行插件逻辑。插件系统具有以下特点：

- **多阶段执行**：支持6个执行阶段（预认证、后认证、预代理、后代理、预响应、后响应）
- **优先级控制**：插件按优先级顺序执行，数值越小优先级越高
- **动态配置**：支持运行时配置更新，无需重启服务
- **健康检查**：内置插件健康检查机制
- **性能监控**：提供详细的插件执行统计信息

## 插件执行阶段

| 阶段 | 说明 | 典型用途 |
|------|------|----------|
| PhasePreAuth | 预认证阶段 | IP过滤、请求大小限制、速率限制 |
| PhasePostAuth | 后认证阶段 | 用户权限检查、审计日志 |
| PhasePreProxy | 预代理阶段 | 请求重写、CORS处理、JWT验证 |
| PhasePostProxy | 后代理阶段 | 熔断器状态更新、响应缓存 |
| PhasePreResponse | 预响应阶段 | 响应转换、数据脱敏 |
| PhasePostResponse | 后响应阶段 | 响应修改、指标收集、日志记录 |

## 内置插件列表

### 1. 速率限制插件 (Rate Limit)

**功能**：基于令牌桶算法的请求速率限制

**优先级**：200

**执行阶段**：PreAuth

**配置示例**：
```yaml
rate_limit:
  enabled: true
  default_rate: 100
  default_burst: 10
  rules:
    - name: "api_limit"
      pattern: "/api/.*"
      rate: 50
      burst: 5
      key_type: "ip"
```

**支持的限制维度**：
- IP地址限制
- 用户ID限制
- 路径限制
- 自定义键限制

### 2. CORS插件 (Cross-Origin Resource Sharing)

**功能**：处理跨域资源共享请求

**优先级**：300

**执行阶段**：PreProxy

**配置示例**：
```yaml
cors:
  enabled: true
  allowed_origins: ["*"]
  allowed_methods: ["GET", "POST", "PUT", "DELETE"]
  allowed_headers: ["Content-Type", "Authorization"]
  allow_credentials: true
  max_age: 3600
```

**特性**：
- 支持预检请求处理
- 灵活的Origin匹配（支持通配符）
- 可配置的方法和头部白名单
- 凭证支持控制

### 3. JWT认证插件 (JWT Authentication)

**功能**：JWT令牌验证和用户身份认证

**优先级**：250

**执行阶段**：PreProxy

**配置示例**：
```yaml
jwt:
  enabled: true
  secret: "your-secret-key"
  algorithm: "HS256"
  skip_paths: ["/login", "/register"]
  custom_claims:
    role: "admin"
```

**支持的算法**：
- HMAC: HS256, HS384, HS512
- RSA: RS256, RS384, RS512

### 4. 请求重写插件 (Request Rewrite)

**功能**：重写请求路径、查询参数和请求头

**优先级**：400

**执行阶段**：PreProxy

**配置示例**：
```yaml
rewrite:
  enabled: true
  path_rules:
    - name: "api_v1_to_v2"
      pattern: "^/api/v1/(.*)"
      replacement: "/api/v2/$1"
      method: "*"
  query_rules:
    - name: "add_version"
      path_pattern: "/api/.*"
      add:
        version: "2.0"
```

**支持的重写类型**：
- 路径重写（正则表达式支持）
- 查询参数添加/删除/重命名
- 请求头修改
- 变量替换支持

### 5. 响应修改插件 (Response Modifier)

**功能**：修改响应状态码、头部和响应体

**优先级**：500

**执行阶段**：PostProxy

**配置示例**：
```yaml
response:
  enabled: true
  header_rules:
    - name: "add_security_headers"
      path_pattern: ".*"
      add:
        X-Frame-Options: "DENY"
        X-Content-Type-Options: "nosniff"
  body_rules:
    - name: "json_transform"
      path_pattern: "/api/.*"
      action: "transform"
      json_path: "$.timestamp"
      json_value: "${timestamp}"
```

**支持的修改操作**：
- 响应头添加/设置/删除
- 响应体替换/追加/前置
- JSON响应转换
- 状态码修改

### 6. IP过滤插件 (IP Filter)

**功能**：基于IP地址的访问控制

**优先级**：150

**执行阶段**：PreAuth

**配置示例**：
```yaml
ip_filter:
  enabled: true
  mode: "blacklist"  # whitelist, blacklist, both
  whitelist:
    - "***********/24"
    - "10.0.0.0/8"
  blacklist:
    - "*************"
  trusted_proxies:
    - "127.0.0.1/8"
```

**特性**：
- 支持CIDR格式的IP范围
- 白名单/黑名单模式
- 真实IP提取（支持代理）
- 路径跳过配置

### 7. 请求大小限制插件 (Size Limit)

**功能**：限制请求体和请求头的大小

**优先级**：50

**执行阶段**：PreAuth

**配置示例**：
```yaml
size_limit:
  enabled: true
  default_max_size: "10MB"
  header_max_size: "1MB"
  rules:
    - name: "upload_limit"
      path_pattern: "/upload/.*"
      max_size: "100MB"
      content_types: ["multipart/form-data"]
```

**支持的大小单位**：
- B (字节)
- KB (千字节)
- MB (兆字节)
- GB (吉字节)

### 8. 熔断器插件 (Circuit Breaker)

**功能**：在后端服务异常时自动熔断保护系统

**优先级**：600

**执行阶段**：PreProxy, PostProxy

**配置示例**：
```yaml
circuit_breaker:
  enabled: true
  default_config:
    failure_threshold: 5
    success_threshold: 3
    timeout: "60s"
    failure_rate: 0.5
    slow_call_threshold: "5s"
  rules:
    - name: "api_breaker"
      path_pattern: "/api/.*"
      failure_threshold: 10
      timeout: "30s"
```

**熔断器状态**：
- **CLOSED**：正常状态，允许所有请求
- **OPEN**：熔断状态，拒绝所有请求
- **HALF_OPEN**：半开状态，允许少量请求测试服务恢复

## 插件配置管理

### 全局配置

插件可以通过配置文件进行全局配置：

```yaml
plugins:
  rate_limit:
    enabled: true
    default_rate: 100
  cors:
    enabled: true
    allowed_origins: ["*"]
  # ... 其他插件配置
```

### 动态配置

支持通过API动态更新插件配置：

```bash
# 更新插件配置
curl -X PUT http://localhost:8080/admin/plugins/rate_limit/config \
  -H "Content-Type: application/json" \
  -d '{"enabled": false}'

# 获取插件状态
curl http://localhost:8080/admin/plugins/rate_limit/status
```

### 插件监控

提供详细的插件执行统计：

```bash
# 获取插件统计信息
curl http://localhost:8080/admin/plugins/stats

# 响应示例
{
  "rate_limit": {
    "executions": 1000,
    "errors": 5,
    "avg_duration": "1.2ms",
    "last_execution": "2024-01-01T12:00:00Z"
  }
}
```

## 自定义插件开发

### 插件接口

所有插件必须实现以下接口：

```go
type Plugin interface {
    Name() string
    Version() string
    Description() string
    Priority() int
    Phases() []PluginPhase
    Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error)
    Configure(config map[string]interface{}) error
    Start() error
    Stop() error
    HealthCheck() error
}
```

### 开发示例

```go
type MyPlugin struct {
    logger *telemetry.Logger
    config *MyConfig
}

func (p *MyPlugin) Name() string { return "my_plugin" }
func (p *MyPlugin) Version() string { return "1.0.0" }
func (p *MyPlugin) Description() string { return "我的自定义插件" }
func (p *MyPlugin) Priority() int { return 100 }

func (p *MyPlugin) Phases() []PluginPhase {
    return []PluginPhase{PhasePreProxy}
}

func (p *MyPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
    // 插件逻辑实现
    return &PluginResult{Continue: true}, nil
}
```

## 最佳实践

1. **性能优化**：避免在插件中执行耗时操作，使用异步处理
2. **错误处理**：合理处理错误，避免影响整个请求链路
3. **配置验证**：在Configure方法中验证配置的有效性
4. **日志记录**：使用结构化日志记录插件执行信息
5. **监控指标**：暴露关键的性能和业务指标
6. **资源清理**：在Stop方法中正确清理资源

## 故障排查

### 常见问题

1. **插件未执行**：检查插件是否已启用，执行阶段是否正确
2. **配置不生效**：验证配置格式，检查配置更新是否成功
3. **性能问题**：查看插件执行统计，识别性能瓶颈
4. **错误率高**：检查插件日志，分析错误原因

### 调试工具

```bash
# 查看插件列表
curl http://localhost:8080/admin/plugins

# 查看特定插件详情
curl http://localhost:8080/admin/plugins/rate_limit

# 查看插件健康状态
curl http://localhost:8080/admin/plugins/health
```

## 总结

API网关的插件系统提供了强大而灵活的扩展能力，通过合理配置和使用内置插件，可以满足大部分API管理需求。对于特殊需求，也可以通过开发自定义插件来实现。
