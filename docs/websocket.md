# WebSocket 完整支持系统

API网关的WebSocket完整支持系统提供了企业级的实时通信能力，包括连接管理、消息路由、协议转换、负载均衡和监控等功能。

## 🌟 核心特性

### 1. 连接管理
- **高性能连接池** - 支持大量并发WebSocket连接
- **连接生命周期管理** - 自动处理连接建立、维护和清理
- **会话保持** - 支持基于Cookie、IP或Header的会话保持
- **连接监控** - 实时监控连接状态和统计信息

### 2. 消息路由
- **智能路由** - 基于路径、子协议和头部的消息路由
- **负载均衡** - 支持轮询、最少连接、IP哈希等策略
- **故障转移** - 自动检测和处理上游服务故障
- **健康检查** - 定期检查上游服务健康状态

### 3. 协议转换
- **消息格式转换** - JSON、XML、二进制格式转换
- **协议适配** - WebSocket到HTTP/gRPC的协议转换
- **内容转换** - 基于模板、正则表达式和JSONPath的内容转换
- **头部管理** - 动态添加、删除和修改消息头部

### 4. 安全认证
- **多种认证方式** - JWT、API Key、自定义认证
- **权限控制** - 基于角色和权限的访问控制
- **Origin验证** - 防止跨域攻击
- **消息过滤** - 基于内容和频率的消息过滤

### 5. 性能优化
- **消息压缩** - 自动压缩大消息以节省带宽
- **连接复用** - 高效的连接池管理
- **异步处理** - 非阻塞的消息处理
- **缓冲优化** - 可配置的读写缓冲区

### 6. 监控和调试
- **详细指标** - 连接数、消息数、错误率等指标
- **实时日志** - 可配置的日志记录
- **分布式追踪** - 支持OpenTracing
- **健康检查** - 服务健康状态监控

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WebSocket     │    │   API Gateway   │    │   Upstream      │
│   Client        │◄──►│   WebSocket     │◄──►│   Services      │
│                 │    │   Manager       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   Monitoring    │
                    │   & Metrics     │
                    └─────────────────┘
```

### 核心组件

1. **WebSocket Manager** - 连接和消息管理
2. **Router** - 路由匹配和分发
3. **Proxy** - 上游连接代理
4. **Connection Pool** - 连接池管理
5. **Message Transformer** - 消息转换
6. **Auth Handler** - 认证处理
7. **Metrics Collector** - 指标收集

## 📋 配置说明

### 基本配置

```yaml
websocket:
  enabled: true
  connection:
    read_buffer_size: 4096
    write_buffer_size: 4096
    check_origin: true
    allowed_origins:
      - "https://app.example.com"
    max_message_size: 1048576
```

### 路由配置

```yaml
websocket:
  routes:
    - name: "chat_room"
      path: "/ws/chat/{room_id}"
      upstream:
        type: "service_discovery"
        service_name: "chat-service"
        load_balancer: "round_robin"
      auth:
        required: true
        methods: ["jwt"]
```

### 代理配置

```yaml
websocket:
  proxy:
    enabled: true
    mode: "transparent"
    buffer_size: 8192
    reconnect:
      enabled: true
      max_retries: 3
      interval: "5s"
```

## 🚀 使用示例

### 1. 基本WebSocket连接

```javascript
// 客户端连接
const ws = new WebSocket('ws://localhost:8080/ws/chat/room1?token=jwt_token');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
};

ws.send(JSON.stringify({
    type: 'chat_message',
    data: {
        text: 'Hello, World!',
        user: 'john_doe'
    }
}));
```

### 2. 带认证的连接

```javascript
// 使用API Key认证
const ws = new WebSocket('ws://localhost:8080/ws/notifications', [], {
    headers: {
        'X-API-Key': 'your_api_key'
    }
});
```

### 3. 子协议连接

```javascript
// 指定子协议
const ws = new WebSocket('ws://localhost:8080/ws/data/stream', ['json-rpc']);
```

## 🔧 API 接口

### 管理API

#### 获取WebSocket统计
```http
GET /websocket/stats
```

响应示例：
```json
{
  "total_connections": 1500,
  "active_connections": 1200,
  "total_messages": 50000,
  "sent_messages": 25000,
  "received_messages": 25000,
  "errors": 10,
  "avg_connection_duration": "300s",
  "avg_message_size": 512
}
```

#### 获取活跃连接
```http
GET /websocket/connections
```

#### 广播消息
```http
POST /websocket/broadcast
Content-Type: application/json

{
  "message": {
    "type": "announcement",
    "data": {
      "text": "系统维护通知"
    }
  },
  "filter": {
    "room_id": "room1"
  }
}
```

#### 关闭连接
```http
DELETE /websocket/connections/{connection_id}
```

## 📊 监控指标

### 连接指标
- `websocket_connections_total` - 总连接数
- `websocket_connections_active` - 活跃连接数
- `websocket_connections_duration` - 连接持续时间

### 消息指标
- `websocket_messages_total` - 总消息数
- `websocket_messages_sent` - 发送消息数
- `websocket_messages_received` - 接收消息数
- `websocket_message_size_bytes` - 消息大小

### 错误指标
- `websocket_errors_total` - 总错误数
- `websocket_connection_errors` - 连接错误数
- `websocket_message_errors` - 消息错误数

### 代理指标
- `websocket_proxy_connections` - 代理连接数
- `websocket_proxy_messages` - 代理消息数
- `websocket_proxy_latency` - 代理延迟

## 🔒 安全最佳实践

### 1. 认证和授权
```yaml
websocket:
  auth:
    enabled: true
    methods: ["jwt", "api_key"]
    jwt:
      secret: "${JWT_SECRET}"
      algorithm: "HS256"
```

### 2. Origin验证
```yaml
websocket:
  connection:
    check_origin: true
    allowed_origins:
      - "https://app.example.com"
      - "https://*.example.com"
```

### 3. 消息过滤
```yaml
websocket:
  proxy:
    message_filter:
      enabled: true
      rules:
        - name: "block_large_messages"
          condition:
            size_range:
              max: 1048576  # 1MB
          action: "deny"
```

### 4. 速率限制
```yaml
websocket:
  proxy:
    message_filter:
      rules:
        - name: "rate_limit_text"
          condition:
            message_type: "text"
            rate_limit:
              requests: 100
              window: "1m"
              key: "ip"
          action: "rate_limit"
```

## ⚡ 性能优化

### 1. 连接优化
- 设置合适的缓冲区大小
- 启用连接复用
- 配置连接超时

### 2. 消息优化
- 启用消息压缩
- 使用二进制格式传输大数据
- 批量处理消息

### 3. 负载均衡优化
- 使用一致性哈希
- 启用会话保持
- 配置健康检查

### 4. 监控优化
- 设置合适的指标收集间隔
- 使用采样减少追踪开销
- 配置日志轮转

## 🐛 故障排除

### 常见问题

#### 1. 连接失败
- 检查Origin配置
- 验证认证信息
- 确认路由配置

#### 2. 消息丢失
- 检查缓冲区大小
- 验证上游服务状态
- 查看错误日志

#### 3. 性能问题
- 监控连接数和消息频率
- 检查内存和CPU使用
- 优化缓冲区配置

### 调试工具

#### 1. 日志分析
```bash
# 查看WebSocket日志
kubectl logs -f deployment/api-gateway | grep websocket
```

#### 2. 指标监控
```bash
# 查看WebSocket指标
curl http://localhost:9090/metrics | grep websocket
```

#### 3. 连接测试
```bash
# 使用wscat测试连接
wscat -c ws://localhost:8080/ws/echo
```

## 📈 扩展性

### 水平扩展
- 支持多实例部署
- 使用Redis进行状态共享
- 配置负载均衡器

### 垂直扩展
- 增加内存和CPU资源
- 优化缓冲区配置
- 调整连接池大小

### 高可用性
- 部署多个副本
- 配置健康检查
- 实现故障转移

## 🔄 版本兼容性

- **WebSocket协议**: RFC 6455
- **HTTP版本**: HTTP/1.1, HTTP/2
- **TLS版本**: TLS 1.2+
- **Go版本**: 1.19+

## 📚 相关文档

- [配置参考](./configuration.md)
- [API文档](./api.md)
- [部署指南](./deployment.md)
- [监控指南](./monitoring.md)
- [安全指南](./security.md)
