# API 网关测试框架说明

## 概述

本 API 网关项目建立了完整的测试框架，包括单元测试、集成测试和性能基准测试，确保代码质量和系统稳定性。

## 测试架构

### 测试目录结构

```
tests/
├── unit/                    # 单元测试
│   ├── auth_test.go        # 认证模块测试
│   ├── config_test.go      # 配置模块测试
│   ├── security_test.go    # 安全模块测试
│   └── oidc_simple_test.go # OIDC认证测试
├── integration/            # 集成测试
│   └── simple_integration_test.go
└── internal/               # 内部模块测试
    └── core/
        └── gateway_test.go # 网关核心测试
```

## 单元测试

### 已实现的单元测试

#### 1. 网关核心测试 (`internal/core/gateway_test.go`)

**测试覆盖**:
- ✅ 健康检查功能测试
- ✅ 运行时间格式化测试
- ✅ 配置重载功能测试
- ✅ 性能基准测试

**测试结果**:
```
=== RUN   TestGateway_HealthCheck
--- PASS: TestGateway_HealthCheck (0.00s)
=== RUN   TestFormatUptime
--- PASS: TestFormatUptime (0.00s)

BenchmarkHealthCheck-4   	  873548	      1181 ns/op	    1997 B/op	      29 allocs/op
```

#### 2. 认证模块测试 (`tests/unit/auth_test.go`)

**测试覆盖**:
- ✅ JWT 认证器测试（有效/无效/过期 token）
- ✅ API Key 认证器测试
- ✅ 认证管理器集成测试
- ✅ 认证性能基准测试

**关键测试场景**:
- 有效 JWT Token 认证
- 无效 JWT Token 处理
- 过期 JWT Token 处理
- 缺少 Authorization 头处理
- API Key 认证（Header 和 Query 参数）
- 多认证器链式处理

#### 3. 安全模块测试 (`tests/unit/security_test.go`)

**测试覆盖**:
- ✅ 限流器测试（Token Bucket 算法）
- ✅ WAF 引擎测试（SQL 注入、XSS 防护）
- ✅ IP 过滤器测试
- ✅ CORS 处理器测试
- ✅ 安全组件性能基准测试

**关键测试场景**:
- 正常请求通过限流
- 超过限制的请求被阻止
- 不同 IP 独立限流
- SQL 注入攻击检测
- XSS 攻击检测
- IP 白名单/黑名单过滤
- CORS 预检请求处理

#### 4. 配置模块测试 (`tests/unit/config_test.go`)

**测试覆盖**:
- ✅ 配置验证测试
- ✅ 配置加载器测试
- ✅ 动态配置测试
- ✅ 内存存储测试
- ✅ 配置性能基准测试

#### 5. OIDC 认证测试 (`tests/unit/oidc_simple_test.go`)

**测试覆盖**:
- ✅ OIDC 配置验证
- ✅ Token 提取功能测试
- ✅ 认证上下文测试
- ✅ 认证结果结构测试
- ✅ OIDC 性能基准测试

**基准测试结果**:
```
BenchmarkOIDCConfig/配置创建-4                 	1000000000	         0.2470 ns/op
BenchmarkOIDCConfig/认证上下文创建-4                       	75486704	        16.93 ns/op
```

## 集成测试

### 集成测试框架 (`tests/integration/simple_integration_test.go`)

**测试覆盖**:
- ✅ 基本网关功能测试
- ✅ 配置加载和验证
- ✅ 日志系统初始化
- ✅ 指标系统初始化
- ✅ HTTP 客户端功能
- ✅ 认证头传递
- ✅ 错误处理
- ✅ 并发请求处理
- ✅ 上下文超时处理

**测试结果**:
```
=== RUN   TestBasicGatewayFunctionality
--- PASS: TestBasicGatewayFunctionality (0.00s)
=== RUN   TestConfigurationValidation
--- PASS: TestConfigurationValidation (0.00s)
=== RUN   TestConcurrentRequests
    simple_integration_test.go:336: 并发测试结果: 成功 1000, 失败 0
--- PASS: TestConcurrentRequests (0.05s)

BenchmarkSimpleRequest-4   	   21468	     58436 ns/op	   19926 B/op	     148 allocs/op
```

### 模拟后端服务器

集成测试包含完整的模拟后端服务器，支持：
- 公开 API 端点
- 受保护 API 端点
- 健康检查端点
- 认证头验证

## 性能基准测试

### 基准测试结果汇总

| 组件 | 操作 | 性能指标 | 内存使用 |
|------|------|----------|----------|
| 健康检查 | 单次请求 | 1,181 ns/op | 1,997 B/op |
| OIDC配置 | 配置创建 | 0.25 ns/op | - |
| OIDC认证 | 上下文创建 | 16.93 ns/op | - |
| 集成测试 | HTTP请求 | 58,436 ns/op | 19,926 B/op |

### 性能分析

1. **健康检查性能**: 约 1.2μs 响应时间，性能优秀
2. **OIDC 配置**: 配置创建几乎无开销
3. **HTTP 请求处理**: 约 58μs 端到端响应时间，包含完整的请求处理流程

## 测试运行指南

### 运行所有测试

```bash
# 运行所有测试
go test ./...

# 运行单元测试
go test ./tests/unit -v

# 运行集成测试
go test ./tests/integration -v

# 运行核心模块测试
go test ./internal/core -v
```

### 运行基准测试

```bash
# 运行所有基准测试
go test ./... -bench=. -benchmem

# 运行特定模块基准测试
go test ./tests/unit -bench=BenchmarkOIDC -benchmem
go test ./tests/integration -bench=BenchmarkSimple -benchmem
```

### 生成测试覆盖率报告

```bash
# 生成覆盖率报告
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html

# 查看覆盖率统计
go tool cover -func=coverage.out
```

## 测试最佳实践

### 1. 测试命名规范

- 测试函数以 `Test` 开头
- 基准测试以 `Benchmark` 开头
- 使用描述性的中文子测试名称
- 遵循 `TestComponent_Function` 格式

### 2. 测试结构

```go
func TestComponent_Function(t *testing.T) {
    t.Run("正常情况", func(t *testing.T) {
        // 测试正常流程
    })
    
    t.Run("异常情况", func(t *testing.T) {
        // 测试异常处理
    })
    
    t.Run("边界条件", func(t *testing.T) {
        // 测试边界情况
    })
}
```

### 3. 断言使用

- 使用 `testify/assert` 进行断言
- 使用 `testify/require` 处理关键错误
- 提供清晰的错误信息

### 4. 测试数据管理

- 使用表驱动测试处理多个测试用例
- 创建测试辅助函数减少重复代码
- 使用模拟对象隔离外部依赖

## 持续集成

### CI/CD 集成

建议在 CI/CD 流水线中包含以下测试步骤：

```yaml
# 示例 GitHub Actions 配置
- name: 运行单元测试
  run: go test ./tests/unit -v

- name: 运行集成测试
  run: go test ./tests/integration -v

- name: 生成覆盖率报告
  run: |
    go test ./... -coverprofile=coverage.out
    go tool cover -func=coverage.out

- name: 运行基准测试
  run: go test ./... -bench=. -benchmem
```

### 质量门禁

- 单元测试通过率: 100%
- 代码覆盖率: > 80%
- 基准测试性能回归: < 10%
- 集成测试通过率: 100%

## 故障排查

### 常见测试问题

1. **测试超时**
   - 增加测试超时时间
   - 检查外部依赖可用性
   - 使用模拟对象替代真实服务

2. **并发测试失败**
   - 检查竞态条件
   - 使用适当的同步机制
   - 避免共享可变状态

3. **内存泄漏**
   - 检查资源清理
   - 使用 defer 语句确保清理
   - 监控基准测试内存使用

### 调试技巧

- 使用 `t.Log()` 输出调试信息
- 启用详细日志模式 (`-v` 标志)
- 使用 `go test -race` 检测竞态条件
- 使用 `go test -timeout` 设置测试超时

## 未来改进

### 计划中的测试增强

1. **端到端测试**: 完整的用户场景测试
2. **压力测试**: 高负载下的系统行为测试
3. **混沌工程**: 故障注入和恢复测试
4. **安全测试**: 渗透测试和漏洞扫描
5. **性能回归测试**: 自动化性能监控

### 测试工具集成

- **测试报告**: 集成测试报告生成工具
- **覆盖率分析**: 详细的覆盖率分析和趋势
- **性能监控**: 基准测试结果历史跟踪
- **质量分析**: 代码质量和技术债务分析
