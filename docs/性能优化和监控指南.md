# 性能优化和监控指南

## 概述

本 API 网关提供了完整的性能优化和监控告警系统，包括自动性能优化、实时监控、智能告警和多种通知方式，确保系统在高负载下的稳定运行。

## 🚀 性能优化系统

### 性能优化器 (Performance Optimizer)

性能优化器是一个自动化的系统组件，持续监控系统性能并执行优化操作。

#### 主要功能

1. **内存优化**
   - 自动监控内存使用情况
   - 超过阈值时自动执行内存清理
   - 强制垃圾回收和内存释放

2. **GC 优化**
   - 自定义 GC 目标百分比
   - 定期强制 GC 执行
   - GC 性能监控

3. **性能监控**
   - 实时收集系统性能指标
   - 内存使用、协程数量、GC 统计
   - 性能趋势分析

#### 配置示例

```yaml
performance:
  optimizer:
    enabled: true
    monitor_interval: 30s
    
    # GC 优化配置
    gc:
      enabled: true
      target_percent: 100
      force_interval: 5m
    
    # 内存优化配置
    memory:
      enabled: true
      threshold_mb: 512
      cleanup_interval: 2m
    
    # 连接池优化配置
    pool:
      enabled: true
      max_idle_conns: 100
      max_open_conns: 1000
      conn_max_lifetime: 30m
```

#### 使用示例

```go
// 创建性能优化器
config := performance.DefaultOptimizerConfig()
optimizer := performance.NewOptimizer(config, logger, metrics)

// 启动优化器
ctx := context.Background()
if err := optimizer.Start(ctx); err != nil {
    log.Fatal("启动性能优化器失败:", err)
}

// 获取性能统计
stats := optimizer.GetStats()
fmt.Printf("内存使用: %d MB\n", stats.MemoryUsage/1024/1024)
fmt.Printf("协程数量: %d\n", stats.GoroutineCount)
fmt.Printf("GC次数: %d\n", stats.GCCount)

// 停止优化器
optimizer.Stop()
```

### 性能基准测试结果

| 组件 | 操作 | 性能指标 | 内存使用 |
|------|------|----------|----------|
| 优化器创建 | 单次创建 | 568 ns/op | 1,464 B/op |
| 性能统计 | 获取统计 | 40 ns/op | 96 B/op |

## 📊 监控告警系统

### 告警管理器 (Alert Manager)

告警管理器提供了完整的监控告警功能，支持多种告警规则和通知方式。

#### 核心功能

1. **告警规则管理**
   - 支持多种条件类型 (>, <, >=, <=, ==, !=)
   - 可配置阈值和持续时间
   - 支持标签过滤和分组

2. **告警状态管理**
   - 告警触发和解决状态跟踪
   - 告警历史记录
   - 告警去重和聚合

3. **多种通知方式**
   - 日志通知
   - Webhook 通知
   - Slack 通知
   - 邮件通知

#### 默认告警规则

系统内置了以下默认告警规则：

1. **高内存使用率**
   - 指标: `performance_memory_usage_bytes`
   - 阈值: 500MB
   - 持续时间: 2分钟
   - 严重级别: warning

2. **高协程数量**
   - 指标: `performance_goroutine_count`
   - 阈值: 1000
   - 持续时间: 1分钟
   - 严重级别: warning

3. **高错误率**
   - 指标: `gateway_requests_error_rate`
   - 阈值: 5%
   - 持续时间: 30秒
   - 严重级别: critical

4. **高响应时间**
   - 指标: `gateway_request_duration_avg`
   - 阈值: 1000ms
   - 持续时间: 1分钟
   - 严重级别: warning

#### 配置示例

```yaml
monitoring:
  alerting:
    enabled: true
    check_interval: 30s
    rules_file: "configs/alert-rules.yaml"
    
    notifications:
      - type: "log"
        enabled: true
        config: {}
      
      - type: "webhook"
        enabled: true
        config:
          url: "https://hooks.example.com/alerts"
          headers:
            Authorization: "Bearer token"
          timeout: "10s"
      
      - type: "slack"
        enabled: true
        config:
          webhook_url: "https://hooks.slack.com/services/..."
          channel: "#alerts"
          username: "API Gateway"
          icon_emoji: ":warning:"
```

#### 使用示例

```go
// 创建告警管理器
config := monitoring.DefaultAlertConfig()
alertManager := monitoring.NewAlertManager(config, logger, metrics)

// 启动告警管理器
ctx := context.Background()
if err := alertManager.Start(ctx); err != nil {
    log.Fatal("启动告警管理器失败:", err)
}

// 添加自定义告警规则
rule := monitoring.AlertRule{
    Name:        "高CPU使用率",
    Description: "CPU使用率超过80%",
    Metric:      "cpu_usage_percent",
    Condition:   ">",
    Threshold:   80.0,
    Duration:    2 * time.Minute,
    Severity:    "warning",
    Enabled:     true,
}
alertManager.AddRule(rule)

// 获取当前告警
alerts := alertManager.GetAlerts()
for _, alert := range alerts {
    fmt.Printf("告警: %s, 状态: %s\n", alert.RuleName, alert.Status)
}

// 停止告警管理器
alertManager.Stop()
```

## 🔔 通知系统

### 支持的通知器

#### 1. 日志通知器 (Log Notifier)

将告警信息记录到日志系统中。

```go
notifier := monitoring.NewLogNotifier(logger)
```

特点：
- 零配置，开箱即用
- 支持不同严重级别的日志输出
- 结构化日志格式

#### 2. Webhook 通知器 (Webhook Notifier)

通过 HTTP POST 请求发送告警信息。

```go
config := map[string]interface{}{
    "url": "https://api.example.com/alerts",
    "headers": map[string]interface{}{
        "Authorization": "Bearer token",
        "Content-Type": "application/json",
    },
    "timeout": "10s",
}
notifier := monitoring.NewWebhookNotifier(config, logger)
```

消息格式：
```json
{
  "alert": {
    "id": "alert-001",
    "rule_name": "高内存使用率",
    "message": "内存使用率超过阈值",
    "severity": "warning",
    "value": 600.0,
    "threshold": 500.0,
    "status": "firing",
    "starts_at": "2025-08-17T08:00:00Z"
  },
  "timestamp": "2025-08-17T08:00:00Z",
  "gateway": "API Gateway"
}
```

#### 3. Slack 通知器 (Slack Notifier)

发送告警信息到 Slack 频道。

```go
config := map[string]interface{}{
    "webhook_url": "https://hooks.slack.com/services/...",
    "channel": "#alerts",
    "username": "API Gateway",
    "icon_emoji": ":warning:",
}
notifier := monitoring.NewSlackNotifier(config, logger)
```

特点：
- 丰富的消息格式
- 支持颜色编码（危险/警告/正常）
- 结构化字段显示

#### 4. 邮件通知器 (Email Notifier)

通过 SMTP 发送邮件告警。

```go
config := map[string]interface{}{
    "smtp_host": "smtp.example.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "password",
    "from": "<EMAIL>",
    "to": []interface{}{"<EMAIL>", "<EMAIL>"},
}
notifier := monitoring.NewEmailNotifier(config, logger)
```

## 📈 性能监控指标

### 系统指标

1. **内存指标**
   - `performance_memory_usage_bytes`: 内存使用量（字节）
   - `performance_memory_cleanup_total`: 内存清理次数

2. **GC 指标**
   - `performance_gc_count_total`: GC 执行次数
   - `performance_gc_pause_time_ms`: GC 暂停时间（毫秒）
   - `performance_force_gc_duration_ms`: 强制 GC 执行时间

3. **协程指标**
   - `performance_goroutine_count`: 当前协程数量

4. **告警指标**
   - `alerts_fired_total`: 触发的告警总数
   - `alerts_resolved_total`: 解决的告警总数

### 业务指标

1. **请求指标**
   - `gateway_requests_total`: 请求总数
   - `gateway_request_duration_seconds`: 请求处理时间
   - `gateway_requests_error_rate`: 错误率

2. **认证指标**
   - `auth_failures_total`: 认证失败次数
   - `auth_success_total`: 认证成功次数

3. **限流指标**
   - `rate_limit_hits_total`: 限流触发次数
   - `rate_limit_allowed_total`: 限流允许次数

## 🛠️ 最佳实践

### 性能优化建议

1. **内存管理**
   ```yaml
   memory:
     threshold_mb: 512  # 根据可用内存调整
     cleanup_interval: 2m  # 平衡性能和资源使用
   ```

2. **GC 调优**
   ```yaml
   gc:
     target_percent: 100  # 默认值，可根据需要调整
     force_interval: 5m   # 避免过于频繁的强制 GC
   ```

3. **监控频率**
   ```yaml
   monitor_interval: 30s  # 平衡实时性和性能开销
   ```

### 告警配置建议

1. **阈值设置**
   - 根据历史数据设置合理阈值
   - 避免过于敏感的告警规则
   - 考虑业务高峰期的正常波动

2. **持续时间**
   - 设置适当的持续时间避免误报
   - 关键指标可以设置较短的持续时间
   - 非关键指标可以设置较长的持续时间

3. **严重级别**
   - `critical`: 需要立即处理的问题
   - `warning`: 需要关注但不紧急的问题
   - `info`: 信息性告警

### 通知策略

1. **分级通知**
   ```yaml
   notifications:
     - type: "log"
       enabled: true
       # 所有告警都记录日志
     
     - type: "slack"
       enabled: true
       # 警告级别以上发送到 Slack
       
     - type: "email"
       enabled: true
       # 仅关键告警发送邮件
   ```

2. **告警去重**
   - 相同规则的重复告警会被合并
   - 避免告警风暴

3. **告警恢复通知**
   - 告警解决时自动发送恢复通知
   - 帮助运维人员了解问题状态

## 🔧 故障排查

### 常见问题

1. **性能优化器无法启动**
   - 检查配置文件格式
   - 确认权限设置
   - 查看日志错误信息

2. **告警不触发**
   - 验证告警规则配置
   - 检查指标数据是否正常
   - 确认告警管理器是否启动

3. **通知发送失败**
   - 检查网络连接
   - 验证通知器配置
   - 查看通知器错误日志

### 调试技巧

1. **启用调试日志**
   ```yaml
   logging:
     level: "debug"
   ```

2. **监控指标查看**
   ```bash
   curl http://localhost:8080/metrics | grep performance
   ```

3. **告警状态查看**
   ```bash
   curl http://localhost:8080/admin/alerts
   ```

## 📊 监控仪表板

建议使用 Grafana 创建监控仪表板，包含以下面板：

1. **系统性能面板**
   - 内存使用趋势
   - 协程数量变化
   - GC 性能指标

2. **业务指标面板**
   - 请求量和响应时间
   - 错误率趋势
   - 认证成功率

3. **告警状态面板**
   - 当前活跃告警
   - 告警历史统计
   - 告警解决时间

## 🚀 未来增强

计划中的功能改进：

1. **机器学习告警**
   - 基于历史数据的异常检测
   - 自适应阈值调整
   - 预测性告警

2. **更多通知渠道**
   - 钉钉、企业微信集成
   - 短信通知
   - 电话告警

3. **高级性能优化**
   - 自动负载均衡调整
   - 动态资源分配
   - 智能缓存策略
