# MCP (Model Context Protocol) 转换功能文档

## 概述

MCP 转换功能是 API 网关的一个重要特性，它提供了将现有 REST API 无侵入式地转换为 MCP (Model Context Protocol) 服务的能力。这使得现有的 API 可以直接被支持 MCP 协议的 AI 应用程序和工具使用，无需修改原有的 API 实现代码。

## 功能特性

### 🔄 无侵入式转换
- 通过中间件方式集成，不需要修改现有 API 代码
- 支持动态配置转换规则
- 与现有网关功能完全兼容

### 📋 协议支持
- 完整的 MCP 协议实现（基于 JSON-RPC 2.0）
- 支持 Request、Response、Notification 三种消息类型
- 实现协议生命周期管理（初始化、操作、关闭）

### 🛠️ 转换功能
- HTTP 方法到 MCP 方法的映射
- 请求参数格式转换（路径参数、查询参数、请求体、请求头）
- 响应数据格式转换
- 错误状态码映射

### ⚙️ 配置管理
- YAML 格式配置文件
- 支持路由级别的转换规则
- 灵活的参数映射配置
- 热重载配置支持

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway                              │
├─────────────────────────────────────────────────────────────┤
│  HTTP Request  →  [MCP Middleware]  →  [Existing APIs]     │
│                        ↓                                    │
│                   [MCP Converter]                           │
│                        ↓                                    │
│                 [MCP Protocol Layer]                        │
│                        ↓                                    │
│                  [Transport Layer]                          │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **MCP 中间件 (Middleware)**
   - 检测 MCP 转换需求
   - 调用相应的转换器
   - 处理转换结果

2. **转换器 (Converter)**
   - API 请求转换器：将 HTTP 请求转换为 MCP 请求
   - 响应转换器：将 MCP 响应转换为 HTTP 响应
   - 错误转换器：处理错误映射

3. **协议层 (Protocol)**
   - JSON-RPC 2.0 消息处理
   - 会话管理
   - 能力协商

4. **传输层 (Transport)**
   - stdio 传输
   - HTTP SSE 传输
   - 自定义传输

## 配置说明

### 基本配置

```yaml
mcp:
  enabled: true
  protocol_version: "2024-11-05"
```

### 转换规则配置

```yaml
conversion_rules:
  - path: "/api/v1/users"
    methods: ["GET", "POST"]
    mcp_method: "users.list"
    enabled: true
    priority: 1
    description: "用户管理 API 转换"
```

### 参数映射配置

```yaml
parameter_mapping:
  # 路径参数映射
  path:
    id: "user.id"
  
  # 查询参数映射
  query:
    page: "pagination.page"
    limit: "pagination.limit"
  
  # 请求体映射
  body:
    name: "user.name"
    email: "user.email"
  
  # 请求头映射
  headers:
    "X-Request-ID": "request.id"
```

### 响应映射配置

```yaml
response_mapping:
  success:
    data: "result.users"
    total: "result.total"
  headers:
    "X-Total-Count": "X-MCP-Total-Count"
```

### 错误映射配置

```yaml
error_mapping:
  status_code_mapping:
    400: -32602  # Invalid params
    404: -32601  # Method not found
    500: -32603  # Internal error
  default_error_code: -32603
  default_error_message: "服务器内部错误"
```

## 使用示例

### 1. 启用 MCP 转换

在网关配置中启用 MCP 中间件：

```go
// 在 gateway.go 中添加 MCP 中间件
func (g *Gateway) addMCPMiddleware() {
    if g.config.MCP.Enabled {
        mcpMiddleware := mcp.NewMiddleware(g.config.MCP, g.logger)
        g.router.Use(mcpMiddleware.Handle())
    }
}
```

### 2. 发送普通 HTTP 请求

```bash
curl -X GET "http://localhost:8080/api/v1/users?page=1&limit=10"
```

### 3. 发送 MCP 格式请求

```bash
curl -X POST "http://localhost:8080/mcp/message" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "users.list",
    "params": {
      "pagination": {
        "page": 1,
        "limit": 10
      }
    }
  }'
```

### 4. 使用 SSE 连接

```javascript
const eventSource = new EventSource('http://localhost:8080/mcp/sse');

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('收到 MCP 事件:', data);
};

eventSource.addEventListener('connected', function(event) {
  const data = JSON.parse(event.data);
  console.log('MCP 连接建立:', data.session_id);
});
```

## API 映射规则

### 路径参数映射

| HTTP 路径 | MCP 参数 | 示例 |
|-----------|----------|------|
| `/api/v1/users/{id}` | `user.id` | `123` → `{"user": {"id": "123"}}` |
| `/api/v1/orders/{orderId}/items/{itemId}` | `order.id`, `item.id` | 映射到嵌套结构 |

### 查询参数映射

| HTTP 查询参数 | MCP 参数 | 类型转换 |
|---------------|----------|----------|
| `page=1` | `pagination.page` | 字符串 → 数字 |
| `active=true` | `filter.active` | 字符串 → 布尔值 |
| `tags=a,b,c` | `filter.tags` | 字符串 → 数组 |

### 请求体映射

支持 JSON 和表单数据的映射：

```yaml
# JSON 请求体
{
  "name": "John Doe",
  "email": "<EMAIL>"
}

# 映射为 MCP 参数
{
  "user": {
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

### 响应映射

```yaml
# MCP 响应
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "users": [...],
    "total": 100
  }
}

# 转换为 HTTP 响应
{
  "success": true,
  "data": [...],
  "total": 100
}
```

## 错误处理

### HTTP 状态码到 MCP 错误码映射

| HTTP 状态码 | MCP 错误码 | 描述 |
|-------------|------------|------|
| 400 | -32602 | 无效参数 |
| 401 | -32001 | 未授权 |
| 403 | -32002 | 禁止访问 |
| 404 | -32601 | 方法未找到 |
| 500 | -32603 | 内部错误 |

### 错误响应格式

```json
{
  "error": {
    "code": -32602,
    "message": "无效参数",
    "data": {
      "field": "email",
      "reason": "格式不正确"
    }
  },
  "request_id": "req_123",
  "success": false
}
```

## 性能优化

### 缓存配置

```yaml
performance:
  cache:
    enabled: true
    type: "memory"
    ttl: 5m
    max_size: 1000
```

### 连接池配置

```yaml
performance:
  connection_pool:
    max_idle_connections: 10
    max_active_connections: 100
    idle_timeout: 5m
```

### 并发控制

```yaml
performance:
  max_concurrent_conversions: 100
  conversion_timeout: 10s
```

## 监控和日志

### 日志配置

```yaml
logging:
  enabled: true
  level: "info"
  log_requests: true
  log_responses: true
  log_conversions: true
  log_performance: true
```

### 指标监控

MCP 转换功能提供以下指标：

- `mcp_requests_total`: MCP 请求总数
- `mcp_conversion_requests_total`: 转换请求总数
- `mcp_conversion_success_total`: 转换成功总数
- `mcp_conversion_errors_total`: 转换错误总数
- `mcp_conversion_duration_seconds`: 转换耗时

## 故障排除

### 常见问题

1. **转换规则不匹配**
   - 检查路径和方法配置
   - 确认规则已启用
   - 验证优先级设置

2. **参数映射失败**
   - 检查映射配置语法
   - 验证参数类型转换
   - 查看详细错误日志

3. **性能问题**
   - 调整并发限制
   - 启用缓存
   - 优化转换规则

### 调试技巧

1. 启用详细日志：
```yaml
logging:
  level: "debug"
  log_conversions: true
```

2. 使用测试端点验证配置：
```bash
curl -X GET "http://localhost:8080/mcp/health"
```

3. 检查转换统计信息：
```bash
curl -X GET "http://localhost:8080/mcp/stats"
```

## 最佳实践

1. **规则设计**
   - 使用清晰的 MCP 方法命名
   - 设置合理的优先级
   - 添加详细的描述信息

2. **参数映射**
   - 使用语义化的参数名称
   - 保持映射结构的一致性
   - 考虑参数的类型转换

3. **性能优化**
   - 启用适当的缓存
   - 设置合理的超时时间
   - 监控转换性能指标

4. **错误处理**
   - 提供清晰的错误消息
   - 映射合适的错误码
   - 记录详细的错误信息
