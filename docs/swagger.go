// Package docs API网关文档
// API Gateway Documentation
//
// 这是一个企业级API网关系统，提供认证、授权、限流、负载均衡等功能
//
// Terms Of Service: https://github.com/your-org/api-gateway
//
//	Schemes: http, https
//	Host: localhost:8080
//	BasePath: /
//	Version: 1.0.0
//	License: MIT https://opensource.org/licenses/MIT
//	Contact: API Gateway Team <<EMAIL>> https://github.com/your-org/api-gateway
//
//	Consumes:
//	- application/json
//	- application/xml
//
//	Produces:
//	- application/json
//	- application/xml
//
//	Security:
//	- Bearer: []
//	- ApiKeyAuth: []
//
//	SecurityDefinitions:
//	Bearer:
//	  type: apiKey
//	  name: Authorization
//	  in: header
//	  description: "JWT Token认证，格式: Bearer {token}"
//	ApiKeyAuth:
//	  type: apiKey
//	  name: X-API-Key
//	  in: header
//	  description: "API Key认证"
//
// swagger:meta
package docs

import (
	"github.com/swaggo/swag"
)

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "https://github.com/your-org/api-gateway",
        "contact": {
            "name": "API Gateway Team",
            "url": "https://github.com/your-org/api-gateway",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {},
    "definitions": {},
    "securityDefinitions": {
        "Bearer": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header",
            "description": "JWT Token认证，格式: Bearer {token}"
        },
        "ApiKeyAuth": {
            "type": "apiKey", 
            "name": "X-API-Key",
            "in": "header",
            "description": "API Key认证"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0.0",
	Host:             "localhost:8080",
	BasePath:         "/",
	Schemes:          []string{"http", "https"},
	Title:            "API Gateway",
	Description:      "企业级API网关系统，提供认证、授权、限流、负载均衡等功能",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
