package core

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestGateway_HealthCheck 测试健康检查功能
func TestGateway_HealthCheck(t *testing.T) {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试配置
	cfg := &config.Config{
		Server: config.ServerConfig{
			Address:      ":8080",
			ReadTimeout:  30,
			WriteTimeout: 30,
			IdleTimeout:  120,
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "json",
			Output: "stdout",
		},
		Metrics: config.MetricsConfig{
			Enabled: true,
			Path:    "/metrics",
			Port:    9090,
		},
		Tracing: config.TracingConfig{
			Enabled:     false,
			ServiceName: "api-gateway-test",
		},
	}

	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.ConvertLoggingConfig(cfg.Logging))
	require.NoError(t, err)

	// 创建测试指标收集器
	metrics, err := telemetry.NewMetrics(cfg.Metrics)
	require.NoError(t, err)

	// 创建网关实例
	gateway := &Gateway{
		config:    cfg,
		logger:    logger,
		metrics:   metrics,
		startTime: time.Now().Add(-1 * time.Hour), // 模拟运行1小时
		version:   "1.0.0-test",
	}

	// 设置路由
	gateway.router = gin.New()
	gateway.router.GET("/health", gateway.healthCheck)

	// 创建测试请求
	req, err := http.NewRequest("GET", "/health", nil)
	require.NoError(t, err)

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 执行请求
	gateway.router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应体
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// 验证响应内容
	assert.Equal(t, "healthy", response["status"])
	assert.Equal(t, "1.0.0-test", response["version"])
	assert.Contains(t, response["uptime"], "小时") // 应该包含小时信息
	assert.NotEmpty(t, response["timestamp"])

	// 验证组件信息存在
	components, ok := response["components"].(map[string]interface{})
	assert.True(t, ok)
	assert.NotNil(t, components)
}

// TestFormatUptime 测试运行时间格式化功能
func TestFormatUptime(t *testing.T) {
	tests := []struct {
		name     string
		duration time.Duration
		expected string
	}{
		{
			name:     "几秒钟",
			duration: 30 * time.Second,
			expected: "30秒",
		},
		{
			name:     "几分钟",
			duration: 5*time.Minute + 30*time.Second,
			expected: "5分钟30秒",
		},
		{
			name:     "几小时",
			duration: 2*time.Hour + 30*time.Minute + 15*time.Second,
			expected: "2小时30分钟15秒",
		},
		{
			name:     "几天",
			duration: 3*24*time.Hour + 5*time.Hour + 20*time.Minute + 45*time.Second,
			expected: "3天5小时20分钟45秒",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatUptime(tt.duration)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGateway_ReloadConfig 测试配置重载功能
func TestGateway_ReloadConfig(t *testing.T) {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试配置
	cfg := &config.Config{
		Server: config.ServerConfig{
			Address: ":8080",
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "json",
			Output: "stdout",
		},
		Metrics: config.MetricsConfig{
			Enabled: true,
			Path:    "/metrics",
			Port:    9090,
		},
	}

	// 创建测试日志器
	logger, err := telemetry.NewLogger(telemetry.ConvertLoggingConfig(cfg.Logging))
	require.NoError(t, err)

	// 创建测试指标收集器
	metrics, err := telemetry.NewMetrics(cfg.Metrics)
	require.NoError(t, err)

	// 创建网关实例
	gateway := &Gateway{
		config:    cfg,
		logger:    logger,
		metrics:   metrics,
		startTime: time.Now(),
		version:   "1.0.0-test",
	}

	// 设置路由
	gateway.router = gin.New()
	gateway.router.POST("/admin/config/reload", gateway.reloadConfig)

	// 测试无效配置文件路径
	t.Run("无效配置文件", func(t *testing.T) {
		req, err := http.NewRequest("POST", "/admin/config/reload?config_file=invalid.yaml", nil)
		require.NoError(t, err)

		w := httptest.NewRecorder()
		gateway.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "配置加载失败", response["error"])
	})
}

// BenchmarkHealthCheck 健康检查性能基准测试
func BenchmarkHealthCheck(b *testing.B) {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建简单配置
	cfg := &config.Config{
		Logging: config.LoggingConfig{
			Level:  "error", // 减少日志输出
			Format: "json",
			Output: "stdout",
		},
		Metrics: config.MetricsConfig{
			Enabled: false, // 禁用指标以提高性能
		},
	}

	// 创建日志器
	logger, _ := telemetry.NewLogger(telemetry.ConvertLoggingConfig(cfg.Logging))
	metrics, _ := telemetry.NewMetrics(cfg.Metrics)

	// 创建网关实例
	gateway := &Gateway{
		config:    cfg,
		logger:    logger,
		metrics:   metrics,
		startTime: time.Now(),
		version:   "1.0.0-bench",
	}

	// 设置路由
	gateway.router = gin.New()
	gateway.router.GET("/health", gateway.healthCheck)

	// 创建测试请求
	req, _ := http.NewRequest("GET", "/health", nil)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			w := httptest.NewRecorder()
			gateway.router.ServeHTTP(w, req)
		}
	})
}
