package handlers

import (
	"fmt"
	"net"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
)

// ValidationRules contains custom validation rules
type ValidationRules struct {
	validator *validator.Validate
}

// NewValidationRules creates a new validation rules instance
func NewValidationRules() *ValidationRules {
	v := validator.New()
	rules := &ValidationRules{validator: v}
	
	// Register custom validation rules
	rules.registerCustomRules()
	
	return rules
}

// registerCustomRules registers custom validation rules
func (vr *ValidationRules) registerCustomRules() {
	// IP address validation
	vr.validator.RegisterValidation("ip", vr.validateIP)
	
	// CIDR validation
	vr.validator.RegisterValidation("cidr", vr.validateCIDR)
	
	// Port validation
	vr.validator.RegisterValidation("port", vr.validatePort)
	
	// HTTP method validation
	vr.validator.RegisterValidation("httpmethod", vr.validateHTTPMethod)
	
	// Path validation
	vr.validator.RegisterValidation("path", vr.validatePath)
	
	// Duration validation
	vr.validator.RegisterValidation("duration", vr.validateDuration)
	
	// JWT algorithm validation
	vr.validator.RegisterValidation("jwtalgo", vr.validateJWTAlgorithm)
	
	// Load balancer algorithm validation
	vr.validator.RegisterValidation("lbalgo", vr.validateLBAlgorithm)
	
	// Rate limit algorithm validation
	vr.validator.RegisterValidation("ratelimitalgo", vr.validateRateLimitAlgorithm)
	
	// Service discovery type validation
	vr.validator.RegisterValidation("discoverytype", vr.validateDiscoveryType)
	
	// Log level validation
	vr.validator.RegisterValidation("loglevel", vr.validateLogLevel)
	
	// Log format validation
	vr.validator.RegisterValidation("logformat", vr.validateLogFormat)
}

// validateIP validates IP address
func (vr *ValidationRules) validateIP(fl validator.FieldLevel) bool {
	ip := fl.Field().String()
	return net.ParseIP(ip) != nil
}

// validateCIDR validates CIDR notation
func (vr *ValidationRules) validateCIDR(fl validator.FieldLevel) bool {
	cidr := fl.Field().String()
	_, _, err := net.ParseCIDR(cidr)
	return err == nil
}

// validatePort validates port number
func (vr *ValidationRules) validatePort(fl validator.FieldLevel) bool {
	port := fl.Field().String()
	if port == "" {
		return false
	}
	
	portNum, err := strconv.Atoi(port)
	if err != nil {
		return false
	}
	
	return portNum > 0 && portNum <= 65535
}

// validateHTTPMethod validates HTTP method
func (vr *ValidationRules) validateHTTPMethod(fl validator.FieldLevel) bool {
	method := strings.ToUpper(fl.Field().String())
	validMethods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "*"}
	
	for _, validMethod := range validMethods {
		if method == validMethod {
			return true
		}
	}
	
	return false
}

// validatePath validates URL path
func (vr *ValidationRules) validatePath(fl validator.FieldLevel) bool {
	path := fl.Field().String()
	if path == "" {
		return false
	}
	
	// Must start with /
	if !strings.HasPrefix(path, "/") {
		return false
	}
	
	// Basic path validation
	_, err := url.Parse(path)
	return err == nil
}

// validateDuration validates duration string
func (vr *ValidationRules) validateDuration(fl validator.FieldLevel) bool {
	duration := fl.Field().String()
	if duration == "" {
		return false
	}
	
	_, err := time.ParseDuration(duration)
	return err == nil
}

// validateJWTAlgorithm validates JWT algorithm
func (vr *ValidationRules) validateJWTAlgorithm(fl validator.FieldLevel) bool {
	algo := fl.Field().String()
	validAlgos := []string{"HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "ES256", "ES384", "ES512"}
	
	for _, validAlgo := range validAlgos {
		if algo == validAlgo {
			return true
		}
	}
	
	return false
}

// validateLBAlgorithm validates load balancer algorithm
func (vr *ValidationRules) validateLBAlgorithm(fl validator.FieldLevel) bool {
	algo := fl.Field().String()
	validAlgos := []string{"round_robin", "weighted_round_robin", "random", "ip_hash", "least_connections"}
	
	for _, validAlgo := range validAlgos {
		if algo == validAlgo {
			return true
		}
	}
	
	return false
}

// validateRateLimitAlgorithm validates rate limit algorithm
func (vr *ValidationRules) validateRateLimitAlgorithm(fl validator.FieldLevel) bool {
	algo := fl.Field().String()
	validAlgos := []string{"token_bucket", "leaky_bucket", "fixed_window", "sliding_window"}
	
	for _, validAlgo := range validAlgos {
		if algo == validAlgo {
			return true
		}
	}
	
	return false
}

// validateDiscoveryType validates service discovery type
func (vr *ValidationRules) validateDiscoveryType(fl validator.FieldLevel) bool {
	discoveryType := fl.Field().String()
	validTypes := []string{"consul", "nacos", "eureka", "static"}
	
	for _, validType := range validTypes {
		if discoveryType == validType {
			return true
		}
	}
	
	return false
}

// validateLogLevel validates log level
func (vr *ValidationRules) validateLogLevel(fl validator.FieldLevel) bool {
	level := strings.ToLower(fl.Field().String())
	validLevels := []string{"debug", "info", "warn", "error", "fatal", "panic"}
	
	for _, validLevel := range validLevels {
		if level == validLevel {
			return true
		}
	}
	
	return false
}

// validateLogFormat validates log format
func (vr *ValidationRules) validateLogFormat(fl validator.FieldLevel) bool {
	format := strings.ToLower(fl.Field().String())
	validFormats := []string{"json", "text", "console"}
	
	for _, validFormat := range validFormats {
		if format == validFormat {
			return true
		}
	}
	
	return false
}

// Common validation structs

// PaginationRequest represents pagination parameters
type PaginationRequest struct {
	Page     int `form:"page" json:"page" validate:"min=1"`
	PageSize int `form:"page_size" json:"page_size" validate:"min=1,max=100"`
}

// GetPaginationDefaults returns default pagination values
func (p *PaginationRequest) GetPaginationDefaults() (int, int) {
	page := p.Page
	if page < 1 {
		page = 1
	}
	
	pageSize := p.PageSize
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}
	
	return page, pageSize
}

// CalculateOffset calculates the offset for database queries
func (p *PaginationRequest) CalculateOffset() int {
	page, pageSize := p.GetPaginationDefaults()
	return (page - 1) * pageSize
}

// IDRequest represents a request with an ID parameter
type IDRequest struct {
	ID string `uri:"id" json:"id" validate:"required,uuid"`
}

// NameRequest represents a request with a name parameter
type NameRequest struct {
	Name string `uri:"name" json:"name" validate:"required,min=1,max=100"`
}

// TimeRangeRequest represents a time range request
type TimeRangeRequest struct {
	StartTime *time.Time `form:"start_time" json:"start_time"`
	EndTime   *time.Time `form:"end_time" json:"end_time"`
}

// Validate validates the time range
func (tr *TimeRangeRequest) Validate() error {
	if tr.StartTime != nil && tr.EndTime != nil {
		if tr.StartTime.After(*tr.EndTime) {
			return fmt.Errorf("start_time must be before end_time")
		}
	}
	return nil
}

// SearchRequest represents a search request
type SearchRequest struct {
	Query    string `form:"q" json:"query" validate:"max=200"`
	SortBy   string `form:"sort_by" json:"sort_by" validate:"max=50"`
	SortDesc bool   `form:"sort_desc" json:"sort_desc"`
}

// FilterRequest represents a filter request
type FilterRequest struct {
	Status   string   `form:"status" json:"status"`
	Category string   `form:"category" json:"category"`
	Tags     []string `form:"tags" json:"tags"`
}

// BulkRequest represents a bulk operation request
type BulkRequest struct {
	IDs    []string `json:"ids" validate:"required,min=1,max=100,dive,uuid"`
	Action string   `json:"action" validate:"required,oneof=delete enable disable"`
}

// ConfigUpdateRequest represents a configuration update request
type ConfigUpdateRequest struct {
	Key   string      `json:"key" validate:"required,min=1,max=100"`
	Value interface{} `json:"value" validate:"required"`
}

// HealthCheckRequest represents a health check request
type HealthCheckRequest struct {
	Component string `form:"component" json:"component"`
	Deep      bool   `form:"deep" json:"deep"`
}
