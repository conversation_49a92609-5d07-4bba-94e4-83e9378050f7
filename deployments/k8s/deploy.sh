#!/bin/bash

# API网关Kubernetes部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev, staging, prod
# 操作: deploy, update, delete, status

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"

# 默认值
ENVIRONMENT="${1:-dev}"
ACTION="${2:-deploy}"
NAMESPACE="api-gateway"
IMAGE_TAG="${IMAGE_TAG:-latest}"
KUBECTL_TIMEOUT="${KUBECTL_TIMEOUT:-300s}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    local deps=("kubectl" "kustomize" "docker")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "缺少依赖工具: $dep"
            exit 1
        fi
    done
    
    log_success "依赖检查完成"
}

# 检查Kubernetes连接
check_k8s_connection() {
    log_info "检查Kubernetes连接..."
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    local context=$(kubectl config current-context)
    log_info "当前Kubernetes上下文: $context"
    
    # 确认生产环境部署
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        log_warning "即将部署到生产环境: $context"
        read -p "确认继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."
    
    cd "$PROJECT_ROOT"
    
    # 构建镜像
    docker build -t "api-gateway:${IMAGE_TAG}" .
    
    # 如果不是latest标签，也打上latest标签
    if [[ "$IMAGE_TAG" != "latest" ]]; then
        docker tag "api-gateway:${IMAGE_TAG}" "api-gateway:latest"
    fi
    
    log_success "镜像构建完成: api-gateway:${IMAGE_TAG}"
}

# 创建命名空间
create_namespace() {
    log_info "创建命名空间..."
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_info "命名空间 $NAMESPACE 已存在"
    else
        kubectl apply -f "${SCRIPT_DIR}/namespace.yaml"
        log_success "命名空间 $NAMESPACE 创建完成"
    fi
}

# 生成证书
generate_certificates() {
    log_info "检查TLS证书..."
    
    local cert_dir="${PROJECT_ROOT}/certs"
    mkdir -p "$cert_dir"
    
    # 检查是否已有证书
    if [[ -f "${cert_dir}/server.crt" && -f "${cert_dir}/server.key" && -f "${cert_dir}/ca.crt" ]]; then
        log_info "证书已存在，跳过生成"
        return
    fi
    
    log_info "生成测试证书..."
    
    # 生成CA私钥
    openssl genrsa -out "${cert_dir}/ca.key" 4096
    
    # 生成CA证书
    openssl req -new -x509 -key "${cert_dir}/ca.key" -sha256 \
        -subj "/C=US/ST=CA/O=Example Corp/CN=Example CA" \
        -days 3650 -out "${cert_dir}/ca.crt"
    
    # 生成服务器私钥
    openssl genrsa -out "${cert_dir}/server.key" 4096
    
    # 生成服务器证书签名请求
    openssl req -new -key "${cert_dir}/server.key" \
        -subj "/C=US/ST=CA/O=Example Corp/CN=api-gateway.example.com" \
        -out "${cert_dir}/server.csr"
    
    # 签发服务器证书
    openssl x509 -req -in "${cert_dir}/server.csr" \
        -CA "${cert_dir}/ca.crt" -CAkey "${cert_dir}/ca.key" \
        -CAcreateserial -out "${cert_dir}/server.crt" \
        -days 365 -sha256
    
    # 生成客户端私钥和证书（用于测试）
    openssl genrsa -out "${cert_dir}/client.key" 4096
    openssl req -new -key "${cert_dir}/client.key" \
        -subj "/C=US/ST=CA/O=Example Corp/OU=Engineering/CN=client.example.com" \
        -out "${cert_dir}/client.csr"
    openssl x509 -req -in "${cert_dir}/client.csr" \
        -CA "${cert_dir}/ca.crt" -CAkey "${cert_dir}/ca.key" \
        -CAcreateserial -out "${cert_dir}/client.crt" \
        -days 365 -sha256
    
    log_success "证书生成完成"
}

# 创建Secrets
create_secrets() {
    log_info "创建Secrets..."
    
    local cert_dir="${PROJECT_ROOT}/certs"
    
    # 创建TLS Secret
    kubectl create secret tls api-gateway-tls \
        --cert="${cert_dir}/server.crt" \
        --key="${cert_dir}/server.key" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建CA Secret
    kubectl create secret generic api-gateway-ca \
        --from-file=ca.crt="${cert_dir}/ca.crt" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建认证Secret
    kubectl create secret generic api-gateway-auth \
        --from-literal=jwt-secret="$(openssl rand -base64 32)" \
        --from-literal=oidc-client-id="example-client-id" \
        --from-literal=oidc-client-secret="example-client-secret" \
        --from-literal=oidc-issuer-url="https://auth.example.com" \
        --from-literal=oidc-redirect-url="https://api-gateway.example.com/auth/callback" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "Secrets创建完成"
}

# 部署应用
deploy_app() {
    log_info "部署API网关..."
    
    cd "$SCRIPT_DIR"
    
    # 使用kustomize构建并应用
    kustomize build . | kubectl apply -f -
    
    # 等待部署完成
    log_info "等待部署完成..."
    kubectl rollout status deployment/api-gateway \
        --namespace="$NAMESPACE" \
        --timeout="$KUBECTL_TIMEOUT"
    
    log_success "API网关部署完成"
}

# 更新应用
update_app() {
    log_info "更新API网关..."
    
    # 更新镜像标签
    kubectl set image deployment/api-gateway \
        api-gateway="api-gateway:${IMAGE_TAG}" \
        --namespace="$NAMESPACE"
    
    # 等待更新完成
    kubectl rollout status deployment/api-gateway \
        --namespace="$NAMESPACE" \
        --timeout="$KUBECTL_TIMEOUT"
    
    log_success "API网关更新完成"
}

# 删除应用
delete_app() {
    log_warning "即将删除API网关部署"
    read -p "确认删除? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "删除已取消"
        exit 0
    fi
    
    log_info "删除API网关..."
    
    cd "$SCRIPT_DIR"
    kustomize build . | kubectl delete -f - || true
    
    log_success "API网关删除完成"
}

# 查看状态
show_status() {
    log_info "API网关状态:"
    
    echo
    echo "=== Pods ==="
    kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=api-gateway
    
    echo
    echo "=== Services ==="
    kubectl get services -n "$NAMESPACE" -l app.kubernetes.io/name=api-gateway
    
    echo
    echo "=== Ingress ==="
    kubectl get ingress -n "$NAMESPACE" -l app.kubernetes.io/name=api-gateway
    
    echo
    echo "=== HPA ==="
    kubectl get hpa -n "$NAMESPACE" -l app.kubernetes.io/name=api-gateway
    
    echo
    echo "=== Events ==="
    kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' | tail -10
}

# 主函数
main() {
    log_info "API网关Kubernetes部署脚本"
    log_info "环境: $ENVIRONMENT"
    log_info "操作: $ACTION"
    log_info "镜像标签: $IMAGE_TAG"
    
    check_dependencies
    check_k8s_connection
    
    case "$ACTION" in
        "deploy")
            build_image
            create_namespace
            generate_certificates
            create_secrets
            deploy_app
            show_status
            ;;
        "update")
            build_image
            update_app
            show_status
            ;;
        "delete")
            delete_app
            ;;
        "status")
            show_status
            ;;
        *)
            log_error "未知操作: $ACTION"
            echo "支持的操作: deploy, update, delete, status"
            exit 1
            ;;
    esac
    
    log_success "操作完成"
}

# 执行主函数
main "$@"
