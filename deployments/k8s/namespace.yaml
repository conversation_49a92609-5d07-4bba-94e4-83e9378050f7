apiVersion: v1
kind: Namespace
metadata:
  name: api-gateway
  labels:
    name: api-gateway
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: infrastructure
    app.kubernetes.io/part-of: api-gateway-system
  annotations:
    description: "API网关系统命名空间"
    contact: "<EMAIL>"
---
# 网络策略 - 限制命名空间间的网络访问
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-gateway-network-policy
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: security
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # 允许来自ingress控制器的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 8443
  # 允许来自监控系统的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  # 允许同命名空间内的流量
  - from:
    - podSelector: {}
  egress:
  # 允许访问Kubernetes API
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # 允许访问后端服务
  - to:
    - namespaceSelector:
        matchLabels:
          name: backend
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
  # 允许访问外部服务（如OIDC提供者）
  - to: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
