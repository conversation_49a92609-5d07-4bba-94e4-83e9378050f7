apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: gateway
    app.kubernetes.io/part-of: api-gateway-system
  annotations:
    deployment.kubernetes.io/revision: "1"
    description: "API网关主要部署"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: api-gateway
      app.kubernetes.io/component: gateway
  template:
    metadata:
      labels:
        app.kubernetes.io/name: api-gateway
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/component: gateway
        app.kubernetes.io/part-of: api-gateway-system
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
        config.checksum: "{{ .Values.configChecksum }}"
    spec:
      serviceAccountName: api-gateway
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: api-gateway
        image: api-gateway:1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: https
          containerPort: 8443
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        # 基本配置
        - name: CONFIG_FILE
          value: "/etc/config/gateway.yaml"
        - name: LOG_LEVEL
          value: "info"
        - name: LOG_FORMAT
          value: "json"
        
        # JWT配置
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: api-gateway-auth
              key: jwt-secret
        
        # OIDC配置
        - name: OIDC_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: api-gateway-auth
              key: oidc-client-id
        - name: OIDC_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: api-gateway-auth
              key: oidc-client-secret
        - name: OIDC_ISSUER_URL
          valueFrom:
            secretKeyRef:
              name: api-gateway-auth
              key: oidc-issuer-url
        - name: OIDC_REDIRECT_URL
          valueFrom:
            secretKeyRef:
              name: api-gateway-auth
              key: oidc-redirect-url
        
        # 数据库配置（可选）
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: api-gateway-database
              key: database-url
              optional: true
        
        # Redis配置（可选）
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: api-gateway-redis
              key: redis-url
              optional: true
        
        # Kubernetes相关
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        
        volumeMounts:
        # 配置文件
        - name: config
          mountPath: /etc/config
          readOnly: true
        
        # TLS证书
        - name: tls-certs
          mountPath: /etc/tls
          readOnly: true
        
        # CA证书
        - name: ca-certs
          mountPath: /etc/ca
          readOnly: true
        
        # API密钥
        - name: api-keys
          mountPath: /etc/api-keys
          readOnly: true
        
        # 日志目录
        - name: logs
          mountPath: /var/log
        
        # 临时目录
        - name: tmp
          mountPath: /tmp
        
        # 健康检查
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: http
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        
        # 启动探针
        startupProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 30
        
        # 资源限制
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        
        # 安全上下文
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          capabilities:
            drop:
            - ALL
      
      # 初始化容器（可选）
      initContainers:
      - name: wait-for-dependencies
        image: busybox:1.35
        command: ['sh', '-c']
        args:
        - |
          echo "等待依赖服务启动..."
          # 等待Redis（如果使用）
          # until nc -z redis.api-gateway.svc.cluster.local 6379; do
          #   echo "等待Redis..."
          #   sleep 2
          # done
          
          # 等待数据库（如果使用）
          # until nc -z postgres.api-gateway.svc.cluster.local 5432; do
          #   echo "等待PostgreSQL..."
          #   sleep 2
          # done
          
          echo "依赖服务已就绪"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      
      volumes:
      # 配置文件
      - name: config
        configMap:
          name: api-gateway-config
          defaultMode: 0644
      
      # TLS证书
      - name: tls-certs
        secret:
          secretName: api-gateway-tls
          defaultMode: 0600
      
      # CA证书
      - name: ca-certs
        secret:
          secretName: api-gateway-ca
          defaultMode: 0644
      
      # API密钥
      - name: api-keys
        secret:
          secretName: api-gateway-api-keys
          defaultMode: 0600
      
      # 日志目录（emptyDir）
      - name: logs
        emptyDir: {}
      
      # 临时目录
      - name: tmp
        emptyDir: {}
      
      # 节点选择器
      nodeSelector:
        kubernetes.io/os: linux
      
      # 容忍度
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      
      # 亲和性
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - api-gateway
              topologyKey: kubernetes.io/hostname
      
      # DNS配置
      dnsPolicy: ClusterFirst
      dnsConfig:
        options:
        - name: ndots
          value: "2"
        - name: edns0
      
      # 终止宽限期
      terminationGracePeriodSeconds: 30
      
      # 重启策略
      restartPolicy: Always
