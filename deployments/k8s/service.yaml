apiVersion: v1
kind: Service
metadata:
  name: api-gateway-service
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: service
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  - name: https
    port: 443
    targetPort: https
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: gateway

---
# LoadBalancer Service (用于外部访问)
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-loadbalancer
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: loadbalancer
  annotations:
    # AWS Load Balancer配置
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:region:account:certificate/cert-id"
    
    # GCP Load Balancer配置
    cloud.google.com/load-balancer-type: "External"
    
    # Azure Load Balancer配置
    service.beta.kubernetes.io/azure-load-balancer-internal: "false"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  - name: https
    port: 443
    targetPort: https
    protocol: TCP
  selector:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: gateway
  # 保持会话亲和性
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800

---
# Headless Service (用于服务发现)
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-headless
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: headless
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 8080
    targetPort: http
    protocol: TCP
  - name: https
    port: 8443
    targetPort: https
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: gateway

---
# 监控专用Service
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-metrics
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: metrics
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
    prometheus.io/scheme: "http"
spec:
  type: ClusterIP
  ports:
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: gateway

---
# Ingress配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-gateway-ingress
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: ingress
  annotations:
    # Nginx Ingress配置
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "5"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    
    # 客户端证书配置
    nginx.ingress.kubernetes.io/auth-tls-verify-client: "on"
    nginx.ingress.kubernetes.io/auth-tls-secret: "api-gateway/api-gateway-ca"
    nginx.ingress.kubernetes.io/auth-tls-verify-depth: "2"
    nginx.ingress.kubernetes.io/auth-tls-pass-certificate-to-upstream: "true"
    
    # 限流配置
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # CORS配置
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://app.example.com,https://admin.example.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET,POST,PUT,DELETE,OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Content-Type,Authorization,X-API-Key,X-Client-Cert-CN"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    
    # 自定义头部
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";
      more_set_headers "Content-Security-Policy: default-src 'self'";
    
    # 证书管理器配置
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    cert-manager.io/acme-challenge-type: "http01"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - api-gateway.example.com
    - api.example.com
    secretName: api-gateway-tls-ingress
  rules:
  - host: api-gateway.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway-service
            port:
              number: 80
  - host: api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway-service
            port:
              number: 80

---
# 管理界面Ingress（可选）
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-gateway-admin-ingress
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: admin-ingress
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    
    # 管理界面访问控制
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    
    # 基本认证（可选）
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: api-gateway-admin-auth
    nginx.ingress.kubernetes.io/auth-realm: "API Gateway Admin"
    
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - admin.api-gateway.example.com
    secretName: api-gateway-admin-tls
  rules:
  - host: admin.api-gateway.example.com
    http:
      paths:
      - path: /admin
        pathType: Prefix
        backend:
          service:
            name: api-gateway-service
            port:
              number: 80
      - path: /metrics
        pathType: Prefix
        backend:
          service:
            name: api-gateway-metrics
            port:
              number: 9090
