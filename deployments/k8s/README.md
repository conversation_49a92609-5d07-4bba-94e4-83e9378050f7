# API网关 Kubernetes 部署指南

本目录包含了API网关在Kubernetes环境中的完整部署配置和脚本。

## 📁 文件结构

```
deployments/k8s/
├── README.md                 # 本文档
├── deploy.sh                 # 部署脚本
├── kustomization.yaml        # Kustomize配置
├── namespace.yaml            # 命名空间和网络策略
├── rbac.yaml                 # RBAC权限配置
├── secrets.yaml              # Secret配置模板
├── configmap.yaml            # ConfigMap配置
├── deployment.yaml           # Deployment配置
├── service.yaml              # Service和Ingress配置
└── autoscaling.yaml          # HPA、PDB和监控配置
```

## 🚀 快速开始

### 前置要求

- Kubernetes 1.20+
- kubectl 已配置并连接到目标集群
- Docker (用于构建镜像)
- kustomize (可选，kubectl内置)
- openssl (用于生成测试证书)

### 一键部署

```bash
# 部署到开发环境
./deploy.sh dev deploy

# 部署到生产环境
./deploy.sh prod deploy

# 更新部署
./deploy.sh prod update

# 查看状态
./deploy.sh prod status

# 删除部署
./deploy.sh prod delete
```

### 手动部署

1. **创建命名空间**
   ```bash
   kubectl apply -f namespace.yaml
   ```

2. **创建RBAC权限**
   ```bash
   kubectl apply -f rbac.yaml
   ```

3. **生成并创建证书**
   ```bash
   # 生成测试证书
   mkdir -p ../../certs
   cd ../../certs
   
   # 生成CA证书
   openssl genrsa -out ca.key 4096
   openssl req -new -x509 -key ca.key -sha256 \
     -subj "/C=US/ST=CA/O=Example Corp/CN=Example CA" \
     -days 3650 -out ca.crt
   
   # 生成服务器证书
   openssl genrsa -out server.key 4096
   openssl req -new -key server.key \
     -subj "/C=US/ST=CA/O=Example Corp/CN=api-gateway.example.com" \
     -out server.csr
   openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key \
     -CAcreateserial -out server.crt -days 365 -sha256
   
   cd ../deployments/k8s
   
   # 创建TLS Secret
   kubectl create secret tls api-gateway-tls \
     --cert=../../certs/server.crt \
     --key=../../certs/server.key \
     --namespace=api-gateway
   
   # 创建CA Secret
   kubectl create secret generic api-gateway-ca \
     --from-file=ca.crt=../../certs/ca.crt \
     --namespace=api-gateway
   ```

4. **创建认证Secret**
   ```bash
   kubectl create secret generic api-gateway-auth \
     --from-literal=jwt-secret="$(openssl rand -base64 32)" \
     --from-literal=oidc-client-id="your-oidc-client-id" \
     --from-literal=oidc-client-secret="your-oidc-client-secret" \
     --from-literal=oidc-issuer-url="https://your-oidc-provider.com" \
     --from-literal=oidc-redirect-url="https://your-gateway.com/auth/callback" \
     --namespace=api-gateway
   ```

5. **部署应用**
   ```bash
   # 使用kustomize部署
   kustomize build . | kubectl apply -f -
   
   # 或者直接应用所有文件
   kubectl apply -f configmap.yaml
   kubectl apply -f deployment.yaml
   kubectl apply -f service.yaml
   kubectl apply -f autoscaling.yaml
   ```

6. **验证部署**
   ```bash
   # 检查Pod状态
   kubectl get pods -n api-gateway
   
   # 检查服务状态
   kubectl get svc -n api-gateway
   
   # 查看日志
   kubectl logs -f deployment/api-gateway -n api-gateway
   ```

## ⚙️ 配置说明

### 环境变量

主要的环境变量配置在 `deployment.yaml` 中：

- `JWT_SECRET`: JWT签名密钥
- `OIDC_CLIENT_ID`: OIDC客户端ID
- `OIDC_CLIENT_SECRET`: OIDC客户端密钥
- `OIDC_ISSUER_URL`: OIDC提供者URL
- `DATABASE_URL`: 数据库连接字符串（可选）
- `REDIS_URL`: Redis连接字符串（可选）

### 资源配置

默认资源配置：
- **CPU请求**: 100m
- **CPU限制**: 500m
- **内存请求**: 256Mi
- **内存限制**: 512Mi
- **副本数**: 3

### 自动扩缩容

HPA配置：
- **最小副本数**: 3
- **最大副本数**: 20
- **CPU目标**: 70%
- **内存目标**: 80%

### 网络配置

- **HTTP端口**: 8080
- **HTTPS端口**: 8443
- **指标端口**: 9090
- **服务类型**: ClusterIP (可通过Ingress访问)

## 🔒 安全配置

### mTLS配置

API网关支持mTLS双向认证：

1. **客户端证书验证**: 通过CA证书验证客户端证书
2. **证书撤销检查**: 支持CRL和OCSP检查
3. **证书属性验证**: 验证CN和组织字段

### RBAC权限

最小权限原则：
- **服务发现**: 读取Services和Endpoints
- **配置管理**: 读取ConfigMaps和Secrets
- **监控**: 访问指标端点
- **事件记录**: 创建和更新事件

### 网络策略

限制网络访问：
- **入站**: 仅允许来自Ingress和监控系统的流量
- **出站**: 仅允许访问后端服务和外部认证服务
- **DNS**: 允许DNS查询

## 📊 监控和告警

### Prometheus指标

自动暴露的指标：
- HTTP请求数量和延迟
- 错误率统计
- 连接数统计
- 资源使用情况

### 告警规则

预配置的告警：
- 高错误率告警 (>5%)
- 高延迟告警 (P99 > 1s)
- Pod不可用告警
- 资源使用率过高告警
- 证书即将过期告警

### 健康检查

多层健康检查：
- **存活探针**: `/health` 端点
- **就绪探针**: `/ready` 端点
- **启动探针**: 启动时健康检查

## 🔧 故障排除

### 常见问题

1. **Pod启动失败**
   ```bash
   # 查看Pod事件
   kubectl describe pod -l app.kubernetes.io/name=api-gateway -n api-gateway
   
   # 查看日志
   kubectl logs -l app.kubernetes.io/name=api-gateway -n api-gateway
   ```

2. **证书问题**
   ```bash
   # 检查证书Secret
   kubectl get secret api-gateway-tls -n api-gateway -o yaml
   
   # 验证证书
   kubectl get secret api-gateway-tls -n api-gateway -o jsonpath='{.data.tls\.crt}' | base64 -d | openssl x509 -text -noout
   ```

3. **服务无法访问**
   ```bash
   # 检查Service
   kubectl get svc -n api-gateway
   
   # 检查Endpoints
   kubectl get endpoints -n api-gateway
   
   # 检查Ingress
   kubectl get ingress -n api-gateway
   ```

4. **性能问题**
   ```bash
   # 查看资源使用情况
   kubectl top pods -n api-gateway
   
   # 查看HPA状态
   kubectl get hpa -n api-gateway
   
   # 查看指标
   kubectl port-forward svc/api-gateway-metrics 9090:9090 -n api-gateway
   # 然后访问 http://localhost:9090/metrics
   ```

### 日志收集

```bash
# 收集所有相关日志
kubectl logs -l app.kubernetes.io/name=api-gateway -n api-gateway --previous > api-gateway-logs.txt

# 实时查看日志
kubectl logs -f deployment/api-gateway -n api-gateway
```

## 🔄 升级和回滚

### 滚动升级

```bash
# 更新镜像
kubectl set image deployment/api-gateway api-gateway=api-gateway:v1.1.0 -n api-gateway

# 查看升级状态
kubectl rollout status deployment/api-gateway -n api-gateway
```

### 回滚

```bash
# 查看历史版本
kubectl rollout history deployment/api-gateway -n api-gateway

# 回滚到上一版本
kubectl rollout undo deployment/api-gateway -n api-gateway

# 回滚到指定版本
kubectl rollout undo deployment/api-gateway --to-revision=2 -n api-gateway
```

## 📝 自定义配置

### 修改配置

1. 编辑 `configmap.yaml` 中的配置
2. 应用更改：
   ```bash
   kubectl apply -f configmap.yaml
   ```
3. 重启Pod以加载新配置：
   ```bash
   kubectl rollout restart deployment/api-gateway -n api-gateway
   ```

### 环境特定配置

使用Kustomize overlay进行环境特定配置：

```bash
# 创建环境特定目录
mkdir -p overlays/prod

# 创建prod环境的kustomization.yaml
cat > overlays/prod/kustomization.yaml << EOF
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../base

patchesStrategicMerge:
- deployment-patch.yaml

images:
- name: api-gateway
  newTag: v1.0.0
EOF

# 部署prod环境
kustomize build overlays/prod | kubectl apply -f -
```

## 📞 支持

如有问题，请联系：
- **技术支持**: <EMAIL>
- **文档**: https://docs.example.com/api-gateway
- **监控**: https://monitoring.example.com/api-gateway
