# 注意：这些是示例Secret，在生产环境中应该使用真实的证书和密钥
# 并且应该通过安全的方式（如Sealed Secrets、External Secrets等）管理

apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-tls
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: tls
type: kubernetes.io/tls
data:
  # 这里应该是base64编码的TLS证书和私钥
  # 使用以下命令生成：
  # kubectl create secret tls api-gateway-tls --cert=server.crt --key=server.key --dry-run=client -o yaml
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...  # base64编码的服务器证书
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...  # base64编码的服务器私钥

---
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-ca
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: ca
type: Opaque
data:
  # CA证书用于验证客户端证书
  ca.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...  # base64编码的CA证书
  ca.crl: LS0tLS1CRUdJTiBYNTA5IENSTC0tLS0t...      # base64编码的CRL文件（可选）

---
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-auth
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: auth
type: Opaque
data:
  # JWT密钥
  jwt-secret: <base64-encoded-jwt-secret>
  
  # OIDC配置
  oidc-client-id: <base64-encoded-oidc-client-id>
  oidc-client-secret: <base64-encoded-oidc-client-secret>
  oidc-issuer-url: <base64-encoded-oidc-issuer-url>
  oidc-redirect-url: <base64-encoded-oidc-redirect-url>

---
# 用于存储API密钥的Secret
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-api-keys
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: auth
type: Opaque
data:
  # API密钥数据，格式为JSON
  api-keys.json: ewogICJhcGlfa2V5cyI6IFsKICAgIHsKICAgICAgImlkIjogImFrXzEyMzQ1Njc4OTAiLAogICAgICAia2V5IjogImFrXzEyMzQ1Njc4OTBhYmNkZWYiLAogICAgICAibmFtZSI6ICJUZXN0IEFQSSBLZXkiLAogICAgICAidXNlcl9pZCI6ICJ0ZXN0LXVzZXIiLAogICAgICAicm9sZXMiOiBbInVzZXIiXSwKICAgICAgImFjdGl2ZSI6IHRydWUKICAgIH0KICBdCn0=

---
# 用于监控的Secret
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-monitoring
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: monitoring
type: Opaque
data:
  # Prometheus认证（如果需要）
  prometheus-username: <base64-encoded-username>
  prometheus-password: <base64-encoded-password>
  
  # Jaeger认证（如果需要）
  jaeger-username: <base64-encoded-username>
  jaeger-password: <base64-encoded-password>

---
# 数据库连接Secret（如果使用数据库存储）
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-database
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: database
type: Opaque
data:
  # 数据库连接字符串
  database-url: <base64-encoded-database-url>
  database-username: <base64-encoded-username>
  database-password: <base64-encoded-password>

---
# Redis连接Secret（如果使用Redis）
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-redis
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: cache
type: Opaque
data:
  # Redis连接信息
  redis-url: <base64-encoded-redis-url>
  redis-password: <base64-encoded-redis-password>

---
# 生成Secret的示例脚本
# 
# 1. 生成TLS Secret:
# kubectl create secret tls api-gateway-tls \
#   --cert=certs/server.crt \
#   --key=certs/server.key \
#   --namespace=api-gateway
#
# 2. 生成CA Secret:
# kubectl create secret generic api-gateway-ca \
#   --from-file=ca.crt=certs/ca.crt \
#   --from-file=ca.crl=certs/ca.crl \
#   --namespace=api-gateway
#
# 3. 生成认证Secret:
# kubectl create secret generic api-gateway-auth \
#   --from-literal=jwt-secret="your-jwt-secret" \
#   --from-literal=oidc-client-id="your-oidc-client-id" \
#   --from-literal=oidc-client-secret="your-oidc-client-secret" \
#   --from-literal=oidc-issuer-url="https://your-oidc-provider.com" \
#   --from-literal=oidc-redirect-url="https://your-gateway.com/auth/callback" \
#   --namespace=api-gateway
#
# 4. 生成API密钥Secret:
# kubectl create secret generic api-gateway-api-keys \
#   --from-file=api-keys.json=configs/api-keys.json \
#   --namespace=api-gateway
