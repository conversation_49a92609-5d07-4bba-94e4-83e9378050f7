# Horizontal Pod Autoscaler (HPA)
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: autoscaling
  annotations:
    description: "API网关水平自动扩缩容"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 20
  metrics:
  # CPU使用率指标
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  
  # 内存使用率指标
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  
  # 自定义指标 - HTTP请求速率
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  
  # 自定义指标 - HTTP请求延迟
  - type: Pods
    pods:
      metric:
        name: http_request_duration_p99
      target:
        type: AverageValue
        averageValue: "500m"  # 500ms
  
  # 扩缩容行为配置
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 5分钟稳定窗口
      policies:
      - type: Percent
        value: 10  # 每次最多缩容10%
        periodSeconds: 60
      - type: Pods
        value: 2   # 每次最多缩容2个Pod
        periodSeconds: 60
      selectPolicy: Min  # 选择最保守的策略
    scaleUp:
      stabilizationWindowSeconds: 60   # 1分钟稳定窗口
      policies:
      - type: Percent
        value: 50  # 每次最多扩容50%
        periodSeconds: 60
      - type: Pods
        value: 4   # 每次最多扩容4个Pod
        periodSeconds: 60
      selectPolicy: Max  # 选择最激进的策略

---
# Pod Disruption Budget (PDB)
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-gateway-pdb
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: availability
  annotations:
    description: "API网关Pod中断预算，确保高可用性"
spec:
  minAvailable: 2  # 至少保持2个Pod运行
  # 或者使用百分比: minAvailable: 50%
  selector:
    matchLabels:
      app.kubernetes.io/name: api-gateway
      app.kubernetes.io/component: gateway

---
# Vertical Pod Autoscaler (VPA) - 可选
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: api-gateway-vpa
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: vpa
  annotations:
    description: "API网关垂直自动扩缩容（建议模式）"
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  updatePolicy:
    updateMode: "Off"  # 仅提供建议，不自动更新
  resourcePolicy:
    containerPolicies:
    - containerName: api-gateway
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 2
        memory: 2Gi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits

---
# ServiceMonitor for Prometheus (如果使用Prometheus Operator)
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: api-gateway-metrics
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: monitoring
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: api-gateway
      app.kubernetes.io/component: metrics
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scheme: http
    honorLabels: true
    relabelings:
    - sourceLabels: [__meta_kubernetes_pod_name]
      targetLabel: pod
    - sourceLabels: [__meta_kubernetes_pod_node_name]
      targetLabel: node
    - sourceLabels: [__meta_kubernetes_namespace]
      targetLabel: namespace

---
# PrometheusRule for alerting (如果使用Prometheus Operator)
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: api-gateway-alerts
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: alerting
    prometheus: kube-prometheus
spec:
  groups:
  - name: api-gateway.rules
    rules:
    # 高错误率告警
    - alert: APIGatewayHighErrorRate
      expr: |
        (
          rate(http_requests_total{job="api-gateway-metrics",code=~"5.."}[5m]) /
          rate(http_requests_total{job="api-gateway-metrics"}[5m])
        ) > 0.05
      for: 5m
      labels:
        severity: warning
        service: api-gateway
      annotations:
        summary: "API网关错误率过高"
        description: "API网关在过去5分钟内的错误率为 {{ $value | humanizePercentage }}"
    
    # 高延迟告警
    - alert: APIGatewayHighLatency
      expr: |
        histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job="api-gateway-metrics"}[5m])) > 1
      for: 5m
      labels:
        severity: warning
        service: api-gateway
      annotations:
        summary: "API网关延迟过高"
        description: "API网关99%分位延迟为 {{ $value }}s"
    
    # Pod不可用告警
    - alert: APIGatewayPodDown
      expr: |
        up{job="api-gateway-metrics"} == 0
      for: 1m
      labels:
        severity: critical
        service: api-gateway
      annotations:
        summary: "API网关Pod不可用"
        description: "API网关Pod {{ $labels.instance }} 已下线超过1分钟"
    
    # 内存使用率过高告警
    - alert: APIGatewayHighMemoryUsage
      expr: |
        (
          container_memory_working_set_bytes{container="api-gateway"} /
          container_spec_memory_limit_bytes{container="api-gateway"}
        ) > 0.9
      for: 5m
      labels:
        severity: warning
        service: api-gateway
      annotations:
        summary: "API网关内存使用率过高"
        description: "API网关Pod {{ $labels.pod }} 内存使用率为 {{ $value | humanizePercentage }}"
    
    # CPU使用率过高告警
    - alert: APIGatewayHighCPUUsage
      expr: |
        (
          rate(container_cpu_usage_seconds_total{container="api-gateway"}[5m]) /
          container_spec_cpu_quota{container="api-gateway"} * container_spec_cpu_period{container="api-gateway"}
        ) > 0.9
      for: 5m
      labels:
        severity: warning
        service: api-gateway
      annotations:
        summary: "API网关CPU使用率过高"
        description: "API网关Pod {{ $labels.pod }} CPU使用率为 {{ $value | humanizePercentage }}"
    
    # 证书即将过期告警
    - alert: APIGatewayCertificateExpiringSoon
      expr: |
        (cert_expiry_timestamp - time()) / 86400 < 30
      for: 1h
      labels:
        severity: warning
        service: api-gateway
      annotations:
        summary: "API网关证书即将过期"
        description: "API网关证书将在 {{ $value }} 天后过期"
    
    # 连接数过高告警
    - alert: APIGatewayHighConnectionCount
      expr: |
        nginx_connections_active > 1000
      for: 5m
      labels:
        severity: warning
        service: api-gateway
      annotations:
        summary: "API网关连接数过高"
        description: "API网关当前活跃连接数为 {{ $value }}"

---
# 自定义指标配置 (Custom Metrics API)
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-custom-metrics
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: metrics
data:
  custom-metrics.yaml: |
    # 自定义指标配置
    metrics:
      # HTTP请求速率
      - name: http_requests_per_second
        query: rate(http_requests_total{job="api-gateway-metrics"}[1m])
        
      # HTTP请求延迟P99
      - name: http_request_duration_p99
        query: histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job="api-gateway-metrics"}[5m]))
        
      # 活跃连接数
      - name: active_connections
        query: nginx_connections_active{job="api-gateway-metrics"}
        
      # 错误率
      - name: error_rate
        query: rate(http_requests_total{job="api-gateway-metrics",code=~"5.."}[5m]) / rate(http_requests_total{job="api-gateway-metrics"}[5m])
        
      # 吞吐量
      - name: throughput
        query: rate(http_requests_total{job="api-gateway-metrics"}[1m])
