apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: api-gateway
  annotations:
    description: "API网关Kubernetes部署配置"

# 命名空间
namespace: api-gateway

# 通用标签
commonLabels:
  app.kubernetes.io/name: api-gateway
  app.kubernetes.io/part-of: api-gateway-system
  app.kubernetes.io/managed-by: kustomize

# 通用注解
commonAnnotations:
  deployment.kubernetes.io/managed-by: "kustomize"
  contact: "<EMAIL>"

# 资源列表
resources:
- namespace.yaml
- rbac.yaml
- secrets.yaml
- configmap.yaml
- deployment.yaml
- service.yaml
- autoscaling.yaml

# 镜像配置
images:
- name: api-gateway
  newTag: "1.0.0"

# 配置生成器
configMapGenerator:
- name: api-gateway-env-config
  literals:
  - LOG_LEVEL=info
  - LOG_FORMAT=json
  - METRICS_ENABLED=true
  - TRACING_ENABLED=true

# Secret生成器
secretGenerator:
- name: api-gateway-generated-secrets
  literals:
  - jwt-secret=your-jwt-secret-here
  type: Opaque

# 补丁配置
patches:
# 生产环境资源限制补丁
- target:
    kind: Deployment
    name: api-gateway
  patch: |-
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: "512Mi"
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: "200m"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: "1Gi"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/cpu
      value: "1000m"

# 副本数补丁
- target:
    kind: Deployment
    name: api-gateway
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 3

# 变量替换
replacements:
- source:
    kind: ConfigMap
    name: api-gateway-config
    fieldPath: metadata.name
  targets:
  - select:
      kind: Deployment
      name: api-gateway
    fieldPaths:
    - spec.template.spec.volumes.[name=config].configMap.name

# 生成器选项
generatorOptions:
  disableNameSuffixHash: false
  labels:
    app.kubernetes.io/component: generated

# 构建元数据
buildMetadata:
- originAnnotations
- transformerAnnotations
- managedByLabel
