apiVersion: v1
kind: ServiceAccount
metadata:
  name: api-gateway
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: serviceaccount
  annotations:
    description: "API网关服务账户"

---
# ClusterRole for API Gateway
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: rbac
rules:
# 服务发现权限 - 读取Services和Endpoints
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch"]

# 读取Pods信息（用于健康检查）
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

# 读取ConfigMaps（用于动态配置）
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]

# 读取Secrets（用于证书和密钥管理）
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]

# 读取Ingress资源（用于路由发现）
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]

# 读取自定义资源（如果使用CRD）
- apiGroups: ["gateway.example.com"]
  resources: ["gateways", "routes", "policies"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 事件权限（用于记录事件）
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]

---
# ClusterRoleBinding for API Gateway
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: rbac
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: api-gateway
subjects:
- kind: ServiceAccount
  name: api-gateway
  namespace: api-gateway

---
# Role for namespace-specific operations
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: api-gateway-namespace
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: rbac
rules:
# 管理本命名空间的ConfigMaps
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 管理本命名空间的Secrets
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 管理本命名空间的Services
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 管理本命名空间的Deployments
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 管理本命名空间的Pods
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 管理本命名空间的PersistentVolumeClaims
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
# RoleBinding for namespace-specific operations
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: api-gateway-namespace
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: rbac
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: api-gateway-namespace
subjects:
- kind: ServiceAccount
  name: api-gateway
  namespace: api-gateway

---
# 监控相关的Role
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: api-gateway-monitoring
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: monitoring
rules:
# Prometheus指标收集权限
- apiGroups: [""]
  resources: ["nodes", "nodes/proxy", "services", "endpoints", "pods"]
  verbs: ["get", "list", "watch"]

# 读取指标相关的资源
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get"]

# 非资源URL权限（用于/metrics端点）
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]

---
# 监控相关的RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: api-gateway-monitoring
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: api-gateway-monitoring
subjects:
- kind: ServiceAccount
  name: api-gateway
  namespace: api-gateway

---
# 用于管理API网关的管理员Role
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: api-gateway-admin
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: admin
rules:
# 完全管理权限
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["*"]

---
# 管理员RoleBinding（需要手动绑定到特定用户或组）
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: api-gateway-admin
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: admin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: api-gateway-admin
subjects:
# 示例：绑定到特定用户（需要根据实际情况修改）
- kind: User
  name: <EMAIL>
  apiGroup: rbac.authorization.k8s.io
# 示例：绑定到特定组
- kind: Group
  name: api-gateway-admins
  apiGroup: rbac.authorization.k8s.io

---
# Pod安全策略（如果集群启用了PSP）
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: api-gateway-psp
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: security
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'

---
# PSP对应的Role
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: api-gateway-psp
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: security
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - api-gateway-psp

---
# PSP对应的RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: api-gateway-psp
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: security
roleRef:
  kind: Role
  name: api-gateway-psp
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: api-gateway
  namespace: api-gateway
