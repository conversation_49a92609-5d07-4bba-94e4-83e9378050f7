apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-config
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: configuration
data:
  gateway.yaml: |
    # API网关配置文件
    server:
      address: ":8080"
      read_timeout: 30
      write_timeout: 30
      idle_timeout: 120
      
      # TLS配置
      tls:
        enabled: true
        cert_file: "/etc/tls/tls.crt"
        key_file: "/etc/tls/tls.key"
        client_auth: "require_and_verify_client_cert"
        client_ca_file: "/etc/ca/ca.crt"

    # 日志配置
    logging:
      level: "info"
      format: "json"
      output: "stdout"

    # 指标配置
    metrics:
      enabled: true
      path: "/metrics"
      port: 9090

    # 追踪配置
    tracing:
      enabled: true
      jaeger:
        endpoint: "http://jaeger-collector.monitoring:14268/api/traces"
        service_name: "api-gateway"

    # 认证配置
    auth:
      # JWT认证
      jwt:
        enabled: true
        secret: "${JWT_SECRET}"
        issuer: "api-gateway"
        audience: "api-clients"
        expiration: 3600

      # API Key认证
      api_key:
        enabled: true
        header_name: "X-API-Key"
        query_param: "api_key"

      # mTLS认证
      mtls:
        enabled: true
        ca_file: "/etc/ca/ca.crt"
        crl_file: "/etc/ca/ca.crl"
        ocsp_enabled: false
        verify_client_cert_cn: true
        allowed_cns:
          - "client.example.com"
          - "service.internal"

      # OIDC认证
      oidc:
        enabled: true
        issuer_url: "${OIDC_ISSUER_URL}"
        client_id: "${OIDC_CLIENT_ID}"
        client_secret: "${OIDC_CLIENT_SECRET}"
        redirect_url: "${OIDC_REDIRECT_URL}"
        scopes: ["openid", "profile", "email"]

    # 安全配置
    security:
      # 限流配置
      rate_limit:
        enabled: true
        algorithm: "token_bucket"
        rules:
          - path: "/api/*"
            method: "*"
            rate: 1000
            burst: 2000
            window: "1m"
            key_by: "ip"

      # WAF配置
      waf:
        enabled: true
        rules:
          - name: "sql_injection"
            pattern: "(?i)(union|select|insert|delete|update|drop|create|alter|exec|script)"
            action: "block"
          - name: "xss_attack"
            pattern: "(?i)(<script|javascript:|onload=|onerror=)"
            action: "block"

      # CORS配置
      cors:
        enabled: true
        allowed_origins: ["*"]
        allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        allowed_headers: ["Content-Type", "Authorization", "X-API-Key"]
        allow_credentials: false
        max_age: 86400

    # 路由配置
    routes:
      # 健康检查路由
      - name: "health-check"
        path: "/health"
        method: "GET"
        upstream:
          type: "static"
          servers:
            - host: "localhost"
              port: 8080
        auth:
          required: false

      # 指标路由
      - name: "metrics"
        path: "/metrics"
        method: "GET"
        upstream:
          type: "static"
          servers:
            - host: "localhost"
              port: 9090
        auth:
          required: false

      # API路由
      - name: "api-v1"
        path: "/api/v1/*"
        method: "*"
        upstream:
          type: "service_discovery"
          service_name: "backend-service"
          load_balancer: "round_robin"
        auth:
          required: true
          methods: ["jwt", "api_key", "mtls"]
        timeout: 30s
        retries: 3

    # 服务发现配置
    discovery:
      consul:
        enabled: false
        address: "consul.service.consul:8500"
        datacenter: "dc1"
        
      kubernetes:
        enabled: true
        namespace: "backend"
        label_selector: "app=backend-service"

    # 插件配置
    plugins:
      directory: "/app/plugins"
      plugins:
        request_id:
          enabled: true
          config:
            header_name: "X-Request-ID"
            generate_if_missing: true
        
        audit_log:
          enabled: true
          config:
            log_file: "/var/log/audit.log"
            include_request_body: false
            include_response_body: false
        
        prometheus_metrics:
          enabled: true
          config:
            namespace: "api_gateway"
            subsystem: "http"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-nginx-config
  namespace: api-gateway
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/component: proxy
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        upstream api_gateway {
            server api-gateway-service:8080;
        }
        
        # 日志格式
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for" '
                       'cert_cn="$ssl_client_s_dn_cn" cert_serial="$ssl_client_serial"';
        
        server {
            listen 443 ssl http2;
            server_name api-gateway.example.com;
            
            # SSL配置
            ssl_certificate /etc/tls/tls.crt;
            ssl_certificate_key /etc/tls/tls.key;
            ssl_client_certificate /etc/ca/ca.crt;
            ssl_verify_client on;
            ssl_verify_depth 2;
            
            # SSL协议配置
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
            ssl_prefer_server_ciphers off;
            
            access_log /var/log/nginx/access.log main;
            error_log /var/log/nginx/error.log;
            
            location / {
                # 传递客户端证书信息
                proxy_set_header X-Client-Cert $ssl_client_escaped_cert;
                proxy_set_header X-Client-Cert-CN $ssl_client_s_dn_cn;
                proxy_set_header X-Client-Cert-Serial $ssl_client_serial;
                proxy_set_header X-Client-Cert-Issuer $ssl_client_i_dn;
                
                # 标准代理头
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_pass http://api_gateway;
                proxy_timeout 30s;
                proxy_connect_timeout 5s;
            }
        }
    }
