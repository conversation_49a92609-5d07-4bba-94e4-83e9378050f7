# 高级缓存系统配置示例
# API Gateway Advanced Cache Configuration Example

# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 指标配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090

# 高级缓存配置
cache:
  # 是否启用缓存
  enabled: true
  
  # 缓存类型：memory, redis, multi_level
  type: "memory"
  
  # 默认TTL
  default_ttl: "5m"
  
  # 最大TTL
  max_ttl: "1h"
  
  # 内存缓存配置
  memory:
    # 最大大小（字节）
    max_size: 104857600  # 100MB
    
    # 最大条目数
    max_items: 10000
    
    # 清理间隔
    cleanup_interval: "5m"
    
    # 淘汰策略：lru, lfu, fifo, random
    eviction_policy: "lru"
  
  # Redis缓存配置
  redis:
    # Redis地址
    address: "redis:6379"
    
    # 密码
    password: ""
    
    # 数据库
    db: 0
    
    # 连接池大小
    pool_size: 10
    
    # 最小空闲连接
    min_idle_conns: 5
    
    # 最大重试次数
    max_retries: 3
    
    # 重试延迟
    retry_delay: "100ms"
    
    # 连接超时
    dial_timeout: "5s"
    
    # 读超时
    read_timeout: "3s"
    
    # 写超时
    write_timeout: "3s"
    
    # 键前缀
    key_prefix: "api_gateway"
  
  # 多级缓存配置
  multi_level:
    # 写入策略：write_through, write_back, write_around
    write_policy: "write_through"
    
    # 读取策略：read_through, read_around
    read_policy: "read_through"
    
    # 缓存级别
    levels:
      # L1缓存：内存
      - name: "l1"
        type: "memory"
        size: 52428800  # 50MB
        ttl: "5m"
        priority: 1
        options:
          max_items: 5000
          eviction_policy: "lru"
      
      # L2缓存：Redis
      - name: "l2"
        type: "redis"
        size: 1073741824  # 1GB
        ttl: "30m"
        priority: 2
        options:
          address: "redis:6379"
          db: 1
  
  # 缓存策略
  policies:
    # API响应缓存策略
    - name: "api_response"
      paths:
        - "/api/v1/users*"
        - "/api/v1/products*"
        - "/api/v1/categories*"
      methods: ["GET"]
      ttl: "10m"
      enabled: true
      priority: 1
      conditions:
        - type: "status_code"
          key: ""
          value: "200"
          operator: "eq"
        - type: "header"
          key: "Cache-Control"
          value: "no-cache"
          operator: "ne"
      vary_by:
        - "user_id"
        - "query_params"
        - "headers"
    
    # 静态资源缓存策略
    - name: "static_assets"
      paths:
        - "/static/*"
        - "/assets/*"
        - "*.css"
        - "*.js"
        - "*.png"
        - "*.jpg"
        - "*.gif"
      methods: ["GET"]
      ttl: "1h"
      enabled: true
      priority: 2
      conditions:
        - type: "status_code"
          value: "200"
          operator: "eq"
      vary_by:
        - "path"
    
    # 用户数据缓存策略
    - name: "user_data"
      paths:
        - "/api/v1/user/profile"
        - "/api/v1/user/settings"
      methods: ["GET"]
      ttl: "2m"
      enabled: true
      priority: 3
      conditions:
        - type: "header"
          key: "Authorization"
          value: ""
          operator: "ne"
      vary_by:
        - "user_id"
        - "headers"
    
    # 搜索结果缓存策略
    - name: "search_results"
      paths:
        - "/api/v1/search*"
      methods: ["GET"]
      ttl: "30s"
      enabled: true
      priority: 4
      conditions:
        - type: "query"
          key: "q"
          value: ""
          operator: "ne"
      vary_by:
        - "query_params"
  
  # 缓存键生成器配置
  key_generator:
    # 是否包含查询参数
    include_query_params: true
    
    # 包含的头部
    include_headers:
      - "Authorization"
      - "Accept-Language"
      - "User-Agent"
      - "X-API-Version"
    
    # 排除的参数
    exclude_params:
      - "timestamp"
      - "_"
      - "callback"
    
    # 排除的头部
    exclude_headers:
      - "Cookie"
      - "Set-Cookie"
      - "X-Request-ID"
    
    # 自定义键模板
    template: "{service_name}:{method}:{path}:{user_id}"
    
    # 哈希算法：md5, sha1, sha256
    hash_algorithm: "sha256"
  
  # 缓存预热配置
  warmup:
    # 是否启用预热
    enabled: true
    
    # 预热策略：startup, scheduled, manual
    strategy: "startup"
    
    # 预热URL列表
    urls:
      - "http://localhost:8080/api/v1/users?page=1"
      - "http://localhost:8080/api/v1/products?category=electronics"
      - "http://localhost:8080/api/v1/categories"
      - "http://localhost:8080/static/css/main.css"
      - "http://localhost:8080/static/js/app.js"
    
    # 预热文件路径
    url_file: "configs/warmup-urls.txt"
    
    # 预热间隔（定时预热）
    interval: "1h"
    
    # 并发数
    concurrency: 5
    
    # 超时时间
    timeout: "30s"
  
  # 缓存一致性配置
  consistency:
    # 一致性模式：eventual, strong, weak
    mode: "eventual"
    
    # 失效策略：ttl, tag, manual
    invalidation_strategy: "tag"
    
    # 失效标签
    tags:
      - "user_data"
      - "product_data"
      - "category_data"
    
    # 失效通知
    notifications:
      # 是否启用通知
      enabled: true
      
      # 通知类型：redis_pubsub, webhook, kafka
      type: "redis_pubsub"
      
      # Redis发布订阅配置
      redis_pubsub:
        # 频道名称
        channel: "cache_invalidation"
        
        # 频道模式
        pattern: "cache:*"
      
      # Webhook配置
      webhook:
        # Webhook URL
        url: "http://cache-invalidation-service/webhook"
        
        # 超时时间
        timeout: "5s"
        
        # 重试次数
        retries: 3
  
  # 压缩配置
  compression:
    # 是否启用压缩
    enabled: true
    
    # 压缩算法：gzip, lz4, snappy, zstd
    algorithm: "gzip"
    
    # 压缩级别
    level: 6
    
    # 最小压缩大小
    min_size: 1024  # 1KB

# 路由配置（缓存相关）
routes:
  # 带缓存的API路由
  - name: "users-api-cached"
    path: "/api/v1/users*"
    method: "*"
    upstream:
      type: "service_discovery"
      service_name: "user-service"
      load_balancer: "round_robin"
    cache:
      enabled: true
      policy: "api_response"
      ttl: "10m"
      vary_by:
        - "user_id"
        - "query_params"
      tags:
        - "user_data"
        - "api_response"
    auth:
      required: true
      methods: ["jwt"]
    timeout: 30s

  # 静态资源路由
  - name: "static-assets-cached"
    path: "/static/*"
    method: "GET"
    upstream:
      type: "static"
      root_dir: "/var/www/static"
    cache:
      enabled: true
      policy: "static_assets"
      ttl: "1h"
      tags:
        - "static_assets"
    timeout: 10s

  # 搜索API路由
  - name: "search-api-cached"
    path: "/api/v1/search*"
    method: "GET"
    upstream:
      type: "service_discovery"
      service_name: "search-service"
    cache:
      enabled: true
      policy: "search_results"
      ttl: "30s"
      vary_by:
        - "query_params"
      tags:
        - "search_results"
    auth:
      required: false
    timeout: 15s

---
# 缓存管理API端点

# 1. 获取缓存统计
# GET /cache/stats
# 返回全局缓存统计信息

# 2. 获取特定缓存统计
# GET /cache/stats/{cache_name}
# 返回指定缓存的统计信息

# 3. 清空缓存
# DELETE /cache/flush
# 清空所有缓存

# 4. 清空特定缓存
# DELETE /cache/flush/{cache_name}
# 清空指定缓存

# 5. 按键删除缓存
# DELETE /cache/keys/{key}
# 删除指定键的缓存

# 6. 按模式删除缓存
# DELETE /cache/pattern/{pattern}
# 删除匹配模式的缓存

# 7. 按标签删除缓存
# DELETE /cache/tags/{tag}
# 删除带有指定标签的缓存

# 8. 预热缓存
# POST /cache/warmup
# Body: {"urls": ["url1", "url2"]}
# 预热指定URL的缓存

# 9. 获取预热状态
# GET /cache/warmup/status
# 返回预热状态

# 10. 获取缓存键
# GET /cache/keys?pattern=*
# 返回匹配模式的缓存键列表

---
# 缓存使用示例

# 1. 基本缓存使用
# curl -X GET "http://localhost:8080/api/v1/users?page=1" \
#   -H "Authorization: Bearer <token>"

# 2. 带缓存控制的请求
# curl -X GET "http://localhost:8080/api/v1/products" \
#   -H "Cache-Control: max-age=300"

# 3. 强制刷新缓存
# curl -X GET "http://localhost:8080/api/v1/users" \
#   -H "Cache-Control: no-cache"

# 4. 缓存管理
# curl -X GET "http://localhost:8080/cache/stats"
# curl -X DELETE "http://localhost:8080/cache/tags/user_data"
# curl -X POST "http://localhost:8080/cache/warmup" \
#   -H "Content-Type: application/json" \
#   -d '{"urls": ["http://localhost:8080/api/v1/users"]}'

---
# 缓存性能优化建议

# 1. 内存缓存优化
# - 合理设置max_items和max_size
# - 选择合适的淘汰策略
# - 定期清理过期项

# 2. Redis缓存优化
# - 配置合适的连接池大小
# - 使用键前缀避免冲突
# - 启用压缩减少内存使用

# 3. 多级缓存优化
# - L1缓存存储热点数据
# - L2缓存存储更多数据
# - 合理配置各级TTL

# 4. 缓存策略优化
# - 根据业务特点设置TTL
# - 使用标签进行批量失效
# - 合理设置vary_by参数

# 5. 预热策略优化
# - 在低峰期进行预热
# - 预热最常访问的数据
# - 控制预热并发数
