# gRPC 协议支持配置示例
# API Gateway gRPC Configuration Example

# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 指标配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090

# 追踪配置
tracing:
  enabled: true
  jaeger:
    endpoint: "http://jaeger:14268/api/traces"
    service_name: "api-gateway-grpc"

# gRPC配置
grpc:
  # 是否启用gRPC支持
  enabled: true
  
  # gRPC服务器配置
  server:
    # 监听地址
    address: ":9090"
    
    # 是否启用反射（开发环境建议启用）
    reflection: true
    
    # 最大接收消息大小（字节）
    max_recv_msg_size: 4194304  # 4MB
    
    # 最大发送消息大小（字节）
    max_send_msg_size: 4194304  # 4MB
    
    # 连接超时（秒）
    connection_timeout: 30
    
    # Keep-alive配置
    keep_alive:
      # Keep-alive时间（秒）
      time: 30
      # Keep-alive超时（秒）
      timeout: 5
      # 是否允许没有流时的keep-alive
      permit_without_stream: true
  
  # gRPC客户端配置
  client:
    # 默认超时时间（秒）
    default_timeout: 30
    
    # 最大接收消息大小（字节）
    max_recv_msg_size: 4194304  # 4MB
    
    # 最大发送消息大小（字节）
    max_send_msg_size: 4194304  # 4MB
    
    # 负载均衡策略
    load_balancer: "round_robin"  # round_robin, pick_first, grpclb
    
    # 重试配置
    retry:
      # 最大重试次数
      max_attempts: 3
      # 初始退避时间（毫秒）
      initial_backoff: 100
      # 最大退避时间（毫秒）
      max_backoff: 30000
      # 退避倍数
      backoff_multiplier: 2.0
      # 可重试的状态码
      retryable_status_codes:
        - "UNAVAILABLE"
        - "DEADLINE_EXCEEDED"
        - "RESOURCE_EXHAUSTED"
    
    # Keep-alive配置
    keep_alive:
      time: 30
      timeout: 5
      permit_without_stream: true
  
  # HTTP到gRPC网关配置
  gateway:
    # 是否启用HTTP到gRPC转换
    enabled: true
    
    # 路径前缀
    path_prefix: "/grpc"
    
    # 是否允许删除请求体
    allow_delete_body: false
    
    # 是否允许PATCH功能
    allow_patch_feature: true
    
    # 元数据映射（HTTP头部到gRPC元数据）
    metadata_mapping:
      "authorization": "authorization"
      "x-request-id": "x-request-id"
      "x-user-id": "x-user-id"
      "x-trace-id": "x-trace-id"
  
  # 健康检查配置
  health_check:
    # 是否启用健康检查
    enabled: true
    
    # 检查间隔（秒）
    interval: 30
    
    # 超时时间（秒）
    timeout: 5
    
    # 不健康阈值
    unhealthy_threshold: 3
    
    # 健康阈值
    healthy_threshold: 2

# 服务发现配置
discovery:
  # 服务发现类型：static, consul, nacos
  type: "consul"
  
  # Consul配置
  consul:
    address: "consul.service.consul:8500"
    datacenter: "dc1"
    token: ""

# 路由配置
routes:
  # gRPC服务路由
  - name: "user-service-grpc"
    path: "/grpc/user.UserService/*"
    method: "*"
    upstream:
      type: "service_discovery"
      service_name: "user-service"
      load_balancer: "round_robin"
      health_check:
        enabled: true
        path: "/health"
        interval: 30s
        timeout: 5s
        retries: 3
    auth:
      required: true
      methods: ["jwt", "api_key"]
    timeout: 30s
    retries: 3
    headers:
      "X-Service-Type": "grpc"

  # HTTP到gRPC转换路由
  - name: "user-service-http-grpc"
    path: "/api/v1/users/*"
    method: "*"
    upstream:
      type: "grpc_gateway"
      service_name: "user.UserService"
      target_address: "user-service:9090"
    transform:
      request:
        # HTTP路径到gRPC方法的映射
        path_mapping:
          "GET /api/v1/users": "user.UserService/ListUsers"
          "GET /api/v1/users/{id}": "user.UserService/GetUser"
          "POST /api/v1/users": "user.UserService/CreateUser"
          "PUT /api/v1/users/{id}": "user.UserService/UpdateUser"
          "DELETE /api/v1/users/{id}": "user.UserService/DeleteUser"
      response:
        # 响应格式转换
        format: "json"
    auth:
      required: true
      methods: ["jwt"]
    timeout: 30s

  # 订单服务gRPC路由
  - name: "order-service-grpc"
    path: "/grpc/order.OrderService/*"
    method: "*"
    upstream:
      type: "service_discovery"
      service_name: "order-service"
      load_balancer: "least_conn"
    auth:
      required: true
      methods: ["jwt", "mtls"]
    timeout: 60s  # 订单处理可能需要更长时间
    retries: 2

# 安全配置
security:
  # 限流配置
  rate_limit:
    enabled: true
    algorithm: "token_bucket"
    rules:
      - path: "/grpc/*"
        method: "*"
        rate: 1000
        burst: 2000
        window: "1m"
        key_by: "user_id"

# 插件配置
plugins:
  directory: "plugins"
  plugins:
    # gRPC拦截器插件
    grpc_interceptor:
      enabled: true
      config:
        # 请求日志
        request_logging: true
        # 响应日志
        response_logging: false
        # 性能监控
        performance_monitoring: true
        # 错误处理
        error_handling: true
    
    # gRPC负载均衡插件
    grpc_load_balancer:
      enabled: true
      config:
        # 负载均衡算法
        algorithm: "weighted_round_robin"
        # 健康检查
        health_check: true
        # 故障转移
        failover: true
    
    # gRPC熔断器插件
    grpc_circuit_breaker:
      enabled: true
      config:
        # 失败阈值
        failure_threshold: 5
        # 恢复超时
        recovery_timeout: 30s
        # 半开状态请求数
        half_open_requests: 3

---
# gRPC服务示例配置

# 用户服务
user_service:
  name: "user-service"
  protocol: "grpc"
  address: "user-service:9090"
  health_check:
    enabled: true
    service_name: "user.UserService"
  metadata:
    version: "v1.0.0"
    team: "user-team"
  tags:
    - "user"
    - "authentication"

# 订单服务
order_service:
  name: "order-service"
  protocol: "grpc"
  address: "order-service:9090"
  health_check:
    enabled: true
    service_name: "order.OrderService"
  metadata:
    version: "v1.2.0"
    team: "order-team"
  tags:
    - "order"
    - "payment"

# 通知服务
notification_service:
  name: "notification-service"
  protocol: "grpc"
  address: "notification-service:9090"
  health_check:
    enabled: true
    service_name: "notification.NotificationService"
  metadata:
    version: "v1.1.0"
    team: "notification-team"
  tags:
    - "notification"
    - "messaging"

---
# gRPC客户端使用示例

# 1. 直接gRPC调用
# grpcurl -plaintext localhost:9090 user.UserService/ListUsers

# 2. HTTP到gRPC转换
# curl -X GET http://localhost:8080/api/v1/users \
#   -H "Authorization: Bearer <token>"

# 3. 健康检查
# curl http://localhost:8080/grpc/health?address=user-service:9090&service=user.UserService

# 4. 服务发现
# curl http://localhost:8080/grpc/services?service=user-service

# 5. 统计信息
# curl http://localhost:8080/grpc/stats
