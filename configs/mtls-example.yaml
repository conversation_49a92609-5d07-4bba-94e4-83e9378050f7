# mTLS 双向认证配置示例
# API Gateway mTLS Configuration Example

# 服务器配置
server:
  address: ":8443"  # HTTPS端口
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  
  # TLS配置
  tls:
    enabled: true
    cert_file: "certs/server.crt"
    key_file: "certs/server.key"
    
    # 客户端证书验证
    client_auth: "require_and_verify_client_cert"
    client_ca_file: "certs/ca.crt"

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 认证配置
auth:
  # mTLS认证器配置
  mtls:
    enabled: true
    
    # CA证书文件路径
    ca_file: "certs/ca.crt"
    
    # CRL文件路径（可选）
    crl_file: "certs/ca.crl"
    
    # OCSP配置
    ocsp_enabled: true
    ocsp_timeout_seconds: 10
    
    # 客户端证书CN验证
    verify_client_cert_cn: true
    allowed_cns:
      - "client1.example.com"
      - "client2.example.com"
      - "api-client"
    
    # 组织验证
    verify_organization: true
    allowed_organizations:
      - "Example Corporation"
      - "Trusted Partner Inc"
      - "Internal Services"

  # 策略配置
  policies:
    engine: "builtin"
    rules:
      - name: "admin_access"
        description: "管理员访问权限"
        effect: "allow"
        subjects:
          - "role:admin"
        resources:
          - "/admin/*"
        actions:
          - "*"
      
      - name: "user_access"
        description: "普通用户访问权限"
        effect: "allow"
        subjects:
          - "role:user"
        resources:
          - "/api/v1/user/*"
        actions:
          - "GET"
          - "POST"

# 安全配置
security:
  # 限流配置
  rate_limit:
    enabled: true
    algorithm: "token_bucket"
    rules:
      - path: "/api/*"
        method: "*"
        rate: 100
        burst: 200
        window: "1m"
        key_by: "client_cert_cn"  # 基于客户端证书CN限流

  # CORS配置
  cors:
    enabled: true
    allowed_origins:
      - "https://app.example.com"
      - "https://admin.example.com"
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allowed_headers:
      - "Content-Type"
      - "Authorization"
      - "X-Client-Cert-CN"
    allow_credentials: true
    max_age: 86400

# 路由配置
routes:
  # 管理API路由
  - name: "admin-api"
    path: "/admin/*"
    method: "*"
    upstream:
      type: "static"
      load_balancer: "round_robin"
      servers:
        - host: "admin-backend.internal"
          port: 8080
    auth:
      required: true
      methods: ["mtls"]
      roles: ["admin"]
    timeout: 30s
    retries: 3

  # 用户API路由
  - name: "user-api"
    path: "/api/v1/user/*"
    method: "*"
    upstream:
      type: "static"
      load_balancer: "round_robin"
      servers:
        - host: "user-backend.internal"
          port: 8080
    auth:
      required: true
      methods: ["mtls"]
      roles: ["user", "admin"]
    timeout: 30s
    retries: 3

  # 公开API路由（仍需要mTLS但不需要特定角色）
  - name: "public-api"
    path: "/api/v1/public/*"
    method: "*"
    upstream:
      type: "static"
      load_balancer: "round_robin"
      servers:
        - host: "public-backend.internal"
          port: 8080
    auth:
      required: true
      methods: ["mtls"]
    timeout: 30s
    retries: 3

# 监控配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090

# 追踪配置
tracing:
  enabled: true
  jaeger:
    endpoint: "http://jaeger:14268/api/traces"
    service_name: "api-gateway-mtls"

# 插件配置
plugins:
  directory: "plugins"
  plugins:
    # 审计日志插件
    audit_log:
      enabled: true
      config:
        log_file: "logs/audit.log"
        include_request_body: false
        include_response_body: false
        log_client_cert_info: true
    
    # 请求ID插件
    request_id:
      enabled: true
      config:
        header_name: "X-Request-ID"
        generate_if_missing: true
    
    # 客户端证书信息插件
    client_cert_info:
      enabled: true
      config:
        # 将客户端证书信息添加到请求头
        add_headers:
          - name: "X-Client-Cert-CN"
            value: "${client_cert.subject.cn}"
          - name: "X-Client-Cert-Org"
            value: "${client_cert.subject.organization}"
          - name: "X-Client-Cert-Serial"
            value: "${client_cert.serial_number}"

---
# 证书生成示例脚本
# 以下是生成测试证书的示例命令

# 1. 生成CA私钥
# openssl genrsa -out ca.key 4096

# 2. 生成CA证书
# openssl req -new -x509 -key ca.key -sha256 -subj "/C=US/ST=CA/O=Example Corp/CN=Example CA" -days 3650 -out ca.crt

# 3. 生成服务器私钥
# openssl genrsa -out server.key 4096

# 4. 生成服务器证书签名请求
# openssl req -new -key server.key -out server.csr -config <(
# cat <<EOF
# [req]
# default_bits = 4096
# prompt = no
# distinguished_name = req_distinguished_name
# req_extensions = req_ext
# 
# [req_distinguished_name]
# C = US
# ST = CA
# O = Example Corp
# CN = api-gateway.example.com
# 
# [req_ext]
# subjectAltName = @alt_names
# 
# [alt_names]
# DNS.1 = api-gateway.example.com
# DNS.2 = localhost
# IP.1 = 127.0.0.1
# EOF
# )

# 5. 签发服务器证书
# openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out server.crt -days 365 -sha256 -extensions req_ext -extfile <(
# cat <<EOF
# [req_ext]
# subjectAltName = @alt_names
# 
# [alt_names]
# DNS.1 = api-gateway.example.com
# DNS.2 = localhost
# IP.1 = 127.0.0.1
# EOF
# )

# 6. 生成客户端私钥
# openssl genrsa -out client.key 4096

# 7. 生成客户端证书签名请求
# openssl req -new -key client.key -subj "/C=US/ST=CA/O=Example Corp/OU=Engineering/CN=client1.example.com/emailAddress=<EMAIL>" -out client.csr

# 8. 签发客户端证书
# openssl x509 -req -in client.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out client.crt -days 365 -sha256 -extensions client_ext -extfile <(
# cat <<EOF
# [client_ext]
# keyUsage = digitalSignature
# extendedKeyUsage = clientAuth
# EOF
# )

# 9. 验证证书
# openssl verify -CAfile ca.crt server.crt
# openssl verify -CAfile ca.crt client.crt

# 10. 查看证书信息
# openssl x509 -in client.crt -text -noout
