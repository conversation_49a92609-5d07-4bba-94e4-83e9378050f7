# Serverless平台集成配置示例
# API Gateway Serverless Platform Integration Configuration Example

# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 指标配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090

# Serverless平台集成配置
serverless:
  # 是否启用Serverless支持
  enabled: true
  
  # AWS Lambda配置
  aws_lambda:
    # 是否启用AWS Lambda
    enabled: true
    
    # AWS区域
    region: "us-east-1"
    
    # AWS访问凭据（建议使用环境变量）
    access_key_id: "${AWS_ACCESS_KEY_ID}"
    secret_access_key: "${AWS_SECRET_ACCESS_KEY}"
    session_token: "${AWS_SESSION_TOKEN}"  # 可选，用于临时凭据
    
    # 自定义端点URL（用于LocalStack等本地测试）
    endpoint_url: ""  # 留空使用默认AWS端点
    
    # 默认超时时间
    default_timeout: "30s"
    
    # 最大重试次数
    max_retries: 3
    
    # 函数信息缓存TTL
    function_cache_ttl: "5m"
  
  # Azure Functions配置
  azure_functions:
    # 是否启用Azure Functions
    enabled: false
    
    # Azure订阅ID
    subscription_id: "${AZURE_SUBSCRIPTION_ID}"
    
    # 资源组名称
    resource_group: "my-resource-group"
    
    # 函数应用名称
    function_app_name: "my-function-app"
    
    # Azure AD应用凭据
    client_id: "${AZURE_CLIENT_ID}"
    client_secret: "${AZURE_CLIENT_SECRET}"
    tenant_id: "${AZURE_TENANT_ID}"
    
    # 默认超时时间
    default_timeout: "30s"
  
  # Google Cloud Functions配置
  google_cloud_functions:
    # 是否启用Google Cloud Functions
    enabled: false
    
    # GCP项目ID
    project_id: "${GCP_PROJECT_ID}"
    
    # GCP区域
    region: "us-central1"
    
    # 服务账户凭据文件路径
    credentials_file: "/path/to/service-account.json"
    
    # 默认超时时间
    default_timeout: "30s"
  
  # OpenFaaS配置
  openfaas:
    # 是否启用OpenFaaS
    enabled: false
    
    # OpenFaaS网关URL
    gateway_url: "http://openfaas-gateway:8080"
    
    # 认证凭据
    username: "${OPENFAAS_USERNAME}"
    password: "${OPENFAAS_PASSWORD}"
    
    # 默认超时时间
    default_timeout: "30s"
  
  # Serverless路由配置
  routes:
    # 用户服务Lambda函数
    - name: "user_service"
      path: "/api/v1/users/*"
      methods: ["GET", "POST", "PUT", "DELETE"]
      provider: "aws_lambda"
      function_name: "user-service-handler"
      invocation_type: "sync"  # sync（同步）或 async（异步）
      timeout: "10s"
      enabled: true
      
      # 重试配置
      retry_config:
        max_retries: 2
        retry_interval: "1s"
        backoff_strategy: "exponential"  # fixed, linear, exponential
        max_backoff: "10s"
        retryable_status_codes: [500, 502, 503, 504]
      
      # 请求/响应转换
      transform:
        request:
          # HTTP头部映射
          header_mapping:
            "X-User-ID": "x-lambda-user-id"
            "Authorization": "x-lambda-auth"
          
          # 查询参数映射
          query_mapping:
            "page": "Page"
            "limit": "Limit"
          
          # 路径参数映射
          path_mapping:
            "id": "userId"
          
          # 请求负载转换模板
          payload_template: |
            {
              "httpMethod": "{{.method}}",
              "path": "{{.path}}",
              "queryStringParameters": {{.query_params | toJson}},
              "pathParameters": {{.path_params | toJson}},
              "headers": {{.headers | toJson}},
              "body": {{.body | toString | toJson}},
              "requestContext": {
                "requestId": "{{.request_id}}",
                "identity": {
                  "sourceIp": "{{.client_ip}}"
                }
              }
            }
          
          content_type: "application/json"
        
        response:
          # HTTP状态码映射
          status_code_mapping:
            200: 200
            400: 400
            404: 404
            500: 500
          
          # 响应头部映射
          header_mapping:
            "x-lambda-request-id": "X-Request-ID"
          
          # 响应负载转换模板
          payload_template: |
            {{if .body}}
              {{.body | fromJson}}
            {{else}}
              {"message": "No content"}
            {{end}}
          
          content_type: "application/json"
      
      # 认证配置
      auth:
        required: true
        methods: ["jwt"]
        roles: ["user", "admin"]
        permissions: ["users:read", "users:write"]
      
      # 缓存配置
      cache:
        enabled: true
        ttl: "5m"
        key_template: "lambda:{{.function_name}}:{{.path}}:{{.query_hash}}"
        conditions:
          - "method == 'GET'"
          - "status_code == 200"
    
    # 数据处理Lambda函数
    - name: "data_processor"
      path: "/api/v1/process"
      methods: ["POST"]
      provider: "aws_lambda"
      function_name: "data-processor"
      invocation_type: "async"  # 异步调用
      timeout: "60s"
      enabled: true
      
      retry_config:
        max_retries: 1
        retry_interval: "5s"
        backoff_strategy: "fixed"
      
      auth:
        required: true
        methods: ["api_key"]
      
      cache:
        enabled: false  # 数据处理不缓存
    
    # 通知服务Lambda函数
    - name: "notification_service"
      path: "/api/v1/notifications"
      methods: ["POST"]
      provider: "aws_lambda"
      function_name: "notification-handler"
      invocation_type: "async"
      timeout: "15s"
      enabled: true
      
      transform:
        request:
          payload_template: |
            {
              "type": "api_gateway_notification",
              "data": {{.body | fromJson}},
              "metadata": {
                "source": "api-gateway",
                "timestamp": "{{.timestamp}}",
                "user_id": "{{.user_id}}"
              }
            }
      
      auth:
        required: false  # 内部通知不需要认证
    
    # 文件处理服务（Azure Functions示例）
    - name: "file_processor"
      path: "/api/v1/files/process"
      methods: ["POST"]
      provider: "azure_functions"
      function_name: "file-processor"
      invocation_type: "sync"
      timeout: "120s"
      enabled: false  # 默认禁用，需要配置Azure
      
      retry_config:
        max_retries: 2
        retry_interval: "2s"
        backoff_strategy: "linear"
      
      auth:
        required: true
        methods: ["jwt"]
        permissions: ["files:process"]
    
    # 图像处理服务（Google Cloud Functions示例）
    - name: "image_processor"
      path: "/api/v1/images/resize"
      methods: ["POST"]
      provider: "google_cloud_functions"
      function_name: "image-resize"
      invocation_type: "sync"
      timeout: "30s"
      enabled: false  # 默认禁用，需要配置GCP
      
      transform:
        request:
          payload_template: |
            {
              "image_data": "{{.body | base64}}",
              "options": {{.query_params | toJson}}
            }
        
        response:
          payload_template: |
            {
              "processed_image": "{{.body | fromJson | .image}}",
              "metadata": {{.body | fromJson | .metadata}}
            }
      
      auth:
        required: true
        methods: ["api_key"]
    
    # 机器学习推理服务（OpenFaaS示例）
    - name: "ml_inference"
      path: "/api/v1/ml/predict"
      methods: ["POST"]
      provider: "openfaas"
      function_name: "ml-inference"
      invocation_type: "sync"
      timeout: "45s"
      enabled: false  # 默认禁用，需要配置OpenFaaS
      
      retry_config:
        max_retries: 1
        retry_interval: "3s"
        backoff_strategy: "fixed"
      
      auth:
        required: true
        methods: ["jwt", "api_key"]
        permissions: ["ml:predict"]
      
      cache:
        enabled: true
        ttl: "1h"
        key_template: "ml:{{.function_name}}:{{.body_hash}}"
        conditions:
          - "method == 'POST'"
          - "status_code == 200"
  
  # 监控配置
  monitoring:
    # 是否启用监控
    enabled: true
    
    # 指标收集
    metrics:
      enabled: true
      prefix: "serverless"
      interval: "30s"
      labels:
        service: "api-gateway"
        component: "serverless"
    
    # 日志配置
    logging:
      enabled: true
      level: "info"
      log_payload: false  # 是否记录请求/响应负载
      max_payload_length: 1024
    
    # 分布式追踪
    tracing:
      enabled: true
      sample_rate: 0.1
      tags:
        service: "api-gateway"
        component: "serverless"

# 路由配置（Serverless相关）
routes:
  # Serverless函数路由
  - name: "serverless-functions"
    path: "/lambda/*"
    method: "ANY"
    upstream:
      type: "serverless"
      serverless_manager: true
    timeout: 300  # 5分钟超时，适应长时间运行的函数

---
# Serverless管理API端点

# 1. 获取Serverless统计
# GET /serverless/stats
# 返回所有提供者的统计信息

# 2. 获取函数列表
# GET /serverless/functions
# 返回所有提供者的函数列表

# 3. 调用函数
# POST /serverless/invoke
# Body: {"provider": "aws_lambda", "function": "my-function", "payload": {...}}
# 直接调用指定的Serverless函数

# 4. 获取函数信息
# GET /serverless/functions/{provider}/{function_name}
# 获取指定函数的详细信息

# 5. 健康检查
# GET /serverless/health
# Serverless服务健康检查

---
# Serverless使用示例

# 1. 同步调用Lambda函数
# POST /api/v1/users
# Content-Type: application/json
# Authorization: Bearer jwt_token
# 
# {
#   "name": "John Doe",
#   "email": "<EMAIL>"
# }

# 2. 异步调用数据处理函数
# POST /api/v1/process
# X-API-Key: your_api_key
# Content-Type: application/json
# 
# {
#   "data": [...],
#   "options": {...}
# }

# 3. 直接函数调用
# POST /serverless/invoke
# Content-Type: application/json
# 
# {
#   "provider": "aws_lambda",
#   "function": "hello-world",
#   "invocation_type": "sync",
#   "payload": {"message": "Hello, World!"}
# }

---
# Serverless最佳实践

# 1. 函数设计
# - 保持函数小而专注
# - 使用环境变量进行配置
# - 实现幂等性
# - 优化冷启动时间

# 2. 错误处理
# - 实现适当的重试策略
# - 使用死信队列处理失败
# - 记录详细的错误日志
# - 监控错误率和延迟

# 3. 性能优化
# - 启用函数缓存
# - 使用连接池
# - 优化负载大小
# - 选择合适的内存配置

# 4. 安全考虑
# - 使用最小权限原则
# - 加密敏感数据
# - 验证输入数据
# - 实施访问控制

# 5. 成本优化
# - 监控函数使用情况
# - 优化执行时间
# - 使用预留并发
# - 定期清理未使用的函数

---
# 多云Serverless策略

# 1. 云厂商选择
# - AWS Lambda: 成熟的生态系统，丰富的集成
# - Azure Functions: 与Microsoft生态系统集成
# - Google Cloud Functions: 机器学习和数据分析优势
# - OpenFaaS: 开源解决方案，支持Kubernetes

# 2. 混合部署
# - 根据功能特性选择合适的平台
# - 实现跨云的负载均衡
# - 建立统一的监控和日志
# - 制定灾备和故障转移策略

# 3. 成本管理
# - 比较不同平台的定价模型
# - 监控跨平台的使用成本
# - 优化函数分布和调用模式
# - 利用免费额度和折扣
