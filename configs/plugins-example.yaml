# API网关插件配置示例
# 本文件展示了所有内置插件的配置示例

plugins:
  # 速率限制插件
  rate_limit:
    enabled: true
    default_rate: 100
    default_burst: 10
    cleanup_interval: "5m"
    rules:
      - name: "login_strict"
        pattern: "^/auth/login$"
        rate: 5
        burst: 1
        key_type: "ip"
        methods: ["POST"]
        enabled: true
      - name: "api_user_limit"
        pattern: "^/api/.*"
        rate: 1000
        burst: 100
        key_type: "user"
        enabled: true
      - name: "upload_limit"
        pattern: "^/upload/.*"
        rate: 10
        burst: 2
        key_type: "ip"
        methods: ["POST", "PUT"]
        enabled: true

  # CORS插件
  cors:
    enabled: true
    allowed_origins: 
      - "https://example.com"
      - "https://*.example.com"
      - "http://localhost:3000"
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: 
      - "Content-Type"
      - "Authorization"
      - "X-Requested-With"
    exposed_headers: ["X-Total-Count"]
    allow_credentials: true
    max_age: 3600
    skip_paths: ["/health", "/metrics"]

  # JWT认证插件
  jwt:
    enabled: true
    secret: "your-super-secret-key-change-in-production"
    algorithm: "HS256"
    skip_paths: 
      - "/auth/login"
      - "/auth/register"
      - "/public/.*"
      - "/health"
      - "/metrics"
    custom_claims:
      role: "user"
      permissions: ["read"]
    token_sources: ["header", "query", "cookie"]
    header_name: "Authorization"
    query_name: "token"
    cookie_name: "jwt_token"

  # 请求重写插件
  rewrite:
    enabled: true
    path_rules:
      - name: "api_version_rewrite"
        pattern: "^/api/v1/(.*)"
        replacement: "/api/v2/$1"
        method: "*"
        enabled: true
      - name: "legacy_redirect"
        pattern: "^/old-api/(.*)"
        replacement: "/api/v2/$1"
        enabled: true
    query_rules:
      - name: "add_version_param"
        path_pattern: "^/api/.*"
        add:
          version: "2.0"
          client: "gateway"
        enabled: true
      - name: "remove_debug_params"
        path_pattern: ".*"
        remove: ["debug", "trace"]
        enabled: true
    header_rules:
      - name: "add_request_headers"
        path_pattern: "^/api/.*"
        add:
          X-Gateway-Version: "1.0.0"
          X-Request-ID: "${request_id}"
        set:
          User-Agent: "API-Gateway/1.0"
        enabled: true

  # 响应修改插件
  response:
    enabled: true
    header_rules:
      - name: "security_headers"
        path_pattern: ".*"
        add:
          X-Frame-Options: "DENY"
          X-Content-Type-Options: "nosniff"
          X-XSS-Protection: "1; mode=block"
          Strict-Transport-Security: "max-age=31536000; includeSubDomains"
        enabled: true
      - name: "api_headers"
        path_pattern: "^/api/.*"
        add:
          X-API-Version: "2.0"
          X-Response-Time: "${timestamp}"
        enabled: true
    body_rules:
      - name: "add_timestamp"
        path_pattern: "^/api/.*"
        content_type: "application/json"
        action: "transform"
        json_path: "$.timestamp"
        json_value: "${timestamp}"
        enabled: true
      - name: "error_message_rewrite"
        path_pattern: ".*"
        status_code: 500
        action: "replace"
        pattern: "Internal Server Error"
        replacement: "服务暂时不可用，请稍后重试"
        enabled: true
    status_rules:
      - name: "maintenance_mode"
        path_pattern: "^/admin/.*"
        original_code: 200
        new_code: 503
        enabled: false

  # IP过滤插件
  ip_filter:
    enabled: true
    mode: "blacklist"  # whitelist, blacklist, both
    whitelist:
      - "***********/24"
      - "10.0.0.0/8"
      - "**********/12"
    blacklist:
      - "*************"
      - "*********/32"
    trusted_proxies:
      - "127.0.0.1/8"
      - "10.0.0.0/8"
      - "**********/12"
      - "***********/16"
    header_name: "X-Forwarded-For"
    skip_paths: 
      - "/health"
      - "/metrics"
      - "/public/.*"

  # 请求大小限制插件
  size_limit:
    enabled: true
    default_max_size: "10MB"
    header_max_size: "1MB"
    check_headers: true
    skip_paths: ["/health", "/metrics"]
    rules:
      - name: "upload_size_limit"
        path_pattern: "^/upload/.*"
        methods: ["POST", "PUT"]
        max_size: "100MB"
        content_types: 
          - "multipart/form-data"
          - "application/octet-stream"
        enabled: true
      - name: "api_size_limit"
        path_pattern: "^/api/.*"
        methods: ["POST", "PUT", "PATCH"]
        max_size: "5MB"
        content_types: ["application/json"]
        enabled: true
      - name: "strict_size_limit"
        path_pattern: "^/auth/.*"
        max_size: "1KB"
        enabled: true

  # 熔断器插件
  circuit_breaker:
    enabled: true
    global_enabled: true
    default_config:
      name: "default"
      failure_threshold: 5
      success_threshold: 3
      timeout: "60s"
      max_requests: 10
      interval: "60s"
      min_requests: 10
      failure_rate: 0.5
      slow_call_threshold: "5s"
      slow_call_rate: 0.5
      enabled: true
    rules:
      - name: "api_circuit_breaker"
        path_pattern: "^/api/.*"
        failure_threshold: 10
        success_threshold: 5
        timeout: "30s"
        max_requests: 20
        interval: "60s"
        min_requests: 20
        failure_rate: 0.6
        slow_call_threshold: "3s"
        slow_call_rate: 0.4
        enabled: true
      - name: "external_service_breaker"
        path_pattern: "^/external/.*"
        failure_threshold: 3
        success_threshold: 2
        timeout: "120s"
        max_requests: 5
        interval: "30s"
        min_requests: 5
        failure_rate: 0.3
        slow_call_threshold: "10s"
        slow_call_rate: 0.3
        enabled: true
      - name: "critical_service_breaker"
        path_pattern: "^/critical/.*"
        failure_threshold: 2
        success_threshold: 3
        timeout: "300s"
        max_requests: 3
        interval: "60s"
        min_requests: 3
        failure_rate: 0.2
        slow_call_threshold: "2s"
        slow_call_rate: 0.2
        enabled: true

# 全局插件配置
plugin_config:
  # 插件执行超时时间
  execution_timeout: "30s"
  
  # 插件错误处理策略
  error_handling: "continue"  # continue, stop, skip
  
  # 插件统计配置
  stats:
    enabled: true
    interval: "60s"
    retention: "24h"
  
  # 插件健康检查配置
  health_check:
    enabled: true
    interval: "30s"
    timeout: "5s"
  
  # 插件日志配置
  logging:
    level: "info"
    format: "json"
    include_request_id: true
    include_execution_time: true
