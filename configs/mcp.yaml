# MCP (Model Context Protocol) 转换功能配置文件
# 此配置文件定义了 API 到 MCP 服务转换的规则和设置

mcp:
  # 是否启用 MCP 转换功能
  enabled: true
  
  # MCP 协议版本
  protocol_version: "2024-11-05"
  
  # 转换规则配置
  conversion_rules:
    # 用户管理 API 转换规则
    - path: "/api/v1/users"
      methods: ["GET", "POST"]
      mcp_method: "users.list"
      enabled: true
      priority: 1
      description: "用户列表和创建用户的 API 转换"
      parameter_mapping:
        query:
          page: "pagination.page"
          limit: "pagination.limit"
          search: "filter.search"
          sort: "sort.field"
          order: "sort.direction"
        body:
          name: "user.name"
          email: "user.email"
          phone: "user.phone"
          role: "user.role"
          active: "user.active"
        headers:
          "X-Request-ID": "request.id"
          "X-User-Agent": "client.user_agent"
      response_mapping:
        success:
          data: "result.users"
          total: "result.total"
          page: "result.pagination.page"
        headers:
          "X-Total-Count": "X-MCP-Total-Count"
      error_mapping:
        status_code_mapping:
          400: -32602  # Invalid params
          401: -32001  # Unauthorized
          403: -32002  # Forbidden
          404: -32601  # Method not found
          500: -32603  # Internal error
        default_error_code: -32603
        default_error_message: "服务器内部错误"
    
    # 单个用户操作 API 转换规则
    - path: "/api/v1/users/{id}"
      methods: ["GET", "PUT", "DELETE"]
      mcp_method: "users.get"
      enabled: true
      priority: 2
      description: "单个用户的获取、更新和删除操作"
      parameter_mapping:
        path:
          id: "user.id"
        body:
          name: "user.name"
          email: "user.email"
          phone: "user.phone"
          role: "user.role"
          active: "user.active"
      response_mapping:
        success:
          data: "result.user"
      error_mapping:
        status_code_mapping:
          400: -32602
          404: -32601
          500: -32603
    
    # 产品管理 API 转换规则
    - path: "/api/v1/products"
      methods: ["GET", "POST"]
      mcp_method: "products.list"
      enabled: true
      priority: 1
      description: "产品列表和创建产品的 API 转换"
      parameter_mapping:
        query:
          page: "pagination.page"
          limit: "pagination.limit"
          category: "filter.category"
          price_min: "filter.price.min"
          price_max: "filter.price.max"
          available: "filter.available"
        body:
          name: "product.name"
          description: "product.description"
          price: "product.price"
          category: "product.category"
          stock: "product.stock"
          available: "product.available"
      response_mapping:
        success:
          data: "result.products"
          total: "result.total"
    
    # 订单管理 API 转换规则
    - path: "/api/v1/orders"
      methods: ["GET", "POST"]
      mcp_method: "orders.list"
      enabled: true
      priority: 1
      description: "订单列表和创建订单的 API 转换"
      parameter_mapping:
        query:
          page: "pagination.page"
          limit: "pagination.limit"
          status: "filter.status"
          user_id: "filter.user_id"
          date_from: "filter.date_range.from"
          date_to: "filter.date_range.to"
        body:
          user_id: "order.user_id"
          items: "order.items"
          total_amount: "order.total_amount"
          shipping_address: "order.shipping_address"
          payment_method: "order.payment_method"
      response_mapping:
        success:
          data: "result.orders"
          total: "result.total"
    
    # 认证相关 API 转换规则
    - path: "/api/v1/auth/login"
      methods: ["POST"]
      mcp_method: "auth.login"
      enabled: true
      priority: 1
      description: "用户登录认证"
      parameter_mapping:
        body:
          username: "credentials.username"
          password: "credentials.password"
          remember_me: "options.remember_me"
      response_mapping:
        success:
          token: "result.access_token"
          refresh_token: "result.refresh_token"
          expires_in: "result.expires_in"
          user: "result.user_info"
    
    # 文件上传 API 转换规则
    - path: "/api/v1/upload"
      methods: ["POST"]
      mcp_method: "files.upload"
      enabled: true
      priority: 1
      description: "文件上传处理"
      parameter_mapping:
        headers:
          "Content-Type": "upload.content_type"
          "Content-Length": "upload.content_length"
        body:
          filename: "file.name"
          description: "file.description"
          category: "file.category"
      response_mapping:
        success:
          file_id: "result.file_id"
          url: "result.file_url"
          size: "result.file_size"
  
  # 服务器配置
  server:
    name: "API Gateway MCP Server"
    version: "1.0.0"
    description: "API 网关 MCP 转换服务器，提供 REST API 到 MCP 协议的无侵入式转换"
    capabilities:
      resources: true
      tools: true
      prompts: true
      logging: true
      sampling: false
    info:
      author: "API Gateway Team"
      license: "MIT"
      documentation: "https://docs.example.com/mcp"
  
  # 传输配置
  transport:
    type: "sse"  # 支持 stdio, sse, custom
    
    # stdio 传输配置
    stdio:
      enabled: false
      buffer_size: 4096
      timeout: 30s
    
    # SSE (Server-Sent Events) 传输配置
    sse:
      enabled: true
      endpoint: "/mcp/sse"
      post_endpoint: "/mcp/message"
      connection_timeout: 30s
      heartbeat_interval: 30s
      max_connections: 100
      cors:
        allowed_origins: ["*"]
        allowed_methods: ["GET", "POST", "OPTIONS"]
        allowed_headers: ["Content-Type", "Authorization", "X-Request-ID"]
        allow_credentials: false
        max_age: 3600
    
    # 自定义传输配置
    custom:
      enabled: false
      # 这里可以添加自定义传输的配置参数
  
  # 性能配置
  performance:
    max_concurrent_conversions: 100
    conversion_timeout: 10s
    cache:
      enabled: true
      type: "memory"  # memory, redis
      ttl: 5m
      max_size: 1000
      # Redis 配置（当类型为 redis 时）
      redis:
        address: "localhost:6379"
        password: ""
        database: 0
        dial_timeout: 5s
        read_timeout: 3s
        write_timeout: 3s
    connection_pool:
      max_idle_connections: 10
      max_active_connections: 100
      idle_timeout: 5m
      max_lifetime: 1h
  
  # 日志配置
  logging:
    enabled: true
    level: "info"  # debug, info, warn, error, fatal
    log_requests: true
    log_responses: true
    log_conversions: true
    log_performance: true

# 环境变量示例（可以在配置中使用 ${VAR_NAME} 格式）
# MCP_ENABLED=true
# MCP_PROTOCOL_VERSION=2024-11-05
# MCP_SERVER_NAME="API Gateway MCP Server"
# MCP_SSE_ENDPOINT=/mcp/sse
# MCP_LOG_LEVEL=info
