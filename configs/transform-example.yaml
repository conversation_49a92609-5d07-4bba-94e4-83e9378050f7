# 完整请求响应转换系统配置示例
# API Gateway Complete Request Response Transform Configuration Example

# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 指标配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090

# 完整转换配置
transform:
  # 是否启用转换功能
  enabled: true
  
  # 请求转换配置
  request:
    # 是否启用请求转换
    enabled: true
    
    # 头部转换规则
    headers:
      # 添加头部
      add:
        "X-Gateway-Version": "1.0.0"
        "X-Request-Time": "${timestamp}"
        "X-Forwarded-By": "api-gateway"
      
      # 删除头部
      remove:
        - "X-Internal-Token"
        - "X-Debug-Info"
      
      # 重命名头部
      rename:
        "Authorization": "X-Auth-Token"
        "User-Agent": "X-User-Agent"
      
      # 替换头部值
      replace:
        "Content-Type": "application/json; charset=utf-8"
      
      # 条件转换规则
      rules:
        - condition:
            type: "path"
            key: ""
            value: "/api/v1/*"
            operator: "contains"
          action:
            type: "add"
            target: "X-API-Version"
            value: "v1"
    
    # 查询参数转换规则
    query:
      # 添加参数
      add:
        "source": "api-gateway"
        "timestamp": "${timestamp}"
      
      # 删除参数
      remove:
        - "debug"
        - "internal"
      
      # 重命名参数
      rename:
        "user_id": "userId"
        "page_size": "pageSize"
      
      # 替换参数值
      replace:
        "format": "json"
    
    # 请求体转换规则
    body:
      # 转换类型：json, xml, form, template, jsonpath
      type: "json"
      
      # JSON转换规则
      json:
        # 添加字段
        add:
          "metadata":
            "gateway": "api-gateway"
            "version": "1.0.0"
            "timestamp": "${timestamp}"
          "request_id": "${request_id}"
        
        # 删除字段
        remove:
          - "password"
          - "secret"
          - "internal_data"
        
        # 重命名字段
        rename:
          "user_id": "userId"
          "user_name": "userName"
          "created_at": "createdAt"
          "updated_at": "updatedAt"
        
        # 替换字段值
        replace:
          "status": "active"
          "source": "api"
        
        # 转换规则
        transform:
          - path: "user.profile.age"
            operation: "transform"
            value: "${user.profile.birthdate | age}"
            condition:
              type: "exists"
              key: "user.profile.birthdate"
          
          - path: "user.permissions"
            operation: "add"
            value: ["read", "write"]
            condition:
              type: "path"
              key: "user.role"
              value: "admin"
              operator: "eq"
      
      # JSONPath转换规则
      jsonpath:
        - path: "$.user.profile"
          operation: "move"
          target_path: "$.profile"
        
        - path: "$.items[*].price"
          operation: "transform"
          value: "${value * 1.1}"  # 价格加10%
        
        - path: "$.metadata.internal"
          operation: "remove"
    
    # 路径转换规则
    path:
      # 前缀添加
      add_prefix: "/v1"
      
      # 前缀删除
      remove_prefix: "/legacy"
      
      # 重写规则
      rewrite:
        - pattern: "^/api/users/([0-9]+)$"
          replacement: "/api/v1/users/$1"
          condition:
            type: "method"
            value: "GET"
            operator: "eq"
        
        - pattern: "^/old-api/(.*)$"
          replacement: "/api/v2/$1"
    
    # 方法转换规则
    method:
      # 方法映射
      mapping:
        "PATCH": "PUT"
        "HEAD": "GET"
      
      # 条件转换规则
      rules:
        - from: "POST"
          to: "PUT"
          condition:
            type: "path"
            value: "/api/v1/users/*"
            operator: "contains"
  
  # 响应转换配置
  response:
    # 是否启用响应转换
    enabled: true
    
    # 头部转换规则
    headers:
      # 添加头部
      add:
        "X-Gateway-Response": "processed"
        "X-Response-Time": "${response_time}"
        "Cache-Control": "public, max-age=300"
      
      # 删除头部
      remove:
        - "X-Internal-Server"
        - "X-Debug-Trace"
      
      # 重命名头部
      rename:
        "X-Rate-Limit": "X-RateLimit-Remaining"
    
    # 响应体转换规则
    body:
      type: "json"
      
      json:
        # 添加字段
        add:
          "meta":
            "gateway": "api-gateway"
            "processed_at": "${timestamp}"
            "request_id": "${request_id}"
        
        # 删除字段
        remove:
          - "internal_id"
          - "debug_info"
        
        # 重命名字段
        rename:
          "user_id": "userId"
          "created_at": "createdAt"
        
        # 转换规则
        transform:
          - path: "data.items"
            operation: "transform"
            value: "${data.items | sort_by('name')}"
          
          - path: "pagination"
            operation: "add"
            value:
              "page": "${query.page || 1}"
              "size": "${query.size || 10}"
              "total": "${data.total_count}"
    
    # 状态码转换规则
    status:
      # 状态码映射
      mapping:
        422: 400  # 将422转换为400
        501: 500  # 将501转换为500
      
      # 条件转换规则
      rules:
        - from: 404
          to: 200
          condition:
            type: "path"
            value: "/api/v1/optional/*"
            operator: "contains"
    
    # 错误处理配置
    error:
      # 是否启用错误转换
      enabled: true
      
      # 错误格式：json, xml, plain
      format: "json"
      
      # 错误模板
      template: |
        {
          "error": {
            "code": "${status_code}",
            "message": "${error_message}",
            "details": "${error_details}",
            "request_id": "${request_id}",
            "timestamp": "${timestamp}"
          }
        }
      
      # 错误映射规则
      rules:
        - status_code: 400
          message: "请求参数无效"
          code: "INVALID_REQUEST"
          fields:
            "type": "validation_error"
        
        - status_code: 401
          message: "身份验证失败"
          code: "AUTHENTICATION_FAILED"
        
        - status_code: 403
          message: "权限不足"
          code: "PERMISSION_DENIED"
        
        - status_code: 404
          message: "资源未找到"
          code: "RESOURCE_NOT_FOUND"
        
        - status_code: 500
          message: "内部服务器错误"
          code: "INTERNAL_ERROR"
  
  # 协议转换配置
  protocol:
    # 是否启用协议转换
    enabled: true
    
    # REST到gRPC转换
    rest_to_grpc:
      # 是否启用
      enabled: true
      
      # 默认超时
      default_timeout: "30s"
      
      # 服务映射
      services:
        - rest_path: "/api/v1/users/{id}"
          grpc_service: "user.UserService"
          grpc_method: "GetUser"
          parameter_mapping:
            "id": "user_id"
          request_mapping: "request"
          response_mapping: "user"
        
        - rest_path: "/api/v1/users"
          grpc_service: "user.UserService"
          grpc_method: "CreateUser"
          parameter_mapping:
            "name": "user_name"
            "email": "user_email"
        
        - rest_path: "/api/v1/orders/{order_id}/items"
          grpc_service: "order.OrderService"
          grpc_method: "GetOrderItems"
          parameter_mapping:
            "order_id": "order_id"
    
    # gRPC到REST转换
    grpc_to_rest:
      # 是否启用
      enabled: true
      
      # 路径前缀
      path_prefix: "/grpc"
      
      # 服务映射
      services:
        - grpc_service: "user.UserService"
          rest_path: "/grpc/users"
          http_method: "POST"
          parameter_mapping:
            "user_id": "id"
        
        - grpc_service: "order.OrderService"
          rest_path: "/grpc/orders"
          http_method: "POST"
    
    # WebSocket转换
    websocket:
      # 是否启用
      enabled: true
      
      # 消息转换规则
      message_rules:
        - message_type: "text"
          template: |
            {
              "type": "message",
              "data": "${message}",
              "timestamp": "${timestamp}",
              "gateway": "api-gateway"
            }
        
        - message_type: "binary"
          template: "base64:${message | base64}"
  
  # GraphQL转换配置
  graphql:
    # 是否启用
    enabled: true
    
    # GraphQL端点
    endpoint: "/graphql"
    
    # Schema文件路径
    schema_path: "schemas/schema.graphql"
    
    # REST到GraphQL转换
    rest_to_graphql:
      # 查询映射
      query_mappings:
        - rest_path: "/api/v1/users/{id}"
          graphql_query: |
            query GetUser($id: ID!) {
              user(id: $id) {
                id
                name
                email
                profile {
                  age
                  city
                }
              }
            }
          variable_mapping:
            "id": "id"
        
        - rest_path: "/api/v1/users"
          graphql_query: |
            query ListUsers($limit: Int, $offset: Int) {
              users(limit: $limit, offset: $offset) {
                id
                name
                email
              }
            }
          variable_mapping:
            "limit": "limit"
            "offset": "offset"
      
      # 变更映射
      mutation_mappings:
        - rest_path: "/api/v1/users"
          http_method: "POST"
          graphql_mutation: |
            mutation CreateUser($input: CreateUserInput!) {
              createUser(input: $input) {
                id
                name
                email
              }
            }
          variable_mapping:
            "name": "input.name"
            "email": "input.email"
        
        - rest_path: "/api/v1/users/{id}"
          http_method: "PUT"
          graphql_mutation: |
            mutation UpdateUser($id: ID!, $input: UpdateUserInput!) {
              updateUser(id: $id, input: $input) {
                id
                name
                email
              }
            }
          variable_mapping:
            "id": "id"
            "name": "input.name"
            "email": "input.email"
    
    # GraphQL到REST转换
    graphql_to_rest:
      # 查询转换规则
      query_rules:
        - operation_name: "GetUser"
          rest_endpoint: "/api/v1/users/{id}"
          http_method: "GET"
          parameter_mapping:
            "id": "id"
        
        - operation_name: "CreateUser"
          rest_endpoint: "/api/v1/users"
          http_method: "POST"
          parameter_mapping:
            "input": "body"
  
  # 数据格式转换配置
  format:
    # 是否启用格式转换
    enabled: true
    
    # JSON到XML转换
    json_to_xml:
      # 根元素名称
      root_element: "response"
      
      # 数组元素名称
      array_element: "item"
      
      # 属性前缀
      attribute_prefix: "@"
      
      # 内容字段名
      content_field: "#text"
    
    # XML到JSON转换
    xml_to_json:
      # 属性前缀
      attribute_prefix: "@"
      
      # 内容字段名
      content_field: "#text"
      
      # 是否保留命名空间
      preserve_namespace: false
    
    # CSV转换
    csv:
      # 分隔符
      delimiter: ","
      
      # 是否包含头部
      has_header: true
      
      # 字段映射
      field_mapping:
        "user_id": "userId"
        "user_name": "userName"
        "created_at": "createdAt"
    
    # YAML转换
    yaml:
      # 缩进大小
      indent: 2
      
      # 是否使用流式格式
      flow_style: false
  
  # 模板配置
  templates:
    # 模板引擎：go, mustache, handlebars
    engine: "go"
    
    # 模板目录
    directory: "templates"
    
    # 预定义模板
    templates:
      "user_response": |
        {
          "user": {
            "id": "{{.user.id}}",
            "name": "{{.user.name}}",
            "email": "{{.user.email}}",
            "profile": {{.user.profile | toJson}}
          },
          "meta": {
            "request_id": "{{.request_id}}",
            "timestamp": "{{.timestamp}}"
          }
        }
      
      "error_response": |
        {
          "error": {
            "code": "{{.error.code}}",
            "message": "{{.error.message}}",
            "details": {{.error.details | toJson}},
            "request_id": "{{.request_id}}"
          }
        }
      
      "list_response": |
        {
          "data": {{.data | toJson}},
          "pagination": {
            "page": {{.pagination.page}},
            "size": {{.pagination.size}},
            "total": {{.pagination.total}}
          },
          "meta": {
            "request_id": "{{.request_id}}",
            "timestamp": "{{.timestamp}}"
          }
        }
    
    # 模板函数
    functions:
      "toJson": "json.Marshal"
      "fromJson": "json.Unmarshal"
      "age": "calculateAge"
      "formatDate": "time.Format"
      "base64": "base64.StdEncoding.EncodeToString"

# 路由配置（转换相关）
routes:
  # JSON转换路由
  - name: "users-api-transform"
    path: "/api/v1/users*"
    method: "*"
    upstream:
      type: "service_discovery"
      service_name: "user-service"
    transform:
      enabled: true
      request:
        headers:
          add:
            "X-Transform": "json"
        body:
          type: "json"
          json:
            add:
              "source": "api-gateway"
            remove:
              - "password"
      response:
        body:
          type: "json"
          json:
            add:
              "processed_by": "api-gateway"
    auth:
      required: true
      methods: ["jwt"]
    timeout: 30s

  # 格式转换路由
  - name: "xml-api-transform"
    path: "/api/xml/*"
    method: "*"
    upstream:
      type: "service_discovery"
      service_name: "legacy-service"
    transform:
      enabled: true
      request:
        headers:
          add:
            "Accept": "application/xml"
      response:
        format: "xml_to_json"
    timeout: 30s

  # GraphQL转换路由
  - name: "graphql-transform"
    path: "/api/graphql/*"
    method: "*"
    upstream:
      type: "graphql"
      endpoint: "http://graphql-service:4000/graphql"
    transform:
      enabled: true
      protocol: "rest_to_graphql"
    timeout: 60s

---
# 转换管理API端点

# 1. 获取转换器列表
# GET /transform/transformers
# 返回所有注册的转换器

# 2. 获取转换器详情
# GET /transform/transformers/{name}
# 返回指定转换器的详细信息

# 3. 获取转换统计
# GET /transform/stats
# 返回转换统计信息

# 4. 测试转换
# POST /transform/test
# Body: {"transformer": "json", "data": {...}}
# 测试指定转换器

# 5. 重新加载配置
# POST /transform/reload
# 重新加载转换配置

---
# 转换使用示例

# 1. JSON字段转换
# 输入: {"user_id": "123", "user_name": "John", "password": "secret"}
# 输出: {"userId": "123", "userName": "John", "source": "api-gateway"}

# 2. 格式转换
# 输入: {"name": "John", "age": 30}
# 输出: <response><name>John</name><age>30</age></response>

# 3. 协议转换
# REST: GET /api/v1/users/123
# gRPC: user.UserService/GetUser {user_id: "123"}

# 4. GraphQL转换
# REST: GET /api/v1/users/123
# GraphQL: query { user(id: "123") { id name email } }

---
# 转换性能优化建议

# 1. JSON转换优化
# - 使用JSONPath进行精确字段操作
# - 避免深度嵌套的转换规则
# - 缓存转换结果

# 2. 格式转换优化
# - 只在必要时进行格式转换
# - 使用流式处理大文件
# - 启用压缩减少传输

# 3. 协议转换优化
# - 复用gRPC连接
# - 使用连接池
# - 设置合适的超时时间

# 4. 模板转换优化
# - 预编译模板
# - 缓存模板结果
# - 使用轻量级模板引擎
