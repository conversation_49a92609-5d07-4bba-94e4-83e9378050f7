# 全平台Serverless集成配置示例
# API Gateway All-Platform Serverless Integration Configuration

# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 指标配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090

# 全平台Serverless配置
serverless:
  # 是否启用Serverless支持
  enabled: true
  
  # ========== 云平台提供者 ==========
  
  # AWS Lambda配置
  aws_lambda:
    enabled: true
    region: "us-east-1"
    access_key_id: "${AWS_ACCESS_KEY_ID}"
    secret_access_key: "${AWS_SECRET_ACCESS_KEY}"
    default_timeout: "30s"
    max_retries: 3
    function_cache_ttl: "5m"
  
  # Azure Functions配置
  azure_functions:
    enabled: false
    subscription_id: "${AZURE_SUBSCRIPTION_ID}"
    resource_group: "my-resource-group"
    function_app_name: "my-function-app"
    client_id: "${AZURE_CLIENT_ID}"
    client_secret: "${AZURE_CLIENT_SECRET}"
    tenant_id: "${AZURE_TENANT_ID}"
    default_timeout: "30s"
  
  # Google Cloud Functions配置
  google_cloud_functions:
    enabled: false
    project_id: "${GCP_PROJECT_ID}"
    region: "us-central1"
    credentials_file: "/path/to/service-account.json"
    default_timeout: "30s"
  
  # ========== 开源/自托管平台 ==========
  
  # OpenFaaS配置（适合Docker环境）
  openfaas:
    enabled: true
    gateway_url: "http://openfaas-gateway:8080"
    username: "${OPENFAAS_USERNAME}"
    password: "${OPENFAAS_PASSWORD}"
    default_timeout: "30s"
  
  # Fn Project配置（Oracle开源）
  fn_project:
    enabled: true
    api_url: "http://fn-api:8080"
    app_name: "myapp"
    token: "${FN_TOKEN}"
    default_timeout: "30s"
  
  # WebAssembly配置（轻量级运行时）
  webassembly:
    enabled: true
    runtime_path: "/usr/local/bin/wasmtime"
    modules_path: "/app/wasm-modules"
    max_instances: 100
    default_timeout: "10s"
  
  # ========== 本地开发平台 ==========
  
  # LocalStack配置（本地AWS模拟）
  localstack:
    enabled: true
    endpoint_url: "http://localstack:4566"
    region: "us-east-1"
    access_key_id: "test"
    secret_key: "test"
    default_timeout: "30s"
  
  # Supabase Functions配置（开源Firebase替代）
  supabase_functions:
    enabled: false
    project_url: "https://your-project.supabase.co"
    anon_key: "${SUPABASE_ANON_KEY}"
    service_key: "${SUPABASE_SERVICE_KEY}"
    default_timeout: "30s"
  
  # Appwrite Functions配置（开源BaaS）
  appwrite_functions:
    enabled: false
    endpoint: "https://appwrite.example.com/v1"
    project_id: "${APPWRITE_PROJECT_ID}"
    api_key: "${APPWRITE_API_KEY}"
    default_timeout: "30s"
  
  # ========== 路由配置 ==========
  routes:
    # AWS Lambda函数
    - name: "aws_user_service"
      path: "/api/v1/users/*"
      methods: ["GET", "POST", "PUT", "DELETE"]
      provider: "aws_lambda"
      function_name: "user-service-handler"
      invocation_type: "sync"
      timeout: "10s"
      enabled: true
      
      auth:
        required: true
        methods: ["jwt"]
      
      cache:
        enabled: true
        ttl: "5m"
        key_template: "aws:{{.function_name}}:{{.path_hash}}"
    
    # OpenFaaS函数（适合Docker环境）
    - name: "openfaas_image_processor"
      path: "/api/v1/images/process"
      methods: ["POST"]
      provider: "openfaas"
      function_name: "image-processor"
      invocation_type: "sync"
      timeout: "60s"
      enabled: true
      
      transform:
        request:
          payload_template: |
            {
              "image_data": "{{.body | base64}}",
              "options": {{.query_params | toJson}}
            }
    
    # Fn Project函数
    - name: "fn_data_processor"
      path: "/api/v1/data/process"
      methods: ["POST"]
      provider: "fn_project"
      function_name: "data-processor"
      invocation_type: "sync"
      timeout: "45s"
      enabled: true
      
      retry_config:
        max_retries: 2
        retry_interval: "2s"
        backoff_strategy: "exponential"
    
    # WebAssembly函数（超轻量级）
    - name: "wasm_calculator"
      path: "/api/v1/calc/*"
      methods: ["GET", "POST"]
      provider: "webassembly"
      function_name: "calculator"
      invocation_type: "sync"
      timeout: "5s"
      enabled: true
      
      cache:
        enabled: true
        ttl: "1h"
        key_template: "wasm:{{.function_name}}:{{.query_hash}}"
    
    # LocalStack函数（本地开发）
    - name: "local_notification"
      path: "/api/v1/notifications"
      methods: ["POST"]
      provider: "localstack"
      function_name: "notification-handler"
      invocation_type: "async"
      timeout: "15s"
      enabled: true
      
      auth:
        required: false  # 本地开发不需要认证
    
    # Supabase Edge Functions
    - name: "supabase_auth"
      path: "/api/v1/auth/*"
      methods: ["POST"]
      provider: "supabase_functions"
      function_name: "auth-handler"
      invocation_type: "sync"
      timeout: "10s"
      enabled: false  # 默认禁用，需要配置
      
      transform:
        request:
          payload_template: |
            {
              "event": "{{.method | lower}}",
              "data": {{.body | fromJson}},
              "headers": {{.headers | toJson}}
            }
    
    # Appwrite Functions
    - name: "appwrite_storage"
      path: "/api/v1/storage/*"
      methods: ["GET", "POST", "DELETE"]
      provider: "appwrite_functions"
      function_name: "storage-handler"
      invocation_type: "sync"
      timeout: "30s"
      enabled: false  # 默认禁用，需要配置
  
  # 监控配置
  monitoring:
    enabled: true
    metrics:
      enabled: true
      prefix: "serverless"
      interval: "30s"
      labels:
        service: "api-gateway"
        component: "serverless"
    
    logging:
      enabled: true
      level: "info"
      log_payload: false
      max_payload_length: 1024
    
    tracing:
      enabled: true
      sample_rate: 0.1
      tags:
        service: "api-gateway"
        component: "serverless"

---
# 不同场景的部署建议

# ========== 场景1: 纯云平台部署 ==========
# 适用于: 大型企业，有云平台账户
# 启用: aws_lambda, azure_functions, google_cloud_functions
# 优势: 高可用、自动扩缩容、按需付费
# 成本: 按使用量付费，适合变化负载

# ========== 场景2: 混合云部署 ==========
# 适用于: 中型企业，部分云化
# 启用: aws_lambda, openfaas, fn_project
# 优势: 灵活性高，可控性强
# 成本: 混合成本模型

# ========== 场景3: 私有化部署 ==========
# 适用于: 对数据安全要求高的企业
# 启用: openfaas, fn_project, webassembly
# 优势: 数据不出企业，完全可控
# 成本: 固定基础设施成本

# ========== 场景4: 开发测试环境 ==========
# 适用于: 本地开发和测试
# 启用: localstack, webassembly, openfaas
# 优势: 快速启动，成本低
# 成本: 几乎零成本

# ========== 场景5: 边缘计算 ==========
# 适用于: IoT、CDN边缘节点
# 启用: webassembly, openfaas
# 优势: 低延迟，轻量级
# 成本: 低资源消耗

# ========== 场景6: 开源优先 ==========
# 适用于: 开源技术栈企业
# 启用: supabase_functions, appwrite_functions, openfaas
# 优势: 避免厂商锁定，社区支持
# 成本: 可控的开源成本

---
# 平台选择指南

# 🏢 企业级生产环境
# 推荐: AWS Lambda + Azure Functions
# 理由: 成熟稳定，企业级支持，丰富的生态

# 🚀 快速原型开发
# 推荐: LocalStack + WebAssembly
# 理由: 快速启动，轻量级，开发友好

# 💰 成本敏感项目
# 推荐: OpenFaaS + Fn Project
# 理由: 开源免费，可自主控制成本

# 🔒 高安全要求
# 推荐: 私有化OpenFaaS + WebAssembly
# 理由: 数据不出企业，完全可控

# ⚡ 高性能要求
# 推荐: WebAssembly + 云平台Lambda
# 理由: WASM高性能，云平台高并发

# 🌐 多云策略
# 推荐: 全平台启用
# 理由: 避免厂商锁定，灾备能力强

---
# 部署最佳实践

# 1. 渐进式迁移
# - 从非关键业务开始
# - 逐步扩展到核心业务
# - 保持传统服务作为备份

# 2. 监控和观测
# - 设置详细的监控指标
# - 配置告警规则
# - 建立日志聚合

# 3. 成本控制
# - 监控各平台使用成本
# - 设置预算告警
# - 定期优化函数配置

# 4. 安全考虑
# - 使用最小权限原则
# - 定期轮换密钥
# - 启用访问日志审计

# 5. 性能优化
# - 选择合适的内存配置
# - 优化函数冷启动时间
# - 使用连接池和缓存
