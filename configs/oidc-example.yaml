# API 网关 OIDC 认证配置示例
# 本配置文件展示了如何配置 OIDC (OpenID Connect) 认证

server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

# 认证配置
auth:
  # JWT 认证配置
  jwt:
    enabled: true
    secret: "your-jwt-secret-key-here"
    algorithm: "HS256"
    expiration: 3600

  # OIDC 认证配置
  oidc:
    enabled: true
    # OIDC 提供者的 Issuer URL (必需)
    issuer: "https://accounts.google.com"
    # 客户端 ID (必需)
    client_id: "your-google-client-id.apps.googleusercontent.com"
    # 客户端密钥 (必需)
    client_secret: "your-google-client-secret"
    # 重定向 URL (必需)
    redirect_url: "http://localhost:8080/auth/callback"
    # 请求的权限范围 (可选，默认为 openid, profile, email)
    scopes:
      - "openid"
      - "profile"
      - "email"
      - "groups"  # 如果提供者支持组信息
    # 是否跳过 TLS 证书验证 (可选，默认为 false，生产环境不建议设为 true)
    skip_verify: false

  # API Key 认证配置
  api_key:
    enabled: true
    header_name: "X-API-Key"
    query_param: "api_key"

  # 策略引擎配置
  policies:
    engine: "builtin"  # 或 "opa"

# 安全配置
security:
  # 限流配置
  rate_limit:
    enabled: true
    algorithm: "token_bucket"
    rules:
      - path: "/api/*"
        method: "*"
        rate: 100
        burst: 200
        window: 60s
        key_by: "ip"

  # CORS 配置
  cors:
    enabled: true
    allowed_origins:
      - "http://localhost:3000"
      - "https://yourdomain.com"
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allowed_headers:
      - "Content-Type"
      - "Authorization"
      - "X-API-Key"
    allow_credentials: true
    max_age: 86400

# 路由配置
routes:
  # 公开路由 - 无需认证
  - name: "public-api"
    path: "/api/v1/public/*"
    method: "*"
    upstream:
      type: "static"
      load_balancer: "round_robin"
      servers:
        - host: "backend1.example.com"
          port: 8081
        - host: "backend2.example.com"
          port: 8081
    timeout: 30s
    retries: 3

  # 受保护路由 - 需要 JWT 或 OIDC 认证
  - name: "protected-api"
    path: "/api/v1/protected/*"
    method: "*"
    upstream:
      type: "static"
      load_balancer: "round_robin"
      servers:
        - host: "backend1.example.com"
          port: 8081
        - host: "backend2.example.com"
          port: 8081
    timeout: 30s
    retries: 3
    # 认证要求
    auth:
      required: true
      methods: ["jwt", "oidc"]  # 支持 JWT 或 OIDC 认证
      roles: ["user", "admin"]  # 需要的角色

  # 管理员路由 - 需要管理员角色
  - name: "admin-api"
    path: "/api/v1/admin/*"
    method: "*"
    upstream:
      type: "static"
      load_balancer: "round_robin"
      servers:
        - host: "admin-backend.example.com"
          port: 8082
    timeout: 30s
    retries: 3
    auth:
      required: true
      methods: ["jwt", "oidc"]
      roles: ["admin"]

  # OIDC 认证回调路由
  - name: "oidc-callback"
    path: "/auth/callback"
    method: "GET"
    upstream:
      type: "static"
      servers:
        - host: "localhost"
          port: 8080
    # 这个路由由网关内部处理，不需要转发到后端

# 插件配置
plugins:
  directory: "plugins"
  plugins:
    # 日志插件
    logger:
      enabled: true
      level: "info"
    
    # 指标插件
    metrics:
      enabled: true
      path: "/metrics"
    
    # JWT 插件
    jwt:
      enabled: true
      paths:
        - "/api/v1/protected/*"
        - "/api/v1/admin/*"
    
    # CORS 插件
    cors:
      enabled: true
      paths:
        - "/api/*"

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 指标配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090

# 链路追踪配置
tracing:
  enabled: true
  service_name: "api-gateway"
  jaeger:
    endpoint: "http://localhost:14268/api/traces"
    sampler_type: "const"
    sampler_param: 1.0

# 服务发现配置
discovery:
  consul:
    enabled: false
    address: "localhost:8500"
    datacenter: "dc1"
  
  nacos:
    enabled: false
    server_configs:
      - ip_addr: "localhost"
        port: 8848

# 管理 API 配置
admin:
  enabled: true
  address: ":8081"
  endpoints:
    - "/admin/health"
    - "/admin/config"
    - "/admin/routes"
    - "/admin/metrics"
