# API Gateway Configuration

server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  tls:
    enabled: false
    cert_file: ""
    key_file: ""
    mtls_mode: "none" # none, request, require

logging:
  level: "info"
  format: "json" # json, text
  output: "stdout" # stdout, file
  file: "logs/gateway.log"
  max_size: 100
  max_backups: 3
  max_age: 28

metrics:
  enabled: true
  path: "/metrics"
  port: 9090

tracing:
  enabled: false
  service_name: "api-gateway"
  endpoint: "http://localhost:14268/api/traces"
  sample_rate: 0.1

auth:
  jwt:
    enabled: true
    secret: "your-jwt-secret-key"
    algorithm: "HS256"
    expiration: 3600
  oidc:
    enabled: false
    issuer: ""
    client_id: ""
    client_secret: ""
    redirect_url: ""
  api_key:
    enabled: true
    header_name: "X-API-Key"
    query_param: "api_key"
  mtls:
    enabled: false
    ca_file: ""
  policies:
    engine: "builtin" # opa, builtin
    opa_path: ""
    policies:
      - name: "admin_access"
        path: "/admin/*"
        method: "*"
        roles: ["admin"]
        permissions: ["admin:read", "admin:write"]
      - name: "user_access"
        path: "/api/v1/*"
        method: "GET"
        roles: ["user", "admin"]
        permissions: ["api:read"]

security:
  rate_limit:
    enabled: true
    algorithm: "token_bucket"
    rules:
      - path: "/api/*"
        method: "*"
        rate: 100
        burst: 200
        window: "1m"
        key_by: "ip"
      - path: "/auth/*"
        method: "POST"
        rate: 10
        burst: 20
        window: "1m"
        key_by: "ip"
  cors:
    enabled: true
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Content-Type", "Authorization", "X-API-Key"]
    exposed_headers: ["X-Request-ID"]
    allow_credentials: true
    max_age: 86400
  waf:
    enabled: true
    rules:
      - name: "sql_injection"
        pattern: "(?i)(union|select|insert|delete|update|drop|create|alter|exec|script)"
        action: "block"
        description: "Block SQL injection attempts"
      - name: "xss_attack"
        pattern: "(?i)(<script|javascript:|on\\w+\\s*=)"
        action: "block"
        description: "Block XSS attacks"
  ip_filter:
    enabled: false
    whitelist: []
    blacklist: []

routes:
  - name: "user_service"
    path: "/api/v1/users/*path"
    method: "*"
    upstream:
      type: "static"
      load_balancer: "round_robin"
      servers:
        - host: "localhost"
          port: 8081
          weight: 1
          backup: false
      health_check:
        enabled: true
        path: "/health"
        interval: "30s"
        timeout: "5s"
        retries: 3
    rewrite:
      enabled: false
      from: ""
      to: ""
    timeout: "30s"
    retries: 3
    headers:
      X-Gateway: "api-gateway"
    plugins: ["auth", "rate_limit", "metrics"]

  - name: "order_service"
    path: "/api/v1/orders/*path"
    method: "*"
    upstream:
      type: "discovery"
      load_balancer: "weighted_round_robin"
      service_name: "order-service"
      health_check:
        enabled: true
        path: "/health"
        interval: "30s"
        timeout: "5s"
        retries: 3
    timeout: "30s"
    retries: 3
    plugins: ["auth", "rate_limit", "metrics"]

plugins:
  directory: "plugins"
  plugins:
    auth:
      enabled: true
      priority: 100
    rate_limit:
      enabled: true
      priority: 200
    metrics:
      enabled: true
      priority: 300
    circuit_breaker:
      enabled: true
      priority: 400
      failure_threshold: 5
      recovery_timeout: "30s"
      timeout: "10s"

discovery:
  type: "consul" # consul, nacos, eureka
  consul:
    address: "localhost:8500"
    datacenter: "dc1"
    token: ""
  nacos:
    server_configs:
      - ip_addr: "localhost"
        port: 8848
    client_config:
      namespace_id: "public"
      timeout_ms: 5000
      not_load_cache_at_start: true
      log_dir: "logs/nacos"
      cache_dir: "cache/nacos"
      log_level: "info"
