# WebSocket完整支持系统配置示例
# API Gateway WebSocket Complete Support Configuration Example

# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 指标配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090

# WebSocket完整配置
websocket:
  # 是否启用WebSocket支持
  enabled: true
  
  # 连接配置
  connection:
    # 读缓冲区大小（字节）
    read_buffer_size: 4096
    
    # 写缓冲区大小（字节）
    write_buffer_size: 4096
    
    # 是否检查Origin头部
    check_origin: true
    
    # 允许的Origin列表
    allowed_origins:
      - "http://localhost:3000"
      - "https://app.example.com"
      - "https://*.example.com"
    
    # 支持的子协议
    subprotocols:
      - "chat"
      - "echo"
      - "json-rpc"
    
    # 握手超时
    handshake_timeout: "10s"
    
    # 读取超时
    read_timeout: "60s"
    
    # 写入超时
    write_timeout: "10s"
    
    # Ping间隔
    ping_interval: "30s"
    
    # Pong超时
    pong_timeout: "10s"
    
    # 最大消息大小（字节）
    max_message_size: 1048576  # 1MB
    
    # 压缩配置
    compression:
      # 是否启用压缩
      enabled: true
      
      # 压缩级别（1-9）
      level: 6
      
      # 压缩阈值（字节）
      threshold: 1024
  
  # 代理配置
  proxy:
    # 是否启用代理
    enabled: true
    
    # 代理模式：transparent（透明）, tunnel（隧道）, transform（转换）
    mode: "transparent"
    
    # 缓冲区大小（字节）
    buffer_size: 8192
    
    # 是否保持连接活跃
    keep_alive: true
    
    # 重连配置
    reconnect:
      # 是否启用自动重连
      enabled: true
      
      # 最大重连次数
      max_retries: 3
      
      # 重连间隔
      interval: "5s"
      
      # 退避策略：fixed（固定）, exponential（指数）, linear（线性）
      backoff_strategy: "exponential"
      
      # 最大退避时间
      max_backoff: "60s"
    
    # 消息过滤
    message_filter:
      # 是否启用过滤
      enabled: true
      
      # 默认动作：allow（允许）, deny（拒绝）
      default_action: "allow"
      
      # 过滤规则
      rules:
        # 阻止大消息
        - name: "block_large_messages"
          condition:
            size_range:
              min: 0
              max: 1048576  # 1MB
          action: "deny"
        
        # 限制文本消息频率
        - name: "rate_limit_text"
          condition:
            message_type: "text"
            rate_limit:
              requests: 100
              window: "1m"
              key: "ip"
          action: "rate_limit"
        
        # 过滤敏感内容
        - name: "filter_sensitive"
          condition:
            content_match:
              type: "regex"
              value: "(password|secret|token)"
              case_sensitive: false
          action: "deny"
  
  # 路由配置
  routes:
    # 聊天室WebSocket
    - name: "chat_room"
      path: "/ws/chat/{room_id}"
      enabled: true
      
      # 上游配置
      upstream:
        type: "service_discovery"
        service_name: "chat-service"
        load_balancer: "round_robin"
        
        # 健康检查
        health_check:
          enabled: true
          interval: "30s"
          timeout: "5s"
          path: "/health"
          message: "ping"
          expected_response: "pong"
        
        # 超时配置
        timeout:
          connect: "10s"
          read: "60s"
          write: "10s"
          idle: "300s"
      
      # 认证配置
      auth:
        required: true
        methods: ["jwt", "api_key"]
        roles: ["user", "admin"]
        permissions: ["chat:read", "chat:write"]
      
      # 中间件
      middlewares:
        - "auth"
        - "rate_limit"
        - "logging"
      
      # 转换配置
      transform:
        enabled: true
        
        # 消息转换规则
        message_rules:
          # JSON消息格式化
          - name: "format_json_message"
            enabled: true
            condition:
              message_type: "text"
              content_match:
                type: "json_path"
                value: "$.type"
            transform:
              type: "content"
              content:
                type: "template"
                template: |
                  {
                    "id": "{{.id}}",
                    "type": "{{.type}}",
                    "data": {{.data | toJson}},
                    "timestamp": "{{.timestamp}}",
                    "room_id": "{{.room_id}}",
                    "user_id": "{{.user_id}}"
                  }
          
          # 添加元数据
          - name: "add_metadata"
            enabled: true
            condition:
              message_type: "text"
            transform:
              type: "content"
              content:
                type: "jsonpath"
                jsonpath:
                  - path: "$.metadata"
                    operation: "add"
                    value:
                      gateway: "api-gateway"
                      processed_at: "{{.timestamp}}"
                      room_id: "{{.room_id}}"
        
        # 连接转换规则
        connection_rules:
          # 路径重写
          - name: "rewrite_path"
            enabled: true
            condition:
              path: "/ws/chat/{room_id}"
            transform:
              path_rewrite: "/chat/room/{{.room_id}}"
              add_headers:
                "X-Gateway": "api-gateway"
                "X-Room-ID": "{{.room_id}}"
    
    # 实时通知WebSocket
    - name: "notifications"
      path: "/ws/notifications"
      enabled: true
      
      upstream:
        type: "static"
        url: "ws://notification-service:8081/ws"
      
      auth:
        required: true
        methods: ["jwt"]
      
      transform:
        enabled: true
        message_rules:
          # 通知消息转换
          - name: "transform_notification"
            enabled: true
            transform:
              type: "format"
              format:
                from: "json"
                to: "json"
                options:
                  add_timestamp: true
                  add_user_id: true
    
    # API实时数据WebSocket
    - name: "realtime_data"
      path: "/ws/data/*"
      enabled: true
      
      upstream:
        type: "service_discovery"
        service_name: "data-service"
        load_balancer: "least_connections"
      
      auth:
        required: true
        methods: ["api_key"]
      
      middlewares:
        - "rate_limit"
        - "compression"
    
    # 调试WebSocket（仅开发环境）
    - name: "debug_echo"
      path: "/ws/debug/echo"
      enabled: false  # 生产环境应禁用
      
      upstream:
        type: "static"
        url: "ws://echo-service:8082/ws"
      
      auth:
        required: false
  
  # 认证配置
  auth:
    # 是否启用认证
    enabled: true
    
    # 认证方法
    methods: ["jwt", "api_key"]
    
    # JWT配置
    jwt:
      enabled: true
      secret: "${JWT_SECRET}"
      algorithm: "HS256"
      token_source: "query"  # query, header, subprotocol
      token_param: "token"
    
    # API Key配置
    api_key:
      enabled: true
      source: "header"  # query, header
      param: "X-API-Key"
      valid_keys:
        - "${API_KEY_1}"
        - "${API_KEY_2}"
    
    # 自定义认证
    custom:
      enabled: false
      url: "http://auth-service:8080/validate"
      timeout: "5s"
      headers:
        "Content-Type": "application/json"
  
  # 监控配置
  monitoring:
    # 是否启用监控
    enabled: true
    
    # 指标收集
    metrics:
      enabled: true
      prefix: "websocket"
      interval: "30s"
      labels:
        service: "api-gateway"
        component: "websocket"
    
    # 日志配置
    logging:
      enabled: true
      level: "info"
      log_messages: false  # 是否记录消息内容
      max_message_length: 1024
    
    # 追踪配置
    tracing:
      enabled: true
      sample_rate: 0.1
      tags:
        service: "api-gateway"
        component: "websocket"
  
  # 负载均衡配置
  load_balancer:
    # 负载均衡策略
    strategy: "round_robin"  # round_robin, least_connections, ip_hash, consistent_hash
    
    # 会话保持
    session_affinity:
      enabled: true
      strategy: "cookie"  # cookie, ip, header
      
      # Cookie配置
      cookie:
        name: "ws_session"
        max_age: 3600
        path: "/ws"
        domain: ".example.com"
        secure: true
        http_only: true
      
      # 头部名称（当strategy为header时）
      header_name: "X-Session-ID"
    
    # 健康检查
    health_check:
      enabled: true
      interval: "30s"
      timeout: "5s"
      path: "/health"
      message: "ping"
      expected_response: "pong"

# 路由配置（WebSocket相关）
routes:
  # WebSocket升级路由
  - name: "websocket-upgrade"
    path: "/ws/*"
    method: "GET"
    upstream:
      type: "websocket"
      websocket_manager: true
    headers:
      required:
        - "Upgrade: websocket"
        - "Connection: Upgrade"
    timeout: 0  # WebSocket连接不设置超时

---
# WebSocket管理API端点

# 1. 获取WebSocket统计
# GET /websocket/stats
# 返回WebSocket连接和消息统计

# 2. 获取活跃连接
# GET /websocket/connections
# 返回当前活跃的WebSocket连接列表

# 3. 广播消息
# POST /websocket/broadcast
# Body: {"message": {...}, "filter": {...}}
# 向匹配条件的连接广播消息

# 4. 关闭连接
# DELETE /websocket/connections/{connection_id}
# 关闭指定的WebSocket连接

# 5. 获取代理统计
# GET /websocket/proxy/stats
# 返回WebSocket代理统计信息

# 6. 健康检查
# GET /websocket/health
# WebSocket服务健康检查

---
# WebSocket使用示例

# 1. 基本WebSocket连接
# ws://localhost:8080/ws/chat/room1?token=jwt_token

# 2. 带认证的WebSocket连接
# ws://localhost:8080/ws/notifications
# Headers: X-API-Key: your_api_key

# 3. 子协议WebSocket连接
# ws://localhost:8080/ws/data/stream
# Sec-WebSocket-Protocol: json-rpc

# 4. 消息格式示例
# {
#   "id": "msg_123",
#   "type": "chat_message",
#   "data": {
#     "text": "Hello, World!",
#     "user": "john_doe"
#   },
#   "timestamp": "2023-01-01T12:00:00Z"
# }

---
# WebSocket性能优化建议

# 1. 连接管理优化
# - 设置合适的连接超时时间
# - 使用连接池管理上游连接
# - 定期清理非活跃连接

# 2. 消息处理优化
# - 启用消息压缩
# - 设置合理的缓冲区大小
# - 使用异步消息处理

# 3. 负载均衡优化
# - 启用会话保持
# - 使用一致性哈希算法
# - 配置健康检查

# 4. 监控和调试
# - 启用详细的指标收集
# - 配置适当的日志级别
# - 使用分布式追踪

---
# WebSocket安全最佳实践

# 1. 认证和授权
# - 始终验证WebSocket连接的身份
# - 使用JWT或API Key进行认证
# - 实施细粒度的权限控制

# 2. 输入验证
# - 验证所有传入的消息
# - 限制消息大小和频率
# - 过滤恶意内容

# 3. 网络安全
# - 使用WSS（WebSocket Secure）
# - 验证Origin头部
# - 实施CORS策略

# 4. 资源保护
# - 设置连接数限制
# - 实施速率限制
# - 监控资源使用情况
