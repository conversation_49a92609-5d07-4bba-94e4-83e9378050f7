// Package main Swagger文档生成工具
//
// API Gateway Swagger Documentation Generator
//
//	@title			API Gateway
//	@version		1.0.0
//	@description	企业级API网关系统，提供认证、授权、限流、负载均衡等功能
//	@termsOfService	https://github.com/your-org/api-gateway
//
//	@contact.name	API Gateway Team
//	@contact.url	https://github.com/your-org/api-gateway
//	@contact.email	<EMAIL>
//
//	@license.name	MIT
//	@license.url	https://opensource.org/licenses/MIT
//
//	@host		localhost:8080
//	@BasePath	/
//
//	@securityDefinitions.apikey	Bearer
//	@in							header
//	@name						Authorization
//	@description				JWT Token认证，格式: Bearer {token}
//
//	@securityDefinitions.apikey	ApiKeyAuth
//	@in							header
//	@name						X-API-Key
//	@description				API Key认证
//
//	@tag.name		健康检查
//	@tag.description	服务健康状态检查相关接口
//
//	@tag.name		管理API
//	@tag.description	网关管理和配置相关接口
//
//	@tag.name		OIDC认证
//	@tag.description	OpenID Connect认证相关接口
//
//	@tag.name		监控
//	@tag.description	监控指标和性能数据接口
//
//	@schemes	http https
//	@produce	json
//	@consumes	json
package main

import (
	"fmt"
	"os"
	"os/exec"
)

func main() {
	fmt.Println("正在生成API文档...")

	// 使用 exec 调用 swag 命令
	cmd := exec.Command("swag", "init",
		"--dir", "./",
		"--generalInfo", "cmd/swagger/main.go",
		"--output", "docs/swagger",
		"--outputTypes", "go,json,yaml",
		"--parseVendor",
		"--parseDependency",
		"--parseInternal",
	)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err := cmd.Run()
	if err != nil {
		fmt.Printf("生成文档失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("API文档生成完成！")
	fmt.Println("文档位置: docs/swagger/")
	fmt.Println("访问地址: http://localhost:8080/swagger/index.html")
}
