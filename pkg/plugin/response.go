package plugin

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"api-gateway/pkg/telemetry"
)

// ResponsePlugin 实现响应内容修改插件
type ResponsePlugin struct {
	logger *telemetry.Logger
	config *ResponseConfig
}

// ResponseConfig 响应修改插件配置
type ResponseConfig struct {
	Enabled      bool                   `json:"enabled" yaml:"enabled"`
	HeaderRules  []ResponseHeaderRule   `json:"header_rules" yaml:"header_rules"`
	BodyRules    []ResponseBodyRule     `json:"body_rules" yaml:"body_rules"`
	StatusRules  []ResponseStatusRule   `json:"status_rules" yaml:"status_rules"`
}

// ResponseHeaderRule 响应头修改规则
type ResponseHeaderRule struct {
	Name        string            `json:"name" yaml:"name"`
	PathPattern string            `json:"path_pattern" yaml:"path_pattern"`
	StatusCode  int               `json:"status_code" yaml:"status_code"` // 可选，匹配特定状态码
	Add         map[string]string `json:"add" yaml:"add"`
	Set         map[string]string `json:"set" yaml:"set"`
	Remove      []string          `json:"remove" yaml:"remove"`
	Enabled     bool              `json:"enabled" yaml:"enabled"`
	pathRegex   *regexp.Regexp
}

// ResponseBodyRule 响应体修改规则
type ResponseBodyRule struct {
	Name         string `json:"name" yaml:"name"`
	PathPattern  string `json:"path_pattern" yaml:"path_pattern"`
	StatusCode   int    `json:"status_code" yaml:"status_code"`
	ContentType  string `json:"content_type" yaml:"content_type"` // 匹配内容类型
	Action       string `json:"action" yaml:"action"`             // replace, append, prepend, transform
	Pattern      string `json:"pattern" yaml:"pattern"`           // 用于replace操作的正则模式
	Replacement  string `json:"replacement" yaml:"replacement"`   // 替换内容
	Template     string `json:"template" yaml:"template"`         // 模板内容
	JSONPath     string `json:"json_path" yaml:"json_path"`       // JSON路径（用于JSON修改）
	JSONValue    interface{} `json:"json_value" yaml:"json_value"` // JSON值
	Enabled      bool   `json:"enabled" yaml:"enabled"`
	pathRegex    *regexp.Regexp
	bodyRegex    *regexp.Regexp
}

// ResponseStatusRule 响应状态码修改规则
type ResponseStatusRule struct {
	Name         string `json:"name" yaml:"name"`
	PathPattern  string `json:"path_pattern" yaml:"path_pattern"`
	OriginalCode int    `json:"original_code" yaml:"original_code"`
	NewCode      int    `json:"new_code" yaml:"new_code"`
	Enabled      bool   `json:"enabled" yaml:"enabled"`
	pathRegex    *regexp.Regexp
}

// NewResponsePlugin 创建新的响应修改插件
func NewResponsePlugin(logger *telemetry.Logger) *ResponsePlugin {
	return &ResponsePlugin{
		logger: logger.With("plugin", "response"),
		config: &ResponseConfig{
			Enabled:     true,
			HeaderRules: []ResponseHeaderRule{},
			BodyRules:   []ResponseBodyRule{},
			StatusRules: []ResponseStatusRule{},
		},
	}
}

func (p *ResponsePlugin) Name() string        { return "response" }
func (p *ResponsePlugin) Version() string     { return "1.0.0" }
func (p *ResponsePlugin) Description() string { return "响应内容修改插件，支持添加、删除、修改响应头和响应体" }
func (p *ResponsePlugin) Priority() int       { return 500 }

func (p *ResponsePlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePostProxy}
}

// Execute 执行响应修改
func (p *ResponsePlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if !p.config.Enabled {
		return &PluginResult{Continue: true}, nil
	}

	// 从元数据中获取响应信息
	statusCode := p.getStatusCodeFromContext(pluginCtx)
	responseHeaders := p.getResponseHeadersFromContext(pluginCtx)
	responseBody := pluginCtx.Body

	// 修改响应状态码
	newStatusCode := p.modifyStatusCode(pluginCtx.Path, statusCode)
	if newStatusCode != statusCode {
		p.logger.Debug("响应状态码已修改",
			"request_id", pluginCtx.RequestID,
			"original_code", statusCode,
			"new_code", newStatusCode)
		statusCode = newStatusCode
	}

	// 修改响应头
	modifiedHeaders := p.modifyHeaders(pluginCtx.Path, statusCode, responseHeaders)

	// 修改响应体
	modifiedBody, err := p.modifyBody(pluginCtx.Path, statusCode, responseHeaders, responseBody)
	if err != nil {
		p.logger.Error("响应体修改失败",
			"request_id", pluginCtx.RequestID,
			"error", err)
		return &PluginResult{Continue: true}, nil
	}

	// 更新插件上下文
	if pluginCtx.Metadata == nil {
		pluginCtx.Metadata = make(map[string]interface{})
	}
	pluginCtx.Metadata["response_status_code"] = statusCode
	pluginCtx.Metadata["response_headers"] = modifiedHeaders
	pluginCtx.Body = modifiedBody

	return &PluginResult{Continue: true}, nil
}

// getStatusCodeFromContext 从上下文获取状态码
func (p *ResponsePlugin) getStatusCodeFromContext(pluginCtx *PluginContext) int {
	if pluginCtx.Metadata != nil {
		if code, ok := pluginCtx.Metadata["response_status_code"].(int); ok {
			return code
		}
	}
	return 200 // 默认状态码
}

// getResponseHeadersFromContext 从上下文获取响应头
func (p *ResponsePlugin) getResponseHeadersFromContext(pluginCtx *PluginContext) map[string]string {
	if pluginCtx.Metadata != nil {
		if headers, ok := pluginCtx.Metadata["response_headers"].(map[string]string); ok {
			return headers
		}
	}
	return make(map[string]string)
}

// modifyStatusCode 修改响应状态码
func (p *ResponsePlugin) modifyStatusCode(path string, originalCode int) int {
	for _, rule := range p.config.StatusRules {
		if !rule.Enabled {
			continue
		}

		// 检查路径匹配
		if rule.pathRegex != nil && !rule.pathRegex.MatchString(path) {
			continue
		}

		// 检查原始状态码匹配
		if rule.OriginalCode != 0 && rule.OriginalCode != originalCode {
			continue
		}

		return rule.NewCode
	}

	return originalCode
}

// modifyHeaders 修改响应头
func (p *ResponsePlugin) modifyHeaders(path string, statusCode int, headers map[string]string) map[string]string {
	result := make(map[string]string)
	
	// 复制原始头部
	for k, v := range headers {
		result[k] = v
	}

	for _, rule := range p.config.HeaderRules {
		if !rule.Enabled {
			continue
		}

		// 检查路径匹配
		if rule.pathRegex != nil && !rule.pathRegex.MatchString(path) {
			continue
		}

		// 检查状态码匹配
		if rule.StatusCode != 0 && rule.StatusCode != statusCode {
			continue
		}

		// 添加头部
		for key, value := range rule.Add {
			if _, exists := result[key]; !exists {
				result[key] = p.expandResponseVariables(value, path, statusCode)
			}
		}

		// 设置头部（覆盖）
		for key, value := range rule.Set {
			result[key] = p.expandResponseVariables(value, path, statusCode)
		}

		// 移除头部
		for _, key := range rule.Remove {
			delete(result, key)
		}
	}

	return result
}

// modifyBody 修改响应体
func (p *ResponsePlugin) modifyBody(path string, statusCode int, headers map[string]string, body []byte) ([]byte, error) {
	result := body

	for _, rule := range p.config.BodyRules {
		if !rule.Enabled {
			continue
		}

		// 检查路径匹配
		if rule.pathRegex != nil && !rule.pathRegex.MatchString(path) {
			continue
		}

		// 检查状态码匹配
		if rule.StatusCode != 0 && rule.StatusCode != statusCode {
			continue
		}

		// 检查内容类型匹配
		if rule.ContentType != "" {
			contentType := headers["Content-Type"]
			if contentType == "" {
				contentType = headers["content-type"]
			}
			if !strings.Contains(contentType, rule.ContentType) {
				continue
			}
		}

		var err error
		result, err = p.applyBodyRule(rule, result, path, statusCode)
		if err != nil {
			return result, err
		}
	}

	return result, nil
}

// applyBodyRule 应用响应体规则
func (p *ResponsePlugin) applyBodyRule(rule ResponseBodyRule, body []byte, path string, statusCode int) ([]byte, error) {
	switch rule.Action {
	case "replace":
		if rule.bodyRegex != nil {
			replacement := p.expandResponseVariables(rule.Replacement, path, statusCode)
			return rule.bodyRegex.ReplaceAll(body, []byte(replacement)), nil
		}
		return body, nil

	case "append":
		content := p.expandResponseVariables(rule.Template, path, statusCode)
		return append(body, []byte(content)...), nil

	case "prepend":
		content := p.expandResponseVariables(rule.Template, path, statusCode)
		return append([]byte(content), body...), nil

	case "transform":
		return p.transformJSONBody(body, rule)

	default:
		return body, fmt.Errorf("不支持的响应体操作: %s", rule.Action)
	}
}

// transformJSONBody 转换JSON响应体
func (p *ResponsePlugin) transformJSONBody(body []byte, rule ResponseBodyRule) ([]byte, error) {
	if len(body) == 0 {
		return body, nil
	}

	var data interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return body, fmt.Errorf("JSON解析失败: %w", err)
	}

	// 简单的JSON路径处理（仅支持顶级字段）
	if rule.JSONPath != "" && rule.JSONValue != nil {
		if dataMap, ok := data.(map[string]interface{}); ok {
			// 移除路径前缀
			path := strings.TrimPrefix(rule.JSONPath, "$.")
			dataMap[path] = rule.JSONValue
		}
	}

	result, err := json.Marshal(data)
	if err != nil {
		return body, fmt.Errorf("JSON序列化失败: %w", err)
	}

	return result, nil
}

// expandResponseVariables 展开响应变量
func (p *ResponsePlugin) expandResponseVariables(template, path string, statusCode int) string {
	result := template

	// 替换内置变量
	result = strings.ReplaceAll(result, "${path}", path)
	result = strings.ReplaceAll(result, "${status_code}", strconv.Itoa(statusCode))
	result = strings.ReplaceAll(result, "${timestamp}", time.Now().Format(time.RFC3339))
	result = strings.ReplaceAll(result, "${unix_timestamp}", strconv.FormatInt(time.Now().Unix(), 10))

	return result
}

// Configure 配置插件
func (p *ResponsePlugin) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		p.config.Enabled = enabled
	}

	// 配置响应头规则
	if headerRules, ok := config["header_rules"].([]interface{}); ok {
		p.config.HeaderRules = make([]ResponseHeaderRule, 0, len(headerRules))
		for _, ruleData := range headerRules {
			if ruleMap, ok := ruleData.(map[string]interface{}); ok {
				rule := ResponseHeaderRule{Enabled: true}
				
				if name, ok := ruleMap["name"].(string); ok {
					rule.Name = name
				}
				if pathPattern, ok := ruleMap["path_pattern"].(string); ok {
					rule.PathPattern = pathPattern
					if regex, err := regexp.Compile(pathPattern); err == nil {
						rule.pathRegex = regex
					}
				}
				if statusCode, ok := ruleMap["status_code"].(int); ok {
					rule.StatusCode = statusCode
				}
				if add, ok := ruleMap["add"].(map[string]interface{}); ok {
					rule.Add = make(map[string]string)
					for k, v := range add {
						if vStr, ok := v.(string); ok {
							rule.Add[k] = vStr
						}
					}
				}
				if set, ok := ruleMap["set"].(map[string]interface{}); ok {
					rule.Set = make(map[string]string)
					for k, v := range set {
						if vStr, ok := v.(string); ok {
							rule.Set[k] = vStr
						}
					}
				}
				if remove, ok := ruleMap["remove"].([]interface{}); ok {
					rule.Remove = make([]string, 0, len(remove))
					for _, item := range remove {
						if itemStr, ok := item.(string); ok {
							rule.Remove = append(rule.Remove, itemStr)
						}
					}
				}
				if enabled, ok := ruleMap["enabled"].(bool); ok {
					rule.Enabled = enabled
				}
				
				p.config.HeaderRules = append(p.config.HeaderRules, rule)
			}
		}
	}

	// 配置响应体规则
	if bodyRules, ok := config["body_rules"].([]interface{}); ok {
		p.config.BodyRules = make([]ResponseBodyRule, 0, len(bodyRules))
		for _, ruleData := range bodyRules {
			if ruleMap, ok := ruleData.(map[string]interface{}); ok {
				rule := ResponseBodyRule{Enabled: true}
				
				if name, ok := ruleMap["name"].(string); ok {
					rule.Name = name
				}
				if pathPattern, ok := ruleMap["path_pattern"].(string); ok {
					rule.PathPattern = pathPattern
					if regex, err := regexp.Compile(pathPattern); err == nil {
						rule.pathRegex = regex
					}
				}
				if statusCode, ok := ruleMap["status_code"].(int); ok {
					rule.StatusCode = statusCode
				}
				if contentType, ok := ruleMap["content_type"].(string); ok {
					rule.ContentType = contentType
				}
				if action, ok := ruleMap["action"].(string); ok {
					rule.Action = action
				}
				if pattern, ok := ruleMap["pattern"].(string); ok {
					rule.Pattern = pattern
					if regex, err := regexp.Compile(pattern); err == nil {
						rule.bodyRegex = regex
					}
				}
				if replacement, ok := ruleMap["replacement"].(string); ok {
					rule.Replacement = replacement
				}
				if template, ok := ruleMap["template"].(string); ok {
					rule.Template = template
				}
				if jsonPath, ok := ruleMap["json_path"].(string); ok {
					rule.JSONPath = jsonPath
				}
				if jsonValue, ok := ruleMap["json_value"]; ok {
					rule.JSONValue = jsonValue
				}
				if enabled, ok := ruleMap["enabled"].(bool); ok {
					rule.Enabled = enabled
				}
				
				p.config.BodyRules = append(p.config.BodyRules, rule)
			}
		}
	}

	// 配置状态码规则
	if statusRules, ok := config["status_rules"].([]interface{}); ok {
		p.config.StatusRules = make([]ResponseStatusRule, 0, len(statusRules))
		for _, ruleData := range statusRules {
			if ruleMap, ok := ruleData.(map[string]interface{}); ok {
				rule := ResponseStatusRule{Enabled: true}
				
				if name, ok := ruleMap["name"].(string); ok {
					rule.Name = name
				}
				if pathPattern, ok := ruleMap["path_pattern"].(string); ok {
					rule.PathPattern = pathPattern
					if regex, err := regexp.Compile(pathPattern); err == nil {
						rule.pathRegex = regex
					}
				}
				if originalCode, ok := ruleMap["original_code"].(int); ok {
					rule.OriginalCode = originalCode
				}
				if newCode, ok := ruleMap["new_code"].(int); ok {
					rule.NewCode = newCode
				}
				if enabled, ok := ruleMap["enabled"].(bool); ok {
					rule.Enabled = enabled
				}
				
				p.config.StatusRules = append(p.config.StatusRules, rule)
			}
		}
	}

	p.logger.Info("响应修改插件配置已更新",
		"enabled", p.config.Enabled,
		"header_rules", len(p.config.HeaderRules),
		"body_rules", len(p.config.BodyRules),
		"status_rules", len(p.config.StatusRules))

	return nil
}

func (p *ResponsePlugin) Start() error {
	p.logger.Info("响应修改插件已启动")
	return nil
}

func (p *ResponsePlugin) Stop() error {
	p.logger.Info("响应修改插件已停止")
	return nil
}

func (p *ResponsePlugin) HealthCheck() error {
	if !p.config.Enabled {
		return fmt.Errorf("响应修改插件未启用")
	}
	return nil
}
