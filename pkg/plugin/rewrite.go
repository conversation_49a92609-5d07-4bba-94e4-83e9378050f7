package plugin

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"api-gateway/pkg/telemetry"
)

// RewritePlugin 实现请求路径和参数重写插件
type RewritePlugin struct {
	logger *telemetry.Logger
	config *RewriteConfig
}

// RewriteConfig 重写插件配置
type RewriteConfig struct {
	Enabled   bool           `json:"enabled" yaml:"enabled"`
	PathRules []PathRewrite  `json:"path_rules" yaml:"path_rules"`
	QueryRules []QueryRewrite `json:"query_rules" yaml:"query_rules"`
	HeaderRules []HeaderRewrite `json:"header_rules" yaml:"header_rules"`
}

// PathRewrite 路径重写规则
type PathRewrite struct {
	Name        string `json:"name" yaml:"name"`
	Pattern     string `json:"pattern" yaml:"pattern"`
	Replacement string `json:"replacement" yaml:"replacement"`
	Method      string `json:"method" yaml:"method"` // 可选，指定HTTP方法
	Enabled     bool   `json:"enabled" yaml:"enabled"`
	regex       *regexp.Regexp
}

// QueryRewrite 查询参数重写规则
type QueryRewrite struct {
	Name        string            `json:"name" yaml:"name"`
	PathPattern string            `json:"path_pattern" yaml:"path_pattern"` // 匹配的路径模式
	Add         map[string]string `json:"add" yaml:"add"`                   // 添加参数
	Remove      []string          `json:"remove" yaml:"remove"`             // 移除参数
	Rename      map[string]string `json:"rename" yaml:"rename"`             // 重命名参数
	Enabled     bool              `json:"enabled" yaml:"enabled"`
	pathRegex   *regexp.Regexp
}

// HeaderRewrite 请求头重写规则
type HeaderRewrite struct {
	Name        string            `json:"name" yaml:"name"`
	PathPattern string            `json:"path_pattern" yaml:"path_pattern"` // 匹配的路径模式
	Add         map[string]string `json:"add" yaml:"add"`                   // 添加头部
	Remove      []string          `json:"remove" yaml:"remove"`             // 移除头部
	Rename      map[string]string `json:"rename" yaml:"rename"`             // 重命名头部
	Set         map[string]string `json:"set" yaml:"set"`                   // 设置头部（覆盖）
	Enabled     bool              `json:"enabled" yaml:"enabled"`
	pathRegex   *regexp.Regexp
}

// NewRewritePlugin 创建新的重写插件
func NewRewritePlugin(logger *telemetry.Logger) *RewritePlugin {
	return &RewritePlugin{
		logger: logger.With("plugin", "rewrite"),
		config: &RewriteConfig{
			Enabled:     true,
			PathRules:   []PathRewrite{},
			QueryRules:  []QueryRewrite{},
			HeaderRules: []HeaderRewrite{},
		},
	}
}

func (p *RewritePlugin) Name() string        { return "rewrite" }
func (p *RewritePlugin) Version() string     { return "1.0.0" }
func (p *RewritePlugin) Description() string { return "请求路径和参数重写插件，支持正则表达式匹配和替换" }
func (p *RewritePlugin) Priority() int       { return 400 }

func (p *RewritePlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreProxy}
}

// Execute 执行重写操作
func (p *RewritePlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if !p.config.Enabled {
		return &PluginResult{Continue: true}, nil
	}

	originalPath := pluginCtx.Path
	originalQuery := pluginCtx.QueryParams
	originalHeaders := pluginCtx.Headers

	// 执行路径重写
	newPath, pathChanged := p.rewritePath(pluginCtx.Path, pluginCtx.Method)
	if pathChanged {
		pluginCtx.Path = newPath
		p.logger.Debug("路径已重写",
			"request_id", pluginCtx.RequestID,
			"original_path", originalPath,
			"new_path", newPath)
	}

	// 执行查询参数重写
	queryChanged := p.rewriteQueryParams(pluginCtx)
	if queryChanged {
		p.logger.Debug("查询参数已重写",
			"request_id", pluginCtx.RequestID,
			"original_query", originalQuery,
			"new_query", pluginCtx.QueryParams)
	}

	// 执行请求头重写
	headerChanged := p.rewriteHeaders(pluginCtx)
	if headerChanged {
		p.logger.Debug("请求头已重写",
			"request_id", pluginCtx.RequestID,
			"original_headers_count", len(originalHeaders),
			"new_headers_count", len(pluginCtx.Headers))
	}

	return &PluginResult{Continue: true}, nil
}

// rewritePath 重写请求路径
func (p *RewritePlugin) rewritePath(path, method string) (string, bool) {
	for _, rule := range p.config.PathRules {
		if !rule.Enabled {
			continue
		}

		// 检查HTTP方法匹配
		if rule.Method != "" && rule.Method != "*" && rule.Method != method {
			continue
		}

		// 检查路径匹配
		if rule.regex != nil {
			if rule.regex.MatchString(path) {
				newPath := rule.regex.ReplaceAllString(path, rule.Replacement)
				return newPath, true
			}
		}
	}

	return path, false
}

// rewriteQueryParams 重写查询参数
func (p *RewritePlugin) rewriteQueryParams(pluginCtx *PluginContext) bool {
	changed := false

	for _, rule := range p.config.QueryRules {
		if !rule.Enabled {
			continue
		}

		// 检查路径匹配
		if rule.pathRegex != nil && !rule.pathRegex.MatchString(pluginCtx.Path) {
			continue
		}

		if pluginCtx.QueryParams == nil {
			pluginCtx.QueryParams = make(map[string]string)
		}

		// 添加参数
		for key, value := range rule.Add {
			// 支持变量替换
			expandedValue := p.expandVariables(value, pluginCtx)
			pluginCtx.QueryParams[key] = expandedValue
			changed = true
		}

		// 移除参数
		for _, key := range rule.Remove {
			if _, exists := pluginCtx.QueryParams[key]; exists {
				delete(pluginCtx.QueryParams, key)
				changed = true
			}
		}

		// 重命名参数
		for oldKey, newKey := range rule.Rename {
			if value, exists := pluginCtx.QueryParams[oldKey]; exists {
				pluginCtx.QueryParams[newKey] = value
				delete(pluginCtx.QueryParams, oldKey)
				changed = true
			}
		}
	}

	return changed
}

// rewriteHeaders 重写请求头
func (p *RewritePlugin) rewriteHeaders(pluginCtx *PluginContext) bool {
	changed := false

	for _, rule := range p.config.HeaderRules {
		if !rule.Enabled {
			continue
		}

		// 检查路径匹配
		if rule.pathRegex != nil && !rule.pathRegex.MatchString(pluginCtx.Path) {
			continue
		}

		if pluginCtx.Headers == nil {
			pluginCtx.Headers = make(map[string]string)
		}

		// 添加头部
		for key, value := range rule.Add {
			// 只有当头部不存在时才添加
			if _, exists := pluginCtx.Headers[key]; !exists {
				expandedValue := p.expandVariables(value, pluginCtx)
				pluginCtx.Headers[key] = expandedValue
				changed = true
			}
		}

		// 设置头部（覆盖）
		for key, value := range rule.Set {
			expandedValue := p.expandVariables(value, pluginCtx)
			pluginCtx.Headers[key] = expandedValue
			changed = true
		}

		// 移除头部
		for _, key := range rule.Remove {
			if _, exists := pluginCtx.Headers[key]; exists {
				delete(pluginCtx.Headers, key)
				changed = true
			}
		}

		// 重命名头部
		for oldKey, newKey := range rule.Rename {
			if value, exists := pluginCtx.Headers[oldKey]; exists {
				pluginCtx.Headers[newKey] = value
				delete(pluginCtx.Headers, oldKey)
				changed = true
			}
		}
	}

	return changed
}

// expandVariables 展开变量
func (p *RewritePlugin) expandVariables(template string, pluginCtx *PluginContext) string {
	result := template

	// 替换内置变量
	result = strings.ReplaceAll(result, "${request_id}", pluginCtx.RequestID)
	result = strings.ReplaceAll(result, "${client_ip}", pluginCtx.ClientIP)
	result = strings.ReplaceAll(result, "${method}", pluginCtx.Method)
	result = strings.ReplaceAll(result, "${path}", pluginCtx.Path)
	result = strings.ReplaceAll(result, "${user_id}", pluginCtx.UserID)

	// 替换查询参数变量
	for key, value := range pluginCtx.QueryParams {
		placeholder := fmt.Sprintf("${query.%s}", key)
		result = strings.ReplaceAll(result, placeholder, value)
	}

	// 替换请求头变量
	for key, value := range pluginCtx.Headers {
		placeholder := fmt.Sprintf("${header.%s}", strings.ToLower(key))
		result = strings.ReplaceAll(result, placeholder, value)
	}

	// 替换元数据变量
	if pluginCtx.Metadata != nil {
		for key, value := range pluginCtx.Metadata {
			placeholder := fmt.Sprintf("${meta.%s}", key)
			result = strings.ReplaceAll(result, placeholder, fmt.Sprintf("%v", value))
		}
	}

	return result
}

// Configure 配置插件
func (p *RewritePlugin) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		p.config.Enabled = enabled
	}

	// 配置路径重写规则
	if pathRules, ok := config["path_rules"].([]interface{}); ok {
		p.config.PathRules = make([]PathRewrite, 0, len(pathRules))
		for _, ruleData := range pathRules {
			if ruleMap, ok := ruleData.(map[string]interface{}); ok {
				rule := PathRewrite{Enabled: true}
				
				if name, ok := ruleMap["name"].(string); ok {
					rule.Name = name
				}
				if pattern, ok := ruleMap["pattern"].(string); ok {
					rule.Pattern = pattern
					// 编译正则表达式
					if regex, err := regexp.Compile(pattern); err == nil {
						rule.regex = regex
					} else {
						p.logger.Error("路径重写规则正则表达式编译失败",
							"name", rule.Name,
							"pattern", pattern,
							"error", err)
						continue
					}
				}
				if replacement, ok := ruleMap["replacement"].(string); ok {
					rule.Replacement = replacement
				}
				if method, ok := ruleMap["method"].(string); ok {
					rule.Method = method
				}
				if enabled, ok := ruleMap["enabled"].(bool); ok {
					rule.Enabled = enabled
				}
				
				p.config.PathRules = append(p.config.PathRules, rule)
			}
		}
	}

	// 配置查询参数重写规则
	if queryRules, ok := config["query_rules"].([]interface{}); ok {
		p.config.QueryRules = make([]QueryRewrite, 0, len(queryRules))
		for _, ruleData := range queryRules {
			if ruleMap, ok := ruleData.(map[string]interface{}); ok {
				rule := QueryRewrite{Enabled: true}
				
				if name, ok := ruleMap["name"].(string); ok {
					rule.Name = name
				}
				if pathPattern, ok := ruleMap["path_pattern"].(string); ok {
					rule.PathPattern = pathPattern
					if regex, err := regexp.Compile(pathPattern); err == nil {
						rule.pathRegex = regex
					}
				}
				if add, ok := ruleMap["add"].(map[string]interface{}); ok {
					rule.Add = make(map[string]string)
					for k, v := range add {
						if vStr, ok := v.(string); ok {
							rule.Add[k] = vStr
						}
					}
				}
				if remove, ok := ruleMap["remove"].([]interface{}); ok {
					rule.Remove = make([]string, 0, len(remove))
					for _, item := range remove {
						if itemStr, ok := item.(string); ok {
							rule.Remove = append(rule.Remove, itemStr)
						}
					}
				}
				if rename, ok := ruleMap["rename"].(map[string]interface{}); ok {
					rule.Rename = make(map[string]string)
					for k, v := range rename {
						if vStr, ok := v.(string); ok {
							rule.Rename[k] = vStr
						}
					}
				}
				if enabled, ok := ruleMap["enabled"].(bool); ok {
					rule.Enabled = enabled
				}
				
				p.config.QueryRules = append(p.config.QueryRules, rule)
			}
		}
	}

	// 配置请求头重写规则
	if headerRules, ok := config["header_rules"].([]interface{}); ok {
		p.config.HeaderRules = make([]HeaderRewrite, 0, len(headerRules))
		for _, ruleData := range headerRules {
			if ruleMap, ok := ruleData.(map[string]interface{}); ok {
				rule := HeaderRewrite{Enabled: true}
				
				if name, ok := ruleMap["name"].(string); ok {
					rule.Name = name
				}
				if pathPattern, ok := ruleMap["path_pattern"].(string); ok {
					rule.PathPattern = pathPattern
					if regex, err := regexp.Compile(pathPattern); err == nil {
						rule.pathRegex = regex
					}
				}
				if add, ok := ruleMap["add"].(map[string]interface{}); ok {
					rule.Add = make(map[string]string)
					for k, v := range add {
						if vStr, ok := v.(string); ok {
							rule.Add[k] = vStr
						}
					}
				}
				if set, ok := ruleMap["set"].(map[string]interface{}); ok {
					rule.Set = make(map[string]string)
					for k, v := range set {
						if vStr, ok := v.(string); ok {
							rule.Set[k] = vStr
						}
					}
				}
				if remove, ok := ruleMap["remove"].([]interface{}); ok {
					rule.Remove = make([]string, 0, len(remove))
					for _, item := range remove {
						if itemStr, ok := item.(string); ok {
							rule.Remove = append(rule.Remove, itemStr)
						}
					}
				}
				if rename, ok := ruleMap["rename"].(map[string]interface{}); ok {
					rule.Rename = make(map[string]string)
					for k, v := range rename {
						if vStr, ok := v.(string); ok {
							rule.Rename[k] = vStr
						}
					}
				}
				if enabled, ok := ruleMap["enabled"].(bool); ok {
					rule.Enabled = enabled
				}
				
				p.config.HeaderRules = append(p.config.HeaderRules, rule)
			}
		}
	}

	p.logger.Info("重写插件配置已更新",
		"enabled", p.config.Enabled,
		"path_rules", len(p.config.PathRules),
		"query_rules", len(p.config.QueryRules),
		"header_rules", len(p.config.HeaderRules))

	return nil
}

func (p *RewritePlugin) Start() error {
	p.logger.Info("重写插件已启动")
	return nil
}

func (p *RewritePlugin) Stop() error {
	p.logger.Info("重写插件已停止")
	return nil
}

func (p *RewritePlugin) HealthCheck() error {
	if !p.config.Enabled {
		return fmt.Errorf("重写插件未启用")
	}
	return nil
}
