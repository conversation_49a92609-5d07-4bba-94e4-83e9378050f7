package plugin

import (
	"context"
	"fmt"
	"regexp"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"
)

// CircuitBreakerPlugin 实现熔断器插件
type CircuitBreakerPlugin struct {
	logger   *telemetry.Logger
	config   *CircuitBreakerConfig
	breakers map[string]*CircuitBreaker
	mu       sync.RWMutex
}

// CircuitBreakerConfig 熔断器插件配置
type CircuitBreakerConfig struct {
	Enabled           bool                    `json:"enabled" yaml:"enabled"`
	DefaultConfig     CircuitBreakerRule     `json:"default_config" yaml:"default_config"`
	Rules             []CircuitBreakerRule   `json:"rules" yaml:"rules"`
	GlobalEnabled     bool                   `json:"global_enabled" yaml:"global_enabled"`
}

// CircuitBreakerRule 熔断器规则
type CircuitBreakerRule struct {
	Name                string        `json:"name" yaml:"name"`
	PathPattern         string        `json:"path_pattern" yaml:"path_pattern"`
	FailureThreshold    int           `json:"failure_threshold" yaml:"failure_threshold"`       // 失败阈值
	SuccessThreshold    int           `json:"success_threshold" yaml:"success_threshold"`       // 成功阈值（半开状态）
	Timeout             time.Duration `json:"timeout" yaml:"timeout"`                           // 超时时间
	MaxRequests         int           `json:"max_requests" yaml:"max_requests"`                 // 半开状态最大请求数
	Interval            time.Duration `json:"interval" yaml:"interval"`                         // 统计间隔
	MinRequests         int           `json:"min_requests" yaml:"min_requests"`                 // 最小请求数
	FailureRate         float64       `json:"failure_rate" yaml:"failure_rate"`                 // 失败率阈值
	SlowCallThreshold   time.Duration `json:"slow_call_threshold" yaml:"slow_call_threshold"`   // 慢调用阈值
	SlowCallRate        float64       `json:"slow_call_rate" yaml:"slow_call_rate"`             // 慢调用率阈值
	Enabled             bool          `json:"enabled" yaml:"enabled"`
	pathRegex           *regexp.Regexp
}

// CircuitBreakerState 熔断器状态
type CircuitBreakerState int

const (
	StateClosed CircuitBreakerState = iota
	StateOpen
	StateHalfOpen
)

func (s CircuitBreakerState) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateOpen:
		return "OPEN"
	case StateHalfOpen:
		return "HALF_OPEN"
	default:
		return "UNKNOWN"
	}
}

// CircuitBreaker 熔断器实现
type CircuitBreaker struct {
	rule         CircuitBreakerRule
	state        CircuitBreakerState
	failures     int
	successes    int
	requests     int
	lastFailTime time.Time
	mu           sync.RWMutex
	
	// 统计窗口
	window       *SlidingWindow
}

// SlidingWindow 滑动窗口统计
type SlidingWindow struct {
	size     time.Duration
	buckets  []WindowBucket
	current  int
	mu       sync.RWMutex
}

// WindowBucket 窗口桶
type WindowBucket struct {
	timestamp    time.Time
	requests     int
	failures     int
	slowCalls    int
	totalTime    time.Duration
}

// NewCircuitBreakerPlugin 创建新的熔断器插件
func NewCircuitBreakerPlugin(logger *telemetry.Logger) *CircuitBreakerPlugin {
	return &CircuitBreakerPlugin{
		logger:   logger.With("plugin", "circuit_breaker"),
		breakers: make(map[string]*CircuitBreaker),
		config: &CircuitBreakerConfig{
			Enabled:       true,
			GlobalEnabled: true,
			DefaultConfig: CircuitBreakerRule{
				Name:                "default",
				FailureThreshold:    5,
				SuccessThreshold:    3,
				Timeout:             time.Minute,
				MaxRequests:         10,
				Interval:            time.Minute,
				MinRequests:         10,
				FailureRate:         0.5,
				SlowCallThreshold:   time.Second * 5,
				SlowCallRate:        0.5,
				Enabled:             true,
			},
		},
	}
}

func (p *CircuitBreakerPlugin) Name() string        { return "circuit_breaker" }
func (p *CircuitBreakerPlugin) Version() string     { return "1.0.0" }
func (p *CircuitBreakerPlugin) Description() string { return "熔断器插件，在后端服务异常时自动熔断保护系统" }
func (p *CircuitBreakerPlugin) Priority() int       { return 600 }

func (p *CircuitBreakerPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreProxy, PhasePostProxy}
}

// Execute 执行熔断器检查
func (p *CircuitBreakerPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if !p.config.Enabled {
		return &PluginResult{Continue: true}, nil
	}

	// 获取适用的熔断器
	breaker := p.getCircuitBreaker(pluginCtx.Path)
	if breaker == nil {
		return &PluginResult{Continue: true}, nil
	}

	switch phase {
	case PhasePreProxy:
		return p.handlePreProxy(breaker, pluginCtx)
	case PhasePostProxy:
		return p.handlePostProxy(breaker, pluginCtx)
	default:
		return &PluginResult{Continue: true}, nil
	}
}

// handlePreProxy 处理代理前阶段
func (p *CircuitBreakerPlugin) handlePreProxy(breaker *CircuitBreaker, pluginCtx *PluginContext) (*PluginResult, error) {
	// 检查熔断器状态
	if !breaker.AllowRequest() {
		p.logger.Warn("熔断器开启，请求被拒绝",
			"request_id", pluginCtx.RequestID,
			"path", pluginCtx.Path,
			"state", breaker.GetState())

		return &PluginResult{
			Continue:   false,
			StatusCode: 503,
			Headers: map[string]string{
				"Content-Type": "application/json",
				"Retry-After":  "60",
			},
			Body: []byte(`{"error":"服务暂时不可用，请稍后重试","code":503}`),
		}, nil
	}

	// 记录请求开始时间
	if pluginCtx.Metadata == nil {
		pluginCtx.Metadata = make(map[string]interface{})
	}
	pluginCtx.Metadata["circuit_breaker_start_time"] = time.Now()

	return &PluginResult{Continue: true}, nil
}

// handlePostProxy 处理代理后阶段
func (p *CircuitBreakerPlugin) handlePostProxy(breaker *CircuitBreaker, pluginCtx *PluginContext) (*PluginResult, error) {
	// 获取请求开始时间
	startTime, ok := pluginCtx.Metadata["circuit_breaker_start_time"].(time.Time)
	if !ok {
		startTime = time.Now()
	}

	duration := time.Since(startTime)
	
	// 获取响应状态码
	statusCode := p.getStatusCodeFromContext(pluginCtx)
	
	// 判断请求是否成功
	isSuccess := statusCode >= 200 && statusCode < 400
	isSlowCall := duration > breaker.rule.SlowCallThreshold

	// 记录请求结果
	breaker.RecordResult(isSuccess, isSlowCall, duration)

	p.logger.Debug("熔断器记录请求结果",
		"request_id", pluginCtx.RequestID,
		"path", pluginCtx.Path,
		"duration", duration,
		"status_code", statusCode,
		"is_success", isSuccess,
		"is_slow_call", isSlowCall,
		"breaker_state", breaker.GetState())

	return &PluginResult{Continue: true}, nil
}

// getStatusCodeFromContext 从上下文获取状态码
func (p *CircuitBreakerPlugin) getStatusCodeFromContext(pluginCtx *PluginContext) int {
	if pluginCtx.Metadata != nil {
		if code, ok := pluginCtx.Metadata["response_status_code"].(int); ok {
			return code
		}
	}
	return 200 // 默认状态码
}

// getCircuitBreaker 获取熔断器
func (p *CircuitBreakerPlugin) getCircuitBreaker(path string) *CircuitBreaker {
	// 查找匹配的规则
	var rule *CircuitBreakerRule
	for _, r := range p.config.Rules {
		if !r.Enabled {
			continue
		}
		if r.pathRegex != nil && r.pathRegex.MatchString(path) {
			rule = &r
			break
		}
	}

	// 如果没有匹配的规则，使用默认配置
	if rule == nil {
		rule = &p.config.DefaultConfig
	}

	key := rule.Name
	if key == "" {
		key = "default"
	}

	p.mu.RLock()
	breaker, exists := p.breakers[key]
	p.mu.RUnlock()

	if exists {
		return breaker
	}

	// 创建新的熔断器
	p.mu.Lock()
	defer p.mu.Unlock()

	// 双重检查
	if breaker, exists := p.breakers[key]; exists {
		return breaker
	}

	breaker = NewCircuitBreaker(*rule)
	p.breakers[key] = breaker

	return breaker
}

// NewCircuitBreaker 创建新的熔断器
func NewCircuitBreaker(rule CircuitBreakerRule) *CircuitBreaker {
	return &CircuitBreaker{
		rule:   rule,
		state:  StateClosed,
		window: NewSlidingWindow(rule.Interval),
	}
}

// NewSlidingWindow 创建新的滑动窗口
func NewSlidingWindow(size time.Duration) *SlidingWindow {
	bucketCount := 10 // 固定10个桶
	return &SlidingWindow{
		size:    size,
		buckets: make([]WindowBucket, bucketCount),
	}
}

// AllowRequest 检查是否允许请求
func (cb *CircuitBreaker) AllowRequest() bool {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	now := time.Now()

	switch cb.state {
	case StateClosed:
		return true
	case StateOpen:
		// 检查是否可以转换到半开状态
		if now.Sub(cb.lastFailTime) > cb.rule.Timeout {
			cb.state = StateHalfOpen
			cb.requests = 0
			cb.successes = 0
			return true
		}
		return false
	case StateHalfOpen:
		// 半开状态下限制请求数量
		return cb.requests < cb.rule.MaxRequests
	default:
		return false
	}
}

// RecordResult 记录请求结果
func (cb *CircuitBreaker) RecordResult(success, slowCall bool, duration time.Duration) {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.requests++
	cb.window.Record(success, slowCall, duration)

	switch cb.state {
	case StateClosed:
		if !success {
			cb.failures++
		}
		cb.checkFailureThreshold()
	case StateHalfOpen:
		if success {
			cb.successes++
			if cb.successes >= cb.rule.SuccessThreshold {
				cb.state = StateClosed
				cb.failures = 0
				cb.requests = 0
			}
		} else {
			cb.state = StateOpen
			cb.lastFailTime = time.Now()
			cb.failures++
		}
	}
}

// checkFailureThreshold 检查失败阈值
func (cb *CircuitBreaker) checkFailureThreshold() {
	stats := cb.window.GetStats()
	
	// 检查最小请求数
	if stats.TotalRequests < cb.rule.MinRequests {
		return
	}

	// 检查失败率
	failureRate := float64(stats.Failures) / float64(stats.TotalRequests)
	if failureRate >= cb.rule.FailureRate {
		cb.state = StateOpen
		cb.lastFailTime = time.Now()
		return
	}

	// 检查慢调用率
	if cb.rule.SlowCallRate > 0 {
		slowCallRate := float64(stats.SlowCalls) / float64(stats.TotalRequests)
		if slowCallRate >= cb.rule.SlowCallRate {
			cb.state = StateOpen
			cb.lastFailTime = time.Now()
		}
	}
}

// GetState 获取熔断器状态
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.state
}

// WindowStats 窗口统计信息
type WindowStats struct {
	TotalRequests int
	Failures      int
	SlowCalls     int
	AvgDuration   time.Duration
}

// Record 记录统计信息
func (sw *SlidingWindow) Record(success, slowCall bool, duration time.Duration) {
	sw.mu.Lock()
	defer sw.mu.Unlock()

	now := time.Now()
	bucketIndex := int(now.Unix()/int64(sw.size.Seconds()*0.1)) % len(sw.buckets)
	
	bucket := &sw.buckets[bucketIndex]
	
	// 如果桶太旧，重置它
	if now.Sub(bucket.timestamp) > sw.size {
		bucket.timestamp = now
		bucket.requests = 0
		bucket.failures = 0
		bucket.slowCalls = 0
		bucket.totalTime = 0
	}

	bucket.requests++
	bucket.totalTime += duration
	
	if !success {
		bucket.failures++
	}
	
	if slowCall {
		bucket.slowCalls++
	}
}

// GetStats 获取统计信息
func (sw *SlidingWindow) GetStats() WindowStats {
	sw.mu.RLock()
	defer sw.mu.RUnlock()

	now := time.Now()
	stats := WindowStats{}

	for _, bucket := range sw.buckets {
		// 只统计有效的桶
		if now.Sub(bucket.timestamp) <= sw.size {
			stats.TotalRequests += bucket.requests
			stats.Failures += bucket.failures
			stats.SlowCalls += bucket.slowCalls
			
			if bucket.requests > 0 {
				stats.AvgDuration += bucket.totalTime / time.Duration(bucket.requests)
			}
		}
	}

	return stats
}

// Configure 配置插件
func (p *CircuitBreakerPlugin) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		p.config.Enabled = enabled
	}

	if globalEnabled, ok := config["global_enabled"].(bool); ok {
		p.config.GlobalEnabled = globalEnabled
	}

	// 配置默认规则
	if defaultConfig, ok := config["default_config"].(map[string]interface{}); ok {
		p.configureRule(&p.config.DefaultConfig, defaultConfig)
	}

	// 配置规则列表
	if rules, ok := config["rules"].([]interface{}); ok {
		p.config.Rules = make([]CircuitBreakerRule, 0, len(rules))
		for _, ruleData := range rules {
			if ruleMap, ok := ruleData.(map[string]interface{}); ok {
				rule := CircuitBreakerRule{Enabled: true}
				p.configureRule(&rule, ruleMap)
				p.config.Rules = append(p.config.Rules, rule)
			}
		}
	}

	p.logger.Info("熔断器插件配置已更新",
		"enabled", p.config.Enabled,
		"global_enabled", p.config.GlobalEnabled,
		"rules_count", len(p.config.Rules))

	return nil
}

// configureRule 配置单个规则
func (p *CircuitBreakerPlugin) configureRule(rule *CircuitBreakerRule, config map[string]interface{}) {
	if name, ok := config["name"].(string); ok {
		rule.Name = name
	}
	if pathPattern, ok := config["path_pattern"].(string); ok {
		rule.PathPattern = pathPattern
		if regex, err := regexp.Compile(pathPattern); err == nil {
			rule.pathRegex = regex
		}
	}
	if failureThreshold, ok := config["failure_threshold"].(int); ok {
		rule.FailureThreshold = failureThreshold
	}
	if successThreshold, ok := config["success_threshold"].(int); ok {
		rule.SuccessThreshold = successThreshold
	}
	if timeout, ok := config["timeout"].(string); ok {
		if duration, err := time.ParseDuration(timeout); err == nil {
			rule.Timeout = duration
		}
	}
	if maxRequests, ok := config["max_requests"].(int); ok {
		rule.MaxRequests = maxRequests
	}
	if interval, ok := config["interval"].(string); ok {
		if duration, err := time.ParseDuration(interval); err == nil {
			rule.Interval = duration
		}
	}
	if minRequests, ok := config["min_requests"].(int); ok {
		rule.MinRequests = minRequests
	}
	if failureRate, ok := config["failure_rate"].(float64); ok {
		rule.FailureRate = failureRate
	}
	if slowCallThreshold, ok := config["slow_call_threshold"].(string); ok {
		if duration, err := time.ParseDuration(slowCallThreshold); err == nil {
			rule.SlowCallThreshold = duration
		}
	}
	if slowCallRate, ok := config["slow_call_rate"].(float64); ok {
		rule.SlowCallRate = slowCallRate
	}
	if enabled, ok := config["enabled"].(bool); ok {
		rule.Enabled = enabled
	}
}

func (p *CircuitBreakerPlugin) Start() error {
	p.logger.Info("熔断器插件已启动")
	return nil
}

func (p *CircuitBreakerPlugin) Stop() error {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	// 清理所有熔断器
	p.breakers = make(map[string]*CircuitBreaker)
	p.logger.Info("熔断器插件已停止")
	return nil
}

func (p *CircuitBreakerPlugin) HealthCheck() error {
	if !p.config.Enabled {
		return fmt.Errorf("熔断器插件未启用")
	}
	return nil
}
