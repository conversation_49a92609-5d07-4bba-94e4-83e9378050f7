package plugin

import (
	"context"
	"fmt"
	"net"
	"strings"

	"api-gateway/pkg/telemetry"
)

// IPFilterPlugin 实现IP白名单和黑名单过滤插件
type IPFilterPlugin struct {
	logger *telemetry.Logger
	config *IPFilterConfig
}

// IPFilterConfig IP过滤插件配置
type IPFilterConfig struct {
	Enabled       bool       `json:"enabled" yaml:"enabled"`
	Mode          string     `json:"mode" yaml:"mode"` // whitelist, blacklist, both
	Whitelist     []string   `json:"whitelist" yaml:"whitelist"`
	Blacklist     []string   `json:"blacklist" yaml:"blacklist"`
	TrustedProxies []string  `json:"trusted_proxies" yaml:"trusted_proxies"`
	HeaderName    string     `json:"header_name" yaml:"header_name"` // 用于获取真实IP的头部名称
	SkipPaths     []string   `json:"skip_paths" yaml:"skip_paths"`
	GeoFilter     *GeoFilter `json:"geo_filter" yaml:"geo_filter"`
	
	// 编译后的网络列表
	whitelistNets []*net.IPNet
	blacklistNets []*net.IPNet
	trustedNets   []*net.IPNet
}

// GeoFilter 地理位置过滤配置
type GeoFilter struct {
	Enabled         bool     `json:"enabled" yaml:"enabled"`
	AllowedCountries []string `json:"allowed_countries" yaml:"allowed_countries"`
	BlockedCountries []string `json:"blocked_countries" yaml:"blocked_countries"`
	DatabasePath    string   `json:"database_path" yaml:"database_path"`
}

// NewIPFilterPlugin 创建新的IP过滤插件
func NewIPFilterPlugin(logger *telemetry.Logger) *IPFilterPlugin {
	return &IPFilterPlugin{
		logger: logger.With("plugin", "ip_filter"),
		config: &IPFilterConfig{
			Enabled:        true,
			Mode:          "blacklist",
			Whitelist:     []string{},
			Blacklist:     []string{},
			TrustedProxies: []string{"127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"},
			HeaderName:    "X-Forwarded-For",
			SkipPaths:     []string{"/health", "/metrics"},
		},
	}
}

func (p *IPFilterPlugin) Name() string        { return "ip_filter" }
func (p *IPFilterPlugin) Version() string     { return "1.0.0" }
func (p *IPFilterPlugin) Description() string { return "IP白名单和黑名单过滤插件，支持CIDR格式和地理位置过滤" }
func (p *IPFilterPlugin) Priority() int       { return 150 }

func (p *IPFilterPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreAuth}
}

// Execute 执行IP过滤检查
func (p *IPFilterPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if !p.config.Enabled {
		return &PluginResult{Continue: true}, nil
	}

	// 检查是否跳过路径
	if p.shouldSkipPath(pluginCtx.Path) {
		return &PluginResult{Continue: true}, nil
	}

	// 获取真实客户端IP
	clientIP := p.getRealClientIP(pluginCtx)
	if clientIP == "" {
		p.logger.Warn("无法获取客户端IP地址",
			"request_id", pluginCtx.RequestID,
			"path", pluginCtx.Path)
		return &PluginResult{Continue: true}, nil
	}

	// 解析IP地址
	ip := net.ParseIP(clientIP)
	if ip == nil {
		p.logger.Warn("无效的IP地址格式",
			"request_id", pluginCtx.RequestID,
			"client_ip", clientIP)
		return &PluginResult{Continue: true}, nil
	}

	// 执行IP过滤检查
	allowed, reason := p.checkIPAllowed(ip, clientIP)
	if !allowed {
		p.logger.Warn("IP访问被拒绝",
			"request_id", pluginCtx.RequestID,
			"client_ip", clientIP,
			"reason", reason,
			"path", pluginCtx.Path)

		return &PluginResult{
			Continue:   false,
			StatusCode: 403,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte(fmt.Sprintf(`{"error":"IP访问被拒绝: %s","code":403}`, reason)),
		}, nil
	}

	p.logger.Debug("IP访问检查通过",
		"request_id", pluginCtx.RequestID,
		"client_ip", clientIP)

	return &PluginResult{Continue: true}, nil
}

// shouldSkipPath 检查是否应该跳过路径
func (p *IPFilterPlugin) shouldSkipPath(path string) bool {
	for _, skipPath := range p.config.SkipPaths {
		if p.matchPath(skipPath, path) {
			return true
		}
	}
	return false
}

// matchPath 路径匹配（支持通配符）
func (p *IPFilterPlugin) matchPath(pattern, path string) bool {
	if pattern == path {
		return true
	}
	
	// 支持简单的通配符匹配
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(path, prefix)
	}
	
	return false
}

// getRealClientIP 获取真实客户端IP
func (p *IPFilterPlugin) getRealClientIP(pluginCtx *PluginContext) string {
	// 首先尝试从指定的头部获取IP
	if p.config.HeaderName != "" {
		if forwardedIP := pluginCtx.Headers[p.config.HeaderName]; forwardedIP != "" {
			// 处理多个IP的情况（取第一个）
			ips := strings.Split(forwardedIP, ",")
			if len(ips) > 0 {
				ip := strings.TrimSpace(ips[0])
				if p.isValidIP(ip) && !p.isTrustedProxy(ip) {
					return ip
				}
			}
		}
	}

	// 尝试其他常见的头部
	headers := []string{
		"X-Real-IP",
		"X-Forwarded-For",
		"CF-Connecting-IP", // Cloudflare
		"True-Client-IP",   // Akamai
		"X-Client-IP",
	}

	for _, header := range headers {
		if ip := pluginCtx.Headers[header]; ip != "" {
			ips := strings.Split(ip, ",")
			for _, singleIP := range ips {
				cleanIP := strings.TrimSpace(singleIP)
				if p.isValidIP(cleanIP) && !p.isTrustedProxy(cleanIP) {
					return cleanIP
				}
			}
		}
	}

	// 最后使用直连IP
	return pluginCtx.ClientIP
}

// isValidIP 检查IP是否有效
func (p *IPFilterPlugin) isValidIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	return parsedIP != nil && !parsedIP.IsLoopback() && !parsedIP.IsUnspecified()
}

// isTrustedProxy 检查IP是否为受信任的代理
func (p *IPFilterPlugin) isTrustedProxy(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	for _, trustedNet := range p.config.trustedNets {
		if trustedNet.Contains(ip) {
			return true
		}
	}

	return false
}

// checkIPAllowed 检查IP是否被允许访问
func (p *IPFilterPlugin) checkIPAllowed(ip net.IP, ipStr string) (bool, string) {
	switch p.config.Mode {
	case "whitelist":
		return p.checkWhitelist(ip), "IP不在白名单中"
	case "blacklist":
		if p.checkBlacklist(ip) {
			return false, "IP在黑名单中"
		}
		return true, ""
	case "both":
		// 先检查黑名单
		if p.checkBlacklist(ip) {
			return false, "IP在黑名单中"
		}
		// 再检查白名单
		if !p.checkWhitelist(ip) {
			return false, "IP不在白名单中"
		}
		return true, ""
	default:
		return true, ""
	}
}

// checkWhitelist 检查IP是否在白名单中
func (p *IPFilterPlugin) checkWhitelist(ip net.IP) bool {
	if len(p.config.whitelistNets) == 0 {
		return true // 如果没有白名单规则，默认允许
	}

	for _, whiteNet := range p.config.whitelistNets {
		if whiteNet.Contains(ip) {
			return true
		}
	}

	return false
}

// checkBlacklist 检查IP是否在黑名单中
func (p *IPFilterPlugin) checkBlacklist(ip net.IP) bool {
	for _, blackNet := range p.config.blacklistNets {
		if blackNet.Contains(ip) {
			return true
		}
	}

	return false
}

// parseIPList 解析IP列表为网络列表
func (p *IPFilterPlugin) parseIPList(ipList []string) ([]*net.IPNet, error) {
	var networks []*net.IPNet

	for _, ipStr := range ipList {
		ipStr = strings.TrimSpace(ipStr)
		if ipStr == "" {
			continue
		}

		// 如果不包含CIDR表示法，添加默认掩码
		if !strings.Contains(ipStr, "/") {
			ip := net.ParseIP(ipStr)
			if ip == nil {
				p.logger.Warn("无效的IP地址", "ip", ipStr)
				continue
			}
			
			if ip.To4() != nil {
				ipStr += "/32" // IPv4
			} else {
				ipStr += "/128" // IPv6
			}
		}

		_, network, err := net.ParseCIDR(ipStr)
		if err != nil {
			p.logger.Warn("无效的CIDR格式", "cidr", ipStr, "error", err)
			continue
		}

		networks = append(networks, network)
	}

	return networks, nil
}

// Configure 配置插件
func (p *IPFilterPlugin) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		p.config.Enabled = enabled
	}

	if mode, ok := config["mode"].(string); ok {
		p.config.Mode = mode
	}

	if whitelist, ok := config["whitelist"].([]interface{}); ok {
		p.config.Whitelist = make([]string, 0, len(whitelist))
		for _, ip := range whitelist {
			if ipStr, ok := ip.(string); ok {
				p.config.Whitelist = append(p.config.Whitelist, ipStr)
			}
		}
	}

	if blacklist, ok := config["blacklist"].([]interface{}); ok {
		p.config.Blacklist = make([]string, 0, len(blacklist))
		for _, ip := range blacklist {
			if ipStr, ok := ip.(string); ok {
				p.config.Blacklist = append(p.config.Blacklist, ipStr)
			}
		}
	}

	if trustedProxies, ok := config["trusted_proxies"].([]interface{}); ok {
		p.config.TrustedProxies = make([]string, 0, len(trustedProxies))
		for _, proxy := range trustedProxies {
			if proxyStr, ok := proxy.(string); ok {
				p.config.TrustedProxies = append(p.config.TrustedProxies, proxyStr)
			}
		}
	}

	if headerName, ok := config["header_name"].(string); ok {
		p.config.HeaderName = headerName
	}

	if skipPaths, ok := config["skip_paths"].([]interface{}); ok {
		p.config.SkipPaths = make([]string, 0, len(skipPaths))
		for _, path := range skipPaths {
			if pathStr, ok := path.(string); ok {
				p.config.SkipPaths = append(p.config.SkipPaths, pathStr)
			}
		}
	}

	// 解析IP列表
	var err error
	p.config.whitelistNets, err = p.parseIPList(p.config.Whitelist)
	if err != nil {
		return fmt.Errorf("解析白名单失败: %w", err)
	}

	p.config.blacklistNets, err = p.parseIPList(p.config.Blacklist)
	if err != nil {
		return fmt.Errorf("解析黑名单失败: %w", err)
	}

	p.config.trustedNets, err = p.parseIPList(p.config.TrustedProxies)
	if err != nil {
		return fmt.Errorf("解析受信任代理列表失败: %w", err)
	}

	p.logger.Info("IP过滤插件配置已更新",
		"enabled", p.config.Enabled,
		"mode", p.config.Mode,
		"whitelist_count", len(p.config.whitelistNets),
		"blacklist_count", len(p.config.blacklistNets),
		"trusted_proxies_count", len(p.config.trustedNets))

	return nil
}

func (p *IPFilterPlugin) Start() error {
	p.logger.Info("IP过滤插件已启动")
	return nil
}

func (p *IPFilterPlugin) Stop() error {
	p.logger.Info("IP过滤插件已停止")
	return nil
}

func (p *IPFilterPlugin) HealthCheck() error {
	if !p.config.Enabled {
		return fmt.Errorf("IP过滤插件未启用")
	}
	return nil
}
