package plugin

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// PluginPhase represents the execution phase of a plugin
type PluginPhase string

const (
	PhasePreAuth    PluginPhase = "pre_auth"
	PhasePostAuth   PluginPhase = "post_auth"
	PhasePreProxy   PluginPhase = "pre_proxy"
	PhasePostProxy  PluginPhase = "post_proxy"
	PhasePreResponse PluginPhase = "pre_response"
	PhasePostResponse PluginPhase = "post_response"
)

// PluginContext contains context information for plugin execution
type PluginContext struct {
	RequestID   string                 `json:"request_id"`
	UserID      string                 `json:"user_id,omitempty"`
	ClientIP    string                 `json:"client_ip"`
	Path        string                 `json:"path"`
	Method      string                 `json:"method"`
	Headers     map[string]string      `json:"headers"`
	QueryParams map[string]string      `json:"query_params"`
	Body        []byte                 `json:"body,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
	Phase       PluginPhase            `json:"phase"`
}

// PluginResult represents the result of plugin execution
type PluginResult struct {
	Continue    bool                   `json:"continue"`
	StatusCode  int                    `json:"status_code,omitempty"`
	Headers     map[string]string      `json:"headers,omitempty"`
	Body        []byte                 `json:"body,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Error       error                  `json:"error,omitempty"`
	Message     string                 `json:"message,omitempty"`
}

// Plugin interface defines the plugin contract
type Plugin interface {
	Name() string
	Version() string
	Description() string
	Priority() int
	Phases() []PluginPhase
	Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error)
	Configure(config map[string]interface{}) error
	Start() error
	Stop() error
	HealthCheck() error
}

// Manager manages plugin lifecycle and execution
type Manager struct {
	config  config.PluginConfig
	logger  *telemetry.Logger
	metrics *telemetry.Metrics
	
	// Plugin registry
	plugins map[string]Plugin
	mu      sync.RWMutex
	
	// Plugin execution order by phase
	phasePlugins map[PluginPhase][]Plugin
	
	// Plugin statistics
	stats map[string]*PluginStats
	statsMu sync.RWMutex
}

// PluginStats contains plugin execution statistics
type PluginStats struct {
	ExecutionCount int64         `json:"execution_count"`
	ErrorCount     int64         `json:"error_count"`
	TotalDuration  time.Duration `json:"total_duration"`
	AverageDuration time.Duration `json:"average_duration"`
	LastExecution  time.Time     `json:"last_execution"`
	LastError      error         `json:"last_error,omitempty"`
}

// NewManager creates a new plugin manager
func NewManager(cfg config.PluginConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) (*Manager, error) {
	manager := &Manager{
		config:       cfg,
		logger:       logger,
		metrics:      metrics,
		plugins:      make(map[string]Plugin),
		phasePlugins: make(map[PluginPhase][]Plugin),
		stats:        make(map[string]*PluginStats),
	}

	// Initialize built-in plugins
	if err := manager.initializeBuiltinPlugins(); err != nil {
		return nil, fmt.Errorf("failed to initialize built-in plugins: %w", err)
	}

	return manager, nil
}

// initializeBuiltinPlugins initializes built-in plugins
func (m *Manager) initializeBuiltinPlugins() error {
	// Register built-in plugins
	builtinPlugins := []Plugin{
		NewLoggingPlugin(m.logger),
		NewMetricsPlugin(m.metrics),
		NewTransformPlugin(),
		NewValidationPlugin(),
		NewCachePlugin(),
		// 新增的常用插件
		NewRateLimitPlugin(m.logger),
		NewCORSPlugin(m.logger),
		NewJWTPlugin(m.logger),
		NewRewritePlugin(m.logger),
		NewResponsePlugin(m.logger),
		NewIPFilterPlugin(m.logger),
		NewSizeLimitPlugin(m.logger),
		NewCircuitBreakerPlugin(m.logger),
	}

	for _, plugin := range builtinPlugins {
		if err := m.RegisterPlugin(plugin); err != nil {
			return fmt.Errorf("failed to register built-in plugin %s: %w", plugin.Name(), err)
		}
	}

	return nil
}

// RegisterPlugin registers a plugin
func (m *Manager) RegisterPlugin(plugin Plugin) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	name := plugin.Name()
	
	// Check if plugin already exists
	if _, exists := m.plugins[name]; exists {
		return fmt.Errorf("plugin %s already registered", name)
	}

	// Configure plugin
	if pluginConfig, exists := m.config.Plugins[name]; exists {
		if configMap, ok := pluginConfig.(map[string]interface{}); ok {
			if err := plugin.Configure(configMap); err != nil {
				return fmt.Errorf("failed to configure plugin %s: %w", name, err)
			}
		}
	}

	// Start plugin
	if err := plugin.Start(); err != nil {
		return fmt.Errorf("failed to start plugin %s: %w", name, err)
	}

	// Register plugin
	m.plugins[name] = plugin
	m.stats[name] = &PluginStats{}

	// Update phase mappings
	m.updatePhasePlugins()

	m.logger.Info("Registered plugin", 
		"name", name, 
		"version", plugin.Version(),
		"phases", plugin.Phases())

	return nil
}

// UnregisterPlugin unregisters a plugin
func (m *Manager) UnregisterPlugin(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	plugin, exists := m.plugins[name]
	if !exists {
		return fmt.Errorf("plugin %s not found", name)
	}

	// Stop plugin
	if err := plugin.Stop(); err != nil {
		m.logger.Error("Failed to stop plugin", "name", name, "error", err)
	}

	// Remove plugin
	delete(m.plugins, name)
	delete(m.stats, name)

	// Update phase mappings
	m.updatePhasePlugins()

	m.logger.Info("Unregistered plugin", "name", name)
	return nil
}

// updatePhasePlugins updates the phase-to-plugin mappings
func (m *Manager) updatePhasePlugins() {
	// Clear existing mappings
	m.phasePlugins = make(map[PluginPhase][]Plugin)

	// Build new mappings
	for _, plugin := range m.plugins {
		for _, phase := range plugin.Phases() {
			m.phasePlugins[phase] = append(m.phasePlugins[phase], plugin)
		}
	}

	// Sort plugins by priority within each phase
	for phase := range m.phasePlugins {
		sort.Slice(m.phasePlugins[phase], func(i, j int) bool {
			return m.phasePlugins[phase][i].Priority() < m.phasePlugins[phase][j].Priority()
		})
	}
}

// ExecutePhase executes all plugins for a specific phase
func (m *Manager) ExecutePhase(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	m.mu.RLock()
	plugins := m.phasePlugins[phase]
	m.mu.RUnlock()

	pluginCtx.Phase = phase

	for _, plugin := range plugins {
		start := time.Now()
		
		result, err := plugin.Execute(ctx, phase, pluginCtx)
		
		duration := time.Since(start)
		
		// Update statistics
		m.updatePluginStats(plugin.Name(), duration, err)
		
		// Record metrics
		if m.metrics != nil {
			m.metrics.RecordPluginDuration(plugin.Name(), string(phase), duration)
			if err != nil {
				m.metrics.RecordPluginError(plugin.Name(), "execution_error")
			}
		}

		if err != nil {
			m.logger.Error("Plugin execution failed",
				"plugin", plugin.Name(),
				"phase", phase,
				"error", err)
			
			// Continue with next plugin unless it's a critical error
			continue
		}

		if result != nil && !result.Continue {
			// Plugin requested to stop execution
			return result, nil
		}

		// Update context with plugin result
		if result != nil {
			if result.Headers != nil {
				for k, v := range result.Headers {
					pluginCtx.Headers[k] = v
				}
			}
			if result.Metadata != nil {
				for k, v := range result.Metadata {
					pluginCtx.Metadata[k] = v
				}
			}
		}
	}

	return &PluginResult{Continue: true}, nil
}

// updatePluginStats updates plugin execution statistics
func (m *Manager) updatePluginStats(pluginName string, duration time.Duration, err error) {
	m.statsMu.Lock()
	defer m.statsMu.Unlock()

	stats, exists := m.stats[pluginName]
	if !exists {
		stats = &PluginStats{}
		m.stats[pluginName] = stats
	}

	stats.ExecutionCount++
	stats.TotalDuration += duration
	stats.AverageDuration = stats.TotalDuration / time.Duration(stats.ExecutionCount)
	stats.LastExecution = time.Now()

	if err != nil {
		stats.ErrorCount++
		stats.LastError = err
	}
}

// GetLoadedPlugins returns information about loaded plugins
func (m *Manager) GetLoadedPlugins() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	plugins := make(map[string]interface{})
	
	for name, plugin := range m.plugins {
		plugins[name] = map[string]interface{}{
			"name":        plugin.Name(),
			"version":     plugin.Version(),
			"description": plugin.Description(),
			"priority":    plugin.Priority(),
			"phases":      plugin.Phases(),
		}
	}

	return plugins
}

// GetPluginStats returns plugin execution statistics
func (m *Manager) GetPluginStats() map[string]*PluginStats {
	m.statsMu.RLock()
	defer m.statsMu.RUnlock()

	stats := make(map[string]*PluginStats)
	for name, stat := range m.stats {
		// Create a copy to avoid race conditions
		statCopy := *stat
		stats[name] = &statCopy
	}

	return stats
}

// HealthCheck performs health checks on all plugins
func (m *Manager) HealthCheck() map[string]error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	results := make(map[string]error)
	
	for name, plugin := range m.plugins {
		err := plugin.HealthCheck()
		results[name] = err
		
		if err != nil {
			m.logger.Warn("Plugin health check failed", "plugin", name, "error", err)
		}
	}

	return results
}

// ReloadPlugin reloads a specific plugin
func (m *Manager) ReloadPlugin(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	plugin, exists := m.plugins[name]
	if !exists {
		return fmt.Errorf("plugin %s not found", name)
	}

	// Stop plugin
	if err := plugin.Stop(); err != nil {
		return fmt.Errorf("failed to stop plugin %s: %w", name, err)
	}

	// Reconfigure plugin
	if pluginConfig, exists := m.config.Plugins[name]; exists {
		if configMap, ok := pluginConfig.(map[string]interface{}); ok {
			if err := plugin.Configure(configMap); err != nil {
				return fmt.Errorf("failed to reconfigure plugin %s: %w", name, err)
			}
		}
	}

	// Start plugin
	if err := plugin.Start(); err != nil {
		return fmt.Errorf("failed to restart plugin %s: %w", name, err)
	}

	m.logger.Info("Reloaded plugin", "name", name)
	return nil
}

// UpdateConfig updates the plugin configuration
func (m *Manager) UpdateConfig(cfg config.PluginConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.config = cfg

	// Reconfigure existing plugins
	for name, plugin := range m.plugins {
		if pluginConfig, exists := m.config.Plugins[name]; exists {
			if configMap, ok := pluginConfig.(map[string]interface{}); ok {
				if err := plugin.Configure(configMap); err != nil {
					m.logger.Error("Failed to reconfigure plugin", "name", name, "error", err)
					continue
				}
				m.logger.Info("Reconfigured plugin", "name", name)
			}
		}
	}

	return nil
}

// Close closes the plugin manager and all plugins
func (m *Manager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	for name, plugin := range m.plugins {
		if err := plugin.Stop(); err != nil {
			m.logger.Error("Failed to stop plugin", "name", name, "error", err)
		}
	}

	return nil
}
