package plugin

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"strings"
	"time"

	"api-gateway/pkg/telemetry"

	"github.com/golang-jwt/jwt/v5"
)

// JWTPlugin 实现JWT令牌验证插件
type JWTPlugin struct {
	logger *telemetry.Logger
	config *JWTConfig
}

// JWTConfig JWT插件配置
type JWTConfig struct {
	Enabled       bool              `json:"enabled" yaml:"enabled"`
	Secret        string            `json:"secret" yaml:"secret"`
	Algorithm     string            `json:"algorithm" yaml:"algorithm"` // HS256, HS384, HS512, RS256, RS384, RS512
	PublicKey     string            `json:"public_key" yaml:"public_key"`
	PrivateKey    string            `json:"private_key" yaml:"private_key"`
	TokenHeader   string            `json:"token_header" yaml:"token_header"`
	TokenPrefix   string            `json:"token_prefix" yaml:"token_prefix"`
	SkipPaths     []string          `json:"skip_paths" yaml:"skip_paths"`
	RequiredClaims map[string]string `json:"required_claims" yaml:"required_claims"`
	Issuer        string            `json:"issuer" yaml:"issuer"`
	Audience      string            `json:"audience" yaml:"audience"`
	ExpiryTolerance time.Duration   `json:"expiry_tolerance" yaml:"expiry_tolerance"`
}

// JWTClaims JWT声明结构
type JWTClaims struct {
	UserID   string                 `json:"user_id,omitempty"`
	Username string                 `json:"username,omitempty"`
	Email    string                 `json:"email,omitempty"`
	Roles    []string               `json:"roles,omitempty"`
	Scopes   []string               `json:"scopes,omitempty"`
	Custom   map[string]interface{} `json:"custom,omitempty"`
	jwt.RegisteredClaims
}

// NewJWTPlugin 创建新的JWT插件
func NewJWTPlugin(logger *telemetry.Logger) *JWTPlugin {
	return &JWTPlugin{
		logger: logger.With("plugin", "jwt"),
		config: &JWTConfig{
			Enabled:         true,
			Algorithm:       "HS256",
			TokenHeader:     "Authorization",
			TokenPrefix:     "Bearer ",
			SkipPaths:       []string{"/health", "/metrics", "/login", "/register"},
			RequiredClaims:  make(map[string]string),
			ExpiryTolerance: time.Minute * 5,
		},
	}
}

func (p *JWTPlugin) Name() string        { return "jwt" }
func (p *JWTPlugin) Version() string     { return "1.0.0" }
func (p *JWTPlugin) Description() string { return "JWT令牌验证插件，支持多种签名算法和自定义声明验证" }
func (p *JWTPlugin) Priority() int       { return 300 }

func (p *JWTPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePostAuth}
}

// Execute 执行JWT验证
func (p *JWTPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if !p.config.Enabled {
		return &PluginResult{Continue: true}, nil
	}

	// 检查是否跳过验证
	if p.shouldSkipPath(pluginCtx.Path) {
		p.logger.Debug("跳过JWT验证",
			"request_id", pluginCtx.RequestID,
			"path", pluginCtx.Path)
		return &PluginResult{Continue: true}, nil
	}

	// 提取JWT令牌
	token, err := p.extractToken(pluginCtx)
	if err != nil {
		p.logger.Warn("JWT令牌提取失败",
			"request_id", pluginCtx.RequestID,
			"error", err)
		
		return &PluginResult{
			Continue:   false,
			StatusCode: 401,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte(`{"error":"JWT令牌提取失败","code":401}`),
		}, nil
	}

	// 验证JWT令牌
	claims, err := p.validateToken(token)
	if err != nil {
		p.logger.Warn("JWT令牌验证失败",
			"request_id", pluginCtx.RequestID,
			"error", err)
		
		return &PluginResult{
			Continue:   false,
			StatusCode: 401,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte(fmt.Sprintf(`{"error":"JWT令牌验证失败: %s","code":401}`, err.Error())),
		}, nil
	}

	// 验证自定义声明
	if err := p.validateCustomClaims(claims); err != nil {
		p.logger.Warn("JWT自定义声明验证失败",
			"request_id", pluginCtx.RequestID,
			"error", err)
		
		return &PluginResult{
			Continue:   false,
			StatusCode: 403,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte(fmt.Sprintf(`{"error":"JWT自定义声明验证失败: %s","code":403}`, err.Error())),
		}, nil
	}

	// 将用户信息添加到上下文
	pluginCtx.UserID = claims.UserID
	if pluginCtx.Metadata == nil {
		pluginCtx.Metadata = make(map[string]interface{})
	}
	pluginCtx.Metadata["jwt_claims"] = claims
	pluginCtx.Metadata["username"] = claims.Username
	pluginCtx.Metadata["email"] = claims.Email
	pluginCtx.Metadata["roles"] = claims.Roles
	pluginCtx.Metadata["scopes"] = claims.Scopes

	p.logger.Debug("JWT验证成功",
		"request_id", pluginCtx.RequestID,
		"user_id", claims.UserID,
		"username", claims.Username)

	return &PluginResult{Continue: true}, nil
}

// shouldSkipPath 检查是否应该跳过路径验证
func (p *JWTPlugin) shouldSkipPath(path string) bool {
	for _, skipPath := range p.config.SkipPaths {
		if p.matchPath(skipPath, path) {
			return true
		}
	}
	return false
}

// matchPath 路径匹配（支持通配符）
func (p *JWTPlugin) matchPath(pattern, path string) bool {
	if pattern == path {
		return true
	}
	
	// 支持简单的通配符匹配
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(path, prefix)
	}
	
	return false
}

// extractToken 从请求中提取JWT令牌
func (p *JWTPlugin) extractToken(pluginCtx *PluginContext) (string, error) {
	// 从指定的头部提取令牌
	authHeader := pluginCtx.Headers[p.config.TokenHeader]
	if authHeader == "" {
		// 尝试小写版本
		authHeader = pluginCtx.Headers[strings.ToLower(p.config.TokenHeader)]
	}
	
	if authHeader == "" {
		return "", fmt.Errorf("缺少认证头部: %s", p.config.TokenHeader)
	}

	// 移除前缀
	if p.config.TokenPrefix != "" {
		if !strings.HasPrefix(authHeader, p.config.TokenPrefix) {
			return "", fmt.Errorf("认证头部格式错误，期望前缀: %s", p.config.TokenPrefix)
		}
		authHeader = strings.TrimPrefix(authHeader, p.config.TokenPrefix)
	}

	token := strings.TrimSpace(authHeader)
	if token == "" {
		return "", fmt.Errorf("JWT令牌为空")
	}

	return token, nil
}

// validateToken 验证JWT令牌
func (p *JWTPlugin) validateToken(tokenString string) (*JWTClaims, error) {
	// 解析令牌
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return p.getSigningKey(token)
	})

	if err != nil {
		return nil, fmt.Errorf("令牌解析失败: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("令牌无效")
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return nil, fmt.Errorf("令牌声明类型错误")
	}

	// 验证标准声明
	if err := p.validateStandardClaims(claims); err != nil {
		return nil, err
	}

	return claims, nil
}

// getSigningKey 获取签名密钥
func (p *JWTPlugin) getSigningKey(token *jwt.Token) (interface{}, error) {
	switch p.config.Algorithm {
	case "HS256", "HS384", "HS512":
		// HMAC算法使用密钥
		if p.config.Secret == "" {
			return nil, fmt.Errorf("HMAC算法需要配置密钥")
		}
		return []byte(p.config.Secret), nil
		
	case "RS256", "RS384", "RS512":
		// RSA算法使用公钥
		if p.config.PublicKey == "" {
			return nil, fmt.Errorf("RSA算法需要配置公钥")
		}
		return p.parseRSAPublicKey(p.config.PublicKey)
		
	default:
		return nil, fmt.Errorf("不支持的签名算法: %s", p.config.Algorithm)
	}
}

// parseRSAPublicKey 解析RSA公钥
func (p *JWTPlugin) parseRSAPublicKey(keyData string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(keyData))
	if block == nil {
		return nil, fmt.Errorf("无法解析PEM格式的公钥")
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("解析公钥失败: %w", err)
	}

	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("公钥不是RSA格式")
	}

	return rsaPub, nil
}

// validateStandardClaims 验证标准声明
func (p *JWTPlugin) validateStandardClaims(claims *JWTClaims) error {
	now := time.Now()

	// 验证过期时间
	if claims.ExpiresAt != nil {
		if now.After(claims.ExpiresAt.Time.Add(p.config.ExpiryTolerance)) {
			return fmt.Errorf("令牌已过期")
		}
	}

	// 验证生效时间
	if claims.NotBefore != nil {
		if now.Before(claims.NotBefore.Time) {
			return fmt.Errorf("令牌尚未生效")
		}
	}

	// 验证签发时间
	if claims.IssuedAt != nil {
		if now.Before(claims.IssuedAt.Time) {
			return fmt.Errorf("令牌签发时间无效")
		}
	}

	// 验证签发者
	if p.config.Issuer != "" && claims.Issuer != p.config.Issuer {
		return fmt.Errorf("令牌签发者不匹配")
	}

	// 验证受众
	if p.config.Audience != "" {
		found := false
		for _, aud := range claims.Audience {
			if aud == p.config.Audience {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("令牌受众不匹配")
		}
	}

	return nil
}

// validateCustomClaims 验证自定义声明
func (p *JWTPlugin) validateCustomClaims(claims *JWTClaims) error {
	for claimName, expectedValue := range p.config.RequiredClaims {
		var actualValue interface{}
		
		// 检查标准字段
		switch claimName {
		case "user_id":
			actualValue = claims.UserID
		case "username":
			actualValue = claims.Username
		case "email":
			actualValue = claims.Email
		default:
			// 检查自定义字段
			if claims.Custom != nil {
				actualValue = claims.Custom[claimName]
			}
		}

		if actualValue == nil {
			return fmt.Errorf("缺少必需的声明: %s", claimName)
		}

		if fmt.Sprintf("%v", actualValue) != expectedValue {
			return fmt.Errorf("声明 %s 的值不匹配", claimName)
		}
	}

	return nil
}

// Configure 配置插件
func (p *JWTPlugin) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		p.config.Enabled = enabled
	}

	if secret, ok := config["secret"].(string); ok {
		p.config.Secret = secret
	}

	if algorithm, ok := config["algorithm"].(string); ok {
		p.config.Algorithm = algorithm
	}

	if publicKey, ok := config["public_key"].(string); ok {
		p.config.PublicKey = publicKey
	}

	if privateKey, ok := config["private_key"].(string); ok {
		p.config.PrivateKey = privateKey
	}

	if tokenHeader, ok := config["token_header"].(string); ok {
		p.config.TokenHeader = tokenHeader
	}

	if tokenPrefix, ok := config["token_prefix"].(string); ok {
		p.config.TokenPrefix = tokenPrefix
	}

	if skipPaths, ok := config["skip_paths"].([]interface{}); ok {
		p.config.SkipPaths = make([]string, 0, len(skipPaths))
		for _, path := range skipPaths {
			if pathStr, ok := path.(string); ok {
				p.config.SkipPaths = append(p.config.SkipPaths, pathStr)
			}
		}
	}

	if requiredClaims, ok := config["required_claims"].(map[string]interface{}); ok {
		p.config.RequiredClaims = make(map[string]string)
		for key, value := range requiredClaims {
			if valueStr, ok := value.(string); ok {
				p.config.RequiredClaims[key] = valueStr
			}
		}
	}

	if issuer, ok := config["issuer"].(string); ok {
		p.config.Issuer = issuer
	}

	if audience, ok := config["audience"].(string); ok {
		p.config.Audience = audience
	}

	if expiryTolerance, ok := config["expiry_tolerance"].(string); ok {
		if duration, err := time.ParseDuration(expiryTolerance); err == nil {
			p.config.ExpiryTolerance = duration
		}
	}

	p.logger.Info("JWT插件配置已更新",
		"enabled", p.config.Enabled,
		"algorithm", p.config.Algorithm,
		"skip_paths", len(p.config.SkipPaths),
		"required_claims", len(p.config.RequiredClaims))

	return nil
}

func (p *JWTPlugin) Start() error {
	p.logger.Info("JWT插件已启动")
	return nil
}

func (p *JWTPlugin) Stop() error {
	p.logger.Info("JWT插件已停止")
	return nil
}

func (p *JWTPlugin) HealthCheck() error {
	if !p.config.Enabled {
		return fmt.Errorf("JWT插件未启用")
	}
	
	// 检查必要的配置
	switch p.config.Algorithm {
	case "HS256", "HS384", "HS512":
		if p.config.Secret == "" {
			return fmt.Errorf("HMAC算法需要配置密钥")
		}
	case "RS256", "RS384", "RS512":
		if p.config.PublicKey == "" {
			return fmt.Errorf("RSA算法需要配置公钥")
		}
	}
	
	return nil
}
