package plugin

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"api-gateway/pkg/telemetry"
)

// SizeLimitPlugin 实现请求体大小限制插件
type SizeLimitPlugin struct {
	logger *telemetry.Logger
	config *SizeLimitConfig
}

// SizeLimitConfig 大小限制插件配置
type SizeLimitConfig struct {
	Enabled        bool              `json:"enabled" yaml:"enabled"`
	DefaultMaxSize int64             `json:"default_max_size" yaml:"default_max_size"` // 字节
	Rules          []SizeLimitRule   `json:"rules" yaml:"rules"`
	SkipPaths      []string          `json:"skip_paths" yaml:"skip_paths"`
	CheckHeaders   bool              `json:"check_headers" yaml:"check_headers"`
	HeaderMaxSize  int64             `json:"header_max_size" yaml:"header_max_size"`
}

// SizeLimitRule 大小限制规则
type SizeLimitRule struct {
	Name        string   `json:"name" yaml:"name"`
	PathPattern string   `json:"path_pattern" yaml:"path_pattern"`
	Methods     []string `json:"methods" yaml:"methods"`
	MaxSize     int64    `json:"max_size" yaml:"max_size"` // 字节
	ContentTypes []string `json:"content_types" yaml:"content_types"`
	Enabled     bool     `json:"enabled" yaml:"enabled"`
	pathRegex   *regexp.Regexp
}

// NewSizeLimitPlugin 创建新的大小限制插件
func NewSizeLimitPlugin(logger *telemetry.Logger) *SizeLimitPlugin {
	return &SizeLimitPlugin{
		logger: logger.With("plugin", "size_limit"),
		config: &SizeLimitConfig{
			Enabled:        true,
			DefaultMaxSize: 10 * 1024 * 1024, // 10MB
			Rules:          []SizeLimitRule{},
			SkipPaths:      []string{"/health", "/metrics"},
			CheckHeaders:   true,
			HeaderMaxSize:  1024 * 1024, // 1MB
		},
	}
}

func (p *SizeLimitPlugin) Name() string        { return "size_limit" }
func (p *SizeLimitPlugin) Version() string     { return "1.0.0" }
func (p *SizeLimitPlugin) Description() string { return "请求体大小限制插件，防止大文件上传攻击" }
func (p *SizeLimitPlugin) Priority() int       { return 50 }

func (p *SizeLimitPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreAuth}
}

// Execute 执行大小限制检查
func (p *SizeLimitPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if !p.config.Enabled {
		return &PluginResult{Continue: true}, nil
	}

	// 检查是否跳过路径
	if p.shouldSkipPath(pluginCtx.Path) {
		return &PluginResult{Continue: true}, nil
	}

	// 检查请求头大小
	if p.config.CheckHeaders {
		if err := p.checkHeadersSize(pluginCtx); err != nil {
			p.logger.Warn("请求头大小超限",
				"request_id", pluginCtx.RequestID,
				"error", err)
			
			return &PluginResult{
				Continue:   false,
				StatusCode: 413,
				Headers: map[string]string{
					"Content-Type": "application/json",
				},
				Body: []byte(fmt.Sprintf(`{"error":"请求头大小超限: %s","code":413}`, err.Error())),
			}, nil
		}
	}

	// 检查请求体大小
	if err := p.checkBodySize(pluginCtx); err != nil {
		p.logger.Warn("请求体大小超限",
			"request_id", pluginCtx.RequestID,
			"path", pluginCtx.Path,
			"method", pluginCtx.Method,
			"body_size", len(pluginCtx.Body),
			"error", err)
		
		return &PluginResult{
			Continue:   false,
			StatusCode: 413,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte(fmt.Sprintf(`{"error":"请求体大小超限: %s","code":413}`, err.Error())),
		}, nil
	}

	p.logger.Debug("请求大小检查通过",
		"request_id", pluginCtx.RequestID,
		"body_size", len(pluginCtx.Body))

	return &PluginResult{Continue: true}, nil
}

// shouldSkipPath 检查是否应该跳过路径
func (p *SizeLimitPlugin) shouldSkipPath(path string) bool {
	for _, skipPath := range p.config.SkipPaths {
		if p.matchPath(skipPath, path) {
			return true
		}
	}
	return false
}

// matchPath 路径匹配（支持通配符）
func (p *SizeLimitPlugin) matchPath(pattern, path string) bool {
	if pattern == path {
		return true
	}
	
	// 支持简单的通配符匹配
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(path, prefix)
	}
	
	return false
}

// checkHeadersSize 检查请求头大小
func (p *SizeLimitPlugin) checkHeadersSize(pluginCtx *PluginContext) error {
	if p.config.HeaderMaxSize <= 0 {
		return nil
	}

	totalSize := int64(0)
	
	// 计算所有请求头的总大小
	for key, value := range pluginCtx.Headers {
		totalSize += int64(len(key) + len(value) + 4) // +4 for ": " and "\r\n"
	}

	if totalSize > p.config.HeaderMaxSize {
		return fmt.Errorf("请求头总大小 %s 超过限制 %s", 
			p.formatSize(totalSize), 
			p.formatSize(p.config.HeaderMaxSize))
	}

	return nil
}

// checkBodySize 检查请求体大小
func (p *SizeLimitPlugin) checkBodySize(pluginCtx *PluginContext) error {
	bodySize := int64(len(pluginCtx.Body))
	
	// 获取适用的大小限制
	maxSize := p.getApplicableMaxSize(pluginCtx)
	
	if maxSize > 0 && bodySize > maxSize {
		return fmt.Errorf("请求体大小 %s 超过限制 %s", 
			p.formatSize(bodySize), 
			p.formatSize(maxSize))
	}

	// 检查Content-Length头部（如果存在）
	if contentLengthStr := pluginCtx.Headers["Content-Length"]; contentLengthStr != "" {
		if contentLength, err := strconv.ParseInt(contentLengthStr, 10, 64); err == nil {
			if maxSize > 0 && contentLength > maxSize {
				return fmt.Errorf("Content-Length %s 超过限制 %s", 
					p.formatSize(contentLength), 
					p.formatSize(maxSize))
			}
		}
	}

	return nil
}

// getApplicableMaxSize 获取适用的最大大小限制
func (p *SizeLimitPlugin) getApplicableMaxSize(pluginCtx *PluginContext) int64 {
	// 检查是否有匹配的规则
	for _, rule := range p.config.Rules {
		if !rule.Enabled {
			continue
		}
		
		// 检查路径匹配
		if rule.pathRegex != nil && !rule.pathRegex.MatchString(pluginCtx.Path) {
			continue
		}
		
		// 检查方法匹配
		if len(rule.Methods) > 0 && !p.containsMethod(rule.Methods, pluginCtx.Method) {
			continue
		}
		
		// 检查内容类型匹配
		if len(rule.ContentTypes) > 0 {
			contentType := pluginCtx.Headers["Content-Type"]
			if contentType == "" {
				contentType = pluginCtx.Headers["content-type"]
			}
			if !p.matchesContentType(rule.ContentTypes, contentType) {
				continue
			}
		}
		
		return rule.MaxSize
	}
	
	// 返回默认限制
	return p.config.DefaultMaxSize
}

// containsMethod 检查方法列表是否包含指定方法
func (p *SizeLimitPlugin) containsMethod(methods []string, method string) bool {
	for _, m := range methods {
		if m == "*" || strings.EqualFold(m, method) {
			return true
		}
	}
	return false
}

// matchesContentType 检查内容类型是否匹配
func (p *SizeLimitPlugin) matchesContentType(allowedTypes []string, contentType string) bool {
	if contentType == "" {
		return false
	}
	
	// 提取主要的内容类型（去掉参数）
	mainType := strings.Split(contentType, ";")[0]
	mainType = strings.TrimSpace(mainType)
	
	for _, allowedType := range allowedTypes {
		if allowedType == "*" || 
		   strings.EqualFold(allowedType, mainType) ||
		   strings.EqualFold(allowedType, contentType) {
			return true
		}
		
		// 支持通配符匹配，如 "image/*"
		if strings.HasSuffix(allowedType, "/*") {
			prefix := strings.TrimSuffix(allowedType, "/*")
			if strings.HasPrefix(mainType, prefix+"/") {
				return true
			}
		}
	}
	
	return false
}

// formatSize 格式化大小显示
func (p *SizeLimitPlugin) formatSize(size int64) string {
	const (
		KB = 1024
		MB = KB * 1024
		GB = MB * 1024
	)
	
	switch {
	case size >= GB:
		return fmt.Sprintf("%.2f GB", float64(size)/GB)
	case size >= MB:
		return fmt.Sprintf("%.2f MB", float64(size)/MB)
	case size >= KB:
		return fmt.Sprintf("%.2f KB", float64(size)/KB)
	default:
		return fmt.Sprintf("%d bytes", size)
	}
}

// parseSizeString 解析大小字符串（如 "10MB", "1GB"）
func (p *SizeLimitPlugin) parseSizeString(sizeStr string) (int64, error) {
	sizeStr = strings.TrimSpace(strings.ToUpper(sizeStr))
	
	if sizeStr == "" {
		return 0, fmt.Errorf("空的大小字符串")
	}
	
	// 如果只是数字，默认为字节
	if size, err := strconv.ParseInt(sizeStr, 10, 64); err == nil {
		return size, nil
	}
	
	// 解析带单位的大小
	var multiplier int64 = 1
	var numStr string
	
	switch {
	case strings.HasSuffix(sizeStr, "GB"):
		multiplier = 1024 * 1024 * 1024
		numStr = strings.TrimSuffix(sizeStr, "GB")
	case strings.HasSuffix(sizeStr, "MB"):
		multiplier = 1024 * 1024
		numStr = strings.TrimSuffix(sizeStr, "MB")
	case strings.HasSuffix(sizeStr, "KB"):
		multiplier = 1024
		numStr = strings.TrimSuffix(sizeStr, "KB")
	case strings.HasSuffix(sizeStr, "B"):
		multiplier = 1
		numStr = strings.TrimSuffix(sizeStr, "B")
	default:
		return 0, fmt.Errorf("不支持的大小单位: %s", sizeStr)
	}
	
	num, err := strconv.ParseFloat(numStr, 64)
	if err != nil {
		return 0, fmt.Errorf("无效的数字: %s", numStr)
	}
	
	return int64(num * float64(multiplier)), nil
}

// Configure 配置插件
func (p *SizeLimitPlugin) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		p.config.Enabled = enabled
	}

	if defaultMaxSize, ok := config["default_max_size"]; ok {
		switch v := defaultMaxSize.(type) {
		case int64:
			p.config.DefaultMaxSize = v
		case int:
			p.config.DefaultMaxSize = int64(v)
		case string:
			if size, err := p.parseSizeString(v); err == nil {
				p.config.DefaultMaxSize = size
			}
		}
	}

	if headerMaxSize, ok := config["header_max_size"]; ok {
		switch v := headerMaxSize.(type) {
		case int64:
			p.config.HeaderMaxSize = v
		case int:
			p.config.HeaderMaxSize = int64(v)
		case string:
			if size, err := p.parseSizeString(v); err == nil {
				p.config.HeaderMaxSize = size
			}
		}
	}

	if checkHeaders, ok := config["check_headers"].(bool); ok {
		p.config.CheckHeaders = checkHeaders
	}

	if skipPaths, ok := config["skip_paths"].([]interface{}); ok {
		p.config.SkipPaths = make([]string, 0, len(skipPaths))
		for _, path := range skipPaths {
			if pathStr, ok := path.(string); ok {
				p.config.SkipPaths = append(p.config.SkipPaths, pathStr)
			}
		}
	}

	// 配置规则
	if rules, ok := config["rules"].([]interface{}); ok {
		p.config.Rules = make([]SizeLimitRule, 0, len(rules))
		for _, ruleData := range rules {
			if ruleMap, ok := ruleData.(map[string]interface{}); ok {
				rule := SizeLimitRule{Enabled: true}
				
				if name, ok := ruleMap["name"].(string); ok {
					rule.Name = name
				}
				if pathPattern, ok := ruleMap["path_pattern"].(string); ok {
					rule.PathPattern = pathPattern
					if regex, err := regexp.Compile(pathPattern); err == nil {
						rule.pathRegex = regex
					}
				}
				if methods, ok := ruleMap["methods"].([]interface{}); ok {
					rule.Methods = make([]string, 0, len(methods))
					for _, method := range methods {
						if methodStr, ok := method.(string); ok {
							rule.Methods = append(rule.Methods, methodStr)
						}
					}
				}
				if maxSize, ok := ruleMap["max_size"]; ok {
					switch v := maxSize.(type) {
					case int64:
						rule.MaxSize = v
					case int:
						rule.MaxSize = int64(v)
					case string:
						if size, err := p.parseSizeString(v); err == nil {
							rule.MaxSize = size
						}
					}
				}
				if contentTypes, ok := ruleMap["content_types"].([]interface{}); ok {
					rule.ContentTypes = make([]string, 0, len(contentTypes))
					for _, ct := range contentTypes {
						if ctStr, ok := ct.(string); ok {
							rule.ContentTypes = append(rule.ContentTypes, ctStr)
						}
					}
				}
				if enabled, ok := ruleMap["enabled"].(bool); ok {
					rule.Enabled = enabled
				}
				
				p.config.Rules = append(p.config.Rules, rule)
			}
		}
	}

	p.logger.Info("大小限制插件配置已更新",
		"enabled", p.config.Enabled,
		"default_max_size", p.formatSize(p.config.DefaultMaxSize),
		"header_max_size", p.formatSize(p.config.HeaderMaxSize),
		"rules_count", len(p.config.Rules))

	return nil
}

func (p *SizeLimitPlugin) Start() error {
	p.logger.Info("大小限制插件已启动")
	return nil
}

func (p *SizeLimitPlugin) Stop() error {
	p.logger.Info("大小限制插件已停止")
	return nil
}

func (p *SizeLimitPlugin) HealthCheck() error {
	if !p.config.Enabled {
		return fmt.Errorf("大小限制插件未启用")
	}
	return nil
}
