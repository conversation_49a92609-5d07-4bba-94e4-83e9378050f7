package serverless

import (
	"context"
	"time"
)

// ServerlessProvider Serverless平台提供者接口
type ServerlessProvider interface {
	// 获取提供者名称
	Name() string
	
	// 调用函数
	InvokeFunction(ctx context.Context, request *InvocationRequest) (*InvocationResponse, error)
	
	// 获取函数信息
	GetFunction(ctx context.Context, functionName string) (*FunctionInfo, error)
	
	// 列出所有函数
	ListFunctions(ctx context.Context) ([]*FunctionInfo, error)
	
	// 健康检查
	HealthCheck(ctx context.Context) error
	
	// 获取统计信息
	GetStats() *ProviderStats
}

// ServerlessManager Serverless管理器接口
type ServerlessManager interface {
	// 注册提供者
	RegisterProvider(provider ServerlessProvider) error
	
	// 获取提供者
	GetProvider(name string) (ServerlessProvider, error)
	
	// 调用函数
	InvokeFunction(ctx context.Context, providerName, functionName string, request *InvocationRequest) (*InvocationResponse, error)
	
	// 路由到Serverless函数
	RouteToFunction(ctx context.Context, route *ServerlessRoute, request *InvocationRequest) (*InvocationResponse, error)
	
	// 获取所有提供者
	GetProviders() []ServerlessProvider
	
	// 启动管理器
	Start() error
	
	// 停止管理器
	Stop() error
}

// InvocationRequest 函数调用请求
type InvocationRequest struct {
	// 函数名称
	FunctionName string `json:"function_name"`
	
	// 调用类型：sync（同步）, async（异步）
	InvocationType string `json:"invocation_type"`
	
	// 请求负载
	Payload []byte `json:"payload"`
	
	// 请求头部
	Headers map[string]string `json:"headers"`
	
	// 查询参数
	QueryParams map[string]string `json:"query_params"`
	
	// 路径参数
	PathParams map[string]string `json:"path_params"`
	
	// 上下文信息
	Context map[string]interface{} `json:"context"`
	
	// 超时时间
	Timeout time.Duration `json:"timeout"`
	
	// 重试次数
	RetryCount int `json:"retry_count"`
}

// InvocationResponse 函数调用响应
type InvocationResponse struct {
	// 状态码
	StatusCode int `json:"status_code"`
	
	// 响应负载
	Payload []byte `json:"payload"`
	
	// 响应头部
	Headers map[string]string `json:"headers"`
	
	// 执行时间
	Duration time.Duration `json:"duration"`
	
	// 计费时间
	BilledDuration time.Duration `json:"billed_duration"`
	
	// 内存使用
	MemoryUsed int64 `json:"memory_used"`
	
	// 日志输出
	LogOutput string `json:"log_output"`
	
	// 错误信息
	Error string `json:"error,omitempty"`
	
	// 函数错误
	FunctionError string `json:"function_error,omitempty"`
	
	// 请求ID
	RequestID string `json:"request_id"`
	
	// 执行环境信息
	ExecutionEnv map[string]interface{} `json:"execution_env"`
}

// FunctionInfo 函数信息
type FunctionInfo struct {
	// 函数名称
	Name string `json:"name"`
	
	// 函数ARN
	ARN string `json:"arn"`
	
	// 运行时
	Runtime string `json:"runtime"`
	
	// 处理器
	Handler string `json:"handler"`
	
	// 代码大小
	CodeSize int64 `json:"code_size"`
	
	// 描述
	Description string `json:"description"`
	
	// 超时时间
	Timeout time.Duration `json:"timeout"`
	
	// 内存大小
	MemorySize int64 `json:"memory_size"`
	
	// 最后修改时间
	LastModified time.Time `json:"last_modified"`
	
	// 版本
	Version string `json:"version"`
	
	// 环境变量
	Environment map[string]string `json:"environment"`
	
	// 标签
	Tags map[string]string `json:"tags"`
	
	// 状态
	State string `json:"state"`
	
	// VPC配置
	VPCConfig *VPCConfig `json:"vpc_config,omitempty"`
}

// VPCConfig VPC配置
type VPCConfig struct {
	SubnetIds        []string `json:"subnet_ids"`
	SecurityGroupIds []string `json:"security_group_ids"`
	VpcId            string   `json:"vpc_id"`
}

// ServerlessRoute Serverless路由配置
type ServerlessRoute struct {
	// 路由名称
	Name string `json:"name"`
	
	// 路径匹配
	Path string `json:"path"`
	
	// HTTP方法
	Methods []string `json:"methods"`
	
	// 提供者名称
	Provider string `json:"provider"`
	
	// 函数名称
	FunctionName string `json:"function_name"`
	
	// 调用类型
	InvocationType string `json:"invocation_type"`
	
	// 超时配置
	Timeout time.Duration `json:"timeout"`
	
	// 重试配置
	RetryConfig *RetryConfig `json:"retry_config"`
	
	// 转换配置
	Transform *TransformConfig `json:"transform"`
	
	// 认证配置
	Auth *AuthConfig `json:"auth"`
	
	// 缓存配置
	Cache *CacheConfig `json:"cache"`
	
	// 是否启用
	Enabled bool `json:"enabled"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	// 最大重试次数
	MaxRetries int `json:"max_retries"`
	
	// 重试间隔
	RetryInterval time.Duration `json:"retry_interval"`
	
	// 退避策略
	BackoffStrategy string `json:"backoff_strategy"`
	
	// 最大退避时间
	MaxBackoff time.Duration `json:"max_backoff"`
	
	// 可重试的错误码
	RetryableStatusCodes []int `json:"retryable_status_codes"`
}

// TransformConfig 转换配置
type TransformConfig struct {
	// 请求转换
	Request *RequestTransform `json:"request"`
	
	// 响应转换
	Response *ResponseTransform `json:"response"`
}

// RequestTransform 请求转换
type RequestTransform struct {
	// 头部映射
	HeaderMapping map[string]string `json:"header_mapping"`
	
	// 查询参数映射
	QueryMapping map[string]string `json:"query_mapping"`
	
	// 路径参数映射
	PathMapping map[string]string `json:"path_mapping"`
	
	// 负载转换模板
	PayloadTemplate string `json:"payload_template"`
	
	// 内容类型转换
	ContentType string `json:"content_type"`
}

// ResponseTransform 响应转换
type ResponseTransform struct {
	// 状态码映射
	StatusCodeMapping map[int]int `json:"status_code_mapping"`
	
	// 头部映射
	HeaderMapping map[string]string `json:"header_mapping"`
	
	// 负载转换模板
	PayloadTemplate string `json:"payload_template"`
	
	// 内容类型转换
	ContentType string `json:"content_type"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	// 是否需要认证
	Required bool `json:"required"`
	
	// 认证方法
	Methods []string `json:"methods"`
	
	// 角色要求
	Roles []string `json:"roles"`
	
	// 权限要求
	Permissions []string `json:"permissions"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	// 是否启用缓存
	Enabled bool `json:"enabled"`
	
	// 缓存TTL
	TTL time.Duration `json:"ttl"`
	
	// 缓存键模板
	KeyTemplate string `json:"key_template"`
	
	// 缓存条件
	Conditions []string `json:"conditions"`
}

// ProviderStats 提供者统计
type ProviderStats struct {
	// 提供者名称
	ProviderName string `json:"provider_name"`
	
	// 总调用次数
	TotalInvocations int64 `json:"total_invocations"`
	
	// 成功调用次数
	SuccessfulInvocations int64 `json:"successful_invocations"`
	
	// 失败调用次数
	FailedInvocations int64 `json:"failed_invocations"`
	
	// 平均执行时间
	AvgDuration time.Duration `json:"avg_duration"`
	
	// 总执行时间
	TotalDuration time.Duration `json:"total_duration"`
	
	// 总计费时间
	TotalBilledDuration time.Duration `json:"total_billed_duration"`
	
	// 平均内存使用
	AvgMemoryUsed int64 `json:"avg_memory_used"`
	
	// 错误率
	ErrorRate float64 `json:"error_rate"`
	
	// 最后调用时间
	LastInvocation time.Time `json:"last_invocation"`
	
	// 活跃函数数量
	ActiveFunctions int `json:"active_functions"`
}

// ServerlessEvent Serverless事件
type ServerlessEvent struct {
	// 事件类型
	Type EventType `json:"type"`
	
	// 提供者名称
	Provider string `json:"provider"`
	
	// 函数名称
	FunctionName string `json:"function_name"`
	
	// 请求ID
	RequestID string `json:"request_id"`
	
	// 事件时间戳
	Timestamp time.Time `json:"timestamp"`
	
	// 事件数据
	Data interface{} `json:"data"`
	
	// 错误信息
	Error string `json:"error,omitempty"`
}

// EventType 事件类型
type EventType string

const (
	EventTypeInvocationStart    EventType = "invocation_start"
	EventTypeInvocationSuccess  EventType = "invocation_success"
	EventTypeInvocationError    EventType = "invocation_error"
	EventTypeInvocationTimeout  EventType = "invocation_timeout"
	EventTypeFunctionDiscovered EventType = "function_discovered"
	EventTypeFunctionUpdated    EventType = "function_updated"
	EventTypeProviderConnected  EventType = "provider_connected"
	EventTypeProviderError      EventType = "provider_error"
)

// ServerlessEventListener Serverless事件监听器
type ServerlessEventListener interface {
	// 处理Serverless事件
	OnServerlessEvent(event *ServerlessEvent) error
	
	// 获取监听的事件类型
	GetEventTypes() []EventType
}
