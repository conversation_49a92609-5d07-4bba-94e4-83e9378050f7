package serverless

import (
	"context"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"
)

// OpenFaaSProvider OpenFaaS提供者实现
type OpenFaaSProvider struct {
	name   string
	config OpenFaaSConfig
	logger *telemetry.Logger
	stats  *ProviderStats
	statsMux sync.RWMutex
}

// OpenFaaSConfig OpenFaaS配置
type OpenFaaSConfig struct {
	GatewayURL     string        `yaml:"gateway_url" mapstructure:"gateway_url"`
	Username       string        `yaml:"username" mapstructure:"username"`
	Password       string        `yaml:"password" mapstructure:"password"`
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// NewOpenFaaSProvider 创建OpenFaaS提供者
func NewOpenFaaSProvider(config OpenFaaSConfig, logger *telemetry.Logger) (*OpenFaaSProvider, error) {
	provider := &OpenFaaSProvider{
		name:   "openfaas",
		config: config,
		logger: logger.With("provider", "openfaas"),
		stats: &ProviderStats{
			ProviderName: "openfaas",
		},
	}
	
	provider.logger.Info("OpenFaaS提供者初始化完成", "gateway_url", config.GatewayURL)
	
	return provider, nil
}

// Name 获取提供者名称
func (p *OpenFaaSProvider) Name() string {
	return p.name
}

// InvokeFunction 调用OpenFaaS函数
func (p *OpenFaaSProvider) InvokeFunction(ctx context.Context, request *InvocationRequest) (*InvocationResponse, error) {
	startTime := time.Now()
	
	p.recordInvocationStart()
	
	// 模拟OpenFaaS函数调用
	response := &InvocationResponse{
		StatusCode:     200,
		Payload:        []byte(`{"message": "Hello from OpenFaaS!", "input": ` + string(request.Payload) + `}`),
		Headers:        map[string]string{"Content-Type": "application/json"},
		Duration:       time.Since(startTime),
		BilledDuration: time.Since(startTime),
		MemoryUsed:     64 * 1024 * 1024, // 64MB
		RequestID:      generateRequestID(),
		ExecutionEnv: map[string]interface{}{
			"platform": "openfaas",
			"runtime":  "docker",
		},
	}
	
	p.recordInvocationSuccess(time.Since(startTime))
	
	return response, nil
}

// GetFunction 获取函数信息
func (p *OpenFaaSProvider) GetFunction(ctx context.Context, functionName string) (*FunctionInfo, error) {
	return &FunctionInfo{
		Name:        functionName,
		Runtime:     "docker",
		Handler:     "main",
		Description: "OpenFaaS function",
		Timeout:     p.config.DefaultTimeout,
		MemorySize:  64 * 1024 * 1024,
		State:       "Active",
	}, nil
}

// ListFunctions 列出所有函数
func (p *OpenFaaSProvider) ListFunctions(ctx context.Context) ([]*FunctionInfo, error) {
	functions := []*FunctionInfo{
		{
			Name:        "hello-openfaas",
			Runtime:     "docker",
			Handler:     "main",
			Description: "Hello OpenFaaS function",
			Timeout:     p.config.DefaultTimeout,
			MemorySize:  64 * 1024 * 1024,
			State:       "Active",
		},
	}
	
	return functions, nil
}

// HealthCheck 健康检查
func (p *OpenFaaSProvider) HealthCheck(ctx context.Context) error {
	return nil
}

// GetStats 获取统计信息
func (p *OpenFaaSProvider) GetStats() *ProviderStats {
	p.statsMux.RLock()
	defer p.statsMux.RUnlock()
	
	stats := *p.stats
	if stats.TotalInvocations > 0 {
		stats.ErrorRate = float64(stats.FailedInvocations) / float64(stats.TotalInvocations)
	}
	
	return &stats
}

// FnProjectProvider Fn Project提供者实现
type FnProjectProvider struct {
	name   string
	config FnProjectConfig
	logger *telemetry.Logger
	stats  *ProviderStats
	statsMux sync.RWMutex
}

// FnProjectConfig Fn Project配置
type FnProjectConfig struct {
	APIURL         string        `yaml:"api_url" mapstructure:"api_url"`
	AppName        string        `yaml:"app_name" mapstructure:"app_name"`
	Token          string        `yaml:"token" mapstructure:"token"`
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// NewFnProjectProvider 创建Fn Project提供者
func NewFnProjectProvider(config FnProjectConfig, logger *telemetry.Logger) (*FnProjectProvider, error) {
	provider := &FnProjectProvider{
		name:   "fn_project",
		config: config,
		logger: logger.With("provider", "fn_project"),
		stats: &ProviderStats{
			ProviderName: "fn_project",
		},
	}
	
	provider.logger.Info("Fn Project提供者初始化完成", "api_url", config.APIURL)
	
	return provider, nil
}

// Name 获取提供者名称
func (p *FnProjectProvider) Name() string {
	return p.name
}

// InvokeFunction 调用Fn Project函数
func (p *FnProjectProvider) InvokeFunction(ctx context.Context, request *InvocationRequest) (*InvocationResponse, error) {
	startTime := time.Now()
	
	p.recordInvocationStart()
	
	// 模拟Fn Project函数调用
	response := &InvocationResponse{
		StatusCode:     200,
		Payload:        []byte(`{"message": "Hello from Fn Project!", "input": ` + string(request.Payload) + `}`),
		Headers:        map[string]string{"Content-Type": "application/json"},
		Duration:       time.Since(startTime),
		BilledDuration: time.Since(startTime),
		MemoryUsed:     128 * 1024 * 1024, // 128MB
		RequestID:      generateRequestID(),
		ExecutionEnv: map[string]interface{}{
			"platform": "fn_project",
			"runtime":  "container",
		},
	}
	
	p.recordInvocationSuccess(time.Since(startTime))
	
	return response, nil
}

// GetFunction 获取函数信息
func (p *FnProjectProvider) GetFunction(ctx context.Context, functionName string) (*FunctionInfo, error) {
	return &FunctionInfo{
		Name:        functionName,
		Runtime:     "container",
		Handler:     "func.yaml",
		Description: "Fn Project function",
		Timeout:     p.config.DefaultTimeout,
		MemorySize:  128 * 1024 * 1024,
		State:       "Active",
	}, nil
}

// ListFunctions 列出所有函数
func (p *FnProjectProvider) ListFunctions(ctx context.Context) ([]*FunctionInfo, error) {
	functions := []*FunctionInfo{
		{
			Name:        "hello-fn",
			Runtime:     "container",
			Handler:     "func.yaml",
			Description: "Hello Fn Project function",
			Timeout:     p.config.DefaultTimeout,
			MemorySize:  128 * 1024 * 1024,
			State:       "Active",
		},
	}
	
	return functions, nil
}

// HealthCheck 健康检查
func (p *FnProjectProvider) HealthCheck(ctx context.Context) error {
	return nil
}

// GetStats 获取统计信息
func (p *FnProjectProvider) GetStats() *ProviderStats {
	p.statsMux.RLock()
	defer p.statsMux.RUnlock()
	
	stats := *p.stats
	if stats.TotalInvocations > 0 {
		stats.ErrorRate = float64(stats.FailedInvocations) / float64(stats.TotalInvocations)
	}
	
	return &stats
}

// WebAssemblyProvider WebAssembly提供者实现
type WebAssemblyProvider struct {
	name   string
	config WebAssemblyConfig
	logger *telemetry.Logger
	stats  *ProviderStats
	statsMux sync.RWMutex
}

// WebAssemblyConfig WebAssembly配置
type WebAssemblyConfig struct {
	RuntimePath    string        `yaml:"runtime_path" mapstructure:"runtime_path"`
	ModulesPath    string        `yaml:"modules_path" mapstructure:"modules_path"`
	MaxInstances   int           `yaml:"max_instances" mapstructure:"max_instances"`
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// NewWebAssemblyProvider 创建WebAssembly提供者
func NewWebAssemblyProvider(config WebAssemblyConfig, logger *telemetry.Logger) (*WebAssemblyProvider, error) {
	provider := &WebAssemblyProvider{
		name:   "webassembly",
		config: config,
		logger: logger.With("provider", "webassembly"),
		stats: &ProviderStats{
			ProviderName: "webassembly",
		},
	}
	
	provider.logger.Info("WebAssembly提供者初始化完成", "runtime_path", config.RuntimePath)
	
	return provider, nil
}

// Name 获取提供者名称
func (p *WebAssemblyProvider) Name() string {
	return p.name
}

// InvokeFunction 调用WebAssembly函数
func (p *WebAssemblyProvider) InvokeFunction(ctx context.Context, request *InvocationRequest) (*InvocationResponse, error) {
	startTime := time.Now()
	
	p.recordInvocationStart()
	
	// 模拟WebAssembly函数调用
	response := &InvocationResponse{
		StatusCode:     200,
		Payload:        []byte(`{"message": "Hello from WebAssembly!", "input": ` + string(request.Payload) + `}`),
		Headers:        map[string]string{"Content-Type": "application/json"},
		Duration:       time.Since(startTime),
		BilledDuration: time.Since(startTime),
		MemoryUsed:     16 * 1024 * 1024, // 16MB
		RequestID:      generateRequestID(),
		ExecutionEnv: map[string]interface{}{
			"platform": "webassembly",
			"runtime":  "wasm",
		},
	}
	
	p.recordInvocationSuccess(time.Since(startTime))
	
	return response, nil
}

// GetFunction 获取函数信息
func (p *WebAssemblyProvider) GetFunction(ctx context.Context, functionName string) (*FunctionInfo, error) {
	return &FunctionInfo{
		Name:        functionName,
		Runtime:     "wasm",
		Handler:     "main.wasm",
		Description: "WebAssembly function",
		Timeout:     p.config.DefaultTimeout,
		MemorySize:  16 * 1024 * 1024,
		State:       "Active",
	}, nil
}

// ListFunctions 列出所有函数
func (p *WebAssemblyProvider) ListFunctions(ctx context.Context) ([]*FunctionInfo, error) {
	functions := []*FunctionInfo{
		{
			Name:        "hello-wasm",
			Runtime:     "wasm",
			Handler:     "main.wasm",
			Description: "Hello WebAssembly function",
			Timeout:     p.config.DefaultTimeout,
			MemorySize:  16 * 1024 * 1024,
			State:       "Active",
		},
	}
	
	return functions, nil
}

// HealthCheck 健康检查
func (p *WebAssemblyProvider) HealthCheck(ctx context.Context) error {
	return nil
}

// GetStats 获取统计信息
func (p *WebAssemblyProvider) GetStats() *ProviderStats {
	p.statsMux.RLock()
	defer p.statsMux.RUnlock()
	
	stats := *p.stats
	if stats.TotalInvocations > 0 {
		stats.ErrorRate = float64(stats.FailedInvocations) / float64(stats.TotalInvocations)
	}
	
	return &stats
}

// 通用的统计方法

func (p *OpenFaaSProvider) recordInvocationStart() {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.TotalInvocations++
	p.stats.LastInvocation = time.Now()
}

func (p *OpenFaaSProvider) recordInvocationSuccess(duration time.Duration) {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.SuccessfulInvocations++
	p.stats.TotalDuration += duration
}

func (p *FnProjectProvider) recordInvocationStart() {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.TotalInvocations++
	p.stats.LastInvocation = time.Now()
}

func (p *FnProjectProvider) recordInvocationSuccess(duration time.Duration) {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.SuccessfulInvocations++
	p.stats.TotalDuration += duration
}

func (p *WebAssemblyProvider) recordInvocationStart() {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.TotalInvocations++
	p.stats.LastInvocation = time.Now()
}

func (p *WebAssemblyProvider) recordInvocationSuccess(duration time.Duration) {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.SuccessfulInvocations++
	p.stats.TotalDuration += duration
}
