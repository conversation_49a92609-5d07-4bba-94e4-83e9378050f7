package serverless

import (
	"context"
	"fmt"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"
)

// LocalStackProvider LocalStack提供者实现
type LocalStackProvider struct {
	name   string
	config LocalStackConfig
	logger *telemetry.Logger
	stats  *ProviderStats
	statsMux sync.RWMutex
}

// LocalStackConfig LocalStack配置
type LocalStackConfig struct {
	EndpointURL    string        `yaml:"endpoint_url" mapstructure:"endpoint_url"`
	Region         string        `yaml:"region" mapstructure:"region"`
	AccessKeyID    string        `yaml:"access_key_id" mapstructure:"access_key_id"`
	SecretKey      string        `yaml:"secret_key" mapstructure:"secret_key"`
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// NewLocalStackProvider 创建LocalStack提供者
func NewLocalStackProvider(config LocalStackConfig, logger *telemetry.Logger) (*LocalStackProvider, error) {
	provider := &LocalStackProvider{
		name:   "localstack",
		config: config,
		logger: logger.With("provider", "localstack"),
		stats: &ProviderStats{
			ProviderName: "localstack",
		},
	}
	
	provider.logger.Info("LocalStack提供者初始化完成", "endpoint_url", config.EndpointURL)
	
	return provider, nil
}

// Name 获取提供者名称
func (p *LocalStackProvider) Name() string {
	return p.name
}

// InvokeFunction 调用LocalStack Lambda函数
func (p *LocalStackProvider) InvokeFunction(ctx context.Context, request *InvocationRequest) (*InvocationResponse, error) {
	startTime := time.Now()
	
	p.recordInvocationStart()
	
	// 模拟LocalStack Lambda调用
	response := &InvocationResponse{
		StatusCode:     200,
		Payload:        []byte(`{"message": "Hello from LocalStack!", "input": ` + string(request.Payload) + `}`),
		Headers:        map[string]string{"Content-Type": "application/json"},
		Duration:       time.Since(startTime),
		BilledDuration: time.Since(startTime),
		MemoryUsed:     128 * 1024 * 1024, // 128MB
		RequestID:      generateRequestID(),
		ExecutionEnv: map[string]interface{}{
			"platform": "localstack",
			"runtime":  "python3.9",
		},
	}
	
	p.recordInvocationSuccess(time.Since(startTime))
	
	return response, nil
}

// GetFunction 获取函数信息
func (p *LocalStackProvider) GetFunction(ctx context.Context, functionName string) (*FunctionInfo, error) {
	return &FunctionInfo{
		Name:        functionName,
		ARN:         fmt.Sprintf("arn:aws:lambda:%s:000000000000:function:%s", p.config.Region, functionName),
		Runtime:     "python3.9",
		Handler:     "lambda_function.lambda_handler",
		Description: "LocalStack Lambda function",
		Timeout:     p.config.DefaultTimeout,
		MemorySize:  128 * 1024 * 1024,
		State:       "Active",
	}, nil
}

// ListFunctions 列出所有函数
func (p *LocalStackProvider) ListFunctions(ctx context.Context) ([]*FunctionInfo, error) {
	functions := []*FunctionInfo{
		{
			Name:        "hello-localstack",
			ARN:         fmt.Sprintf("arn:aws:lambda:%s:000000000000:function:hello-localstack", p.config.Region),
			Runtime:     "python3.9",
			Handler:     "lambda_function.lambda_handler",
			Description: "Hello LocalStack function",
			Timeout:     p.config.DefaultTimeout,
			MemorySize:  128 * 1024 * 1024,
			State:       "Active",
		},
	}
	
	return functions, nil
}

// HealthCheck 健康检查
func (p *LocalStackProvider) HealthCheck(ctx context.Context) error {
	return nil
}

// GetStats 获取统计信息
func (p *LocalStackProvider) GetStats() *ProviderStats {
	p.statsMux.RLock()
	defer p.statsMux.RUnlock()
	
	stats := *p.stats
	if stats.TotalInvocations > 0 {
		stats.ErrorRate = float64(stats.FailedInvocations) / float64(stats.TotalInvocations)
	}
	
	return &stats
}

// SupabaseFunctionsProvider Supabase Functions提供者实现
type SupabaseFunctionsProvider struct {
	name   string
	config SupabaseFunctionsConfig
	logger *telemetry.Logger
	stats  *ProviderStats
	statsMux sync.RWMutex
}

// SupabaseFunctionsConfig Supabase Functions配置
type SupabaseFunctionsConfig struct {
	ProjectURL     string        `yaml:"project_url" mapstructure:"project_url"`
	AnonKey        string        `yaml:"anon_key" mapstructure:"anon_key"`
	ServiceKey     string        `yaml:"service_key" mapstructure:"service_key"`
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// NewSupabaseFunctionsProvider 创建Supabase Functions提供者
func NewSupabaseFunctionsProvider(config SupabaseFunctionsConfig, logger *telemetry.Logger) (*SupabaseFunctionsProvider, error) {
	provider := &SupabaseFunctionsProvider{
		name:   "supabase_functions",
		config: config,
		logger: logger.With("provider", "supabase_functions"),
		stats: &ProviderStats{
			ProviderName: "supabase_functions",
		},
	}
	
	provider.logger.Info("Supabase Functions提供者初始化完成", "project_url", config.ProjectURL)
	
	return provider, nil
}

// Name 获取提供者名称
func (p *SupabaseFunctionsProvider) Name() string {
	return p.name
}

// InvokeFunction 调用Supabase Edge Function
func (p *SupabaseFunctionsProvider) InvokeFunction(ctx context.Context, request *InvocationRequest) (*InvocationResponse, error) {
	startTime := time.Now()
	
	p.recordInvocationStart()
	
	// 模拟Supabase Edge Function调用
	response := &InvocationResponse{
		StatusCode:     200,
		Payload:        []byte(`{"message": "Hello from Supabase Edge Functions!", "input": ` + string(request.Payload) + `}`),
		Headers:        map[string]string{"Content-Type": "application/json"},
		Duration:       time.Since(startTime),
		BilledDuration: time.Since(startTime),
		MemoryUsed:     32 * 1024 * 1024, // 32MB
		RequestID:      generateRequestID(),
		ExecutionEnv: map[string]interface{}{
			"platform": "supabase",
			"runtime":  "deno",
		},
	}
	
	p.recordInvocationSuccess(time.Since(startTime))
	
	return response, nil
}

// GetFunction 获取函数信息
func (p *SupabaseFunctionsProvider) GetFunction(ctx context.Context, functionName string) (*FunctionInfo, error) {
	return &FunctionInfo{
		Name:        functionName,
		Runtime:     "deno",
		Handler:     "index.ts",
		Description: "Supabase Edge Function",
		Timeout:     p.config.DefaultTimeout,
		MemorySize:  32 * 1024 * 1024,
		State:       "Active",
	}, nil
}

// ListFunctions 列出所有函数
func (p *SupabaseFunctionsProvider) ListFunctions(ctx context.Context) ([]*FunctionInfo, error) {
	functions := []*FunctionInfo{
		{
			Name:        "hello-supabase",
			Runtime:     "deno",
			Handler:     "index.ts",
			Description: "Hello Supabase Edge Function",
			Timeout:     p.config.DefaultTimeout,
			MemorySize:  32 * 1024 * 1024,
			State:       "Active",
		},
	}
	
	return functions, nil
}

// HealthCheck 健康检查
func (p *SupabaseFunctionsProvider) HealthCheck(ctx context.Context) error {
	return nil
}

// GetStats 获取统计信息
func (p *SupabaseFunctionsProvider) GetStats() *ProviderStats {
	p.statsMux.RLock()
	defer p.statsMux.RUnlock()
	
	stats := *p.stats
	if stats.TotalInvocations > 0 {
		stats.ErrorRate = float64(stats.FailedInvocations) / float64(stats.TotalInvocations)
	}
	
	return &stats
}

// AppwriteFunctionsProvider Appwrite Functions提供者实现
type AppwriteFunctionsProvider struct {
	name   string
	config AppwriteFunctionsConfig
	logger *telemetry.Logger
	stats  *ProviderStats
	statsMux sync.RWMutex
}

// AppwriteFunctionsConfig Appwrite Functions配置
type AppwriteFunctionsConfig struct {
	Endpoint       string        `yaml:"endpoint" mapstructure:"endpoint"`
	ProjectID      string        `yaml:"project_id" mapstructure:"project_id"`
	APIKey         string        `yaml:"api_key" mapstructure:"api_key"`
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// NewAppwriteFunctionsProvider 创建Appwrite Functions提供者
func NewAppwriteFunctionsProvider(config AppwriteFunctionsConfig, logger *telemetry.Logger) (*AppwriteFunctionsProvider, error) {
	provider := &AppwriteFunctionsProvider{
		name:   "appwrite_functions",
		config: config,
		logger: logger.With("provider", "appwrite_functions"),
		stats: &ProviderStats{
			ProviderName: "appwrite_functions",
		},
	}
	
	provider.logger.Info("Appwrite Functions提供者初始化完成", "endpoint", config.Endpoint)
	
	return provider, nil
}

// Name 获取提供者名称
func (p *AppwriteFunctionsProvider) Name() string {
	return p.name
}

// InvokeFunction 调用Appwrite Function
func (p *AppwriteFunctionsProvider) InvokeFunction(ctx context.Context, request *InvocationRequest) (*InvocationResponse, error) {
	startTime := time.Now()
	
	p.recordInvocationStart()
	
	// 模拟Appwrite Function调用
	response := &InvocationResponse{
		StatusCode:     200,
		Payload:        []byte(`{"message": "Hello from Appwrite Functions!", "input": ` + string(request.Payload) + `}`),
		Headers:        map[string]string{"Content-Type": "application/json"},
		Duration:       time.Since(startTime),
		BilledDuration: time.Since(startTime),
		MemoryUsed:     64 * 1024 * 1024, // 64MB
		RequestID:      generateRequestID(),
		ExecutionEnv: map[string]interface{}{
			"platform": "appwrite",
			"runtime":  "node",
		},
	}
	
	p.recordInvocationSuccess(time.Since(startTime))
	
	return response, nil
}

// GetFunction 获取函数信息
func (p *AppwriteFunctionsProvider) GetFunction(ctx context.Context, functionName string) (*FunctionInfo, error) {
	return &FunctionInfo{
		Name:        functionName,
		Runtime:     "node",
		Handler:     "index.js",
		Description: "Appwrite Function",
		Timeout:     p.config.DefaultTimeout,
		MemorySize:  64 * 1024 * 1024,
		State:       "Active",
	}, nil
}

// ListFunctions 列出所有函数
func (p *AppwriteFunctionsProvider) ListFunctions(ctx context.Context) ([]*FunctionInfo, error) {
	functions := []*FunctionInfo{
		{
			Name:        "hello-appwrite",
			Runtime:     "node",
			Handler:     "index.js",
			Description: "Hello Appwrite Function",
			Timeout:     p.config.DefaultTimeout,
			MemorySize:  64 * 1024 * 1024,
			State:       "Active",
		},
	}
	
	return functions, nil
}

// HealthCheck 健康检查
func (p *AppwriteFunctionsProvider) HealthCheck(ctx context.Context) error {
	return nil
}

// GetStats 获取统计信息
func (p *AppwriteFunctionsProvider) GetStats() *ProviderStats {
	p.statsMux.RLock()
	defer p.statsMux.RUnlock()
	
	stats := *p.stats
	if stats.TotalInvocations > 0 {
		stats.ErrorRate = float64(stats.FailedInvocations) / float64(stats.TotalInvocations)
	}
	
	return &stats
}

// 通用的统计方法

func (p *LocalStackProvider) recordInvocationStart() {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.TotalInvocations++
	p.stats.LastInvocation = time.Now()
}

func (p *LocalStackProvider) recordInvocationSuccess(duration time.Duration) {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.SuccessfulInvocations++
	p.stats.TotalDuration += duration
}

func (p *SupabaseFunctionsProvider) recordInvocationStart() {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.TotalInvocations++
	p.stats.LastInvocation = time.Now()
}

func (p *SupabaseFunctionsProvider) recordInvocationSuccess(duration time.Duration) {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.SuccessfulInvocations++
	p.stats.TotalDuration += duration
}

func (p *AppwriteFunctionsProvider) recordInvocationStart() {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.TotalInvocations++
	p.stats.LastInvocation = time.Now()
}

func (p *AppwriteFunctionsProvider) recordInvocationSuccess(duration time.Duration) {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	p.stats.SuccessfulInvocations++
	p.stats.TotalDuration += duration
}
