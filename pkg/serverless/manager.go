package serverless

import (
	"context"
	"fmt"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// Manager Serverless管理器实现
type Manager struct {
	config config.ServerlessConfig
	logger *telemetry.Logger
	
	// 提供者管理
	providers    map[string]ServerlessProvider
	providersMux sync.RWMutex
	
	// 路由管理
	routes    []*ServerlessRoute
	routesMux sync.RWMutex
	
	// 事件监听器
	listeners []ServerlessEventListener
	listenersMux sync.RWMutex
	
	// 生命周期
	running bool
	
	// 上下文
	ctx    context.Context
	cancel context.CancelFunc
	
	// 等待组
	wg sync.WaitGroup
}

// NewManager 创建Serverless管理器
func NewManager(cfg config.ServerlessConfig, logger *telemetry.Logger) (*Manager, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	manager := &Manager{
		config:    cfg,
		logger:    logger.With("component", "serverless-manager"),
		providers: make(map[string]ServerlessProvider),
		routes:    make([]*ServerlessRoute, 0),
		listeners: make([]ServerlessEventListener, 0),
		ctx:       ctx,
		cancel:    cancel,
	}
	
	// 初始化提供者
	if err := manager.initializeProviders(); err != nil {
		return nil, fmt.Errorf("初始化Serverless提供者失败: %w", err)
	}
	
	// 初始化路由
	if err := manager.initializeRoutes(); err != nil {
		return nil, fmt.Errorf("初始化Serverless路由失败: %w", err)
	}
	
	manager.logger.Info("Serverless管理器初始化完成",
		"providers_count", len(manager.providers),
		"routes_count", len(manager.routes))
	
	return manager, nil
}

// RegisterProvider 注册提供者
func (m *Manager) RegisterProvider(provider ServerlessProvider) error {
	m.providersMux.Lock()
	defer m.providersMux.Unlock()
	
	name := provider.Name()
	if _, exists := m.providers[name]; exists {
		return fmt.Errorf("提供者 %s 已存在", name)
	}
	
	m.providers[name] = provider
	
	m.logger.Info("注册Serverless提供者", "name", name)
	
	// 发送提供者连接事件
	m.emitEvent(&ServerlessEvent{
		Type:      EventTypeProviderConnected,
		Provider:  name,
		Timestamp: time.Now(),
	})
	
	return nil
}

// GetProvider 获取提供者
func (m *Manager) GetProvider(name string) (ServerlessProvider, error) {
	m.providersMux.RLock()
	defer m.providersMux.RUnlock()
	
	provider, exists := m.providers[name]
	if !exists {
		return nil, fmt.Errorf("提供者 %s 不存在", name)
	}
	
	return provider, nil
}

// InvokeFunction 调用函数
func (m *Manager) InvokeFunction(ctx context.Context, providerName, functionName string, request *InvocationRequest) (*InvocationResponse, error) {
	// 获取提供者
	provider, err := m.GetProvider(providerName)
	if err != nil {
		return nil, err
	}
	
	// 设置函数名称
	request.FunctionName = functionName
	
	// 发送调用开始事件
	requestID := generateRequestID()
	m.emitEvent(&ServerlessEvent{
		Type:         EventTypeInvocationStart,
		Provider:     providerName,
		FunctionName: functionName,
		RequestID:    requestID,
		Timestamp:    time.Now(),
		Data:         request,
	})
	
	// 调用函数
	response, err := provider.InvokeFunction(ctx, request)
	if err != nil {
		// 发送调用错误事件
		m.emitEvent(&ServerlessEvent{
			Type:         EventTypeInvocationError,
			Provider:     providerName,
			FunctionName: functionName,
			RequestID:    requestID,
			Timestamp:    time.Now(),
			Error:        err.Error(),
		})
		
		return nil, fmt.Errorf("调用函数失败: %w", err)
	}
	
	// 设置请求ID
	response.RequestID = requestID
	
	// 发送调用成功事件
	m.emitEvent(&ServerlessEvent{
		Type:         EventTypeInvocationSuccess,
		Provider:     providerName,
		FunctionName: functionName,
		RequestID:    requestID,
		Timestamp:    time.Now(),
		Data:         response,
	})
	
	return response, nil
}

// RouteToFunction 路由到Serverless函数
func (m *Manager) RouteToFunction(ctx context.Context, route *ServerlessRoute, request *InvocationRequest) (*InvocationResponse, error) {
	if !route.Enabled {
		return nil, fmt.Errorf("路由 %s 已禁用", route.Name)
	}
	
	// 应用转换配置
	if route.Transform != nil && route.Transform.Request != nil {
		if err := m.applyRequestTransform(request, route.Transform.Request); err != nil {
			return nil, fmt.Errorf("请求转换失败: %w", err)
		}
	}
	
	// 设置超时
	if route.Timeout > 0 {
		request.Timeout = route.Timeout
	}
	
	// 设置调用类型
	if route.InvocationType != "" {
		request.InvocationType = route.InvocationType
	}
	
	// 设置重试配置
	if route.RetryConfig != nil {
		request.RetryCount = route.RetryConfig.MaxRetries
	}
	
	// 调用函数
	response, err := m.InvokeFunction(ctx, route.Provider, route.FunctionName, request)
	if err != nil {
		// 处理重试
		if route.RetryConfig != nil && request.RetryCount > 0 {
			return m.retryInvocation(ctx, route, request, err)
		}
		return nil, err
	}
	
	// 应用响应转换
	if route.Transform != nil && route.Transform.Response != nil {
		if err := m.applyResponseTransform(response, route.Transform.Response); err != nil {
			return nil, fmt.Errorf("响应转换失败: %w", err)
		}
	}
	
	return response, nil
}

// GetProviders 获取所有提供者
func (m *Manager) GetProviders() []ServerlessProvider {
	m.providersMux.RLock()
	defer m.providersMux.RUnlock()
	
	providers := make([]ServerlessProvider, 0, len(m.providers))
	for _, provider := range m.providers {
		providers = append(providers, provider)
	}
	
	return providers
}

// Start 启动管理器
func (m *Manager) Start() error {
	if m.running {
		return nil
	}
	
	m.logger.Info("启动Serverless管理器")
	
	// 启动监控协程
	m.wg.Add(1)
	go m.monitoringLoop()
	
	// 启动函数发现协程
	m.wg.Add(1)
	go m.functionDiscoveryLoop()
	
	m.running = true
	
	m.logger.Info("Serverless管理器启动完成")
	
	return nil
}

// Stop 停止管理器
func (m *Manager) Stop() error {
	if !m.running {
		return nil
	}
	
	m.logger.Info("停止Serverless管理器")
	
	// 取消上下文
	m.cancel()
	
	// 等待协程结束
	m.wg.Wait()
	
	m.running = false
	
	m.logger.Info("Serverless管理器已停止")
	
	return nil
}

// AddEventListener 添加事件监听器
func (m *Manager) AddEventListener(listener ServerlessEventListener) {
	m.listenersMux.Lock()
	defer m.listenersMux.Unlock()
	
	m.listeners = append(m.listeners, listener)
}

// 私有方法

// initializeProviders 初始化提供者
func (m *Manager) initializeProviders() error {
	// 初始化AWS Lambda提供者
	if m.config.AWSLambda.Enabled {
		provider, err := NewAWSLambdaProvider(AWSLambdaConfig{
			Region:           m.config.AWSLambda.Region,
			AccessKeyID:      m.config.AWSLambda.AccessKeyID,
			SecretAccessKey:  m.config.AWSLambda.SecretAccessKey,
			SessionToken:     m.config.AWSLambda.SessionToken,
			EndpointURL:      m.config.AWSLambda.EndpointURL,
			DefaultTimeout:   m.config.AWSLambda.DefaultTimeout,
			MaxRetries:       m.config.AWSLambda.MaxRetries,
			FunctionCacheTTL: m.config.AWSLambda.FunctionCacheTTL,
		}, m.logger)
		if err != nil {
			return fmt.Errorf("初始化AWS Lambda提供者失败: %w", err)
		}
		
		if err := m.RegisterProvider(provider); err != nil {
			return err
		}
	}
	
	// 初始化OpenFaaS提供者
	if m.config.OpenFaaS.Enabled {
		provider, err := NewOpenFaaSProvider(OpenFaaSConfig{
			GatewayURL:     m.config.OpenFaaS.GatewayURL,
			Username:       m.config.OpenFaaS.Username,
			Password:       m.config.OpenFaaS.Password,
			DefaultTimeout: m.config.OpenFaaS.DefaultTimeout,
		}, m.logger)
		if err != nil {
			return fmt.Errorf("初始化OpenFaaS提供者失败: %w", err)
		}

		if err := m.RegisterProvider(provider); err != nil {
			return err
		}
	}

	// 初始化Fn Project提供者
	if m.config.FnProject.Enabled {
		provider, err := NewFnProjectProvider(FnProjectConfig{
			APIURL:         m.config.FnProject.APIURL,
			AppName:        m.config.FnProject.AppName,
			Token:          m.config.FnProject.Token,
			DefaultTimeout: m.config.FnProject.DefaultTimeout,
		}, m.logger)
		if err != nil {
			return fmt.Errorf("初始化Fn Project提供者失败: %w", err)
		}

		if err := m.RegisterProvider(provider); err != nil {
			return err
		}
	}

	// 初始化WebAssembly提供者
	if m.config.WebAssembly.Enabled {
		provider, err := NewWebAssemblyProvider(WebAssemblyConfig{
			RuntimePath:    m.config.WebAssembly.RuntimePath,
			ModulesPath:    m.config.WebAssembly.ModulesPath,
			MaxInstances:   m.config.WebAssembly.MaxInstances,
			DefaultTimeout: m.config.WebAssembly.DefaultTimeout,
		}, m.logger)
		if err != nil {
			return fmt.Errorf("初始化WebAssembly提供者失败: %w", err)
		}

		if err := m.RegisterProvider(provider); err != nil {
			return err
		}
	}

	// 初始化LocalStack提供者
	if m.config.LocalStack.Enabled {
		provider, err := NewLocalStackProvider(LocalStackConfig{
			EndpointURL:    m.config.LocalStack.EndpointURL,
			Region:         m.config.LocalStack.Region,
			AccessKeyID:    m.config.LocalStack.AccessKeyID,
			SecretKey:      m.config.LocalStack.SecretKey,
			DefaultTimeout: m.config.LocalStack.DefaultTimeout,
		}, m.logger)
		if err != nil {
			return fmt.Errorf("初始化LocalStack提供者失败: %w", err)
		}

		if err := m.RegisterProvider(provider); err != nil {
			return err
		}
	}

	// 初始化Supabase Functions提供者
	if m.config.SupabaseFunctions.Enabled {
		provider, err := NewSupabaseFunctionsProvider(SupabaseFunctionsConfig{
			ProjectURL:     m.config.SupabaseFunctions.ProjectURL,
			AnonKey:        m.config.SupabaseFunctions.AnonKey,
			ServiceKey:     m.config.SupabaseFunctions.ServiceKey,
			DefaultTimeout: m.config.SupabaseFunctions.DefaultTimeout,
		}, m.logger)
		if err != nil {
			return fmt.Errorf("初始化Supabase Functions提供者失败: %w", err)
		}

		if err := m.RegisterProvider(provider); err != nil {
			return err
		}
	}

	// 初始化Appwrite Functions提供者
	if m.config.AppwriteFunctions.Enabled {
		provider, err := NewAppwriteFunctionsProvider(AppwriteFunctionsConfig{
			Endpoint:       m.config.AppwriteFunctions.Endpoint,
			ProjectID:      m.config.AppwriteFunctions.ProjectID,
			APIKey:         m.config.AppwriteFunctions.APIKey,
			DefaultTimeout: m.config.AppwriteFunctions.DefaultTimeout,
		}, m.logger)
		if err != nil {
			return fmt.Errorf("初始化Appwrite Functions提供者失败: %w", err)
		}

		if err := m.RegisterProvider(provider); err != nil {
			return err
		}
	}

	return nil
}

// initializeRoutes 初始化路由
func (m *Manager) initializeRoutes() error {
	for _, routeConfig := range m.config.Routes {
		route := &ServerlessRoute{
			Name:           routeConfig.Name,
			Path:           routeConfig.Path,
			Methods:        routeConfig.Methods,
			Provider:       routeConfig.Provider,
			FunctionName:   routeConfig.FunctionName,
			InvocationType: routeConfig.InvocationType,
			Timeout:        routeConfig.Timeout,
			Enabled:        routeConfig.Enabled,
		}
		
		// 转换重试配置
		if routeConfig.RetryConfig.MaxRetries > 0 {
			route.RetryConfig = &RetryConfig{
				MaxRetries:           routeConfig.RetryConfig.MaxRetries,
				RetryInterval:        routeConfig.RetryConfig.RetryInterval,
				BackoffStrategy:      routeConfig.RetryConfig.BackoffStrategy,
				MaxBackoff:           routeConfig.RetryConfig.MaxBackoff,
				RetryableStatusCodes: routeConfig.RetryConfig.RetryableStatusCodes,
			}
		}
		
		m.routes = append(m.routes, route)
	}
	
	return nil
}

// applyRequestTransform 应用请求转换
func (m *Manager) applyRequestTransform(request *InvocationRequest, transform *RequestTransform) error {
	// 应用头部映射
	if transform.HeaderMapping != nil {
		for from, to := range transform.HeaderMapping {
			if value, exists := request.Headers[from]; exists {
				request.Headers[to] = value
				delete(request.Headers, from)
			}
		}
	}
	
	// 应用查询参数映射
	if transform.QueryMapping != nil {
		for from, to := range transform.QueryMapping {
			if value, exists := request.QueryParams[from]; exists {
				request.QueryParams[to] = value
				delete(request.QueryParams, from)
			}
		}
	}
	
	// 应用路径参数映射
	if transform.PathMapping != nil {
		for from, to := range transform.PathMapping {
			if value, exists := request.PathParams[from]; exists {
				request.PathParams[to] = value
				delete(request.PathParams, from)
			}
		}
	}
	
	// 应用负载转换模板
	if transform.PayloadTemplate != "" {
		// 这里可以实现模板转换逻辑
		m.logger.Debug("应用请求负载转换模板", "template", transform.PayloadTemplate)
	}
	
	return nil
}

// applyResponseTransform 应用响应转换
func (m *Manager) applyResponseTransform(response *InvocationResponse, transform *ResponseTransform) error {
	// 应用状态码映射
	if transform.StatusCodeMapping != nil {
		if newCode, exists := transform.StatusCodeMapping[response.StatusCode]; exists {
			response.StatusCode = newCode
		}
	}
	
	// 应用头部映射
	if transform.HeaderMapping != nil {
		for from, to := range transform.HeaderMapping {
			if value, exists := response.Headers[from]; exists {
				response.Headers[to] = value
				delete(response.Headers, from)
			}
		}
	}
	
	// 应用负载转换模板
	if transform.PayloadTemplate != "" {
		// 这里可以实现模板转换逻辑
		m.logger.Debug("应用响应负载转换模板", "template", transform.PayloadTemplate)
	}
	
	return nil
}

// retryInvocation 重试调用
func (m *Manager) retryInvocation(ctx context.Context, route *ServerlessRoute, request *InvocationRequest, lastErr error) (*InvocationResponse, error) {
	retryConfig := route.RetryConfig
	
	for i := 0; i < retryConfig.MaxRetries; i++ {
		// 计算退避时间
		backoffDuration := m.calculateBackoff(i, retryConfig)
		
		// 等待退避时间
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(backoffDuration):
		}
		
		// 重试调用
		response, err := m.InvokeFunction(ctx, route.Provider, route.FunctionName, request)
		if err == nil {
			return response, nil
		}
		
		lastErr = err
		m.logger.Warn("函数调用重试失败",
			"retry", i+1,
			"max_retries", retryConfig.MaxRetries,
			"error", err)
	}
	
	return nil, fmt.Errorf("函数调用重试失败，已达到最大重试次数: %w", lastErr)
}

// calculateBackoff 计算退避时间
func (m *Manager) calculateBackoff(retryCount int, config *RetryConfig) time.Duration {
	switch config.BackoffStrategy {
	case "exponential":
		duration := config.RetryInterval * time.Duration(1<<retryCount)
		if duration > config.MaxBackoff {
			return config.MaxBackoff
		}
		return duration
	case "linear":
		duration := config.RetryInterval * time.Duration(retryCount+1)
		if duration > config.MaxBackoff {
			return config.MaxBackoff
		}
		return duration
	default: // fixed
		return config.RetryInterval
	}
}

// emitEvent 发送事件
func (m *Manager) emitEvent(event *ServerlessEvent) {
	m.listenersMux.RLock()
	listeners := make([]ServerlessEventListener, len(m.listeners))
	copy(listeners, m.listeners)
	m.listenersMux.RUnlock()
	
	if len(listeners) == 0 {
		return
	}
	
	// 异步发送事件
	go func() {
		for _, listener := range listeners {
			// 检查监听器是否关心这种事件类型
			eventTypes := listener.GetEventTypes()
			if len(eventTypes) > 0 {
				found := false
				for _, et := range eventTypes {
					if et == event.Type {
						found = true
						break
					}
				}
				if !found {
					continue
				}
			}
			
			if err := listener.OnServerlessEvent(event); err != nil {
				m.logger.Error("Serverless事件处理失败", "event_type", event.Type, "error", err)
			}
		}
	}()
}

// monitoringLoop 监控循环
func (m *Manager) monitoringLoop() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.collectMetrics()
		}
	}
}

// functionDiscoveryLoop 函数发现循环
func (m *Manager) functionDiscoveryLoop() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.discoverFunctions()
		}
	}
}

// collectMetrics 收集指标
func (m *Manager) collectMetrics() {
	m.providersMux.RLock()
	defer m.providersMux.RUnlock()
	
	for name, provider := range m.providers {
		stats := provider.GetStats()
		m.logger.Debug("Serverless提供者指标",
			"provider", name,
			"total_invocations", stats.TotalInvocations,
			"success_rate", 1.0-stats.ErrorRate,
			"avg_duration", stats.AvgDuration)
	}
}

// discoverFunctions 发现函数
func (m *Manager) discoverFunctions() {
	m.providersMux.RLock()
	defer m.providersMux.RUnlock()
	
	for name, provider := range m.providers {
		functions, err := provider.ListFunctions(m.ctx)
		if err != nil {
			m.logger.Error("函数发现失败", "provider", name, "error", err)
			continue
		}
		
		for _, function := range functions {
			m.emitEvent(&ServerlessEvent{
				Type:         EventTypeFunctionDiscovered,
				Provider:     name,
				FunctionName: function.Name,
				Timestamp:    time.Now(),
				Data:         function,
			})
		}
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}
