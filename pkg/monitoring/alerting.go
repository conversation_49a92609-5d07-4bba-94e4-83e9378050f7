package monitoring

import (
	"context"
	"fmt"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"
)

// AlertManager 告警管理器
type AlertManager struct {
	logger  *telemetry.Logger
	metrics *telemetry.Metrics
	config  AlertConfig
	
	// 告警规则
	rules    []AlertRule
	rulesMux sync.RWMutex
	
	// 告警状态
	alerts    map[string]*Alert
	alertsMux sync.RWMutex
	
	// 通知器
	notifiers []Notifier
	
	// 控制
	running bool
	stopCh  chan struct{}
	wg      sync.WaitGroup
}

// AlertConfig 告警配置
type AlertConfig struct {
	// 是否启用告警
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// 检查间隔
	CheckInterval time.Duration `yaml:"check_interval" json:"check_interval"`
	
	// 告警规则文件
	RulesFile string `yaml:"rules_file" json:"rules_file"`
	
	// 通知配置
	Notifications []NotificationConfig `yaml:"notifications" json:"notifications"`
}

// AlertRule 告警规则
type AlertRule struct {
	// 规则名称
	Name string `yaml:"name" json:"name"`
	
	// 规则描述
	Description string `yaml:"description" json:"description"`
	
	// 指标名称
	Metric string `yaml:"metric" json:"metric"`
	
	// 条件类型 (>, <, >=, <=, ==, !=)
	Condition string `yaml:"condition" json:"condition"`
	
	// 阈值
	Threshold float64 `yaml:"threshold" json:"threshold"`
	
	// 持续时间
	Duration time.Duration `yaml:"duration" json:"duration"`
	
	// 严重级别 (critical, warning, info)
	Severity string `yaml:"severity" json:"severity"`
	
	// 标签过滤
	Labels map[string]string `yaml:"labels" json:"labels"`
	
	// 是否启用
	Enabled bool `yaml:"enabled" json:"enabled"`
}

// Alert 告警实例
type Alert struct {
	// 告警ID
	ID string `json:"id"`
	
	// 规则名称
	RuleName string `json:"rule_name"`
	
	// 告警消息
	Message string `json:"message"`
	
	// 严重级别
	Severity string `json:"severity"`
	
	// 当前值
	Value float64 `json:"value"`
	
	// 阈值
	Threshold float64 `json:"threshold"`
	
	// 标签
	Labels map[string]string `json:"labels"`
	
	// 状态 (firing, resolved)
	Status string `json:"status"`
	
	// 开始时间
	StartsAt time.Time `json:"starts_at"`
	
	// 结束时间
	EndsAt *time.Time `json:"ends_at,omitempty"`
	
	// 最后更新时间
	UpdatedAt time.Time `json:"updated_at"`
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	// 通知类型 (webhook, email, slack)
	Type string `yaml:"type" json:"type"`
	
	// 通知配置
	Config map[string]interface{} `yaml:"config" json:"config"`
	
	// 是否启用
	Enabled bool `yaml:"enabled" json:"enabled"`
}

// Notifier 通知器接口
type Notifier interface {
	// 发送通知
	Send(ctx context.Context, alert *Alert) error
	
	// 通知器类型
	Type() string
}

// NewAlertManager 创建告警管理器
func NewAlertManager(config AlertConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) *AlertManager {
	return &AlertManager{
		logger:    logger.With("component", "alert-manager"),
		metrics:   metrics,
		config:    config,
		rules:     make([]AlertRule, 0),
		alerts:    make(map[string]*Alert),
		notifiers: make([]Notifier, 0),
		stopCh:    make(chan struct{}),
	}
}

// Start 启动告警管理器
func (am *AlertManager) Start(ctx context.Context) error {
	if !am.config.Enabled {
		am.logger.Info("告警管理器已禁用")
		return nil
	}

	am.running = true
	am.logger.Info("启动告警管理器", "check_interval", am.config.CheckInterval)

	// 加载告警规则
	if err := am.loadRules(); err != nil {
		return fmt.Errorf("加载告警规则失败: %w", err)
	}

	// 初始化通知器
	if err := am.initNotifiers(); err != nil {
		return fmt.Errorf("初始化通知器失败: %w", err)
	}

	// 启动检查循环
	am.wg.Add(1)
	go am.checkLoop(ctx)

	return nil
}

// Stop 停止告警管理器
func (am *AlertManager) Stop() error {
	if !am.running {
		return nil
	}

	am.logger.Info("停止告警管理器")
	am.running = false
	close(am.stopCh)
	am.wg.Wait()

	return nil
}

// AddRule 添加告警规则
func (am *AlertManager) AddRule(rule AlertRule) {
	am.rulesMux.Lock()
	defer am.rulesMux.Unlock()
	
	am.rules = append(am.rules, rule)
	am.logger.Info("添加告警规则", "name", rule.Name, "metric", rule.Metric)
}

// GetAlerts 获取当前告警
func (am *AlertManager) GetAlerts() []*Alert {
	am.alertsMux.RLock()
	defer am.alertsMux.RUnlock()
	
	alerts := make([]*Alert, 0, len(am.alerts))
	for _, alert := range am.alerts {
		alerts = append(alerts, alert)
	}
	
	return alerts
}

// loadRules 加载告警规则
func (am *AlertManager) loadRules() error {
	// 添加默认规则
	defaultRules := []AlertRule{
		{
			Name:        "高内存使用率",
			Description: "内存使用率超过阈值",
			Metric:      "performance_memory_usage_bytes",
			Condition:   ">",
			Threshold:   500 * 1024 * 1024, // 500MB
			Duration:    2 * time.Minute,
			Severity:    "warning",
			Enabled:     true,
		},
		{
			Name:        "高协程数量",
			Description: "协程数量超过阈值",
			Metric:      "performance_goroutine_count",
			Condition:   ">",
			Threshold:   1000,
			Duration:    1 * time.Minute,
			Severity:    "warning",
			Enabled:     true,
		},
		{
			Name:        "高错误率",
			Description: "错误率超过阈值",
			Metric:      "gateway_requests_error_rate",
			Condition:   ">",
			Threshold:   0.05, // 5%
			Duration:    30 * time.Second,
			Severity:    "critical",
			Enabled:     true,
		},
		{
			Name:        "高响应时间",
			Description: "平均响应时间超过阈值",
			Metric:      "gateway_request_duration_avg",
			Condition:   ">",
			Threshold:   1000, // 1秒
			Duration:    1 * time.Minute,
			Severity:    "warning",
			Enabled:     true,
		},
	}

	am.rulesMux.Lock()
	defer am.rulesMux.Unlock()
	
	am.rules = defaultRules
	am.logger.Info("加载默认告警规则", "count", len(defaultRules))
	
	return nil
}

// initNotifiers 初始化通知器
func (am *AlertManager) initNotifiers() error {
	for _, config := range am.config.Notifications {
		if !config.Enabled {
			continue
		}

		var notifier Notifier
		switch config.Type {
		case "webhook":
			notifier = NewWebhookNotifier(config.Config, am.logger)
		case "log":
			notifier = NewLogNotifier(am.logger)
		default:
			am.logger.Warn("不支持的通知器类型", "type", config.Type)
			continue
		}

		am.notifiers = append(am.notifiers, notifier)
		am.logger.Info("初始化通知器", "type", config.Type)
	}

	return nil
}

// checkLoop 检查循环
func (am *AlertManager) checkLoop(ctx context.Context) {
	defer am.wg.Done()
	
	ticker := time.NewTicker(am.config.CheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-am.stopCh:
			return
		case <-ticker.C:
			am.checkRules(ctx)
		}
	}
}

// checkRules 检查告警规则
func (am *AlertManager) checkRules(ctx context.Context) {
	am.rulesMux.RLock()
	rules := make([]AlertRule, len(am.rules))
	copy(rules, am.rules)
	am.rulesMux.RUnlock()

	for _, rule := range rules {
		if !rule.Enabled {
			continue
		}

		am.checkRule(ctx, rule)
	}
}

// checkRule 检查单个规则
func (am *AlertManager) checkRule(ctx context.Context, rule AlertRule) {
	// 这里应该从指标系统获取实际值
	// 为了演示，我们使用模拟值
	value := am.getMetricValue(rule.Metric, rule.Labels)
	
	// 检查条件
	triggered := am.evaluateCondition(value, rule.Condition, rule.Threshold)
	
	alertID := fmt.Sprintf("%s_%s", rule.Name, rule.Metric)
	
	am.alertsMux.Lock()
	defer am.alertsMux.Unlock()
	
	existingAlert, exists := am.alerts[alertID]
	
	if triggered {
		if !exists {
			// 创建新告警
			alert := &Alert{
				ID:        alertID,
				RuleName:  rule.Name,
				Message:   fmt.Sprintf("%s: %s %s %.2f (阈值: %.2f)", rule.Name, rule.Metric, rule.Condition, value, rule.Threshold),
				Severity:  rule.Severity,
				Value:     value,
				Threshold: rule.Threshold,
				Labels:    rule.Labels,
				Status:    "firing",
				StartsAt:  time.Now(),
				UpdatedAt: time.Now(),
			}
			
			am.alerts[alertID] = alert
			am.logger.Warn("触发告警", "rule", rule.Name, "value", value, "threshold", rule.Threshold)
			
			// 发送通知
			am.sendNotification(ctx, alert)
			
			// 记录告警指标
			am.logger.Info("告警触发", "rule", rule.Name, "severity", rule.Severity)
		} else {
			// 更新现有告警
			existingAlert.Value = value
			existingAlert.UpdatedAt = time.Now()
		}
	} else {
		if exists && existingAlert.Status == "firing" {
			// 解决告警
			now := time.Now()
			existingAlert.Status = "resolved"
			existingAlert.EndsAt = &now
			existingAlert.UpdatedAt = now
			
			am.logger.Info("告警已解决", "rule", rule.Name, "value", value)
			
			// 记录告警解决指标
			am.logger.Info("告警解决", "rule", rule.Name, "severity", rule.Severity)
		}
	}
}

// getMetricValue 获取指标值（模拟实现）
func (am *AlertManager) getMetricValue(metric string, labels map[string]string) float64 {
	// 这里应该从实际的指标系统获取值
	// 为了演示，返回模拟值
	switch metric {
	case "performance_memory_usage_bytes":
		return 400 * 1024 * 1024 // 400MB
	case "performance_goroutine_count":
		return 500
	case "gateway_requests_error_rate":
		return 0.02 // 2%
	case "gateway_request_duration_avg":
		return 500 // 500ms
	default:
		return 0
	}
}

// evaluateCondition 评估条件
func (am *AlertManager) evaluateCondition(value float64, condition string, threshold float64) bool {
	switch condition {
	case ">":
		return value > threshold
	case "<":
		return value < threshold
	case ">=":
		return value >= threshold
	case "<=":
		return value <= threshold
	case "==":
		return value == threshold
	case "!=":
		return value != threshold
	default:
		return false
	}
}

// sendNotification 发送通知
func (am *AlertManager) sendNotification(ctx context.Context, alert *Alert) {
	for _, notifier := range am.notifiers {
		go func(n Notifier) {
			if err := n.Send(ctx, alert); err != nil {
				am.logger.Error("发送通知失败", "type", n.Type(), "error", err)
			}
		}(notifier)
	}
}

// DefaultAlertConfig 默认告警配置
func DefaultAlertConfig() AlertConfig {
	return AlertConfig{
		Enabled:       true,
		CheckInterval: 30 * time.Second,
		RulesFile:     "configs/alert-rules.yaml",
		Notifications: []NotificationConfig{
			{
				Type:    "log",
				Enabled: true,
				Config:  map[string]interface{}{},
			},
		},
	}
}
