package monitoring

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"api-gateway/pkg/telemetry"
)

// LogNotifier 日志通知器
type LogNotifier struct {
	logger *telemetry.Logger
}

// NewLogNotifier 创建日志通知器
func NewLogNotifier(logger *telemetry.Logger) *LogNotifier {
	return &LogNotifier{
		logger: logger.With("component", "log-notifier"),
	}
}

// Send 发送日志通知
func (ln *LogNotifier) Send(ctx context.Context, alert *Alert) error {
	switch alert.Severity {
	case "critical":
		ln.logger.Error("🚨 严重告警",
			"rule", alert.RuleName,
			"message", alert.Message,
			"value", alert.Value,
			"threshold", alert.Threshold,
			"status", alert.Status,
			"starts_at", alert.StartsAt.Format(time.RFC3339))
	case "warning":
		ln.logger.Warn("⚠️ 警告告警",
			"rule", alert.RuleName,
			"message", alert.Message,
			"value", alert.Value,
			"threshold", alert.Threshold,
			"status", alert.Status,
			"starts_at", alert.StartsAt.Format(time.RFC3339))
	default:
		ln.logger.Info("ℹ️ 信息告警",
			"rule", alert.RuleName,
			"message", alert.Message,
			"value", alert.Value,
			"threshold", alert.Threshold,
			"status", alert.Status,
			"starts_at", alert.StartsAt.Format(time.RFC3339))
	}

	return nil
}

// Type 返回通知器类型
func (ln *LogNotifier) Type() string {
	return "log"
}

// WebhookNotifier Webhook通知器
type WebhookNotifier struct {
	logger *telemetry.Logger
	config WebhookConfig
	client *http.Client
}

// WebhookConfig Webhook配置
type WebhookConfig struct {
	URL     string            `json:"url"`
	Headers map[string]string `json:"headers"`
	Timeout time.Duration     `json:"timeout"`
}

// NewWebhookNotifier 创建Webhook通知器
func NewWebhookNotifier(config map[string]interface{}, logger *telemetry.Logger) *WebhookNotifier {
	webhookConfig := WebhookConfig{
		Timeout: 10 * time.Second,
		Headers: make(map[string]string),
	}

	// 解析配置
	if url, ok := config["url"].(string); ok {
		webhookConfig.URL = url
	}
	if headers, ok := config["headers"].(map[string]interface{}); ok {
		for k, v := range headers {
			if str, ok := v.(string); ok {
				webhookConfig.Headers[k] = str
			}
		}
	}
	if timeout, ok := config["timeout"].(string); ok {
		if d, err := time.ParseDuration(timeout); err == nil {
			webhookConfig.Timeout = d
		}
	}

	return &WebhookNotifier{
		logger: logger.With("component", "webhook-notifier"),
		config: webhookConfig,
		client: &http.Client{
			Timeout: webhookConfig.Timeout,
		},
	}
}

// Send 发送Webhook通知
func (wn *WebhookNotifier) Send(ctx context.Context, alert *Alert) error {
	if wn.config.URL == "" {
		return fmt.Errorf("webhook URL未配置")
	}

	// 构建通知消息
	message := WebhookMessage{
		Alert:     alert,
		Timestamp: time.Now(),
		Gateway:   "API Gateway",
	}

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化通知消息失败: %w", err)
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", wn.config.URL, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "API-Gateway-AlertManager/1.0")
	for k, v := range wn.config.Headers {
		req.Header.Set(k, v)
	}

	// 发送请求
	resp, err := wn.client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("webhook响应错误: %d %s", resp.StatusCode, resp.Status)
	}

	wn.logger.Info("Webhook通知发送成功",
		"url", wn.config.URL,
		"alert", alert.RuleName,
		"status_code", resp.StatusCode)

	return nil
}

// Type 返回通知器类型
func (wn *WebhookNotifier) Type() string {
	return "webhook"
}

// WebhookMessage Webhook消息格式
type WebhookMessage struct {
	Alert     *Alert    `json:"alert"`
	Timestamp time.Time `json:"timestamp"`
	Gateway   string    `json:"gateway"`
}

// SlackNotifier Slack通知器
type SlackNotifier struct {
	logger *telemetry.Logger
	config SlackConfig
	client *http.Client
}

// SlackConfig Slack配置
type SlackConfig struct {
	WebhookURL string        `json:"webhook_url"`
	Channel    string        `json:"channel"`
	Username   string        `json:"username"`
	IconEmoji  string        `json:"icon_emoji"`
	Timeout    time.Duration `json:"timeout"`
}

// NewSlackNotifier 创建Slack通知器
func NewSlackNotifier(config map[string]interface{}, logger *telemetry.Logger) *SlackNotifier {
	slackConfig := SlackConfig{
		Username:  "API Gateway",
		IconEmoji: ":warning:",
		Timeout:   10 * time.Second,
	}

	// 解析配置
	if url, ok := config["webhook_url"].(string); ok {
		slackConfig.WebhookURL = url
	}
	if channel, ok := config["channel"].(string); ok {
		slackConfig.Channel = channel
	}
	if username, ok := config["username"].(string); ok {
		slackConfig.Username = username
	}
	if emoji, ok := config["icon_emoji"].(string); ok {
		slackConfig.IconEmoji = emoji
	}

	return &SlackNotifier{
		logger: logger.With("component", "slack-notifier"),
		config: slackConfig,
		client: &http.Client{
			Timeout: slackConfig.Timeout,
		},
	}
}

// Send 发送Slack通知
func (sn *SlackNotifier) Send(ctx context.Context, alert *Alert) error {
	if sn.config.WebhookURL == "" {
		return fmt.Errorf("Slack webhook URL未配置")
	}

	// 构建Slack消息
	message := SlackMessage{
		Channel:   sn.config.Channel,
		Username:  sn.config.Username,
		IconEmoji: sn.config.IconEmoji,
		Text:      sn.formatAlertMessage(alert),
		Attachments: []SlackAttachment{
			{
				Color: sn.getAlertColor(alert.Severity),
				Fields: []SlackField{
					{Title: "规则", Value: alert.RuleName, Short: true},
					{Title: "严重级别", Value: alert.Severity, Short: true},
					{Title: "当前值", Value: fmt.Sprintf("%.2f", alert.Value), Short: true},
					{Title: "阈值", Value: fmt.Sprintf("%.2f", alert.Threshold), Short: true},
					{Title: "状态", Value: alert.Status, Short: true},
					{Title: "时间", Value: alert.StartsAt.Format("2006-01-02 15:04:05"), Short: true},
				},
			},
		},
	}

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化Slack消息失败: %w", err)
	}

	// 发送请求
	req, err := http.NewRequestWithContext(ctx, "POST", sn.config.WebhookURL, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := sn.client.Do(req)
	if err != nil {
		return fmt.Errorf("发送Slack请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Slack响应错误: %d %s", resp.StatusCode, resp.Status)
	}

	sn.logger.Info("Slack通知发送成功", "alert", alert.RuleName)
	return nil
}

// Type 返回通知器类型
func (sn *SlackNotifier) Type() string {
	return "slack"
}

// formatAlertMessage 格式化告警消息
func (sn *SlackNotifier) formatAlertMessage(alert *Alert) string {
	emoji := "ℹ️"
	switch alert.Severity {
	case "critical":
		emoji = "🚨"
	case "warning":
		emoji = "⚠️"
	}

	return fmt.Sprintf("%s *%s* - %s", emoji, alert.Severity, alert.Message)
}

// getAlertColor 获取告警颜色
func (sn *SlackNotifier) getAlertColor(severity string) string {
	switch severity {
	case "critical":
		return "danger"
	case "warning":
		return "warning"
	default:
		return "good"
	}
}

// SlackMessage Slack消息格式
type SlackMessage struct {
	Channel     string            `json:"channel,omitempty"`
	Username    string            `json:"username,omitempty"`
	IconEmoji   string            `json:"icon_emoji,omitempty"`
	Text        string            `json:"text"`
	Attachments []SlackAttachment `json:"attachments,omitempty"`
}

// SlackAttachment Slack附件
type SlackAttachment struct {
	Color  string       `json:"color,omitempty"`
	Fields []SlackField `json:"fields,omitempty"`
}

// SlackField Slack字段
type SlackField struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

// EmailNotifier 邮件通知器
type EmailNotifier struct {
	logger *telemetry.Logger
	config EmailConfig
}

// EmailConfig 邮件配置
type EmailConfig struct {
	SMTPHost     string   `json:"smtp_host"`
	SMTPPort     int      `json:"smtp_port"`
	Username     string   `json:"username"`
	Password     string   `json:"password"`
	From         string   `json:"from"`
	To           []string `json:"to"`
	Subject      string   `json:"subject"`
	TemplatePath string   `json:"template_path"`
}

// NewEmailNotifier 创建邮件通知器
func NewEmailNotifier(config map[string]interface{}, logger *telemetry.Logger) *EmailNotifier {
	emailConfig := EmailConfig{
		SMTPPort: 587,
		Subject:  "API Gateway Alert",
	}

	// 解析配置
	if host, ok := config["smtp_host"].(string); ok {
		emailConfig.SMTPHost = host
	}
	if port, ok := config["smtp_port"].(float64); ok {
		emailConfig.SMTPPort = int(port)
	}
	if username, ok := config["username"].(string); ok {
		emailConfig.Username = username
	}
	if password, ok := config["password"].(string); ok {
		emailConfig.Password = password
	}
	if from, ok := config["from"].(string); ok {
		emailConfig.From = from
	}
	if to, ok := config["to"].([]interface{}); ok {
		for _, addr := range to {
			if str, ok := addr.(string); ok {
				emailConfig.To = append(emailConfig.To, str)
			}
		}
	}

	return &EmailNotifier{
		logger: logger.With("component", "email-notifier"),
		config: emailConfig,
	}
}

// Send 发送邮件通知
func (en *EmailNotifier) Send(ctx context.Context, alert *Alert) error {
	// 这里应该实现实际的邮件发送逻辑
	// 为了简化，我们只记录日志
	en.logger.Info("邮件通知发送",
		"to", en.config.To,
		"subject", en.config.Subject,
		"alert", alert.RuleName)

	return nil
}

// Type 返回通知器类型
func (en *EmailNotifier) Type() string {
	return "email"
}
