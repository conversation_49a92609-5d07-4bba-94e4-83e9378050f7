package protocol

import (
	"encoding/json"
	"testing"
)

// TestRequest_Validate 测试请求消息验证
func TestRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		request *Request
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效请求",
			request: &Request{
				JSONRPC: JSONRPCVersion,
				ID:      "test-id",
				Method:  "test.method",
				Params:  map[string]interface{}{"key": "value"},
			},
			wantErr: false,
		},
		{
			name: "无效 JSON-RPC 版本",
			request: &Request{
				JSONRPC: "1.0",
				ID:      "test-id",
				Method:  "test.method",
			},
			wantErr: true,
			errMsg:  "无效的 JSON-RPC 版本",
		},
		{
			name: "空 ID",
			request: &Request{
				JSONRPC: JSONRPCVersion,
				ID:      nil,
				Method:  "test.method",
			},
			wantErr: true,
			errMsg:  "请求 ID 不能为 null",
		},
		{
			name: "空方法名",
			request: &Request{
				JSONRPC: JSONRPCVersion,
				ID:      "test-id",
				Method:  "",
			},
			wantErr: true,
			errMsg:  "方法名不能为空",
		},
		{
			name: "数字 ID",
			request: &Request{
				JSONRPC: JSONRPCVersion,
				ID:      123,
				Method:  "test.method",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Request.Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err != nil && tt.errMsg != "" {
				if !contains(err.Error(), tt.errMsg) {
					t.Errorf("Request.Validate() error = %v, want error containing %v", err, tt.errMsg)
				}
			}
		})
	}
}

// TestResponse_Validate 测试响应消息验证
func TestResponse_Validate(t *testing.T) {
	tests := []struct {
		name     string
		response *Response
		wantErr  bool
		errMsg   string
	}{
		{
			name: "有效成功响应",
			response: &Response{
				JSONRPC: JSONRPCVersion,
				ID:      "test-id",
				Result:  map[string]interface{}{"success": true},
			},
			wantErr: false,
		},
		{
			name: "有效错误响应",
			response: &Response{
				JSONRPC: JSONRPCVersion,
				ID:      "test-id",
				Error: &Error{
					Code:    -32600,
					Message: "Invalid Request",
				},
			},
			wantErr: false,
		},
		{
			name: "同时包含 result 和 error",
			response: &Response{
				JSONRPC: JSONRPCVersion,
				ID:      "test-id",
				Result:  map[string]interface{}{"success": true},
				Error: &Error{
					Code:    -32600,
					Message: "Invalid Request",
				},
			},
			wantErr: true,
			errMsg:  "响应不能同时包含 result 和 error",
		},
		{
			name: "既没有 result 也没有 error",
			response: &Response{
				JSONRPC: JSONRPCVersion,
				ID:      "test-id",
			},
			wantErr: true,
			errMsg:  "响应必须包含 result 或 error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.response.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Response.Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err != nil && tt.errMsg != "" {
				if !contains(err.Error(), tt.errMsg) {
					t.Errorf("Response.Validate() error = %v, want error containing %v", err, tt.errMsg)
				}
			}
		})
	}
}

// TestNotification_Validate 测试通知消息验证
func TestNotification_Validate(t *testing.T) {
	tests := []struct {
		name         string
		notification *Notification
		wantErr      bool
		errMsg       string
	}{
		{
			name: "有效通知",
			notification: &Notification{
				JSONRPC: JSONRPCVersion,
				Method:  "test.notification",
				Params:  map[string]interface{}{"key": "value"},
			},
			wantErr: false,
		},
		{
			name: "无效 JSON-RPC 版本",
			notification: &Notification{
				JSONRPC: "1.0",
				Method:  "test.notification",
			},
			wantErr: true,
			errMsg:  "无效的 JSON-RPC 版本",
		},
		{
			name: "空方法名",
			notification: &Notification{
				JSONRPC: JSONRPCVersion,
				Method:  "",
			},
			wantErr: true,
			errMsg:  "方法名不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.notification.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Notification.Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err != nil && tt.errMsg != "" {
				if !contains(err.Error(), tt.errMsg) {
					t.Errorf("Notification.Validate() error = %v, want error containing %v", err, tt.errMsg)
				}
			}
		})
	}
}

// TestParseMessage 测试消息解析
func TestParseMessage(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		wantType MessageType
		wantErr  bool
	}{
		{
			name:     "解析请求消息",
			jsonData: `{"jsonrpc":"2.0","id":"1","method":"test.method","params":{"key":"value"}}`,
			wantType: MessageTypeRequest,
			wantErr:  false,
		},
		{
			name:     "解析响应消息",
			jsonData: `{"jsonrpc":"2.0","id":"1","result":{"success":true}}`,
			wantType: MessageTypeResponse,
			wantErr:  false,
		},
		{
			name:     "解析通知消息",
			jsonData: `{"jsonrpc":"2.0","method":"test.notification","params":{"key":"value"}}`,
			wantType: MessageTypeNotification,
			wantErr:  false,
		},
		{
			name:     "无效 JSON",
			jsonData: `{"jsonrpc":"2.0","id":"1","method":}`,
			wantErr:  true,
		},
		{
			name:     "无效 JSON-RPC 版本",
			jsonData: `{"jsonrpc":"1.0","id":"1","method":"test.method"}`,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			msg, err := ParseMessage([]byte(tt.jsonData))
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseMessage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && msg.GetType() != tt.wantType {
				t.Errorf("ParseMessage() type = %v, want %v", msg.GetType(), tt.wantType)
			}
		})
	}
}

// TestMessage_ToJSON 测试消息序列化
func TestMessage_ToJSON(t *testing.T) {
	tests := []struct {
		name string
		msg  Message
	}{
		{
			name: "请求消息序列化",
			msg: &Request{
				JSONRPC: JSONRPCVersion,
				ID:      "test-id",
				Method:  "test.method",
				Params:  map[string]interface{}{"key": "value"},
			},
		},
		{
			name: "响应消息序列化",
			msg: &Response{
				JSONRPC: JSONRPCVersion,
				ID:      "test-id",
				Result:  map[string]interface{}{"success": true},
			},
		},
		{
			name: "通知消息序列化",
			msg: &Notification{
				JSONRPC: JSONRPCVersion,
				Method:  "test.notification",
				Params:  map[string]interface{}{"key": "value"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, err := tt.msg.ToJSON()
			if err != nil {
				t.Errorf("Message.ToJSON() error = %v", err)
				return
			}

			// 验证生成的 JSON 可以被解析
			var parsed map[string]interface{}
			if err := json.Unmarshal(jsonData, &parsed); err != nil {
				t.Errorf("生成的 JSON 无法解析: %v", err)
			}

			// 验证 JSON-RPC 版本
			if parsed["jsonrpc"] != JSONRPCVersion {
				t.Errorf("JSON-RPC 版本不正确: got %v, want %v", parsed["jsonrpc"], JSONRPCVersion)
			}
		})
	}
}

// TestNewRequest 测试创建请求
func TestNewRequest(t *testing.T) {
	id := "test-id"
	method := "test.method"
	params := map[string]interface{}{"key": "value"}

	req := NewRequest(id, method, params)

	if req.JSONRPC != JSONRPCVersion {
		t.Errorf("NewRequest() JSONRPC = %v, want %v", req.JSONRPC, JSONRPCVersion)
	}
	if req.ID != id {
		t.Errorf("NewRequest() ID = %v, want %v", req.ID, id)
	}
	if req.Method != method {
		t.Errorf("NewRequest() Method = %v, want %v", req.Method, method)
	}
	if req.Params != params {
		t.Errorf("NewRequest() Params = %v, want %v", req.Params, params)
	}
}

// TestNewResponse 测试创建响应
func TestNewResponse(t *testing.T) {
	id := "test-id"
	result := map[string]interface{}{"success": true}

	resp := NewResponse(id, result)

	if resp.JSONRPC != JSONRPCVersion {
		t.Errorf("NewResponse() JSONRPC = %v, want %v", resp.JSONRPC, JSONRPCVersion)
	}
	if resp.ID != id {
		t.Errorf("NewResponse() ID = %v, want %v", resp.ID, id)
	}
	if resp.Result != result {
		t.Errorf("NewResponse() Result = %v, want %v", resp.Result, result)
	}
}

// TestNewErrorResponse 测试创建错误响应
func TestNewErrorResponse(t *testing.T) {
	id := "test-id"
	err := &Error{
		Code:    -32600,
		Message: "Invalid Request",
	}

	resp := NewErrorResponse(id, err)

	if resp.JSONRPC != JSONRPCVersion {
		t.Errorf("NewErrorResponse() JSONRPC = %v, want %v", resp.JSONRPC, JSONRPCVersion)
	}
	if resp.ID != id {
		t.Errorf("NewErrorResponse() ID = %v, want %v", resp.ID, id)
	}
	if resp.Error != err {
		t.Errorf("NewErrorResponse() Error = %v, want %v", resp.Error, err)
	}
}

// TestError_Error 测试错误接口实现
func TestError_Error(t *testing.T) {
	err := &Error{
		Code:    -32600,
		Message: "Invalid Request",
		Data:    "additional data",
	}

	expected := "JSON-RPC 错误 -32600: Invalid Request"
	if err.Error() != expected {
		t.Errorf("Error.Error() = %v, want %v", err.Error(), expected)
	}
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || containsSubstring(s, substr))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
