package protocol

import (
	"context"
	"fmt"
)

// MCP 协议方法名常量
const (
	// MethodInitialize 初始化方法
	MethodInitialize = "initialize"
	// MethodInitialized 初始化完成通知
	MethodInitialized = "initialized"
	// MethodShutdown 关闭方法
	MethodShutdown = "shutdown"
	// MethodExit 退出通知
	MethodExit = "exit"
)

// InitializeParams 初始化参数
type InitializeParams struct {
	// 协议版本
	ProtocolVersion string `json:"protocolVersion"`
	
	// 客户端能力
	Capabilities *ClientCapabilities `json:"capabilities"`
	
	// 客户端信息
	ClientInfo *ClientInfo `json:"clientInfo"`
}

// InitializeResult 初始化结果
type InitializeResult struct {
	// 协议版本
	ProtocolVersion string `json:"protocolVersion"`
	
	// 服务器能力
	Capabilities *ServerCapabilities `json:"capabilities"`
	
	// 服务器信息
	ServerInfo *ServerInfo `json:"serverInfo"`
}

// LifecycleManager MCP 生命周期管理器
type LifecycleManager struct {
	// 会话管理器
	session *Session
	
	// 支持的协议版本
	supportedVersions []string
	
	// 服务器信息
	serverInfo *ServerInfo
	
	// 服务器能力
	serverCapabilities *ServerCapabilities
	
	// 初始化处理器
	initializeHandler func(params *InitializeParams) (*InitializeResult, error)
	
	// 关闭处理器
	shutdownHandler func() error
}

// NewLifecycleManager 创建新的生命周期管理器
func NewLifecycleManager(session *Session, serverInfo *ServerInfo, serverCapabilities *ServerCapabilities) *LifecycleManager {
	return &LifecycleManager{
		session:            session,
		supportedVersions:  []string{"2024-11-05"},
		serverInfo:         serverInfo,
		serverCapabilities: serverCapabilities,
	}
}

// SetInitializeHandler 设置初始化处理器
func (lm *LifecycleManager) SetInitializeHandler(handler func(params *InitializeParams) (*InitializeResult, error)) {
	lm.initializeHandler = handler
}

// SetShutdownHandler 设置关闭处理器
func (lm *LifecycleManager) SetShutdownHandler(handler func() error) {
	lm.shutdownHandler = handler
}

// HandleRequest 处理请求消息
func (lm *LifecycleManager) HandleRequest(ctx context.Context, req *Request) (*Response, error) {
	switch req.Method {
	case MethodInitialize:
		return lm.handleInitialize(ctx, req)
	case MethodShutdown:
		return lm.handleShutdown(ctx, req)
	default:
		return nil, fmt.Errorf("不支持的生命周期方法: %s", req.Method)
	}
}

// HandleNotification 处理通知消息
func (lm *LifecycleManager) HandleNotification(ctx context.Context, notif *Notification) error {
	switch notif.Method {
	case MethodInitialized:
		return lm.handleInitialized(ctx, notif)
	case MethodExit:
		return lm.handleExit(ctx, notif)
	default:
		return fmt.Errorf("不支持的生命周期通知: %s", notif.Method)
	}
}

// handleInitialize 处理初始化请求
func (lm *LifecycleManager) handleInitialize(ctx context.Context, req *Request) (*Response, error) {
	// 检查会话状态
	if lm.session.GetState() != SessionStateUninitialized {
		return NewErrorResponse(req.ID, NewError(
			ErrorCodeInvalidRequest,
			"会话已初始化",
			nil,
		)), nil
	}
	
	// 解析初始化参数
	var params InitializeParams
	if req.Params != nil {
		// 这里需要将 req.Params 转换为 InitializeParams
		// 实际实现中可能需要使用 json.Marshal/Unmarshal
		if paramsMap, ok := req.Params.(map[string]interface{}); ok {
			if protocolVersion, ok := paramsMap["protocolVersion"].(string); ok {
				params.ProtocolVersion = protocolVersion
			}
			
			// 解析客户端信息
			if clientInfoMap, ok := paramsMap["clientInfo"].(map[string]interface{}); ok {
				params.ClientInfo = &ClientInfo{}
				if name, ok := clientInfoMap["name"].(string); ok {
					params.ClientInfo.Name = name
				}
				if version, ok := clientInfoMap["version"].(string); ok {
					params.ClientInfo.Version = version
				}
				if description, ok := clientInfoMap["description"].(string); ok {
					params.ClientInfo.Description = description
				}
			}
			
			// 解析客户端能力
			if capabilitiesMap, ok := paramsMap["capabilities"].(map[string]interface{}); ok {
				params.Capabilities = &ClientCapabilities{}
				
				// 解析根目录能力
				if rootsMap, ok := capabilitiesMap["roots"].(map[string]interface{}); ok {
					params.Capabilities.Roots = &RootsCapability{}
					if listChanged, ok := rootsMap["listChanged"].(bool); ok {
						params.Capabilities.Roots.ListChanged = listChanged
					}
				}
				
				// 解析采样能力
				if samplingMap, ok := capabilitiesMap["sampling"].(map[string]interface{}); ok {
					params.Capabilities.Sampling = &SamplingCapability{
						Config: samplingMap,
					}
				}
				
				// 解析实验性功能
				if experimental, ok := capabilitiesMap["experimental"].(map[string]interface{}); ok {
					params.Capabilities.Experimental = experimental
				}
			}
		}
	}
	
	// 验证协议版本
	if !lm.isSupportedVersion(params.ProtocolVersion) {
		return NewErrorResponse(req.ID, NewError(
			ErrorCodeInvalidParams,
			fmt.Sprintf("不支持的协议版本: %s", params.ProtocolVersion),
			map[string]interface{}{
				"supported_versions": lm.supportedVersions,
			},
		)), nil
	}
	
	// 初始化会话
	err := lm.session.Initialize(params.ProtocolVersion, params.ClientInfo, params.Capabilities)
	if err != nil {
		return NewErrorResponse(req.ID, NewError(
			ErrorCodeInternalError,
			fmt.Sprintf("会话初始化失败: %v", err),
			nil,
		)), nil
	}
	
	// 设置服务器信息
	lm.session.SetServerInfo(lm.serverInfo, lm.serverCapabilities)
	
	// 创建初始化结果
	result := &InitializeResult{
		ProtocolVersion: params.ProtocolVersion,
		Capabilities:    lm.serverCapabilities,
		ServerInfo:      lm.serverInfo,
	}
	
	// 调用自定义初始化处理器
	if lm.initializeHandler != nil {
		customResult, err := lm.initializeHandler(&params)
		if err != nil {
			return NewErrorResponse(req.ID, NewError(
				ErrorCodeInternalError,
				fmt.Sprintf("初始化处理失败: %v", err),
				nil,
			)), nil
		}
		if customResult != nil {
			result = customResult
		}
	}
	
	return NewResponse(req.ID, result), nil
}

// handleInitialized 处理初始化完成通知
func (lm *LifecycleManager) handleInitialized(ctx context.Context, notif *Notification) error {
	// 检查会话状态
	if lm.session.GetState() != SessionStateReady {
		return fmt.Errorf("会话未就绪，无法处理 initialized 通知")
	}
	
	// 这里可以添加初始化完成后的逻辑
	
	return nil
}

// handleShutdown 处理关闭请求
func (lm *LifecycleManager) handleShutdown(ctx context.Context, req *Request) (*Response, error) {
	// 检查会话状态
	if lm.session.GetState() == SessionStateClosed {
		return NewResponse(req.ID, nil), nil
	}
	
	// 调用自定义关闭处理器
	if lm.shutdownHandler != nil {
		if err := lm.shutdownHandler(); err != nil {
			return NewErrorResponse(req.ID, NewError(
				ErrorCodeInternalError,
				fmt.Sprintf("关闭处理失败: %v", err),
				nil,
			)), nil
		}
	}
	
	// 设置会话状态为关闭中
	lm.session.SetState(SessionStateClosing)
	
	return NewResponse(req.ID, nil), nil
}

// handleExit 处理退出通知
func (lm *LifecycleManager) handleExit(ctx context.Context, notif *Notification) error {
	// 关闭会话
	return lm.session.Close()
}

// isSupportedVersion 检查是否支持指定的协议版本
func (lm *LifecycleManager) isSupportedVersion(version string) bool {
	for _, v := range lm.supportedVersions {
		if v == version {
			return true
		}
	}
	return false
}

// GetSupportedVersions 获取支持的协议版本
func (lm *LifecycleManager) GetSupportedVersions() []string {
	return lm.supportedVersions
}

// AddSupportedVersion 添加支持的协议版本
func (lm *LifecycleManager) AddSupportedVersion(version string) {
	for _, v := range lm.supportedVersions {
		if v == version {
			return // 已存在
		}
	}
	lm.supportedVersions = append(lm.supportedVersions, version)
}

// CreateInitializeRequest 创建初始化请求
func CreateInitializeRequest(id interface{}, protocolVersion string, clientInfo *ClientInfo, capabilities *ClientCapabilities) *Request {
	params := &InitializeParams{
		ProtocolVersion: protocolVersion,
		Capabilities:    capabilities,
		ClientInfo:      clientInfo,
	}
	
	return NewRequest(id, MethodInitialize, params)
}

// CreateInitializedNotification 创建初始化完成通知
func CreateInitializedNotification() *Notification {
	return NewNotification(MethodInitialized, nil)
}

// CreateShutdownRequest 创建关闭请求
func CreateShutdownRequest(id interface{}) *Request {
	return NewRequest(id, MethodShutdown, nil)
}

// CreateExitNotification 创建退出通知
func CreateExitNotification() *Notification {
	return NewNotification(MethodExit, nil)
}
