package protocol

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// SessionState 会话状态
type SessionState string

const (
	// SessionStateUninitialized 未初始化状态
	SessionStateUninitialized SessionState = "uninitialized"
	// SessionStateInitializing 初始化中状态
	SessionStateInitializing SessionState = "initializing"
	// SessionStateReady 就绪状态
	SessionStateReady SessionState = "ready"
	// SessionStateClosing 关闭中状态
	SessionStateClosing SessionState = "closing"
	// SessionStateClosed 已关闭状态
	SessionStateClosed SessionState = "closed"
	// SessionStateError 错误状态
	SessionStateError SessionState = "error"
)

// Session MCP 会话管理器
type Session struct {
	// 会话 ID
	ID string
	
	// 会话状态
	state SessionState
	
	// 状态锁
	stateMu sync.RWMutex
	
	// 协议版本
	protocolVersion string
	
	// 客户端信息
	clientInfo *ClientInfo
	
	// 服务器信息
	serverInfo *ServerInfo
	
	// 客户端能力
	clientCapabilities *ClientCapabilities
	
	// 服务器能力
	serverCapabilities *ServerCapabilities
	
	// 会话创建时间
	createdAt time.Time
	
	// 最后活跃时间
	lastActiveAt time.Time
	
	// 会话超时时间
	timeout time.Duration
	
	// 上下文
	ctx context.Context
	
	// 取消函数
	cancel context.CancelFunc
	
	// 消息处理器
	messageHandler MessageHandler
	
	// 错误处理器
	errorHandler ErrorHandler
	
	// 会话事件通道
	events chan SessionEvent
	
	// 等待组
	wg sync.WaitGroup
}

// ClientInfo 客户端信息
type ClientInfo struct {
	// 客户端名称
	Name string `json:"name"`
	
	// 客户端版本
	Version string `json:"version"`
	
	// 客户端描述
	Description string `json:"description,omitempty"`
	
	// 额外信息
	Extra map[string]interface{} `json:"extra,omitempty"`
}

// ServerInfo 服务器信息
type ServerInfo struct {
	// 服务器名称
	Name string `json:"name"`
	
	// 服务器版本
	Version string `json:"version"`
	
	// 服务器描述
	Description string `json:"description,omitempty"`
	
	// 额外信息
	Extra map[string]interface{} `json:"extra,omitempty"`
}

// ClientCapabilities 客户端能力
type ClientCapabilities struct {
	// 根目录支持
	Roots *RootsCapability `json:"roots,omitempty"`
	
	// 采样支持
	Sampling *SamplingCapability `json:"sampling,omitempty"`
	
	// 实验性功能
	Experimental map[string]interface{} `json:"experimental,omitempty"`
}

// ServerCapabilities 服务器能力
type ServerCapabilities struct {
	// 日志支持
	Logging *LoggingCapability `json:"logging,omitempty"`
	
	// 提示支持
	Prompts *PromptsCapability `json:"prompts,omitempty"`
	
	// 资源支持
	Resources *ResourcesCapability `json:"resources,omitempty"`
	
	// 工具支持
	Tools *ToolsCapability `json:"tools,omitempty"`
	
	// 实验性功能
	Experimental map[string]interface{} `json:"experimental,omitempty"`
}

// RootsCapability 根目录能力
type RootsCapability struct {
	// 是否支持列表变更通知
	ListChanged bool `json:"listChanged,omitempty"`
}

// SamplingCapability 采样能力
type SamplingCapability struct {
	// 采样配置
	Config map[string]interface{} `json:"config,omitempty"`
}

// LoggingCapability 日志能力
type LoggingCapability struct {
	// 日志配置
	Config map[string]interface{} `json:"config,omitempty"`
}

// PromptsCapability 提示能力
type PromptsCapability struct {
	// 是否支持列表变更通知
	ListChanged bool `json:"listChanged,omitempty"`
}

// ResourcesCapability 资源能力
type ResourcesCapability struct {
	// 是否支持订阅
	Subscribe bool `json:"subscribe,omitempty"`
	
	// 是否支持列表变更通知
	ListChanged bool `json:"listChanged,omitempty"`
}

// ToolsCapability 工具能力
type ToolsCapability struct {
	// 是否支持列表变更通知
	ListChanged bool `json:"listChanged,omitempty"`
}

// MessageHandler 消息处理器接口
type MessageHandler interface {
	// HandleRequest 处理请求消息
	HandleRequest(ctx context.Context, req *Request) (*Response, error)
	
	// HandleNotification 处理通知消息
	HandleNotification(ctx context.Context, notif *Notification) error
}

// ErrorHandler 错误处理器接口
type ErrorHandler interface {
	// HandleError 处理错误
	HandleError(ctx context.Context, err error)
}

// SessionEvent 会话事件
type SessionEvent struct {
	// 事件类型
	Type SessionEventType
	
	// 会话 ID
	SessionID string
	
	// 事件时间
	Timestamp time.Time
	
	// 事件数据
	Data interface{}
	
	// 错误信息
	Error error
}

// SessionEventType 会话事件类型
type SessionEventType string

const (
	// SessionEventTypeStateChanged 状态变更事件
	SessionEventTypeStateChanged SessionEventType = "state_changed"
	// SessionEventTypeMessageReceived 消息接收事件
	SessionEventTypeMessageReceived SessionEventType = "message_received"
	// SessionEventTypeMessageSent 消息发送事件
	SessionEventTypeMessageSent SessionEventType = "message_sent"
	// SessionEventTypeError 错误事件
	SessionEventTypeError SessionEventType = "error"
	// SessionEventTypeTimeout 超时事件
	SessionEventTypeTimeout SessionEventType = "timeout"
)

// NewSession 创建新的会话
func NewSession(id string, timeout time.Duration) *Session {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &Session{
		ID:             id,
		state:          SessionStateUninitialized,
		createdAt:      time.Now(),
		lastActiveAt:   time.Now(),
		timeout:        timeout,
		ctx:            ctx,
		cancel:         cancel,
		events:         make(chan SessionEvent, 100),
	}
}

// GetState 获取会话状态
func (s *Session) GetState() SessionState {
	s.stateMu.RLock()
	defer s.stateMu.RUnlock()
	return s.state
}

// SetState 设置会话状态
func (s *Session) SetState(state SessionState) {
	s.stateMu.Lock()
	oldState := s.state
	s.state = state
	s.stateMu.Unlock()
	
	// 发送状态变更事件
	s.emitEvent(SessionEventTypeStateChanged, map[string]interface{}{
		"old_state": oldState,
		"new_state": state,
	}, nil)
}

// Initialize 初始化会话
func (s *Session) Initialize(protocolVersion string, clientInfo *ClientInfo, clientCapabilities *ClientCapabilities) error {
	if s.GetState() != SessionStateUninitialized {
		return fmt.Errorf("会话已初始化，当前状态: %s", s.GetState())
	}
	
	s.SetState(SessionStateInitializing)
	
	s.protocolVersion = protocolVersion
	s.clientInfo = clientInfo
	s.clientCapabilities = clientCapabilities
	
	// 这里可以添加初始化逻辑
	
	s.SetState(SessionStateReady)
	return nil
}

// SetServerInfo 设置服务器信息
func (s *Session) SetServerInfo(serverInfo *ServerInfo, serverCapabilities *ServerCapabilities) {
	s.serverInfo = serverInfo
	s.serverCapabilities = serverCapabilities
}

// GetClientInfo 获取客户端信息
func (s *Session) GetClientInfo() *ClientInfo {
	return s.clientInfo
}

// GetServerInfo 获取服务器信息
func (s *Session) GetServerInfo() *ServerInfo {
	return s.serverInfo
}

// GetClientCapabilities 获取客户端能力
func (s *Session) GetClientCapabilities() *ClientCapabilities {
	return s.clientCapabilities
}

// GetServerCapabilities 获取服务器能力
func (s *Session) GetServerCapabilities() *ServerCapabilities {
	return s.serverCapabilities
}

// SetMessageHandler 设置消息处理器
func (s *Session) SetMessageHandler(handler MessageHandler) {
	s.messageHandler = handler
}

// SetErrorHandler 设置错误处理器
func (s *Session) SetErrorHandler(handler ErrorHandler) {
	s.errorHandler = handler
}

// ProcessMessage 处理消息
func (s *Session) ProcessMessage(msg Message) (*Response, error) {
	if s.GetState() != SessionStateReady {
		return nil, fmt.Errorf("会话未就绪，当前状态: %s", s.GetState())
	}
	
	s.updateLastActiveTime()
	
	switch m := msg.(type) {
	case *Request:
		s.emitEvent(SessionEventTypeMessageReceived, m, nil)
		if s.messageHandler != nil {
			resp, err := s.messageHandler.HandleRequest(s.ctx, m)
			if err != nil {
				s.handleError(err)
				return nil, err
			}
			s.emitEvent(SessionEventTypeMessageSent, resp, nil)
			return resp, nil
		}
		return nil, fmt.Errorf("未设置消息处理器")
		
	case *Notification:
		s.emitEvent(SessionEventTypeMessageReceived, m, nil)
		if s.messageHandler != nil {
			err := s.messageHandler.HandleNotification(s.ctx, m)
			if err != nil {
				s.handleError(err)
				return nil, err
			}
		}
		return nil, nil
		
	default:
		return nil, fmt.Errorf("不支持的消息类型: %T", msg)
	}
}

// Close 关闭会话
func (s *Session) Close() error {
	if s.GetState() == SessionStateClosed {
		return nil
	}
	
	s.SetState(SessionStateClosing)
	
	// 取消上下文
	s.cancel()
	
	// 等待所有 goroutine 完成
	s.wg.Wait()
	
	// 关闭事件通道
	close(s.events)
	
	s.SetState(SessionStateClosed)
	return nil
}

// IsExpired 检查会话是否过期
func (s *Session) IsExpired() bool {
	return time.Since(s.lastActiveAt) > s.timeout
}

// GetEvents 获取事件通道
func (s *Session) GetEvents() <-chan SessionEvent {
	return s.events
}

// updateLastActiveTime 更新最后活跃时间
func (s *Session) updateLastActiveTime() {
	s.lastActiveAt = time.Now()
}

// emitEvent 发送事件
func (s *Session) emitEvent(eventType SessionEventType, data interface{}, err error) {
	event := SessionEvent{
		Type:      eventType,
		SessionID: s.ID,
		Timestamp: time.Now(),
		Data:      data,
		Error:     err,
	}
	
	select {
	case s.events <- event:
	default:
		// 事件通道满了，丢弃事件
	}
}

// handleError 处理错误
func (s *Session) handleError(err error) {
	s.emitEvent(SessionEventTypeError, nil, err)
	
	if s.errorHandler != nil {
		s.errorHandler.HandleError(s.ctx, err)
	}
}
