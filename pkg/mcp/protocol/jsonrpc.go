package protocol

import (
	"encoding/json"
	"fmt"
)

// JSONRPCVersion JSON-RPC 协议版本
const JSONRPCVersion = "2.0"

// MessageType 消息类型
type MessageType string

const (
	// MessageTypeRequest 请求消息
	MessageTypeRequest MessageType = "request"
	// MessageTypeResponse 响应消息
	MessageTypeResponse MessageType = "response"
	// MessageTypeNotification 通知消息
	MessageTypeNotification MessageType = "notification"
)

// Message JSON-RPC 消息接口
type Message interface {
	// GetType 获取消息类型
	GetType() MessageType
	// Validate 验证消息格式
	Validate() error
	// ToJSON 转换为 JSON
	ToJSON() ([]byte, error)
}

// Request JSON-RPC 请求消息
type Request struct {
	// JSON-RPC 版本，必须为 "2.0"
	JSONRPC string `json:"jsonrpc"`
	
	// 请求 ID，字符串或数字类型，不能为 null
	ID interface{} `json:"id"`
	
	// 方法名
	Method string `json:"method"`
	
	// 参数（可选）
	Params interface{} `json:"params,omitempty"`
}

// GetType 获取消息类型
func (r *Request) GetType() MessageType {
	return MessageTypeRequest
}

// Validate 验证请求消息格式
func (r *Request) Validate() error {
	if r.JSONRPC != JSONRPCVersion {
		return fmt.Errorf("无效的 JSON-RPC 版本: %s，期望: %s", r.JSONRPC, JSONRPCVersion)
	}
	
	if r.ID == nil {
		return fmt.Errorf("请求 ID 不能为 null")
	}
	
	// 验证 ID 类型（必须是字符串或数字）
	switch r.ID.(type) {
	case string, int, int32, int64, float32, float64:
		// 有效类型
	default:
		return fmt.Errorf("请求 ID 必须是字符串或数字类型")
	}
	
	if r.Method == "" {
		return fmt.Errorf("方法名不能为空")
	}
	
	return nil
}

// ToJSON 转换为 JSON
func (r *Request) ToJSON() ([]byte, error) {
	return json.Marshal(r)
}

// Response JSON-RPC 响应消息
type Response struct {
	// JSON-RPC 版本，必须为 "2.0"
	JSONRPC string `json:"jsonrpc"`
	
	// 请求 ID，与对应请求的 ID 相同
	ID interface{} `json:"id"`
	
	// 成功结果（与 Error 互斥）
	Result interface{} `json:"result,omitempty"`
	
	// 错误信息（与 Result 互斥）
	Error *Error `json:"error,omitempty"`
}

// GetType 获取消息类型
func (r *Response) GetType() MessageType {
	return MessageTypeResponse
}

// Validate 验证响应消息格式
func (r *Response) Validate() error {
	if r.JSONRPC != JSONRPCVersion {
		return fmt.Errorf("无效的 JSON-RPC 版本: %s，期望: %s", r.JSONRPC, JSONRPCVersion)
	}
	
	// Result 和 Error 必须有且仅有一个
	if r.Result != nil && r.Error != nil {
		return fmt.Errorf("响应不能同时包含 result 和 error")
	}
	
	if r.Result == nil && r.Error == nil {
		return fmt.Errorf("响应必须包含 result 或 error")
	}
	
	// 如果有错误，验证错误格式
	if r.Error != nil {
		if err := r.Error.Validate(); err != nil {
			return fmt.Errorf("错误格式无效: %w", err)
		}
	}
	
	return nil
}

// ToJSON 转换为 JSON
func (r *Response) ToJSON() ([]byte, error) {
	return json.Marshal(r)
}

// Notification JSON-RPC 通知消息
type Notification struct {
	// JSON-RPC 版本，必须为 "2.0"
	JSONRPC string `json:"jsonrpc"`
	
	// 方法名
	Method string `json:"method"`
	
	// 参数（可选）
	Params interface{} `json:"params,omitempty"`
}

// GetType 获取消息类型
func (n *Notification) GetType() MessageType {
	return MessageTypeNotification
}

// Validate 验证通知消息格式
func (n *Notification) Validate() error {
	if n.JSONRPC != JSONRPCVersion {
		return fmt.Errorf("无效的 JSON-RPC 版本: %s，期望: %s", n.JSONRPC, JSONRPCVersion)
	}
	
	if n.Method == "" {
		return fmt.Errorf("方法名不能为空")
	}
	
	return nil
}

// ToJSON 转换为 JSON
func (n *Notification) ToJSON() ([]byte, error) {
	return json.Marshal(n)
}

// Error JSON-RPC 错误信息
type Error struct {
	// 错误码，必须是整数
	Code int `json:"code"`
	
	// 错误消息
	Message string `json:"message"`
	
	// 错误数据（可选）
	Data interface{} `json:"data,omitempty"`
}

// Validate 验证错误格式
func (e *Error) Validate() error {
	if e.Message == "" {
		return fmt.Errorf("错误消息不能为空")
	}
	
	return nil
}

// Error 实现 error 接口
func (e *Error) Error() string {
	return fmt.Sprintf("JSON-RPC 错误 %d: %s", e.Code, e.Message)
}

// 预定义的错误码
const (
	// ErrorCodeParseError 解析错误
	ErrorCodeParseError = -32700
	// ErrorCodeInvalidRequest 无效请求
	ErrorCodeInvalidRequest = -32600
	// ErrorCodeMethodNotFound 方法未找到
	ErrorCodeMethodNotFound = -32601
	// ErrorCodeInvalidParams 无效参数
	ErrorCodeInvalidParams = -32602
	// ErrorCodeInternalError 内部错误
	ErrorCodeInternalError = -32603
)

// 预定义错误
var (
	// ErrParseError 解析错误
	ErrParseError = &Error{
		Code:    ErrorCodeParseError,
		Message: "解析错误",
	}
	
	// ErrInvalidRequest 无效请求
	ErrInvalidRequest = &Error{
		Code:    ErrorCodeInvalidRequest,
		Message: "无效请求",
	}
	
	// ErrMethodNotFound 方法未找到
	ErrMethodNotFound = &Error{
		Code:    ErrorCodeMethodNotFound,
		Message: "方法未找到",
	}
	
	// ErrInvalidParams 无效参数
	ErrInvalidParams = &Error{
		Code:    ErrorCodeInvalidParams,
		Message: "无效参数",
	}
	
	// ErrInternalError 内部错误
	ErrInternalError = &Error{
		Code:    ErrorCodeInternalError,
		Message: "内部错误",
	}
)

// NewRequest 创建新的请求消息
func NewRequest(id interface{}, method string, params interface{}) *Request {
	return &Request{
		JSONRPC: JSONRPCVersion,
		ID:      id,
		Method:  method,
		Params:  params,
	}
}

// NewResponse 创建新的响应消息
func NewResponse(id interface{}, result interface{}) *Response {
	return &Response{
		JSONRPC: JSONRPCVersion,
		ID:      id,
		Result:  result,
	}
}

// NewErrorResponse 创建新的错误响应消息
func NewErrorResponse(id interface{}, err *Error) *Response {
	return &Response{
		JSONRPC: JSONRPCVersion,
		ID:      id,
		Error:   err,
	}
}

// NewNotification 创建新的通知消息
func NewNotification(method string, params interface{}) *Notification {
	return &Notification{
		JSONRPC: JSONRPCVersion,
		Method:  method,
		Params:  params,
	}
}

// NewError 创建新的错误
func NewError(code int, message string, data interface{}) *Error {
	return &Error{
		Code:    code,
		Message: message,
		Data:    data,
	}
}

// ParseMessage 解析 JSON-RPC 消息
func ParseMessage(data []byte) (Message, error) {
	// 首先尝试解析为通用的 JSON 对象
	var raw map[string]interface{}
	if err := json.Unmarshal(data, &raw); err != nil {
		return nil, fmt.Errorf("JSON 解析失败: %w", err)
	}
	
	// 检查 JSON-RPC 版本
	jsonrpc, ok := raw["jsonrpc"].(string)
	if !ok || jsonrpc != JSONRPCVersion {
		return nil, fmt.Errorf("无效的 JSON-RPC 版本")
	}
	
	// 根据是否有 ID 字段判断消息类型
	if _, hasID := raw["id"]; hasID {
		// 有 ID 字段，检查是否有 method 字段
		if _, hasMethod := raw["method"]; hasMethod {
			// 有 method 字段，是请求消息
			var req Request
			if err := json.Unmarshal(data, &req); err != nil {
				return nil, fmt.Errorf("请求消息解析失败: %w", err)
			}
			return &req, nil
		} else {
			// 没有 method 字段，是响应消息
			var resp Response
			if err := json.Unmarshal(data, &resp); err != nil {
				return nil, fmt.Errorf("响应消息解析失败: %w", err)
			}
			return &resp, nil
		}
	} else {
		// 没有 ID 字段，是通知消息
		var notif Notification
		if err := json.Unmarshal(data, &notif); err != nil {
			return nil, fmt.Errorf("通知消息解析失败: %w", err)
		}
		return &notif, nil
	}
}
