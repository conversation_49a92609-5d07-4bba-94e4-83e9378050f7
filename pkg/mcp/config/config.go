package config

import (
	"time"
)

// MCPConfig MCP 转换功能的配置结构
type MCPConfig struct {
	// 是否启用 MCP 转换功能
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// MCP 协议版本
	ProtocolVersion string `yaml:"protocol_version" json:"protocol_version"`
	
	// 转换规则配置
	ConversionRules []ConversionRule `yaml:"conversion_rules" json:"conversion_rules"`
	
	// 服务器配置
	Server ServerConfig `yaml:"server" json:"server"`
	
	// 传输配置
	Transport TransportConfig `yaml:"transport" json:"transport"`
	
	// 性能配置
	Performance PerformanceConfig `yaml:"performance" json:"performance"`
	
	// 日志配置
	Logging LoggingConfig `yaml:"logging" json:"logging"`
}

// ConversionRule 转换规则配置
type ConversionRule struct {
	// 匹配的路径模式
	Path string `yaml:"path" json:"path"`
	
	// 支持的 HTTP 方法
	Methods []string `yaml:"methods" json:"methods"`
	
	// 对应的 MCP 方法名
	MCPMethod string `yaml:"mcp_method" json:"mcp_method"`
	
	// 参数映射配置
	ParameterMapping ParameterMapping `yaml:"parameter_mapping" json:"parameter_mapping"`
	
	// 响应映射配置
	ResponseMapping ResponseMapping `yaml:"response_mapping" json:"response_mapping"`
	
	// 错误映射配置
	ErrorMapping ErrorMapping `yaml:"error_mapping" json:"error_mapping"`
	
	// 是否启用此规则
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// 规则优先级（数字越大优先级越高）
	Priority int `yaml:"priority" json:"priority"`
	
	// 规则描述
	Description string `yaml:"description" json:"description"`
}

// ParameterMapping 参数映射配置
type ParameterMapping struct {
	// 路径参数映射 (如 {id} -> user.id)
	Path map[string]string `yaml:"path" json:"path"`
	
	// 查询参数映射
	Query map[string]string `yaml:"query" json:"query"`
	
	// 请求体参数映射
	Body map[string]string `yaml:"body" json:"body"`
	
	// 请求头映射
	Headers map[string]string `yaml:"headers" json:"headers"`
	
	// 自定义映射函数
	CustomMapping string `yaml:"custom_mapping" json:"custom_mapping"`
}

// ResponseMapping 响应映射配置
type ResponseMapping struct {
	// 成功响应映射
	Success map[string]string `yaml:"success" json:"success"`
	
	// 错误响应映射
	Error map[string]string `yaml:"error" json:"error"`
	
	// 状态码映射
	StatusCode map[int]int `yaml:"status_code" json:"status_code"`
	
	// 响应头映射
	Headers map[string]string `yaml:"headers" json:"headers"`
	
	// 自定义响应转换函数
	CustomMapping string `yaml:"custom_mapping" json:"custom_mapping"`
}

// ErrorMapping 错误映射配置
type ErrorMapping struct {
	// HTTP 状态码到 MCP 错误码的映射
	StatusCodeMapping map[int]int `yaml:"status_code_mapping" json:"status_code_mapping"`
	
	// 错误消息映射
	MessageMapping map[string]string `yaml:"message_mapping" json:"message_mapping"`
	
	// 默认错误码
	DefaultErrorCode int `yaml:"default_error_code" json:"default_error_code"`
	
	// 默认错误消息
	DefaultErrorMessage string `yaml:"default_error_message" json:"default_error_message"`
}

// ServerConfig MCP 服务器配置
type ServerConfig struct {
	// 服务器名称
	Name string `yaml:"name" json:"name"`
	
	// 服务器版本
	Version string `yaml:"version" json:"version"`
	
	// 服务器描述
	Description string `yaml:"description" json:"description"`
	
	// 服务器能力配置
	Capabilities ServerCapabilities `yaml:"capabilities" json:"capabilities"`
	
	// 服务器信息
	Info map[string]interface{} `yaml:"info" json:"info"`
}

// ServerCapabilities 服务器能力配置
type ServerCapabilities struct {
	// 是否支持资源
	Resources bool `yaml:"resources" json:"resources"`
	
	// 是否支持工具
	Tools bool `yaml:"tools" json:"tools"`
	
	// 是否支持提示
	Prompts bool `yaml:"prompts" json:"prompts"`
	
	// 是否支持日志
	Logging bool `yaml:"logging" json:"logging"`
	
	// 是否支持采样
	Sampling bool `yaml:"sampling" json:"sampling"`
	
	// 实验性功能
	Experimental map[string]interface{} `yaml:"experimental" json:"experimental"`
}

// TransportConfig 传输配置
type TransportConfig struct {
	// 传输类型 (stdio, sse, custom)
	Type string `yaml:"type" json:"type"`
	
	// stdio 传输配置
	Stdio StdioConfig `yaml:"stdio" json:"stdio"`
	
	// SSE 传输配置
	SSE SSEConfig `yaml:"sse" json:"sse"`
	
	// 自定义传输配置
	Custom map[string]interface{} `yaml:"custom" json:"custom"`
}

// StdioConfig stdio 传输配置
type StdioConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// 缓冲区大小
	BufferSize int `yaml:"buffer_size" json:"buffer_size"`
	
	// 超时设置
	Timeout time.Duration `yaml:"timeout" json:"timeout"`
}

// SSEConfig SSE 传输配置
type SSEConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// SSE 端点路径
	Endpoint string `yaml:"endpoint" json:"endpoint"`
	
	// POST 消息端点路径
	PostEndpoint string `yaml:"post_endpoint" json:"post_endpoint"`
	
	// 连接超时
	ConnectionTimeout time.Duration `yaml:"connection_timeout" json:"connection_timeout"`
	
	// 心跳间隔
	HeartbeatInterval time.Duration `yaml:"heartbeat_interval" json:"heartbeat_interval"`
	
	// 最大连接数
	MaxConnections int `yaml:"max_connections" json:"max_connections"`
	
	// CORS 配置
	CORS CORSConfig `yaml:"cors" json:"cors"`
}

// CORSConfig CORS 配置
type CORSConfig struct {
	// 允许的源
	AllowedOrigins []string `yaml:"allowed_origins" json:"allowed_origins"`
	
	// 允许的方法
	AllowedMethods []string `yaml:"allowed_methods" json:"allowed_methods"`
	
	// 允许的头部
	AllowedHeaders []string `yaml:"allowed_headers" json:"allowed_headers"`
	
	// 是否允许凭证
	AllowCredentials bool `yaml:"allow_credentials" json:"allow_credentials"`
	
	// 最大年龄
	MaxAge int `yaml:"max_age" json:"max_age"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	// 最大并发转换数
	MaxConcurrentConversions int `yaml:"max_concurrent_conversions" json:"max_concurrent_conversions"`
	
	// 转换超时
	ConversionTimeout time.Duration `yaml:"conversion_timeout" json:"conversion_timeout"`
	
	// 缓存配置
	Cache CacheConfig `yaml:"cache" json:"cache"`
	
	// 连接池配置
	ConnectionPool ConnectionPoolConfig `yaml:"connection_pool" json:"connection_pool"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	// 是否启用缓存
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// 缓存类型 (memory, redis)
	Type string `yaml:"type" json:"type"`
	
	// 缓存 TTL
	TTL time.Duration `yaml:"ttl" json:"ttl"`
	
	// 最大缓存大小
	MaxSize int `yaml:"max_size" json:"max_size"`
	
	// Redis 配置（当类型为 redis 时）
	Redis RedisConfig `yaml:"redis" json:"redis"`
}

// RedisConfig Redis 缓存配置
type RedisConfig struct {
	// Redis 地址
	Address string `yaml:"address" json:"address"`
	
	// 密码
	Password string `yaml:"password" json:"password"`
	
	// 数据库
	Database int `yaml:"database" json:"database"`
	
	// 连接超时
	DialTimeout time.Duration `yaml:"dial_timeout" json:"dial_timeout"`
	
	// 读取超时
	ReadTimeout time.Duration `yaml:"read_timeout" json:"read_timeout"`
	
	// 写入超时
	WriteTimeout time.Duration `yaml:"write_timeout" json:"write_timeout"`
}

// ConnectionPoolConfig 连接池配置
type ConnectionPoolConfig struct {
	// 最大空闲连接数
	MaxIdleConnections int `yaml:"max_idle_connections" json:"max_idle_connections"`
	
	// 最大活跃连接数
	MaxActiveConnections int `yaml:"max_active_connections" json:"max_active_connections"`
	
	// 连接空闲超时
	IdleTimeout time.Duration `yaml:"idle_timeout" json:"idle_timeout"`
	
	// 连接最大生命周期
	MaxLifetime time.Duration `yaml:"max_lifetime" json:"max_lifetime"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	// 是否启用 MCP 日志
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// 日志级别
	Level string `yaml:"level" json:"level"`
	
	// 是否记录请求详情
	LogRequests bool `yaml:"log_requests" json:"log_requests"`
	
	// 是否记录响应详情
	LogResponses bool `yaml:"log_responses" json:"log_responses"`
	
	// 是否记录转换详情
	LogConversions bool `yaml:"log_conversions" json:"log_conversions"`
	
	// 是否记录性能指标
	LogPerformance bool `yaml:"log_performance" json:"log_performance"`
}

// DefaultMCPConfig 返回默认的 MCP 配置
func DefaultMCPConfig() *MCPConfig {
	return &MCPConfig{
		Enabled:         false,
		ProtocolVersion: "2024-11-05",
		ConversionRules: []ConversionRule{},
		Server: ServerConfig{
			Name:        "API Gateway MCP Server",
			Version:     "1.0.0",
			Description: "API Gateway MCP 转换服务器",
			Capabilities: ServerCapabilities{
				Resources: true,
				Tools:     true,
				Prompts:   true,
				Logging:   true,
				Sampling:  false,
			},
		},
		Transport: TransportConfig{
			Type: "sse",
			SSE: SSEConfig{
				Enabled:           true,
				Endpoint:          "/mcp/sse",
				PostEndpoint:      "/mcp/message",
				ConnectionTimeout: 30 * time.Second,
				HeartbeatInterval: 30 * time.Second,
				MaxConnections:    100,
				CORS: CORSConfig{
					AllowedOrigins:   []string{"*"},
					AllowedMethods:   []string{"GET", "POST", "OPTIONS"},
					AllowedHeaders:   []string{"Content-Type", "Authorization"},
					AllowCredentials: false,
					MaxAge:           3600,
				},
			},
		},
		Performance: PerformanceConfig{
			MaxConcurrentConversions: 100,
			ConversionTimeout:        10 * time.Second,
			Cache: CacheConfig{
				Enabled: true,
				Type:    "memory",
				TTL:     5 * time.Minute,
				MaxSize: 1000,
			},
			ConnectionPool: ConnectionPoolConfig{
				MaxIdleConnections:   10,
				MaxActiveConnections: 100,
				IdleTimeout:          5 * time.Minute,
				MaxLifetime:          1 * time.Hour,
			},
		},
		Logging: LoggingConfig{
			Enabled:        true,
			Level:          "info",
			LogRequests:    true,
			LogResponses:   true,
			LogConversions: true,
			LogPerformance: true,
		},
	}
}
