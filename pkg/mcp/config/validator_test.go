package config

import (
	"testing"
	"time"
)

// TestConfigValidator_Validate 测试配置验证
func TestConfigValidator_Validate(t *testing.T) {
	validator := NewConfigValidator()

	tests := []struct {
		name    string
		config  *MCPConfig
		wantErr bool
		errMsg  string
	}{
		{
			name:    "有效配置",
			config:  createValidConfig(),
			wantErr: false,
		},
		{
			name: "空协议版本",
			config: &MCPConfig{
				Enabled:         true,
				ProtocolVersion: "",
			},
			wantErr: true,
			errMsg:  "协议版本不能为空",
		},
		{
			name: "无效协议版本格式",
			config: &MCPConfig{
				Enabled:         true,
				ProtocolVersion: "invalid-version",
			},
			wantErr: true,
			errMsg:  "无效的协议版本格式",
		},
		{
			name: "空服务器名称",
			config: &MCPConfig{
				Enabled:         true,
				ProtocolVersion: "2024-11-05",
				Server: ServerConfig{
					Name:    "",
					Version: "1.0.0",
				},
			},
			wantErr: true,
			errMsg:  "服务器名称不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.Validate(tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err != nil && tt.errMsg != "" {
				if !contains(err.Error(), tt.errMsg) {
					t.Errorf("Validate() error = %v, want error containing %v", err, tt.errMsg)
				}
			}
		})
	}
}

// TestConfigValidator_ValidateConversionRule 测试转换规则验证
func TestConfigValidator_ValidateConversionRule(t *testing.T) {
	validator := NewConfigValidator()

	tests := []struct {
		name    string
		rule    *ConversionRule
		wantErr bool
		errMsg  string
	}{
		{
			name:    "有效转换规则",
			rule:    createValidConversionRule(),
			wantErr: false,
		},
		{
			name: "空路径",
			rule: &ConversionRule{
				Path:      "",
				Methods:   []string{"GET"},
				MCPMethod: "test.method",
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "路径不能为空",
		},
		{
			name: "无效路径格式",
			rule: &ConversionRule{
				Path:      "invalid-path",
				Methods:   []string{"GET"},
				MCPMethod: "test.method",
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "无效的路径格式",
		},
		{
			name: "空方法列表",
			rule: &ConversionRule{
				Path:      "/api/v1/test",
				Methods:   []string{},
				MCPMethod: "test.method",
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "至少需要指定一个 HTTP 方法",
		},
		{
			name: "无效 HTTP 方法",
			rule: &ConversionRule{
				Path:      "/api/v1/test",
				Methods:   []string{"INVALID"},
				MCPMethod: "test.method",
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "无效的 HTTP 方法",
		},
		{
			name: "空 MCP 方法名",
			rule: &ConversionRule{
				Path:      "/api/v1/test",
				Methods:   []string{"GET"},
				MCPMethod: "",
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "MCP 方法名不能为空",
		},
		{
			name: "无效 MCP 方法名格式",
			rule: &ConversionRule{
				Path:      "/api/v1/test",
				Methods:   []string{"GET"},
				MCPMethod: "123invalid",
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "无效的 MCP 方法名格式",
		},
		{
			name: "负数优先级",
			rule: &ConversionRule{
				Path:      "/api/v1/test",
				Methods:   []string{"GET"},
				MCPMethod: "test.method",
				Priority:  -1,
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "优先级不能为负数",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateConversionRule(tt.rule)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateConversionRule() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err != nil && tt.errMsg != "" {
				if !contains(err.Error(), tt.errMsg) {
					t.Errorf("ValidateConversionRule() error = %v, want error containing %v", err, tt.errMsg)
				}
			}
		})
	}
}

// TestConfigValidator_ValidateServerConfig 测试服务器配置验证
func TestConfigValidator_ValidateServerConfig(t *testing.T) {
	validator := NewConfigValidator()

	tests := []struct {
		name    string
		config  *ServerConfig
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效服务器配置",
			config: &ServerConfig{
				Name:    "Test Server",
				Version: "1.0.0",
			},
			wantErr: false,
		},
		{
			name: "空服务器名称",
			config: &ServerConfig{
				Name:    "",
				Version: "1.0.0",
			},
			wantErr: true,
			errMsg:  "服务器名称不能为空",
		},
		{
			name: "空服务器版本",
			config: &ServerConfig{
				Name:    "Test Server",
				Version: "",
			},
			wantErr: true,
			errMsg:  "服务器版本不能为空",
		},
		{
			name: "无效版本格式",
			config: &ServerConfig{
				Name:    "Test Server",
				Version: "invalid-version",
			},
			wantErr: true,
			errMsg:  "无效的版本格式",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateServerConfig(tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateServerConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err != nil && tt.errMsg != "" {
				if !contains(err.Error(), tt.errMsg) {
					t.Errorf("ValidateServerConfig() error = %v, want error containing %v", err, tt.errMsg)
				}
			}
		})
	}
}

// TestConfigValidator_ValidateTransportConfig 测试传输配置验证
func TestConfigValidator_ValidateTransportConfig(t *testing.T) {
	validator := NewConfigValidator()

	tests := []struct {
		name    string
		config  *TransportConfig
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效 SSE 传输配置",
			config: &TransportConfig{
				Type: "sse",
				SSE: SSEConfig{
					Enabled:           true,
					Endpoint:          "/mcp/sse",
					PostEndpoint:      "/mcp/message",
					ConnectionTimeout: 30 * time.Second,
					MaxConnections:    100,
				},
			},
			wantErr: false,
		},
		{
			name: "空传输类型",
			config: &TransportConfig{
				Type: "",
			},
			wantErr: true,
			errMsg:  "传输类型不能为空",
		},
		{
			name: "无效传输类型",
			config: &TransportConfig{
				Type: "invalid",
			},
			wantErr: true,
			errMsg:  "无效的传输类型",
		},
		{
			name: "SSE 端点为空",
			config: &TransportConfig{
				Type: "sse",
				SSE: SSEConfig{
					Endpoint:     "",
					PostEndpoint: "/mcp/message",
				},
			},
			wantErr: true,
			errMsg:  "SSE 端点不能为空",
		},
		{
			name: "无效的 SSE 端点路径",
			config: &TransportConfig{
				Type: "sse",
				SSE: SSEConfig{
					Endpoint:     "invalid-endpoint",
					PostEndpoint: "/mcp/message",
				},
			},
			wantErr: true,
			errMsg:  "无效的端点路径",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateTransportConfig(tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateTransportConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err != nil && tt.errMsg != "" {
				if !contains(err.Error(), tt.errMsg) {
					t.Errorf("ValidateTransportConfig() error = %v, want error containing %v", err, tt.errMsg)
				}
			}
		})
	}
}

// TestValidationErrors 测试验证错误集合
func TestValidationErrors(t *testing.T) {
	errors := &ValidationErrors{}

	// 测试空错误集合
	if errors.HasErrors() {
		t.Error("Empty ValidationErrors should not have errors")
	}

	if errors.Error() != "无验证错误" {
		t.Errorf("Empty ValidationErrors.Error() = %v, want %v", errors.Error(), "无验证错误")
	}

	// 添加一个错误
	errors.AddError("field1", "message1", "value1")

	if !errors.HasErrors() {
		t.Error("ValidationErrors with one error should have errors")
	}

	expectedSingle := "字段 'field1' 验证失败: message1 (值: value1)"
	if errors.Error() != expectedSingle {
		t.Errorf("Single ValidationErrors.Error() = %v, want %v", errors.Error(), expectedSingle)
	}

	// 添加另一个错误
	errors.AddError("field2", "message2", "value2")

	errorMsg := errors.Error()
	if !contains(errorMsg, "发现 2 个验证错误") {
		t.Errorf("Multiple ValidationErrors.Error() should contain count, got: %v", errorMsg)
	}

	if !contains(errorMsg, "field1") || !contains(errorMsg, "field2") {
		t.Errorf("Multiple ValidationErrors.Error() should contain all fields, got: %v", errorMsg)
	}
}

// TestStrictConfigValidator 测试严格模式验证器
func TestStrictConfigValidator(t *testing.T) {
	validator := NewStrictConfigValidator()

	// 严格模式下，没有转换规则应该报错
	config := &MCPConfig{
		Enabled:         true,
		ProtocolVersion: "2024-11-05",
		ConversionRules: []ConversionRule{},
		Server: ServerConfig{
			Name:    "Test Server",
			Version: "1.0.0",
		},
		Transport: TransportConfig{
			Type: "sse",
			SSE: SSEConfig{
				Enabled:           true,
				Endpoint:          "/mcp/sse",
				PostEndpoint:      "/mcp/message",
				ConnectionTimeout: 30 * time.Second,
				MaxConnections:    100,
			},
		},
	}

	err := validator.Validate(config)
	if err == nil {
		t.Error("Strict validator should require at least one conversion rule")
	}

	if !contains(err.Error(), "必须至少有一个转换规则") {
		t.Errorf("Strict validator error should mention conversion rules, got: %v", err.Error())
	}
}

// 辅助函数

// createValidConfig 创建有效的配置
func createValidConfig() *MCPConfig {
	return &MCPConfig{
		Enabled:         true,
		ProtocolVersion: "2024-11-05",
		ConversionRules: []ConversionRule{
			*createValidConversionRule(),
		},
		Server: ServerConfig{
			Name:    "Test Server",
			Version: "1.0.0",
		},
		Transport: TransportConfig{
			Type: "sse",
			SSE: SSEConfig{
				Enabled:           true,
				Endpoint:          "/mcp/sse",
				PostEndpoint:      "/mcp/message",
				ConnectionTimeout: 30 * time.Second,
				MaxConnections:    100,
			},
		},
		Performance: PerformanceConfig{
			MaxConcurrentConversions: 100,
			ConversionTimeout:        10 * time.Second,
		},
		Logging: LoggingConfig{
			Enabled: true,
			Level:   "info",
		},
	}
}

// createValidConversionRule 创建有效的转换规则
func createValidConversionRule() *ConversionRule {
	return &ConversionRule{
		Path:      "/api/v1/test",
		Methods:   []string{"GET", "POST"},
		MCPMethod: "test.method",
		Enabled:   true,
		Priority:  1,
		ParameterMapping: ParameterMapping{
			Query: map[string]string{
				"page": "pagination.page",
			},
		},
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || containsSubstring(s, substr))
}

// containsSubstring 检查字符串是否包含子字符串
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
