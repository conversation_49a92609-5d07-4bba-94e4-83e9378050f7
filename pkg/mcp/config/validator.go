package config

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"
)

// ConfigValidator 配置验证器接口
type ConfigValidator interface {
	// Validate 验证配置
	Validate(config *MCPConfig) error
	
	// ValidateConversionRule 验证转换规则
	ValidateConversionRule(rule *ConversionRule) error
	
	// ValidateServerConfig 验证服务器配置
	ValidateServerConfig(config *ServerConfig) error
	
	// ValidateTransportConfig 验证传输配置
	ValidateTransportConfig(config *TransportConfig) error
}

// ConfigValidatorImpl 配置验证器实现
type ConfigValidatorImpl struct {
	// 严格模式（更严格的验证）
	strictMode bool
	
	// 自定义验证规则
	customValidators map[string]func(interface{}) error
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string
	Message string
	Value   interface{}
}

// Error 实现 error 接口
func (ve *ValidationError) Error() string {
	return fmt.Sprintf("字段 '%s' 验证失败: %s (值: %v)", ve.Field, ve.Message, ve.Value)
}

// ValidationErrors 多个验证错误
type ValidationErrors struct {
	Errors []*ValidationError
}

// Error 实现 error 接口
func (ves *ValidationErrors) Error() string {
	if len(ves.Errors) == 0 {
		return "无验证错误"
	}
	
	if len(ves.Errors) == 1 {
		return ves.Errors[0].Error()
	}
	
	var messages []string
	for _, err := range ves.Errors {
		messages = append(messages, err.Error())
	}
	
	return fmt.Sprintf("发现 %d 个验证错误:\n%s", len(ves.Errors), strings.Join(messages, "\n"))
}

// AddError 添加验证错误
func (ves *ValidationErrors) AddError(field, message string, value interface{}) {
	ves.Errors = append(ves.Errors, &ValidationError{
		Field:   field,
		Message: message,
		Value:   value,
	})
}

// HasErrors 检查是否有错误
func (ves *ValidationErrors) HasErrors() bool {
	return len(ves.Errors) > 0
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator() ConfigValidator {
	return &ConfigValidatorImpl{
		strictMode:       false,
		customValidators: make(map[string]func(interface{}) error),
	}
}

// NewStrictConfigValidator 创建严格模式的配置验证器
func NewStrictConfigValidator() ConfigValidator {
	return &ConfigValidatorImpl{
		strictMode:       true,
		customValidators: make(map[string]func(interface{}) error),
	}
}

// Validate 验证配置
func (cv *ConfigValidatorImpl) Validate(config *MCPConfig) error {
	errors := &ValidationErrors{}
	
	// 验证基本配置
	cv.validateBasicConfig(config, errors)
	
	// 验证转换规则
	cv.validateConversionRules(config.ConversionRules, errors)
	
	// 验证服务器配置
	if err := cv.ValidateServerConfig(&config.Server); err != nil {
		if ve, ok := err.(*ValidationErrors); ok {
			errors.Errors = append(errors.Errors, ve.Errors...)
		} else {
			errors.AddError("server", err.Error(), config.Server)
		}
	}
	
	// 验证传输配置
	if err := cv.ValidateTransportConfig(&config.Transport); err != nil {
		if ve, ok := err.(*ValidationErrors); ok {
			errors.Errors = append(errors.Errors, ve.Errors...)
		} else {
			errors.AddError("transport", err.Error(), config.Transport)
		}
	}
	
	// 验证性能配置
	cv.validatePerformanceConfig(&config.Performance, errors)
	
	// 验证日志配置
	cv.validateLoggingConfig(&config.Logging, errors)
	
	if errors.HasErrors() {
		return errors
	}
	
	return nil
}

// validateBasicConfig 验证基本配置
func (cv *ConfigValidatorImpl) validateBasicConfig(config *MCPConfig, errors *ValidationErrors) {
	// 验证协议版本
	if config.ProtocolVersion == "" {
		errors.AddError("protocol_version", "协议版本不能为空", config.ProtocolVersion)
	} else if !cv.isValidProtocolVersion(config.ProtocolVersion) {
		errors.AddError("protocol_version", "无效的协议版本格式", config.ProtocolVersion)
	}
}

// validateConversionRules 验证转换规则
func (cv *ConfigValidatorImpl) validateConversionRules(rules []ConversionRule, errors *ValidationErrors) {
	if len(rules) == 0 && cv.strictMode {
		errors.AddError("conversion_rules", "严格模式下必须至少有一个转换规则", len(rules))
		return
	}
	
	// 检查重复的规则
	ruleMap := make(map[string]int)
	
	for i, rule := range rules {
		fieldPrefix := fmt.Sprintf("conversion_rules[%d]", i)
		
		// 验证单个规则
		if err := cv.ValidateConversionRule(&rule); err != nil {
			if ve, ok := err.(*ValidationErrors); ok {
				for _, e := range ve.Errors {
					e.Field = fieldPrefix + "." + e.Field
					errors.Errors = append(errors.Errors, e)
				}
			} else {
				errors.AddError(fieldPrefix, err.Error(), rule)
			}
		}
		
		// 检查重复规则
		ruleKey := fmt.Sprintf("%s:%s", rule.Path, strings.Join(rule.Methods, ","))
		if existingIndex, exists := ruleMap[ruleKey]; exists {
			errors.AddError(fieldPrefix, 
				fmt.Sprintf("与规则 %d 重复 (路径: %s, 方法: %v)", existingIndex, rule.Path, rule.Methods), 
				rule)
		} else {
			ruleMap[ruleKey] = i
		}
	}
}

// ValidateConversionRule 验证转换规则
func (cv *ConfigValidatorImpl) ValidateConversionRule(rule *ConversionRule) error {
	errors := &ValidationErrors{}
	
	// 验证路径
	if rule.Path == "" {
		errors.AddError("path", "路径不能为空", rule.Path)
	} else if !cv.isValidPath(rule.Path) {
		errors.AddError("path", "无效的路径格式", rule.Path)
	}
	
	// 验证方法
	if len(rule.Methods) == 0 {
		errors.AddError("methods", "至少需要指定一个 HTTP 方法", rule.Methods)
	} else {
		for i, method := range rule.Methods {
			if !cv.isValidHTTPMethod(method) {
				errors.AddError(fmt.Sprintf("methods[%d]", i), "无效的 HTTP 方法", method)
			}
		}
	}
	
	// 验证 MCP 方法
	if rule.MCPMethod == "" {
		errors.AddError("mcp_method", "MCP 方法名不能为空", rule.MCPMethod)
	} else if !cv.isValidMCPMethod(rule.MCPMethod) {
		errors.AddError("mcp_method", "无效的 MCP 方法名格式", rule.MCPMethod)
	}
	
	// 验证优先级
	if rule.Priority < 0 {
		errors.AddError("priority", "优先级不能为负数", rule.Priority)
	}
	
	// 验证参数映射
	cv.validateParameterMapping(&rule.ParameterMapping, errors)
	
	// 验证响应映射
	cv.validateResponseMapping(&rule.ResponseMapping, errors)
	
	// 验证错误映射
	cv.validateErrorMapping(&rule.ErrorMapping, errors)
	
	if errors.HasErrors() {
		return errors
	}
	
	return nil
}

// ValidateServerConfig 验证服务器配置
func (cv *ConfigValidatorImpl) ValidateServerConfig(config *ServerConfig) error {
	errors := &ValidationErrors{}
	
	// 验证服务器名称
	if config.Name == "" {
		errors.AddError("name", "服务器名称不能为空", config.Name)
	}
	
	// 验证服务器版本
	if config.Version == "" {
		errors.AddError("version", "服务器版本不能为空", config.Version)
	} else if !cv.isValidVersion(config.Version) {
		errors.AddError("version", "无效的版本格式", config.Version)
	}
	
	if errors.HasErrors() {
		return errors
	}
	
	return nil
}

// ValidateTransportConfig 验证传输配置
func (cv *ConfigValidatorImpl) ValidateTransportConfig(config *TransportConfig) error {
	errors := &ValidationErrors{}
	
	// 验证传输类型
	if config.Type == "" {
		errors.AddError("type", "传输类型不能为空", config.Type)
	} else if !cv.isValidTransportType(config.Type) {
		errors.AddError("type", "无效的传输类型", config.Type)
	}
	
	// 根据传输类型验证具体配置
	switch config.Type {
	case "sse":
		cv.validateSSEConfig(&config.SSE, errors)
	case "stdio":
		cv.validateStdioConfig(&config.Stdio, errors)
	}
	
	if errors.HasErrors() {
		return errors
	}
	
	return nil
}

// validateParameterMapping 验证参数映射
func (cv *ConfigValidatorImpl) validateParameterMapping(mapping *ParameterMapping, errors *ValidationErrors) {
	// 验证映射键值对格式
	cv.validateMappingKeys("parameter_mapping.path", mapping.Path, errors)
	cv.validateMappingKeys("parameter_mapping.query", mapping.Query, errors)
	cv.validateMappingKeys("parameter_mapping.body", mapping.Body, errors)
	cv.validateMappingKeys("parameter_mapping.headers", mapping.Headers, errors)
}

// validateResponseMapping 验证响应映射
func (cv *ConfigValidatorImpl) validateResponseMapping(mapping *ResponseMapping, errors *ValidationErrors) {
	cv.validateMappingKeys("response_mapping.success", mapping.Success, errors)
	cv.validateMappingKeys("response_mapping.error", mapping.Error, errors)
	cv.validateMappingKeys("response_mapping.headers", mapping.Headers, errors)
}

// validateErrorMapping 验证错误映射
func (cv *ConfigValidatorImpl) validateErrorMapping(mapping *ErrorMapping, errors *ValidationErrors) {
	// 验证状态码映射
	for httpCode, mcpCode := range mapping.StatusCodeMapping {
		if httpCode < 100 || httpCode > 599 {
			errors.AddError("error_mapping.status_code_mapping", 
				fmt.Sprintf("无效的 HTTP 状态码: %d", httpCode), httpCode)
		}
		if mcpCode == 0 {
			errors.AddError("error_mapping.status_code_mapping", 
				fmt.Sprintf("MCP 错误码不能为 0 (HTTP 状态码: %d)", httpCode), mcpCode)
		}
	}
}

// validatePerformanceConfig 验证性能配置
func (cv *ConfigValidatorImpl) validatePerformanceConfig(config *PerformanceConfig, errors *ValidationErrors) {
	if config.MaxConcurrentConversions <= 0 {
		errors.AddError("performance.max_concurrent_conversions", 
			"最大并发转换数必须大于 0", config.MaxConcurrentConversions)
	}
	
	if config.ConversionTimeout <= 0 {
		errors.AddError("performance.conversion_timeout", 
			"转换超时时间必须大于 0", config.ConversionTimeout)
	}
}

// validateLoggingConfig 验证日志配置
func (cv *ConfigValidatorImpl) validateLoggingConfig(config *LoggingConfig, errors *ValidationErrors) {
	validLevels := []string{"debug", "info", "warn", "error", "fatal"}
	if config.Level != "" && !cv.contains(validLevels, config.Level) {
		errors.AddError("logging.level", "无效的日志级别", config.Level)
	}
}

// validateSSEConfig 验证 SSE 配置
func (cv *ConfigValidatorImpl) validateSSEConfig(config *SSEConfig, errors *ValidationErrors) {
	if config.Endpoint == "" {
		errors.AddError("sse.endpoint", "SSE 端点不能为空", config.Endpoint)
	} else if !cv.isValidPath(config.Endpoint) {
		errors.AddError("sse.endpoint", "无效的端点路径", config.Endpoint)
	}
	
	if config.PostEndpoint == "" {
		errors.AddError("sse.post_endpoint", "POST 端点不能为空", config.PostEndpoint)
	} else if !cv.isValidPath(config.PostEndpoint) {
		errors.AddError("sse.post_endpoint", "无效的端点路径", config.PostEndpoint)
	}
	
	if config.ConnectionTimeout <= 0 {
		errors.AddError("sse.connection_timeout", "连接超时时间必须大于 0", config.ConnectionTimeout)
	}
	
	if config.MaxConnections <= 0 {
		errors.AddError("sse.max_connections", "最大连接数必须大于 0", config.MaxConnections)
	}
}

// validateStdioConfig 验证 stdio 配置
func (cv *ConfigValidatorImpl) validateStdioConfig(config *StdioConfig, errors *ValidationErrors) {
	if config.BufferSize <= 0 {
		errors.AddError("stdio.buffer_size", "缓冲区大小必须大于 0", config.BufferSize)
	}
	
	if config.Timeout <= 0 {
		errors.AddError("stdio.timeout", "超时时间必须大于 0", config.Timeout)
	}
}

// validateMappingKeys 验证映射键值对
func (cv *ConfigValidatorImpl) validateMappingKeys(fieldPrefix string, mapping map[string]string, errors *ValidationErrors) {
	for key, value := range mapping {
		if key == "" {
			errors.AddError(fieldPrefix, "映射键不能为空", key)
		}
		if value == "" {
			errors.AddError(fieldPrefix, fmt.Sprintf("映射值不能为空 (键: %s)", key), value)
		}
	}
}

// 验证辅助方法

// isValidProtocolVersion 验证协议版本格式
func (cv *ConfigValidatorImpl) isValidProtocolVersion(version string) bool {
	// 协议版本格式：YYYY-MM-DD
	matched, _ := regexp.MatchString(`^\d{4}-\d{2}-\d{2}$`, version)
	return matched
}

// isValidPath 验证路径格式
func (cv *ConfigValidatorImpl) isValidPath(path string) bool {
	if path == "" {
		return false
	}
	
	// 路径必须以 / 开头
	if !strings.HasPrefix(path, "/") {
		return false
	}
	
	// 使用 url.Parse 验证路径格式
	_, err := url.Parse(path)
	return err == nil
}

// isValidHTTPMethod 验证 HTTP 方法
func (cv *ConfigValidatorImpl) isValidHTTPMethod(method string) bool {
	validMethods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}
	return cv.contains(validMethods, strings.ToUpper(method))
}

// isValidMCPMethod 验证 MCP 方法名
func (cv *ConfigValidatorImpl) isValidMCPMethod(method string) bool {
	// MCP 方法名格式：字母、数字、点、下划线
	matched, _ := regexp.MatchString(`^[a-zA-Z][a-zA-Z0-9._]*$`, method)
	return matched
}

// isValidVersion 验证版本格式
func (cv *ConfigValidatorImpl) isValidVersion(version string) bool {
	// 支持语义化版本格式：x.y.z
	matched, _ := regexp.MatchString(`^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$`, version)
	return matched
}

// isValidTransportType 验证传输类型
func (cv *ConfigValidatorImpl) isValidTransportType(transportType string) bool {
	validTypes := []string{"stdio", "sse", "custom"}
	return cv.contains(validTypes, transportType)
}

// contains 检查切片是否包含指定元素
func (cv *ConfigValidatorImpl) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
