package config

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// ConfigLoader 配置加载器接口
type ConfigLoader interface {
	// LoadConfig 加载配置
	LoadConfig(configPath string) (*MCPConfig, error)
	
	// LoadConfigFromBytes 从字节数据加载配置
	LoadConfigFromBytes(data []byte) (*MCPConfig, error)
	
	// ReloadConfig 重新加载配置
	ReloadConfig() (*MCPConfig, error)
	
	// WatchConfig 监听配置文件变化
	WatchConfig(callback func(*MCPConfig, error)) error
	
	// StopWatching 停止监听配置文件
	StopWatching() error
}

// ConfigLoaderImpl 配置加载器实现
type ConfigLoaderImpl struct {
	// 配置文件路径
	configPath string
	
	// 当前配置
	currentConfig *MCPConfig
	
	// 配置验证器
	validator ConfigValidator
	
	// 文件监听器
	watcher FileWatcher
	
	// 是否正在监听
	watching bool
	
	// 监听回调
	watchCallback func(*MCPConfig, error)
}

// FileWatcher 文件监听器接口
type FileWatcher interface {
	// Watch 开始监听文件
	Watch(filePath string, callback func(string)) error
	
	// Stop 停止监听
	Stop() error
}

// NewConfigLoader 创建配置加载器
func NewConfigLoader(validator ConfigValidator) ConfigLoader {
	return &ConfigLoaderImpl{
		validator: validator,
		watching:  false,
	}
}

// LoadConfig 加载配置
func (cl *ConfigLoaderImpl) LoadConfig(configPath string) (*MCPConfig, error) {
	cl.configPath = configPath
	
	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}
	
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	
	// 解析配置
	config, err := cl.LoadConfigFromBytes(data)
	if err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}
	
	cl.currentConfig = config
	return config, nil
}

// LoadConfigFromBytes 从字节数据加载配置
func (cl *ConfigLoaderImpl) LoadConfigFromBytes(data []byte) (*MCPConfig, error) {
	// 创建默认配置
	config := DefaultMCPConfig()
	
	// 解析 YAML
	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("YAML 解析失败: %w", err)
	}
	
	// 处理环境变量替换
	if err := cl.processEnvironmentVariables(config); err != nil {
		return nil, fmt.Errorf("环境变量处理失败: %w", err)
	}
	
	// 验证配置
	if cl.validator != nil {
		if err := cl.validator.Validate(config); err != nil {
			return nil, fmt.Errorf("配置验证失败: %w", err)
		}
	}
	
	return config, nil
}

// ReloadConfig 重新加载配置
func (cl *ConfigLoaderImpl) ReloadConfig() (*MCPConfig, error) {
	if cl.configPath == "" {
		return nil, fmt.Errorf("未设置配置文件路径")
	}
	
	return cl.LoadConfig(cl.configPath)
}

// WatchConfig 监听配置文件变化
func (cl *ConfigLoaderImpl) WatchConfig(callback func(*MCPConfig, error)) error {
	if cl.configPath == "" {
		return fmt.Errorf("未设置配置文件路径")
	}
	
	if cl.watching {
		return fmt.Errorf("已在监听配置文件")
	}
	
	cl.watchCallback = callback
	
	// 创建文件监听器
	if cl.watcher == nil {
		cl.watcher = NewSimpleFileWatcher()
	}
	
	// 开始监听
	err := cl.watcher.Watch(cl.configPath, func(filePath string) {
		// 重新加载配置
		config, err := cl.ReloadConfig()
		if err != nil {
			if cl.watchCallback != nil {
				cl.watchCallback(nil, err)
			}
			return
		}
		
		cl.currentConfig = config
		if cl.watchCallback != nil {
			cl.watchCallback(config, nil)
		}
	})
	
	if err != nil {
		return fmt.Errorf("启动文件监听失败: %w", err)
	}
	
	cl.watching = true
	return nil
}

// StopWatching 停止监听配置文件
func (cl *ConfigLoaderImpl) StopWatching() error {
	if !cl.watching {
		return nil
	}
	
	if cl.watcher != nil {
		if err := cl.watcher.Stop(); err != nil {
			return fmt.Errorf("停止文件监听失败: %w", err)
		}
	}
	
	cl.watching = false
	cl.watchCallback = nil
	return nil
}

// processEnvironmentVariables 处理环境变量替换
func (cl *ConfigLoaderImpl) processEnvironmentVariables(config *MCPConfig) error {
	// 这里可以实现环境变量替换逻辑
	// 例如：${ENV_VAR} 或 $ENV_VAR 格式的替换
	
	// 处理服务器配置中的环境变量
	config.Server.Name = cl.expandEnvironmentVariables(config.Server.Name)
	config.Server.Version = cl.expandEnvironmentVariables(config.Server.Version)
	config.Server.Description = cl.expandEnvironmentVariables(config.Server.Description)
	
	// 处理传输配置中的环境变量
	config.Transport.SSE.Endpoint = cl.expandEnvironmentVariables(config.Transport.SSE.Endpoint)
	config.Transport.SSE.PostEndpoint = cl.expandEnvironmentVariables(config.Transport.SSE.PostEndpoint)
	
	// 处理转换规则中的环境变量
	for i := range config.ConversionRules {
		rule := &config.ConversionRules[i]
		rule.Path = cl.expandEnvironmentVariables(rule.Path)
		rule.MCPMethod = cl.expandEnvironmentVariables(rule.MCPMethod)
		rule.Description = cl.expandEnvironmentVariables(rule.Description)
	}
	
	return nil
}

// expandEnvironmentVariables 展开环境变量
func (cl *ConfigLoaderImpl) expandEnvironmentVariables(value string) string {
	if value == "" {
		return value
	}
	
	// 支持 ${VAR} 和 $VAR 格式
	return os.ExpandEnv(value)
}

// SimpleFileWatcher 简单文件监听器实现
type SimpleFileWatcher struct {
	// 监听的文件路径
	filePath string
	
	// 文件修改时间
	lastModTime time.Time
	
	// 停止信号
	stopChan chan struct{}
	
	// 回调函数
	callback func(string)
	
	// 是否正在运行
	running bool
}

// NewSimpleFileWatcher 创建简单文件监听器
func NewSimpleFileWatcher() FileWatcher {
	return &SimpleFileWatcher{
		stopChan: make(chan struct{}),
	}
}

// Watch 开始监听文件
func (sfw *SimpleFileWatcher) Watch(filePath string, callback func(string)) error {
	if sfw.running {
		return fmt.Errorf("文件监听器已在运行")
	}
	
	sfw.filePath = filePath
	sfw.callback = callback
	sfw.running = true
	
	// 获取初始修改时间
	if stat, err := os.Stat(filePath); err == nil {
		sfw.lastModTime = stat.ModTime()
	}
	
	// 启动监听 goroutine
	go sfw.watchLoop()
	
	return nil
}

// Stop 停止监听
func (sfw *SimpleFileWatcher) Stop() error {
	if !sfw.running {
		return nil
	}
	
	close(sfw.stopChan)
	sfw.running = false
	return nil
}

// watchLoop 监听循环
func (sfw *SimpleFileWatcher) watchLoop() {
	ticker := time.NewTicker(1 * time.Second) // 每秒检查一次
	defer ticker.Stop()
	
	for {
		select {
		case <-sfw.stopChan:
			return
		case <-ticker.C:
			if sfw.checkFileModified() {
				if sfw.callback != nil {
					sfw.callback(sfw.filePath)
				}
			}
		}
	}
}

// checkFileModified 检查文件是否被修改
func (sfw *SimpleFileWatcher) checkFileModified() bool {
	stat, err := os.Stat(sfw.filePath)
	if err != nil {
		return false
	}
	
	modTime := stat.ModTime()
	if modTime.After(sfw.lastModTime) {
		sfw.lastModTime = modTime
		return true
	}
	
	return false
}

// LoadConfigFromDirectory 从目录加载配置
func LoadConfigFromDirectory(dirPath string) (*MCPConfig, error) {
	// 查找配置文件
	configFiles := []string{
		"mcp.yaml",
		"mcp.yml",
		"config.yaml",
		"config.yml",
	}
	
	var configPath string
	for _, fileName := range configFiles {
		fullPath := filepath.Join(dirPath, fileName)
		if _, err := os.Stat(fullPath); err == nil {
			configPath = fullPath
			break
		}
	}
	
	if configPath == "" {
		return nil, fmt.Errorf("在目录 %s 中未找到配置文件", dirPath)
	}
	
	loader := NewConfigLoader(NewConfigValidator())
	return loader.LoadConfig(configPath)
}

// LoadConfigFromMultipleSources 从多个来源加载配置
func LoadConfigFromMultipleSources(sources ...string) (*MCPConfig, error) {
	var config *MCPConfig
	var err error
	
	loader := NewConfigLoader(NewConfigValidator())
	
	for _, source := range sources {
		// 检查是否是文件
		if stat, statErr := os.Stat(source); statErr == nil {
			if stat.IsDir() {
				// 目录
				config, err = LoadConfigFromDirectory(source)
			} else {
				// 文件
				config, err = loader.LoadConfig(source)
			}
			
			if err == nil {
				return config, nil
			}
		}
	}
	
	if err != nil {
		return nil, fmt.Errorf("从所有来源加载配置失败，最后一个错误: %w", err)
	}
	
	return nil, fmt.Errorf("未找到有效的配置文件")
}

// MergeConfigs 合并多个配置
func MergeConfigs(base *MCPConfig, overrides ...*MCPConfig) *MCPConfig {
	result := *base // 复制基础配置
	
	for _, override := range overrides {
		if override == nil {
			continue
		}
		
		// 合并基本设置
		if override.Enabled {
			result.Enabled = override.Enabled
		}
		
		if override.ProtocolVersion != "" {
			result.ProtocolVersion = override.ProtocolVersion
		}
		
		// 合并转换规则（追加）
		result.ConversionRules = append(result.ConversionRules, override.ConversionRules...)
		
		// 合并服务器配置
		if override.Server.Name != "" {
			result.Server.Name = override.Server.Name
		}
		if override.Server.Version != "" {
			result.Server.Version = override.Server.Version
		}
		if override.Server.Description != "" {
			result.Server.Description = override.Server.Description
		}
		
		// 合并传输配置
		if override.Transport.Type != "" {
			result.Transport.Type = override.Transport.Type
		}
		
		// 这里可以继续添加其他字段的合并逻辑
	}
	
	return &result
}
