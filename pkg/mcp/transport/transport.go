package transport

import (
	"context"
	"io"

	"api-gateway/pkg/mcp/protocol"
)

// Transport 传输层接口
type Transport interface {
	// Start 启动传输层
	Start(ctx context.Context) error
	
	// Stop 停止传输层
	Stop(ctx context.Context) error
	
	// Send 发送消息
	Send(ctx context.Context, msg protocol.Message) error
	
	// Receive 接收消息通道
	Receive() <-chan MessageEvent
	
	// GetType 获取传输类型
	GetType() TransportType
	
	// IsConnected 检查是否已连接
	IsConnected() bool
	
	// GetConnectionInfo 获取连接信息
	GetConnectionInfo() *ConnectionInfo
}

// TransportType 传输类型
type TransportType string

const (
	// TransportTypeStdio stdio 传输
	TransportTypeStdio TransportType = "stdio"
	// TransportTypeSSE HTTP SSE 传输
	TransportTypeSSE TransportType = "sse"
	// TransportTypeCustom 自定义传输
	TransportTypeCustom TransportType = "custom"
)

// MessageEvent 消息事件
type MessageEvent struct {
	// 消息
	Message protocol.Message
	
	// 错误
	Error error
	
	// 连接信息
	ConnectionInfo *ConnectionInfo
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	// 连接 ID
	ID string
	
	// 传输类型
	Type TransportType
	
	// 远程地址
	RemoteAddr string
	
	// 本地地址
	LocalAddr string
	
	// 连接时间
	ConnectedAt int64
	
	// 额外信息
	Extra map[string]interface{}
}

// TransportConfig 传输配置接口
type TransportConfig interface {
	// GetType 获取传输类型
	GetType() TransportType
	
	// Validate 验证配置
	Validate() error
}

// TransportFactory 传输工厂接口
type TransportFactory interface {
	// CreateTransport 创建传输实例
	CreateTransport(config TransportConfig) (Transport, error)
	
	// GetSupportedTypes 获取支持的传输类型
	GetSupportedTypes() []TransportType
}

// BaseTransport 基础传输实现
type BaseTransport struct {
	// 传输类型
	transportType TransportType
	
	// 连接信息
	connectionInfo *ConnectionInfo
	
	// 是否已连接
	connected bool
	
	// 消息通道
	messageChannel chan MessageEvent
	
	// 上下文
	ctx context.Context
	
	// 取消函数
	cancel context.CancelFunc
}

// NewBaseTransport 创建基础传输
func NewBaseTransport(transportType TransportType, connectionInfo *ConnectionInfo) *BaseTransport {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &BaseTransport{
		transportType:  transportType,
		connectionInfo: connectionInfo,
		connected:      false,
		messageChannel: make(chan MessageEvent, 100),
		ctx:            ctx,
		cancel:         cancel,
	}
}

// GetType 获取传输类型
func (bt *BaseTransport) GetType() TransportType {
	return bt.transportType
}

// IsConnected 检查是否已连接
func (bt *BaseTransport) IsConnected() bool {
	return bt.connected
}

// GetConnectionInfo 获取连接信息
func (bt *BaseTransport) GetConnectionInfo() *ConnectionInfo {
	return bt.connectionInfo
}

// Receive 接收消息通道
func (bt *BaseTransport) Receive() <-chan MessageEvent {
	return bt.messageChannel
}

// SetConnected 设置连接状态
func (bt *BaseTransport) SetConnected(connected bool) {
	bt.connected = connected
}

// SendMessage 发送消息到通道
func (bt *BaseTransport) SendMessage(msg protocol.Message, err error) {
	event := MessageEvent{
		Message:        msg,
		Error:          err,
		ConnectionInfo: bt.connectionInfo,
	}
	
	select {
	case bt.messageChannel <- event:
	case <-bt.ctx.Done():
		// 上下文已取消，停止发送
	default:
		// 通道满了，丢弃消息
	}
}

// Close 关闭传输
func (bt *BaseTransport) Close() error {
	bt.cancel()
	close(bt.messageChannel)
	bt.connected = false
	return nil
}

// StreamTransport 流传输接口
type StreamTransport interface {
	Transport
	
	// GetReader 获取读取器
	GetReader() io.Reader
	
	// GetWriter 获取写入器
	GetWriter() io.Writer
}

// NetworkTransport 网络传输接口
type NetworkTransport interface {
	Transport
	
	// GetLocalAddr 获取本地地址
	GetLocalAddr() string
	
	// GetRemoteAddr 获取远程地址
	GetRemoteAddr() string
	
	// SetKeepAlive 设置保活
	SetKeepAlive(enabled bool) error
	
	// SetTimeout 设置超时
	SetTimeout(timeout int) error
}

// TransportManager 传输管理器
type TransportManager struct {
	// 传输工厂
	factories map[TransportType]TransportFactory
	
	// 活跃传输
	transports map[string]Transport
}

// NewTransportManager 创建传输管理器
func NewTransportManager() *TransportManager {
	return &TransportManager{
		factories:  make(map[TransportType]TransportFactory),
		transports: make(map[string]Transport),
	}
}

// RegisterFactory 注册传输工厂
func (tm *TransportManager) RegisterFactory(transportType TransportType, factory TransportFactory) {
	tm.factories[transportType] = factory
}

// CreateTransport 创建传输
func (tm *TransportManager) CreateTransport(config TransportConfig) (Transport, error) {
	factory, exists := tm.factories[config.GetType()]
	if !exists {
		return nil, &TransportError{
			Type:    ErrorTypeUnsupportedTransport,
			Message: "不支持的传输类型",
			Details: map[string]interface{}{
				"transport_type": config.GetType(),
			},
		}
	}
	
	transport, err := factory.CreateTransport(config)
	if err != nil {
		return nil, err
	}
	
	// 注册传输
	connectionInfo := transport.GetConnectionInfo()
	if connectionInfo != nil {
		tm.transports[connectionInfo.ID] = transport
	}
	
	return transport, nil
}

// GetTransport 获取传输
func (tm *TransportManager) GetTransport(connectionID string) Transport {
	return tm.transports[connectionID]
}

// RemoveTransport 移除传输
func (tm *TransportManager) RemoveTransport(connectionID string) {
	if transport, exists := tm.transports[connectionID]; exists {
		transport.Stop(context.Background())
		delete(tm.transports, connectionID)
	}
}

// GetAllTransports 获取所有传输
func (tm *TransportManager) GetAllTransports() map[string]Transport {
	result := make(map[string]Transport)
	for id, transport := range tm.transports {
		result[id] = transport
	}
	return result
}

// StopAll 停止所有传输
func (tm *TransportManager) StopAll(ctx context.Context) error {
	for _, transport := range tm.transports {
		if err := transport.Stop(ctx); err != nil {
			// 记录错误但继续停止其他传输
		}
	}
	tm.transports = make(map[string]Transport)
	return nil
}

// TransportError 传输错误
type TransportError struct {
	Type    ErrorType
	Message string
	Details map[string]interface{}
	Cause   error
}

// Error 实现 error 接口
func (e *TransportError) Error() string {
	if e.Cause != nil {
		return e.Message + ": " + e.Cause.Error()
	}
	return e.Message
}

// Unwrap 实现 errors.Unwrap 接口
func (e *TransportError) Unwrap() error {
	return e.Cause
}

// ErrorType 错误类型
type ErrorType string

const (
	// ErrorTypeConnectionFailed 连接失败
	ErrorTypeConnectionFailed ErrorType = "connection_failed"
	// ErrorTypeConnectionClosed 连接关闭
	ErrorTypeConnectionClosed ErrorType = "connection_closed"
	// ErrorTypeMessageSendFailed 消息发送失败
	ErrorTypeMessageSendFailed ErrorType = "message_send_failed"
	// ErrorTypeMessageReceiveFailed 消息接收失败
	ErrorTypeMessageReceiveFailed ErrorType = "message_receive_failed"
	// ErrorTypeUnsupportedTransport 不支持的传输类型
	ErrorTypeUnsupportedTransport ErrorType = "unsupported_transport"
	// ErrorTypeInvalidConfig 无效配置
	ErrorTypeInvalidConfig ErrorType = "invalid_config"
	// ErrorTypeTimeout 超时
	ErrorTypeTimeout ErrorType = "timeout"
)

// NewTransportError 创建传输错误
func NewTransportError(errorType ErrorType, message string, cause error) *TransportError {
	return &TransportError{
		Type:    errorType,
		Message: message,
		Cause:   cause,
	}
}

// NewTransportErrorWithDetails 创建带详情的传输错误
func NewTransportErrorWithDetails(errorType ErrorType, message string, details map[string]interface{}, cause error) *TransportError {
	return &TransportError{
		Type:    errorType,
		Message: message,
		Details: details,
		Cause:   cause,
	}
}
