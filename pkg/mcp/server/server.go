package server

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"

	"api-gateway/pkg/mcp/config"
	"api-gateway/pkg/mcp/protocol"
	"api-gateway/pkg/mcp/transport"
	"api-gateway/pkg/telemetry"
)

// MCPServer MCP 服务器
type MCPServer struct {
	// 配置
	config *config.MCPConfig
	
	// 会话管理器
	sessionManager *SessionManager
	
	// 传输管理器
	transportManager *transport.TransportManager
	
	// 生命周期管理器
	lifecycleManager *protocol.LifecycleManager
	
	// 日志记录器
	logger *telemetry.Logger
	
	// 指标收集器
	metrics *telemetry.Metrics
	
	// WebSocket 升级器
	upgrader websocket.Upgrader
	
	// 服务器状态
	running bool
	
	// 互斥锁
	mu sync.RWMutex
	
	// 上下文
	ctx context.Context
	
	// 取消函数
	cancel context.CancelFunc
	
	// 等待组
	wg sync.WaitGroup
}

// SessionManager 会话管理器
type SessionManager struct {
	// 会话映射
	sessions map[string]*protocol.Session
	
	// 互斥锁
	mu sync.RWMutex
	
	// 会话超时时间
	sessionTimeout time.Duration
	
	// 清理间隔
	cleanupInterval time.Duration
	
	// 停止信号
	stopChan chan struct{}
}

// ServerStats 服务器统计信息
type ServerStats struct {
	// 运行状态
	Running bool `json:"running"`
	
	// 活跃会话数
	ActiveSessions int `json:"active_sessions"`
	
	// 总请求数
	TotalRequests int64 `json:"total_requests"`
	
	// 成功请求数
	SuccessfulRequests int64 `json:"successful_requests"`
	
	// 失败请求数
	FailedRequests int64 `json:"failed_requests"`
	
	// 启动时间
	StartTime time.Time `json:"start_time"`
	
	// 运行时间
	Uptime time.Duration `json:"uptime"`
}

// NewMCPServer 创建 MCP 服务器
func NewMCPServer(cfg *config.MCPConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) *MCPServer {
	ctx, cancel := context.WithCancel(context.Background())
	
	// 创建会话管理器
	sessionManager := &SessionManager{
		sessions:        make(map[string]*protocol.Session),
		sessionTimeout:  30 * time.Minute,
		cleanupInterval: 5 * time.Minute,
		stopChan:        make(chan struct{}),
	}
	
	// 创建传输管理器
	transportManager := transport.NewTransportManager()
	
	// 创建生命周期管理器
	serverInfo := &protocol.ServerInfo{
		Name:        cfg.Server.Name,
		Version:     cfg.Server.Version,
		Description: cfg.Server.Description,
	}
	
	serverCapabilities := &protocol.ServerCapabilities{
		Logging:   &protocol.LoggingCapability{},
		Prompts:   &protocol.PromptsCapability{ListChanged: true},
		Resources: &protocol.ResourcesCapability{Subscribe: true, ListChanged: true},
		Tools:     &protocol.ToolsCapability{ListChanged: true},
	}
	
	// 创建一个临时会话用于生命周期管理器
	tempSession := protocol.NewSession("temp", 30*time.Minute)
	lifecycleManager := protocol.NewLifecycleManager(tempSession, serverInfo, serverCapabilities)
	
	// 配置 WebSocket 升级器
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			// 这里可以实现更严格的源检查
			return true
		},
		Subprotocols: []string{"mcp"},
	}
	
	server := &MCPServer{
		config:           cfg,
		sessionManager:   sessionManager,
		transportManager: transportManager,
		lifecycleManager: lifecycleManager,
		logger:           logger,
		metrics:          metrics,
		upgrader:         upgrader,
		running:          false,
		ctx:              ctx,
		cancel:           cancel,
	}
	
	// 启动会话清理 goroutine
	go sessionManager.startCleanup()
	
	return server
}

// Start 启动服务器
func (s *MCPServer) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if s.running {
		return fmt.Errorf("MCP 服务器已在运行")
	}
	
	s.running = true
	
	if s.logger != nil {
		s.logger.Info("MCP 服务器启动",
			"protocol_version", s.config.ProtocolVersion,
			"transport_type", s.config.Transport.Type,
		)
	}
	
	return nil
}

// Stop 停止服务器
func (s *MCPServer) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if !s.running {
		return nil
	}
	
	s.running = false
	s.cancel()
	
	// 停止会话管理器
	close(s.sessionManager.stopChan)
	
	// 关闭所有会话
	s.sessionManager.mu.Lock()
	for _, session := range s.sessionManager.sessions {
		session.Close()
	}
	s.sessionManager.sessions = make(map[string]*protocol.Session)
	s.sessionManager.mu.Unlock()
	
	// 等待所有 goroutine 完成
	s.wg.Wait()
	
	if s.logger != nil {
		s.logger.Info("MCP 服务器已停止")
	}
	
	return nil
}

// HandleHTTPRequest 处理 HTTP 请求
func (s *MCPServer) HandleHTTPRequest(c *gin.Context) {
	if !s.running {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "MCP 服务器未运行",
		})
		return
	}
	
	// 记录请求指标
	if s.metrics != nil {
		s.metrics.IncCounter("mcp_server_requests_total", map[string]string{
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
		})
	}
	
	switch c.Request.URL.Path {
	case s.config.Transport.SSE.Endpoint:
		s.handleSSEConnection(c)
	case s.config.Transport.SSE.PostEndpoint:
		s.handleMessagePost(c)
	default:
		c.JSON(http.StatusNotFound, gin.H{
			"error": "未找到端点",
		})
	}
}

// handleSSEConnection 处理 SSE 连接
func (s *MCPServer) handleSSEConnection(c *gin.Context) {
	// 设置 SSE 响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")
	
	// 创建会话
	sessionID := s.generateSessionID()
	session := protocol.NewSession(sessionID, s.sessionManager.sessionTimeout)
	
	// 注册会话
	s.sessionManager.addSession(sessionID, session)
	defer s.sessionManager.removeSession(sessionID)
	
	if s.logger != nil {
		s.logger.Debug("SSE 连接建立", "session_id", sessionID, "client_ip", c.ClientIP())
	}
	
	// 发送连接确认
	s.writeSSEEvent(c.Writer, "connected", map[string]interface{}{
		"session_id": sessionID,
		"server":     s.config.Server.Name,
		"version":    s.config.Server.Version,
	})
	
	// 保持连接
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-s.ctx.Done():
			return
		case <-c.Request.Context().Done():
			return
		case <-ticker.C:
			// 发送心跳
			s.writeSSEEvent(c.Writer, "heartbeat", map[string]interface{}{
				"timestamp": time.Now().Unix(),
			})
		case event := <-session.GetEvents():
			// 发送会话事件
			s.writeSSEEvent(c.Writer, "session_event", event)
		}
	}
}

// handleMessagePost 处理消息 POST 请求
func (s *MCPServer) handleMessagePost(c *gin.Context) {
	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "读取请求体失败",
		})
		return
	}
	
	// 解析 MCP 消息
	msg, err := protocol.ParseMessage(body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "消息解析失败",
			"details": err.Error(),
		})
		return
	}
	
	// 验证消息
	if err := msg.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "消息验证失败",
			"details": err.Error(),
		})
		return
	}
	
	// 处理消息
	response, err := s.processMessage(c.Request.Context(), msg)
	if err != nil {
		if s.logger != nil {
			s.logger.Error("消息处理失败", "error", err.Error())
		}
		
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "消息处理失败",
			"details": err.Error(),
		})
		return
	}
	
	// 返回响应
	if response != nil {
		c.JSON(http.StatusOK, response)
	} else {
		c.Status(http.StatusNoContent)
	}
}

// processMessage 处理 MCP 消息
func (s *MCPServer) processMessage(ctx context.Context, msg protocol.Message) (interface{}, error) {
	switch m := msg.(type) {
	case *protocol.Request:
		return s.processRequest(ctx, m)
	case *protocol.Notification:
		return nil, s.processNotification(ctx, m)
	default:
		return nil, fmt.Errorf("不支持的消息类型: %T", msg)
	}
}

// processRequest 处理请求消息
func (s *MCPServer) processRequest(ctx context.Context, req *protocol.Request) (*protocol.Response, error) {
	if s.logger != nil {
		s.logger.Debug("处理 MCP 请求", "method", req.Method, "id", req.ID)
	}
	
	// 检查是否是生命周期方法
	if s.isLifecycleMethod(req.Method) {
		return s.lifecycleManager.HandleRequest(ctx, req)
	}
	
	// 处理其他方法
	return s.handleCustomMethod(ctx, req)
}

// processNotification 处理通知消息
func (s *MCPServer) processNotification(ctx context.Context, notif *protocol.Notification) error {
	if s.logger != nil {
		s.logger.Debug("处理 MCP 通知", "method", notif.Method)
	}
	
	// 检查是否是生命周期方法
	if s.isLifecycleMethod(notif.Method) {
		return s.lifecycleManager.HandleNotification(ctx, notif)
	}
	
	// 处理其他通知
	return s.handleCustomNotification(ctx, notif)
}

// isLifecycleMethod 检查是否是生命周期方法
func (s *MCPServer) isLifecycleMethod(method string) bool {
	lifecycleMethods := []string{
		protocol.MethodInitialize,
		protocol.MethodInitialized,
		protocol.MethodShutdown,
		protocol.MethodExit,
	}
	
	for _, lm := range lifecycleMethods {
		if method == lm {
			return true
		}
	}
	
	return false
}

// handleCustomMethod 处理自定义方法
func (s *MCPServer) handleCustomMethod(ctx context.Context, req *protocol.Request) (*protocol.Response, error) {
	// 这里可以实现自定义方法的处理逻辑
	// 目前返回一个简单的成功响应
	
	result := map[string]interface{}{
		"message": "方法处理成功",
		"method":  req.Method,
		"params":  req.Params,
	}
	
	return protocol.NewResponse(req.ID, result), nil
}

// handleCustomNotification 处理自定义通知
func (s *MCPServer) handleCustomNotification(ctx context.Context, notif *protocol.Notification) error {
	// 这里可以实现自定义通知的处理逻辑
	if s.logger != nil {
		s.logger.Info("收到自定义通知", "method", notif.Method, "params", notif.Params)
	}
	
	return nil
}

// writeSSEEvent 写入 SSE 事件
func (s *MCPServer) writeSSEEvent(w http.ResponseWriter, eventType string, data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	
	_, err = fmt.Fprintf(w, "event: %s\ndata: %s\n\n", eventType, jsonData)
	if err != nil {
		return err
	}
	
	if flusher, ok := w.(http.Flusher); ok {
		flusher.Flush()
	}
	
	return nil
}

// generateSessionID 生成会话 ID
func (s *MCPServer) generateSessionID() string {
	return fmt.Sprintf("session_%d", time.Now().UnixNano())
}

// UpdateConfig 更新配置
func (s *MCPServer) UpdateConfig(cfg *config.MCPConfig) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.config = cfg
	
	if s.logger != nil {
		s.logger.Info("MCP 服务器配置已更新")
	}
	
	return nil
}

// GetStats 获取统计信息
func (s *MCPServer) GetStats() *ServerStats {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	return &ServerStats{
		Running:        s.running,
		ActiveSessions: s.sessionManager.getSessionCount(),
		StartTime:      time.Now(), // 这里应该记录实际的启动时间
		Uptime:         time.Since(time.Now()), // 这里应该计算实际的运行时间
	}
}

// Shutdown 关闭服务器
func (s *MCPServer) Shutdown(ctx context.Context) error {
	return s.Stop()
}

// 会话管理器方法

// addSession 添加会话
func (sm *SessionManager) addSession(id string, session *protocol.Session) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.sessions[id] = session
}

// removeSession 移除会话
func (sm *SessionManager) removeSession(id string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	if session, exists := sm.sessions[id]; exists {
		session.Close()
		delete(sm.sessions, id)
	}
}

// getSession 获取会话
func (sm *SessionManager) getSession(id string) *protocol.Session {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.sessions[id]
}

// getSessionCount 获取会话数量
func (sm *SessionManager) getSessionCount() int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return len(sm.sessions)
}

// startCleanup 启动清理 goroutine
func (sm *SessionManager) startCleanup() {
	ticker := time.NewTicker(sm.cleanupInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-sm.stopChan:
			return
		case <-ticker.C:
			sm.cleanupExpiredSessions()
		}
	}
}

// cleanupExpiredSessions 清理过期会话
func (sm *SessionManager) cleanupExpiredSessions() {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	var expiredSessions []string
	for id, session := range sm.sessions {
		if session.IsExpired() {
			expiredSessions = append(expiredSessions, id)
		}
	}
	
	for _, id := range expiredSessions {
		if session, exists := sm.sessions[id]; exists {
			session.Close()
			delete(sm.sessions, id)
		}
	}
}
