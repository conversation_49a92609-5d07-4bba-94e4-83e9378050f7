package converter

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"api-gateway/pkg/mcp/config"
	"api-gateway/pkg/telemetry"
)

// ErrorConverter 错误到 HTTP 响应转换器
type ErrorConverter interface {
	// Convert 转换错误为 HTTP 响应
	Convert(ctx context.Context, err error, httpResp http.ResponseWriter) error
	
	// UpdateRules 更新转换规则
	UpdateRules(rules []config.ConversionRule)
	
	// GetStats 获取统计信息
	GetStats() *ErrorConverterStats
}

// ErrorConverterImpl 错误转换器实现
type ErrorConverterImpl struct {
	// 转换规则
	rules []config.ConversionRule
	
	// 日志记录器
	logger *telemetry.Logger
	
	// 统计信息
	stats *ErrorConverterStats
}

// ErrorConverterStats 错误转换器统计信息
type ErrorConverterStats struct {
	// 总转换次数
	TotalConversions int64 `json:"total_conversions"`
	
	// 成功转换次数
	SuccessfulConversions int64 `json:"successful_conversions"`
	
	// 失败转换次数
	FailedConversions int64 `json:"failed_conversions"`
	
	// 转换错误次数
	ConversionErrors int64 `json:"conversion_errors"`
	
	// 网络错误次数
	NetworkErrors int64 `json:"network_errors"`
	
	// 未知错误次数
	UnknownErrors int64 `json:"unknown_errors"`
}

// NewErrorConverter 创建错误转换器
func NewErrorConverter(rules []config.ConversionRule, logger *telemetry.Logger) ErrorConverter {
	return &ErrorConverterImpl{
		rules:  rules,
		logger: logger,
		stats:  &ErrorConverterStats{},
	}
}

// Convert 转换错误为 HTTP 响应
func (ec *ErrorConverterImpl) Convert(ctx context.Context, err error, httpResp http.ResponseWriter) error {
	ec.stats.TotalConversions++
	
	// 分析错误类型
	errorInfo := ec.analyzeError(err)
	
	// 设置响应头
	httpResp.Header().Set("Content-Type", "application/json")
	httpResp.Header().Set("X-Error-Type", string(errorInfo.Type))
	
	// 准备错误响应
	errorResponse := map[string]interface{}{
		"error": map[string]interface{}{
			"code":    errorInfo.Code,
			"message": errorInfo.Message,
			"type":    errorInfo.Type,
		},
		"success": false,
	}
	
	// 添加错误详情（如果有）
	if errorInfo.Details != nil {
		errorResponse["error"].(map[string]interface{})["details"] = errorInfo.Details
	}
	
	// 添加错误追踪信息（开发环境）
	if ec.shouldIncludeStackTrace(ctx) {
		errorResponse["error"].(map[string]interface{})["stack_trace"] = fmt.Sprintf("%+v", err)
	}
	
	// 序列化错误响应
	responseBytes, err := json.Marshal(errorResponse)
	if err != nil {
		ec.stats.FailedConversions++
		// 如果序列化失败，返回简单的错误响应
		return ec.sendSimpleErrorResponse(httpResp, http.StatusInternalServerError, "内部服务器错误")
	}
	
	// 写入错误响应
	httpResp.WriteHeader(errorInfo.HTTPStatus)
	if _, writeErr := httpResp.Write(responseBytes); writeErr != nil {
		ec.stats.FailedConversions++
		return fmt.Errorf("写入错误响应失败: %w", writeErr)
	}
	
	ec.stats.SuccessfulConversions++
	
	if ec.logger != nil {
		ec.logger.Error("错误转换为 HTTP 响应",
			"error_type", errorInfo.Type,
			"error_code", errorInfo.Code,
			"http_status", errorInfo.HTTPStatus,
			"error_message", errorInfo.Message,
			"original_error", err.Error(),
		)
	}
	
	return nil
}

// ErrorInfo 错误信息
type ErrorInfo struct {
	Type       ErrorType
	Code       int
	Message    string
	HTTPStatus int
	Details    map[string]interface{}
}

// analyzeError 分析错误类型
func (ec *ErrorConverterImpl) analyzeError(err error) *ErrorInfo {
	// 检查是否是转换错误
	if convErr, ok := err.(*ConversionError); ok {
		ec.updateErrorStats(convErr.Type)
		return &ErrorInfo{
			Type:       convErr.Type,
			Code:       ec.mapConversionErrorToCode(convErr.Type),
			Message:    convErr.Message,
			HTTPStatus: ec.mapConversionErrorToHTTPStatus(convErr.Type),
			Details:    convErr.Details,
		}
	}
	
	// 检查是否是网络错误
	if ec.isNetworkError(err) {
		ec.stats.NetworkErrors++
		return &ErrorInfo{
			Type:       "network_error",
			Code:       5001,
			Message:    "网络连接错误",
			HTTPStatus: http.StatusBadGateway,
			Details: map[string]interface{}{
				"original_error": err.Error(),
			},
		}
	}
	
	// 检查是否是超时错误
	if ec.isTimeoutError(err) {
		return &ErrorInfo{
			Type:       "timeout_error",
			Code:       5002,
			Message:    "请求超时",
			HTTPStatus: http.StatusRequestTimeout,
			Details: map[string]interface{}{
				"original_error": err.Error(),
			},
		}
	}
	
	// 默认为未知错误
	ec.stats.UnknownErrors++
	return &ErrorInfo{
		Type:       "unknown_error",
		Code:       5000,
		Message:    "未知错误",
		HTTPStatus: http.StatusInternalServerError,
		Details: map[string]interface{}{
			"original_error": err.Error(),
		},
	}
}

// updateErrorStats 更新错误统计
func (ec *ErrorConverterImpl) updateErrorStats(errorType ErrorType) {
	switch errorType {
	case ErrorTypeNoMatchingRule, ErrorTypeParameterMapping, ErrorTypeResponseMapping:
		ec.stats.ConversionErrors++
	case ErrorTypeInvalidRequest, ErrorTypeInvalidResponse:
		ec.stats.ConversionErrors++
	case ErrorTypeConversionFailed:
		ec.stats.ConversionErrors++
	default:
		ec.stats.UnknownErrors++
	}
}

// mapConversionErrorToCode 映射转换错误到错误码
func (ec *ErrorConverterImpl) mapConversionErrorToCode(errorType ErrorType) int {
	switch errorType {
	case ErrorTypeNoMatchingRule:
		return 4001
	case ErrorTypeParameterMapping:
		return 4002
	case ErrorTypeResponseMapping:
		return 4003
	case ErrorTypeInvalidRequest:
		return 4004
	case ErrorTypeInvalidResponse:
		return 4005
	case ErrorTypeConversionFailed:
		return 4006
	default:
		return 5000
	}
}

// mapConversionErrorToHTTPStatus 映射转换错误到 HTTP 状态码
func (ec *ErrorConverterImpl) mapConversionErrorToHTTPStatus(errorType ErrorType) int {
	switch errorType {
	case ErrorTypeNoMatchingRule:
		return http.StatusNotFound
	case ErrorTypeParameterMapping, ErrorTypeInvalidRequest:
		return http.StatusBadRequest
	case ErrorTypeResponseMapping, ErrorTypeInvalidResponse:
		return http.StatusInternalServerError
	case ErrorTypeConversionFailed:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// isNetworkError 检查是否是网络错误
func (ec *ErrorConverterImpl) isNetworkError(err error) bool {
	// 这里可以检查具体的网络错误类型
	// 例如：connection refused, timeout, DNS resolution failed 等
	errorStr := err.Error()
	networkErrorKeywords := []string{
		"connection refused",
		"connection reset",
		"no route to host",
		"network unreachable",
		"dns resolution failed",
	}
	
	for _, keyword := range networkErrorKeywords {
		if contains(errorStr, keyword) {
			return true
		}
	}
	
	return false
}

// isTimeoutError 检查是否是超时错误
func (ec *ErrorConverterImpl) isTimeoutError(err error) bool {
	errorStr := err.Error()
	timeoutKeywords := []string{
		"timeout",
		"deadline exceeded",
		"context deadline exceeded",
	}
	
	for _, keyword := range timeoutKeywords {
		if contains(errorStr, keyword) {
			return true
		}
	}
	
	return false
}

// shouldIncludeStackTrace 检查是否应该包含堆栈跟踪
func (ec *ErrorConverterImpl) shouldIncludeStackTrace(ctx context.Context) bool {
	// 这里可以根据环境变量或配置决定是否包含堆栈跟踪
	// 通常只在开发环境包含
	return false
}

// sendSimpleErrorResponse 发送简单的错误响应
func (ec *ErrorConverterImpl) sendSimpleErrorResponse(httpResp http.ResponseWriter, statusCode int, message string) error {
	httpResp.Header().Set("Content-Type", "application/json")
	httpResp.WriteHeader(statusCode)
	
	simpleError := map[string]interface{}{
		"error": map[string]interface{}{
			"code":    statusCode,
			"message": message,
		},
		"success": false,
	}
	
	responseBytes, _ := json.Marshal(simpleError)
	_, err := httpResp.Write(responseBytes)
	return err
}

// UpdateRules 更新转换规则
func (ec *ErrorConverterImpl) UpdateRules(rules []config.ConversionRule) {
	ec.rules = rules
}

// GetStats 获取统计信息
func (ec *ErrorConverterImpl) GetStats() *ErrorConverterStats {
	return ec.stats
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    len(s) > len(substr) && 
		    (s[:len(substr)] == substr || 
		     s[len(s)-len(substr):] == substr || 
		     containsSubstring(s, substr)))
}

// containsSubstring 检查字符串是否包含子字符串
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
