package converter

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"api-gateway/pkg/mcp/config"
	"api-gateway/pkg/mcp/protocol"
	"api-gateway/pkg/telemetry"
)

// RequestConverter HTTP 请求到 MCP 请求转换器
type RequestConverter interface {
	// Convert 转换 HTTP 请求为 MCP 请求
	Convert(ctx context.Context, httpReq *http.Request, rule *config.ConversionRule) (*protocol.Request, error)
	
	// UpdateRules 更新转换规则
	UpdateRules(rules []config.ConversionRule)
	
	// GetStats 获取统计信息
	GetStats() *RequestConverterStats
}

// RequestConverterImpl 请求转换器实现
type RequestConverterImpl struct {
	// 转换规则
	rules []config.ConversionRule
	
	// 日志记录器
	logger *telemetry.Logger
	
	// 统计信息
	stats *RequestConverterStats
}

// RequestConverterStats 请求转换器统计信息
type RequestConverterStats struct {
	// 总转换次数
	TotalConversions int64 `json:"total_conversions"`
	
	// 成功转换次数
	SuccessfulConversions int64 `json:"successful_conversions"`
	
	// 失败转换次数
	FailedConversions int64 `json:"failed_conversions"`
	
	// 参数映射错误次数
	ParameterMappingErrors int64 `json:"parameter_mapping_errors"`
	
	// 请求体解析错误次数
	BodyParsingErrors int64 `json:"body_parsing_errors"`
}

// NewRequestConverter 创建请求转换器
func NewRequestConverter(rules []config.ConversionRule, logger *telemetry.Logger) RequestConverter {
	return &RequestConverterImpl{
		rules:  rules,
		logger: logger,
		stats:  &RequestConverterStats{},
	}
}

// Convert 转换 HTTP 请求为 MCP 请求
func (rc *RequestConverterImpl) Convert(ctx context.Context, httpReq *http.Request, rule *config.ConversionRule) (*protocol.Request, error) {
	rc.stats.TotalConversions++
	
	// 生成请求 ID
	requestID := rc.generateRequestID(httpReq)
	
	// 映射参数
	params, err := rc.mapParameters(httpReq, rule)
	if err != nil {
		rc.stats.ParameterMappingErrors++
		rc.stats.FailedConversions++
		return nil, NewConversionError(ErrorTypeParameterMapping, "参数映射失败", err)
	}
	
	// 创建 MCP 请求
	mcpReq := protocol.NewRequest(requestID, rule.MCPMethod, params)
	
	// 验证请求
	if err := mcpReq.Validate(); err != nil {
		rc.stats.FailedConversions++
		return nil, NewConversionError(ErrorTypeInvalidRequest, "生成的 MCP 请求无效", err)
	}
	
	rc.stats.SuccessfulConversions++
	
	if rc.logger != nil {
		rc.logger.Debug("HTTP 请求转换为 MCP 请求成功",
			"http_method", httpReq.Method,
			"http_path", httpReq.URL.Path,
			"mcp_method", rule.MCPMethod,
			"request_id", requestID,
		)
	}
	
	return mcpReq, nil
}

// mapParameters 映射请求参数
func (rc *RequestConverterImpl) mapParameters(httpReq *http.Request, rule *config.ConversionRule) (interface{}, error) {
	params := make(map[string]interface{})
	
	// 映射路径参数
	if err := rc.mapPathParameters(httpReq, rule, params); err != nil {
		return nil, fmt.Errorf("路径参数映射失败: %w", err)
	}
	
	// 映射查询参数
	if err := rc.mapQueryParameters(httpReq, rule, params); err != nil {
		return nil, fmt.Errorf("查询参数映射失败: %w", err)
	}
	
	// 映射请求头
	if err := rc.mapHeaderParameters(httpReq, rule, params); err != nil {
		return nil, fmt.Errorf("请求头映射失败: %w", err)
	}
	
	// 映射请求体
	if err := rc.mapBodyParameters(httpReq, rule, params); err != nil {
		return nil, fmt.Errorf("请求体映射失败: %w", err)
	}
	
	// 如果没有参数，返回 nil
	if len(params) == 0 {
		return nil, nil
	}
	
	return params, nil
}

// mapPathParameters 映射路径参数
func (rc *RequestConverterImpl) mapPathParameters(httpReq *http.Request, rule *config.ConversionRule, params map[string]interface{}) error {
	if rule.ParameterMapping.Path == nil {
		return nil
	}
	
	// 提取路径参数
	pathParams := rc.extractPathParameters(httpReq.URL.Path, rule.Path)
	
	// 映射参数
	for sourceKey, targetKey := range rule.ParameterMapping.Path {
		if value, exists := pathParams[sourceKey]; exists {
			rc.setNestedValue(params, targetKey, value)
		}
	}
	
	return nil
}

// mapQueryParameters 映射查询参数
func (rc *RequestConverterImpl) mapQueryParameters(httpReq *http.Request, rule *config.ConversionRule, params map[string]interface{}) error {
	if rule.ParameterMapping.Query == nil {
		return nil
	}
	
	queryParams := httpReq.URL.Query()
	
	// 映射参数
	for sourceKey, targetKey := range rule.ParameterMapping.Query {
		if values := queryParams[sourceKey]; len(values) > 0 {
			// 如果只有一个值，直接使用字符串
			// 如果有多个值，使用数组
			var value interface{}
			if len(values) == 1 {
				value = rc.convertQueryValue(values[0])
			} else {
				convertedValues := make([]interface{}, len(values))
				for i, v := range values {
					convertedValues[i] = rc.convertQueryValue(v)
				}
				value = convertedValues
			}
			
			rc.setNestedValue(params, targetKey, value)
		}
	}
	
	return nil
}

// mapHeaderParameters 映射请求头
func (rc *RequestConverterImpl) mapHeaderParameters(httpReq *http.Request, rule *config.ConversionRule, params map[string]interface{}) error {
	if rule.ParameterMapping.Headers == nil {
		return nil
	}
	
	// 映射参数
	for sourceKey, targetKey := range rule.ParameterMapping.Headers {
		if value := httpReq.Header.Get(sourceKey); value != "" {
			rc.setNestedValue(params, targetKey, value)
		}
	}
	
	return nil
}

// mapBodyParameters 映射请求体
func (rc *RequestConverterImpl) mapBodyParameters(httpReq *http.Request, rule *config.ConversionRule, params map[string]interface{}) error {
	if rule.ParameterMapping.Body == nil || httpReq.Body == nil {
		return nil
	}
	
	// 读取请求体
	bodyBytes, err := io.ReadAll(httpReq.Body)
	if err != nil {
		rc.stats.BodyParsingErrors++
		return fmt.Errorf("读取请求体失败: %w", err)
	}
	
	// 重新设置请求体，以便后续处理
	httpReq.Body = io.NopCloser(strings.NewReader(string(bodyBytes)))
	
	// 解析请求体
	var bodyData map[string]interface{}
	contentType := httpReq.Header.Get("Content-Type")
	
	if strings.Contains(contentType, "application/json") {
		if err := json.Unmarshal(bodyBytes, &bodyData); err != nil {
			rc.stats.BodyParsingErrors++
			return fmt.Errorf("JSON 请求体解析失败: %w", err)
		}
	} else if strings.Contains(contentType, "application/x-www-form-urlencoded") {
		formData, err := url.ParseQuery(string(bodyBytes))
		if err != nil {
			rc.stats.BodyParsingErrors++
			return fmt.Errorf("表单数据解析失败: %w", err)
		}
		
		bodyData = make(map[string]interface{})
		for key, values := range formData {
			if len(values) == 1 {
				bodyData[key] = values[0]
			} else {
				bodyData[key] = values
			}
		}
	} else {
		// 其他类型的请求体，作为字符串处理
		bodyData = map[string]interface{}{
			"raw": string(bodyBytes),
		}
	}
	
	// 映射参数
	for sourceKey, targetKey := range rule.ParameterMapping.Body {
		if value, exists := bodyData[sourceKey]; exists {
			rc.setNestedValue(params, targetKey, value)
		}
	}
	
	return nil
}

// extractPathParameters 提取路径参数
func (rc *RequestConverterImpl) extractPathParameters(requestPath, rulePath string) map[string]string {
	params := make(map[string]string)
	
	// 简单的路径参数提取实现
	// 实际实现中可能需要使用更复杂的路径匹配库
	
	requestParts := strings.Split(strings.Trim(requestPath, "/"), "/")
	ruleParts := strings.Split(strings.Trim(rulePath, "/"), "/")
	
	if len(requestParts) != len(ruleParts) {
		return params
	}
	
	for i, rulePart := range ruleParts {
		if strings.HasPrefix(rulePart, "{") && strings.HasSuffix(rulePart, "}") {
			paramName := strings.Trim(rulePart, "{}")
			params[paramName] = requestParts[i]
		}
	}
	
	return params
}

// convertQueryValue 转换查询参数值
func (rc *RequestConverterImpl) convertQueryValue(value string) interface{} {
	// 尝试转换为数字
	if intVal, err := strconv.Atoi(value); err == nil {
		return intVal
	}
	
	if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
		return floatVal
	}
	
	// 尝试转换为布尔值
	if boolVal, err := strconv.ParseBool(value); err == nil {
		return boolVal
	}
	
	// 默认返回字符串
	return value
}

// setNestedValue 设置嵌套值
func (rc *RequestConverterImpl) setNestedValue(params map[string]interface{}, key string, value interface{}) {
	keys := strings.Split(key, ".")
	current := params
	
	// 创建嵌套结构
	for i, k := range keys[:len(keys)-1] {
		if _, exists := current[k]; !exists {
			current[k] = make(map[string]interface{})
		}
		
		if nested, ok := current[k].(map[string]interface{}); ok {
			current = nested
		} else {
			// 如果不是 map，创建新的 map
			newMap := make(map[string]interface{})
			current[k] = newMap
			current = newMap
		}
	}
	
	// 设置最终值
	finalKey := keys[len(keys)-1]
	current[finalKey] = value
}

// generateRequestID 生成请求 ID
func (rc *RequestConverterImpl) generateRequestID(httpReq *http.Request) interface{} {
	// 优先使用请求头中的 Request-ID
	if requestID := httpReq.Header.Get("X-Request-ID"); requestID != "" {
		return requestID
	}
	
	// 使用时间戳和随机数生成 ID
	return fmt.Sprintf("req_%d_%s", rc.stats.TotalConversions, httpReq.Method)
}

// UpdateRules 更新转换规则
func (rc *RequestConverterImpl) UpdateRules(rules []config.ConversionRule) {
	rc.rules = rules
}

// GetStats 获取统计信息
func (rc *RequestConverterImpl) GetStats() *RequestConverterStats {
	return rc.stats
}
