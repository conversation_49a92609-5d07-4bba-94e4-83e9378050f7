package converter

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"api-gateway/pkg/mcp/config"
	"api-gateway/pkg/mcp/protocol"
	"api-gateway/pkg/telemetry"
)

// ResponseConverter MCP 响应到 HTTP 响应转换器
type ResponseConverter interface {
	// Convert 转换 MCP 响应为 HTTP 响应
	Convert(ctx context.Context, mcpResp *protocol.Response, httpResp http.ResponseWriter) error
	
	// UpdateRules 更新转换规则
	UpdateRules(rules []config.ConversionRule)
	
	// GetStats 获取统计信息
	GetStats() *ResponseConverterStats
}

// ResponseConverterImpl 响应转换器实现
type ResponseConverterImpl struct {
	// 转换规则
	rules []config.ConversionRule
	
	// 日志记录器
	logger *telemetry.Logger
	
	// 统计信息
	stats *ResponseConverterStats
}

// ResponseConverterStats 响应转换器统计信息
type ResponseConverterStats struct {
	// 总转换次数
	TotalConversions int64 `json:"total_conversions"`
	
	// 成功转换次数
	SuccessfulConversions int64 `json:"successful_conversions"`
	
	// 失败转换次数
	FailedConversions int64 `json:"failed_conversions"`
	
	// 响应映射错误次数
	ResponseMappingErrors int64 `json:"response_mapping_errors"`
	
	// JSON 序列化错误次数
	JSONSerializationErrors int64 `json:"json_serialization_errors"`
}

// NewResponseConverter 创建响应转换器
func NewResponseConverter(rules []config.ConversionRule, logger *telemetry.Logger) ResponseConverter {
	return &ResponseConverterImpl{
		rules:  rules,
		logger: logger,
		stats:  &ResponseConverterStats{},
	}
}

// Convert 转换 MCP 响应为 HTTP 响应
func (rc *ResponseConverterImpl) Convert(ctx context.Context, mcpResp *protocol.Response, httpResp http.ResponseWriter) error {
	rc.stats.TotalConversions++
	
	// 验证 MCP 响应
	if err := mcpResp.Validate(); err != nil {
		rc.stats.FailedConversions++
		return NewConversionError(ErrorTypeInvalidResponse, "MCP 响应无效", err)
	}
	
	// 处理错误响应
	if mcpResp.Error != nil {
		return rc.convertErrorResponse(mcpResp, httpResp)
	}
	
	// 处理成功响应
	return rc.convertSuccessResponse(mcpResp, httpResp)
}

// convertSuccessResponse 转换成功响应
func (rc *ResponseConverterImpl) convertSuccessResponse(mcpResp *protocol.Response, httpResp http.ResponseWriter) error {
	// 设置默认状态码
	statusCode := http.StatusOK
	
	// 设置响应头
	httpResp.Header().Set("Content-Type", "application/json")
	httpResp.Header().Set("X-MCP-Request-ID", fmt.Sprintf("%v", mcpResp.ID))
	
	// 准备响应数据
	responseData := rc.prepareResponseData(mcpResp.Result)
	
	// 序列化响应数据
	responseBytes, err := json.Marshal(responseData)
	if err != nil {
		rc.stats.JSONSerializationErrors++
		rc.stats.FailedConversions++
		return NewConversionError(ErrorTypeResponseMapping, "响应数据序列化失败", err)
	}
	
	// 写入响应
	httpResp.WriteHeader(statusCode)
	if _, err := httpResp.Write(responseBytes); err != nil {
		rc.stats.FailedConversions++
		return NewConversionError(ErrorTypeResponseMapping, "写入响应失败", err)
	}
	
	rc.stats.SuccessfulConversions++
	
	if rc.logger != nil {
		rc.logger.Debug("MCP 响应转换为 HTTP 响应成功",
			"request_id", mcpResp.ID,
			"status_code", statusCode,
			"response_size", len(responseBytes),
		)
	}
	
	return nil
}

// convertErrorResponse 转换错误响应
func (rc *ResponseConverterImpl) convertErrorResponse(mcpResp *protocol.Response, httpResp http.ResponseWriter) error {
	// 映射错误码到 HTTP 状态码
	statusCode := rc.mapErrorCodeToHTTPStatus(mcpResp.Error.Code)
	
	// 设置响应头
	httpResp.Header().Set("Content-Type", "application/json")
	httpResp.Header().Set("X-MCP-Request-ID", fmt.Sprintf("%v", mcpResp.ID))
	httpResp.Header().Set("X-MCP-Error-Code", fmt.Sprintf("%d", mcpResp.Error.Code))
	
	// 准备错误响应数据
	errorResponse := map[string]interface{}{
		"error": map[string]interface{}{
			"code":    mcpResp.Error.Code,
			"message": mcpResp.Error.Message,
		},
		"request_id": mcpResp.ID,
	}
	
	// 如果有错误数据，添加到响应中
	if mcpResp.Error.Data != nil {
		errorResponse["error"].(map[string]interface{})["data"] = mcpResp.Error.Data
	}
	
	// 序列化错误响应
	responseBytes, err := json.Marshal(errorResponse)
	if err != nil {
		rc.stats.JSONSerializationErrors++
		rc.stats.FailedConversions++
		return NewConversionError(ErrorTypeResponseMapping, "错误响应序列化失败", err)
	}
	
	// 写入错误响应
	httpResp.WriteHeader(statusCode)
	if _, err := httpResp.Write(responseBytes); err != nil {
		rc.stats.FailedConversions++
		return NewConversionError(ErrorTypeResponseMapping, "写入错误响应失败", err)
	}
	
	rc.stats.SuccessfulConversions++
	
	if rc.logger != nil {
		rc.logger.Debug("MCP 错误响应转换为 HTTP 响应成功",
			"request_id", mcpResp.ID,
			"mcp_error_code", mcpResp.Error.Code,
			"http_status_code", statusCode,
			"error_message", mcpResp.Error.Message,
		)
	}
	
	return nil
}

// prepareResponseData 准备响应数据
func (rc *ResponseConverterImpl) prepareResponseData(result interface{}) interface{} {
	if result == nil {
		return map[string]interface{}{
			"success": true,
		}
	}
	
	// 如果结果已经是 map，直接返回
	if resultMap, ok := result.(map[string]interface{}); ok {
		return resultMap
	}
	
	// 否则包装在 data 字段中
	return map[string]interface{}{
		"success": true,
		"data":    result,
	}
}

// mapErrorCodeToHTTPStatus 映射错误码到 HTTP 状态码
func (rc *ResponseConverterImpl) mapErrorCodeToHTTPStatus(errorCode int) int {
	switch errorCode {
	case protocol.ErrorCodeParseError:
		return http.StatusBadRequest
	case protocol.ErrorCodeInvalidRequest:
		return http.StatusBadRequest
	case protocol.ErrorCodeMethodNotFound:
		return http.StatusNotFound
	case protocol.ErrorCodeInvalidParams:
		return http.StatusBadRequest
	case protocol.ErrorCodeInternalError:
		return http.StatusInternalServerError
	default:
		// 自定义错误码映射
		if errorCode >= -32099 && errorCode <= -32000 {
			// 服务器错误范围
			return http.StatusInternalServerError
		} else if errorCode >= -32768 && errorCode <= -32000 {
			// 预留错误范围
			return http.StatusBadRequest
		} else if errorCode > 0 {
			// 应用程序定义的错误
			return http.StatusBadRequest
		} else {
			// 其他错误
			return http.StatusInternalServerError
		}
	}
}

// mapResponseFields 映射响应字段
func (rc *ResponseConverterImpl) mapResponseFields(result interface{}, mapping map[string]string) interface{} {
	if mapping == nil || len(mapping) == 0 {
		return result
	}
	
	// 如果结果不是 map，无法进行字段映射
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return result
	}
	
	mappedResult := make(map[string]interface{})
	
	// 应用字段映射
	for sourceField, targetField := range mapping {
		if value, exists := resultMap[sourceField]; exists {
			rc.setNestedValue(mappedResult, targetField, value)
		}
	}
	
	// 添加未映射的字段
	for key, value := range resultMap {
		if _, mapped := mapping[key]; !mapped {
			mappedResult[key] = value
		}
	}
	
	return mappedResult
}

// setNestedValue 设置嵌套值
func (rc *ResponseConverterImpl) setNestedValue(data map[string]interface{}, key string, value interface{}) {
	keys := strings.Split(key, ".")
	current := data
	
	// 创建嵌套结构
	for i, k := range keys[:len(keys)-1] {
		if _, exists := current[k]; !exists {
			current[k] = make(map[string]interface{})
		}
		
		if nested, ok := current[k].(map[string]interface{}); ok {
			current = nested
		} else {
			// 如果不是 map，创建新的 map
			newMap := make(map[string]interface{})
			current[k] = newMap
			current = newMap
		}
	}
	
	// 设置最终值
	finalKey := keys[len(keys)-1]
	current[finalKey] = value
}

// addCustomHeaders 添加自定义响应头
func (rc *ResponseConverterImpl) addCustomHeaders(httpResp http.ResponseWriter, headers map[string]string) {
	if headers == nil {
		return
	}
	
	for key, value := range headers {
		httpResp.Header().Set(key, value)
	}
}

// UpdateRules 更新转换规则
func (rc *ResponseConverterImpl) UpdateRules(rules []config.ConversionRule) {
	rc.rules = rules
}

// GetStats 获取统计信息
func (rc *ResponseConverterImpl) GetStats() *ResponseConverterStats {
	return rc.stats
}

// GetRule 根据请求 ID 获取转换规则
func (rc *ResponseConverterImpl) GetRule(requestID interface{}) *config.ConversionRule {
	// 这里可以实现根据请求 ID 查找对应规则的逻辑
	// 为了简化，暂时返回第一个启用的规则
	for i := range rc.rules {
		rule := &rc.rules[i]
		if rule.Enabled {
			return rule
		}
	}
	return nil
}
