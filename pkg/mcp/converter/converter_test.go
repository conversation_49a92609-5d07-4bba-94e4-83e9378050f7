package converter

import (
	"bytes"
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"api-gateway/pkg/mcp/config"
	"api-gateway/pkg/telemetry"
)

// TestConverterManager_CanConvert 测试转换器是否可以转换请求
func TestConverterManager_CanConvert(t *testing.T) {
	rules := []config.ConversionRule{
		{
			Path:        "/api/v1/users",
			Methods:     []string{"GET", "POST"},
			MCPMethod:   "users.list",
			Enabled:     true,
			Priority:    1,
		},
		{
			Path:        "/api/v1/users/{id}",
			Methods:     []string{"GET", "PUT", "DELETE"},
			MCPMethod:   "users.get",
			Enabled:     true,
			Priority:    2,
		},
		{
			Path:        "/api/v1/disabled",
			Methods:     []string{"GET"},
			MCPMethod:   "disabled.method",
			Enabled:     false,
			Priority:    1,
		},
	}

	logger := &telemetry.Logger{}
	metrics := &telemetry.Metrics{}
	cm := NewConverterManager(rules, logger, metrics)

	tests := []struct {
		name     string
		method   string
		path     string
		expected bool
	}{
		{
			name:     "匹配的路径和方法",
			method:   "GET",
			path:     "/api/v1/users",
			expected: true,
		},
		{
			name:     "匹配的路径，不匹配的方法",
			method:   "DELETE",
			path:     "/api/v1/users",
			expected: false,
		},
		{
			name:     "不匹配的路径",
			method:   "GET",
			path:     "/api/v1/products",
			expected: false,
		},
		{
			name:     "禁用的规则",
			method:   "GET",
			path:     "/api/v1/disabled",
			expected: false,
		},
		{
			name:     "带参数的路径",
			method:   "GET",
			path:     "/api/v1/users/123",
			expected: false, // 当前实现不支持路径参数匹配
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, tt.path, nil)
			result := cm.CanConvert(req)
			if result != tt.expected {
				t.Errorf("CanConvert() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestConverterManager_GetConversionRule 测试获取转换规则
func TestConverterManager_GetConversionRule(t *testing.T) {
	rules := []config.ConversionRule{
		{
			Path:        "/api/v1/users",
			Methods:     []string{"GET", "POST"},
			MCPMethod:   "users.list",
			Enabled:     true,
			Priority:    1,
		},
		{
			Path:        "/api/v1/users",
			Methods:     []string{"GET"},
			MCPMethod:   "users.get",
			Enabled:     true,
			Priority:    2, // 更高优先级
		},
	}

	logger := &telemetry.Logger{}
	metrics := &telemetry.Metrics{}
	cm := NewConverterManager(rules, logger, metrics)

	req := httptest.NewRequest("GET", "/api/v1/users", nil)
	rule := cm.GetConversionRule(req)

	if rule == nil {
		t.Fatal("GetConversionRule() returned nil")
	}

	// 应该返回优先级更高的规则
	if rule.Priority != 2 {
		t.Errorf("GetConversionRule() priority = %v, want %v", rule.Priority, 2)
	}

	if rule.MCPMethod != "users.get" {
		t.Errorf("GetConversionRule() MCPMethod = %v, want %v", rule.MCPMethod, "users.get")
	}
}

// TestConverterManager_ConvertRequest 测试请求转换
func TestConverterManager_ConvertRequest(t *testing.T) {
	rules := []config.ConversionRule{
		{
			Path:      "/api/v1/users",
			Methods:   []string{"GET"},
			MCPMethod: "users.list",
			Enabled:   true,
			Priority:  1,
			ParameterMapping: config.ParameterMapping{
				Query: map[string]string{
					"page":  "pagination.page",
					"limit": "pagination.limit",
				},
			},
		},
	}

	logger := &telemetry.Logger{}
	metrics := &telemetry.Metrics{}
	cm := NewConverterManager(rules, logger, metrics)

	tests := []struct {
		name    string
		method  string
		path    string
		query   string
		wantErr bool
	}{
		{
			name:    "成功转换带查询参数的请求",
			method:  "GET",
			path:    "/api/v1/users",
			query:   "page=1&limit=10",
			wantErr: false,
		},
		{
			name:    "没有匹配规则的请求",
			method:  "GET",
			path:    "/api/v1/products",
			query:   "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url := tt.path
			if tt.query != "" {
				url += "?" + tt.query
			}
			req := httptest.NewRequest(tt.method, url, nil)

			mcpReq, err := cm.ConvertRequest(context.Background(), req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConvertRequest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if mcpReq == nil {
					t.Error("ConvertRequest() returned nil request")
					return
				}

				if mcpReq.Method != "users.list" {
					t.Errorf("ConvertRequest() method = %v, want %v", mcpReq.Method, "users.list")
				}

				// 验证参数映射
				if tt.query != "" {
					params, ok := mcpReq.Params.(map[string]interface{})
					if !ok {
						t.Error("ConvertRequest() params is not a map")
						return
					}

					pagination, ok := params["pagination"].(map[string]interface{})
					if !ok {
						t.Error("ConvertRequest() pagination params not found")
						return
					}

					if pagination["page"] != 1 {
						t.Errorf("ConvertRequest() page = %v, want %v", pagination["page"], 1)
					}

					if pagination["limit"] != 10 {
						t.Errorf("ConvertRequest() limit = %v, want %v", pagination["limit"], 10)
					}
				}
			}
		})
	}
}

// TestConverterManager_ConvertResponse 测试响应转换
func TestConverterManager_ConvertResponse(t *testing.T) {
	logger := &telemetry.Logger{}
	metrics := &telemetry.Metrics{}
	cm := NewConverterManager([]config.ConversionRule{}, logger, metrics)

	tests := []struct {
		name       string
		mcpResp    interface{}
		wantStatus int
	}{
		{
			name: "成功响应",
			mcpResp: map[string]interface{}{
				"jsonrpc": "2.0",
				"id":      "test-id",
				"result":  map[string]interface{}{"success": true},
			},
			wantStatus: http.StatusOK,
		},
		{
			name: "错误响应",
			mcpResp: map[string]interface{}{
				"jsonrpc": "2.0",
				"id":      "test-id",
				"error": map[string]interface{}{
					"code":    -32600,
					"message": "Invalid Request",
				},
			},
			wantStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 这里需要创建一个模拟的 MCP 响应对象
			// 由于当前的实现需要 *protocol.Response，我们需要适配
			// 暂时跳过这个测试，在实际集成时再完善
			t.Skip("需要完整的 protocol.Response 对象")
		})
	}
}

// TestRequestConverter_Convert 测试请求转换器
func TestRequestConverter_Convert(t *testing.T) {
	logger := &telemetry.Logger{}
	converter := NewRequestConverter([]config.ConversionRule{}, logger)

	rule := &config.ConversionRule{
		Path:      "/api/v1/users",
		Methods:   []string{"POST"},
		MCPMethod: "users.create",
		ParameterMapping: config.ParameterMapping{
			Body: map[string]string{
				"name":  "user.name",
				"email": "user.email",
			},
		},
	}

	jsonBody := `{"name":"John Doe","email":"<EMAIL>"}`
	req := httptest.NewRequest("POST", "/api/v1/users", strings.NewReader(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	mcpReq, err := converter.Convert(context.Background(), req, rule)
	if err != nil {
		t.Fatalf("Convert() error = %v", err)
	}

	if mcpReq.Method != "users.create" {
		t.Errorf("Convert() method = %v, want %v", mcpReq.Method, "users.create")
	}

	params, ok := mcpReq.Params.(map[string]interface{})
	if !ok {
		t.Fatal("Convert() params is not a map")
	}

	user, ok := params["user"].(map[string]interface{})
	if !ok {
		t.Fatal("Convert() user params not found")
	}

	if user["name"] != "John Doe" {
		t.Errorf("Convert() user.name = %v, want %v", user["name"], "John Doe")
	}

	if user["email"] != "<EMAIL>" {
		t.Errorf("Convert() user.email = %v, want %v", user["email"], "<EMAIL>")
	}
}

// TestRequestConverter_MapQueryParameters 测试查询参数映射
func TestRequestConverter_MapQueryParameters(t *testing.T) {
	logger := &telemetry.Logger{}
	converter := NewRequestConverter([]config.ConversionRule{}, logger).(*RequestConverterImpl)

	rule := &config.ConversionRule{
		ParameterMapping: config.ParameterMapping{
			Query: map[string]string{
				"page":   "pagination.page",
				"limit":  "pagination.limit",
				"search": "filter.search",
			},
		},
	}

	req := httptest.NewRequest("GET", "/api/v1/users?page=2&limit=20&search=john", nil)
	params := make(map[string]interface{})

	err := converter.mapQueryParameters(req, rule, params)
	if err != nil {
		t.Fatalf("mapQueryParameters() error = %v", err)
	}

	pagination, ok := params["pagination"].(map[string]interface{})
	if !ok {
		t.Fatal("pagination params not found")
	}

	if pagination["page"] != 2 {
		t.Errorf("pagination.page = %v, want %v", pagination["page"], 2)
	}

	if pagination["limit"] != 20 {
		t.Errorf("pagination.limit = %v, want %v", pagination["limit"], 20)
	}

	filter, ok := params["filter"].(map[string]interface{})
	if !ok {
		t.Fatal("filter params not found")
	}

	if filter["search"] != "john" {
		t.Errorf("filter.search = %v, want %v", filter["search"], "john")
	}
}

// TestRequestConverter_MapBodyParameters 测试请求体参数映射
func TestRequestConverter_MapBodyParameters(t *testing.T) {
	logger := &telemetry.Logger{}
	converter := NewRequestConverter([]config.ConversionRule{}, logger).(*RequestConverterImpl)

	rule := &config.ConversionRule{
		ParameterMapping: config.ParameterMapping{
			Body: map[string]string{
				"name":     "user.name",
				"email":    "user.email",
				"age":      "user.age",
				"active":   "user.active",
			},
		},
	}

	tests := []struct {
		name        string
		contentType string
		body        string
		wantErr     bool
	}{
		{
			name:        "JSON 请求体",
			contentType: "application/json",
			body:        `{"name":"John","email":"<EMAIL>","age":30,"active":true}`,
			wantErr:     false,
		},
		{
			name:        "表单请求体",
			contentType: "application/x-www-form-urlencoded",
			body:        "name=John&email=<EMAIL>&age=30&active=true",
			wantErr:     false,
		},
		{
			name:        "无效 JSON",
			contentType: "application/json",
			body:        `{"name":"John","email":}`,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("POST", "/api/v1/users", bytes.NewReader([]byte(tt.body)))
			req.Header.Set("Content-Type", tt.contentType)

			params := make(map[string]interface{})
			err := converter.mapBodyParameters(req, rule, params)

			if (err != nil) != tt.wantErr {
				t.Errorf("mapBodyParameters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				user, ok := params["user"].(map[string]interface{})
				if !ok {
					t.Fatal("user params not found")
				}

				if user["name"] != "John" {
					t.Errorf("user.name = %v, want %v", user["name"], "John")
				}

				if user["email"] != "<EMAIL>" {
					t.Errorf("user.email = %v, want %v", user["email"], "<EMAIL>")
				}
			}
		})
	}
}

// TestConversionError 测试转换错误
func TestConversionError(t *testing.T) {
	err := NewConversionError(ErrorTypeParameterMapping, "参数映射失败", nil)

	if err.Type != ErrorTypeParameterMapping {
		t.Errorf("ConversionError.Type = %v, want %v", err.Type, ErrorTypeParameterMapping)
	}

	if err.Message != "参数映射失败" {
		t.Errorf("ConversionError.Message = %v, want %v", err.Message, "参数映射失败")
	}

	expectedError := "参数映射失败"
	if err.Error() != expectedError {
		t.Errorf("ConversionError.Error() = %v, want %v", err.Error(), expectedError)
	}
}

// TestConversionErrorWithDetails 测试带详情的转换错误
func TestConversionErrorWithDetails(t *testing.T) {
	details := map[string]interface{}{
		"field": "user.name",
		"value": nil,
	}

	err := NewConversionErrorWithDetails(ErrorTypeParameterMapping, "参数映射失败", details, nil)

	if err.Details == nil {
		t.Error("ConversionError.Details is nil")
	}

	if err.Details["field"] != "user.name" {
		t.Errorf("ConversionError.Details[field] = %v, want %v", err.Details["field"], "user.name")
	}
}
