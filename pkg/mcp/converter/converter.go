package converter

import (
	"context"
	"net/http"

	"api-gateway/pkg/mcp/config"
	"api-gateway/pkg/mcp/protocol"
	"api-gateway/pkg/telemetry"
)

// Converter MCP 转换器接口
type Converter interface {
	// ConvertRequest 转换 HTTP 请求为 MCP 请求
	ConvertRequest(ctx context.Context, httpReq *http.Request) (*protocol.Request, error)
	
	// ConvertResponse 转换 MCP 响应为 HTTP 响应
	ConvertResponse(ctx context.Context, mcpResp *protocol.Response, httpResp http.ResponseWriter) error
	
	// ConvertError 转换错误为 MCP 错误响应
	ConvertError(ctx context.Context, err error, httpResp http.ResponseWriter) error
	
	// CanConvert 检查是否可以转换指定的请求
	CanConvert(httpReq *http.Request) bool
	
	// GetConversionRule 获取转换规则
	GetConversionRule(httpReq *http.Request) *config.ConversionRule
}

// ConverterManager 转换器管理器
type ConverterManager struct {
	// 转换规则
	rules []config.ConversionRule
	
	// 请求转换器
	requestConverter RequestConverter
	
	// 响应转换器
	responseConverter ResponseConverter
	
	// 错误转换器
	errorConverter ErrorConverter
	
	// 日志记录器
	logger *telemetry.Logger
	
	// 指标收集器
	metrics *telemetry.Metrics
}

// NewConverterManager 创建转换器管理器
func NewConverterManager(rules []config.ConversionRule, logger *telemetry.Logger, metrics *telemetry.Metrics) *ConverterManager {
	return &ConverterManager{
		rules:             rules,
		requestConverter:  NewRequestConverter(rules, logger),
		responseConverter: NewResponseConverter(rules, logger),
		errorConverter:    NewErrorConverter(rules, logger),
		logger:            logger,
		metrics:           metrics,
	}
}

// ConvertRequest 转换 HTTP 请求为 MCP 请求
func (cm *ConverterManager) ConvertRequest(ctx context.Context, httpReq *http.Request) (*protocol.Request, error) {
	// 记录转换开始
	if cm.metrics != nil {
		cm.metrics.IncCounter("mcp_conversion_requests_total", map[string]string{
			"type": "request",
		})
	}
	
	// 查找匹配的转换规则
	rule := cm.GetConversionRule(httpReq)
	if rule == nil {
		return nil, &ConversionError{
			Type:    ErrorTypeNoMatchingRule,
			Message: "未找到匹配的转换规则",
			Details: map[string]interface{}{
				"method": httpReq.Method,
				"path":   httpReq.URL.Path,
			},
		}
	}
	
	// 执行转换
	mcpReq, err := cm.requestConverter.Convert(ctx, httpReq, rule)
	if err != nil {
		if cm.metrics != nil {
			cm.metrics.IncCounter("mcp_conversion_errors_total", map[string]string{
				"type":  "request",
				"error": "conversion_failed",
			})
		}
		return nil, err
	}
	
	// 记录转换成功
	if cm.metrics != nil {
		cm.metrics.IncCounter("mcp_conversion_success_total", map[string]string{
			"type": "request",
		})
	}
	
	return mcpReq, nil
}

// ConvertResponse 转换 MCP 响应为 HTTP 响应
func (cm *ConverterManager) ConvertResponse(ctx context.Context, mcpResp *protocol.Response, httpResp http.ResponseWriter) error {
	// 记录转换开始
	if cm.metrics != nil {
		cm.metrics.IncCounter("mcp_conversion_requests_total", map[string]string{
			"type": "response",
		})
	}
	
	// 执行转换
	err := cm.responseConverter.Convert(ctx, mcpResp, httpResp)
	if err != nil {
		if cm.metrics != nil {
			cm.metrics.IncCounter("mcp_conversion_errors_total", map[string]string{
				"type":  "response",
				"error": "conversion_failed",
			})
		}
		return err
	}
	
	// 记录转换成功
	if cm.metrics != nil {
		cm.metrics.IncCounter("mcp_conversion_success_total", map[string]string{
			"type": "response",
		})
	}
	
	return nil
}

// ConvertError 转换错误为 MCP 错误响应
func (cm *ConverterManager) ConvertError(ctx context.Context, err error, httpResp http.ResponseWriter) error {
	// 记录错误转换
	if cm.metrics != nil {
		cm.metrics.IncCounter("mcp_conversion_requests_total", map[string]string{
			"type": "error",
		})
	}
	
	// 执行错误转换
	convertErr := cm.errorConverter.Convert(ctx, err, httpResp)
	if convertErr != nil {
		if cm.metrics != nil {
			cm.metrics.IncCounter("mcp_conversion_errors_total", map[string]string{
				"type":  "error",
				"error": "conversion_failed",
			})
		}
		return convertErr
	}
	
	// 记录转换成功
	if cm.metrics != nil {
		cm.metrics.IncCounter("mcp_conversion_success_total", map[string]string{
			"type": "error",
		})
	}
	
	return nil
}

// CanConvert 检查是否可以转换指定的请求
func (cm *ConverterManager) CanConvert(httpReq *http.Request) bool {
	return cm.GetConversionRule(httpReq) != nil
}

// GetConversionRule 获取转换规则
func (cm *ConverterManager) GetConversionRule(httpReq *http.Request) *config.ConversionRule {
	// 按优先级排序查找匹配的规则
	var bestRule *config.ConversionRule
	var bestPriority int = -1
	
	for i := range cm.rules {
		rule := &cm.rules[i]
		
		// 检查规则是否启用
		if !rule.Enabled {
			continue
		}
		
		// 检查路径是否匹配
		if !cm.pathMatches(httpReq.URL.Path, rule.Path) {
			continue
		}
		
		// 检查方法是否匹配
		if !cm.methodMatches(httpReq.Method, rule.Methods) {
			continue
		}
		
		// 检查优先级
		if rule.Priority > bestPriority {
			bestRule = rule
			bestPriority = rule.Priority
		}
	}
	
	return bestRule
}

// pathMatches 检查路径是否匹配
func (cm *ConverterManager) pathMatches(requestPath, rulePath string) bool {
	// 这里可以实现更复杂的路径匹配逻辑
	// 支持通配符、正则表达式等
	
	// 简单的字符串匹配
	if requestPath == rulePath {
		return true
	}
	
	// 支持路径参数匹配 (如 /api/v1/users/{id})
	return cm.pathParameterMatches(requestPath, rulePath)
}

// pathParameterMatches 检查路径参数是否匹配
func (cm *ConverterManager) pathParameterMatches(requestPath, rulePath string) bool {
	// 实现路径参数匹配逻辑
	// 例如：/api/v1/users/123 匹配 /api/v1/users/{id}
	
	// 这里可以使用正则表达式或其他路径匹配库
	// 为了简化，这里只做基本实现
	
	return false // 暂时返回 false，实际实现中需要完善
}

// methodMatches 检查方法是否匹配
func (cm *ConverterManager) methodMatches(requestMethod string, ruleMethods []string) bool {
	if len(ruleMethods) == 0 {
		return true // 空列表表示匹配所有方法
	}
	
	for _, method := range ruleMethods {
		if method == requestMethod {
			return true
		}
	}
	
	return false
}

// UpdateRules 更新转换规则
func (cm *ConverterManager) UpdateRules(rules []config.ConversionRule) {
	cm.rules = rules
	
	// 更新子转换器的规则
	cm.requestConverter.UpdateRules(rules)
	cm.responseConverter.UpdateRules(rules)
	cm.errorConverter.UpdateRules(rules)
	
	if cm.logger != nil {
		cm.logger.Info("MCP 转换规则已更新", "rule_count", len(rules))
	}
}

// GetRules 获取转换规则
func (cm *ConverterManager) GetRules() []config.ConversionRule {
	return cm.rules
}

// GetStats 获取转换统计信息
func (cm *ConverterManager) GetStats() *ConversionStats {
	return &ConversionStats{
		TotalRules:        len(cm.rules),
		EnabledRules:      cm.countEnabledRules(),
		RequestConverter:  cm.requestConverter.GetStats(),
		ResponseConverter: cm.responseConverter.GetStats(),
		ErrorConverter:    cm.errorConverter.GetStats(),
	}
}

// countEnabledRules 统计启用的规则数量
func (cm *ConverterManager) countEnabledRules() int {
	count := 0
	for _, rule := range cm.rules {
		if rule.Enabled {
			count++
		}
	}
	return count
}

// ConversionStats 转换统计信息
type ConversionStats struct {
	// 总规则数
	TotalRules int `json:"total_rules"`
	
	// 启用的规则数
	EnabledRules int `json:"enabled_rules"`
	
	// 请求转换器统计
	RequestConverter *RequestConverterStats `json:"request_converter"`
	
	// 响应转换器统计
	ResponseConverter *ResponseConverterStats `json:"response_converter"`
	
	// 错误转换器统计
	ErrorConverter *ErrorConverterStats `json:"error_converter"`
}

// ConversionError 转换错误
type ConversionError struct {
	Type    ErrorType
	Message string
	Details map[string]interface{}
	Cause   error
}

// Error 实现 error 接口
func (e *ConversionError) Error() string {
	if e.Cause != nil {
		return e.Message + ": " + e.Cause.Error()
	}
	return e.Message
}

// Unwrap 实现 errors.Unwrap 接口
func (e *ConversionError) Unwrap() error {
	return e.Cause
}

// ErrorType 错误类型
type ErrorType string

const (
	// ErrorTypeNoMatchingRule 没有匹配的规则
	ErrorTypeNoMatchingRule ErrorType = "no_matching_rule"
	// ErrorTypeParameterMapping 参数映射错误
	ErrorTypeParameterMapping ErrorType = "parameter_mapping"
	// ErrorTypeResponseMapping 响应映射错误
	ErrorTypeResponseMapping ErrorType = "response_mapping"
	// ErrorTypeInvalidRequest 无效请求
	ErrorTypeInvalidRequest ErrorType = "invalid_request"
	// ErrorTypeInvalidResponse 无效响应
	ErrorTypeInvalidResponse ErrorType = "invalid_response"
	// ErrorTypeConversionFailed 转换失败
	ErrorTypeConversionFailed ErrorType = "conversion_failed"
)

// NewConversionError 创建转换错误
func NewConversionError(errorType ErrorType, message string, cause error) *ConversionError {
	return &ConversionError{
		Type:    errorType,
		Message: message,
		Cause:   cause,
	}
}

// NewConversionErrorWithDetails 创建带详情的转换错误
func NewConversionErrorWithDetails(errorType ErrorType, message string, details map[string]interface{}, cause error) *ConversionError {
	return &ConversionError{
		Type:    errorType,
		Message: message,
		Details: details,
		Cause:   cause,
	}
}
