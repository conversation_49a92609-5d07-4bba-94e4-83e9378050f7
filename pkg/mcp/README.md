# MCP (Model Context Protocol) 转换模块

## 概述

MCP 转换模块提供了一个无侵入式的 API 到 MCP (Model Context Protocol) 服务转换功能。该模块允许将现有的 REST API 自动转换为 MCP 服务格式，无需修改现有的 API 实现代码。

## 功能特性

### 🔄 无侵入式转换
- 通过中间件方式集成，不需要修改现有 API 代码
- 支持动态配置转换规则
- 与现有网关功能完全兼容

### 📋 协议支持
- 完整的 MCP 协议实现（基于 JSON-RPC 2.0）
- 支持 Request、Response、Notification 三种消息类型
- 实现协议生命周期管理（初始化、操作、关闭）

### 🛠️ 转换功能
- HTTP 方法到 MCP 方法的映射
- 请求参数格式转换
- 响应数据格式转换
- 错误状态码映射

### ⚙️ 配置管理
- YAML 格式配置文件
- 支持路由级别的转换规则
- 灵活的参数映射配置
- 热重载配置支持

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway                              │
├─────────────────────────────────────────────────────────────┤
│  HTTP Request  →  [MCP Middleware]  →  [Existing APIs]     │
│                        ↓                                    │
│                   [MCP Converter]                           │
│                        ↓                                    │
│                 [MCP Protocol Layer]                        │
│                        ↓                                    │
│                  [Transport Layer]                          │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **MCP 中间件 (Middleware)**
   - 检测 MCP 转换需求
   - 调用相应的转换器
   - 处理转换结果

2. **转换器 (Converter)**
   - API 请求转换器
   - 响应转换器
   - 错误转换器

3. **协议层 (Protocol)**
   - JSON-RPC 2.0 消息处理
   - 会话管理
   - 能力协商

4. **传输层 (Transport)**
   - stdio 传输
   - HTTP SSE 传输
   - 自定义传输

5. **配置管理 (Config)**
   - 转换规则配置
   - 路由映射配置
   - 参数映射配置

## 目录结构

```
pkg/mcp/
├── README.md                 # 本文档
├── config/                   # 配置管理
│   ├── config.go            # 配置结构定义
│   ├── loader.go            # 配置加载器
│   └── validator.go         # 配置验证器
├── protocol/                 # MCP 协议实现
│   ├── jsonrpc.go           # JSON-RPC 2.0 实现
│   ├── session.go           # 会话管理
│   ├── lifecycle.go         # 生命周期管理
│   └── capabilities.go      # 能力协商
├── transport/                # 传输层实现
│   ├── transport.go         # 传输层接口
│   ├── stdio.go             # stdio 传输
│   ├── sse.go               # HTTP SSE 传输
│   └── custom.go            # 自定义传输
├── converter/                # 转换器实现
│   ├── converter.go         # 转换器接口
│   ├── request.go           # 请求转换器
│   ├── response.go          # 响应转换器
│   └── error.go             # 错误转换器
├── middleware/               # 中间件实现
│   ├── middleware.go        # MCP 中间件
│   └── handler.go           # 处理器
├── server/                   # MCP 服务器实现
│   ├── server.go            # MCP 服务器
│   ├── resources.go         # 资源管理
│   ├── tools.go             # 工具管理
│   └── prompts.go           # 提示管理
└── examples/                 # 示例和测试
    ├── basic/               # 基础示例
    ├── advanced/            # 高级示例
    └── configs/             # 配置示例
```

## 快速开始

### 1. 配置 MCP 转换

创建 MCP 配置文件 `configs/mcp.yaml`：

```yaml
mcp:
  enabled: true
  protocol_version: "2024-11-05"
  
  # 转换规则配置
  conversion_rules:
    - path: "/api/v1/users"
      methods: ["GET", "POST"]
      mcp_method: "users.list"
      parameter_mapping:
        query:
          page: "pagination.page"
          limit: "pagination.limit"
        body:
          name: "user.name"
          email: "user.email"
    
    - path: "/api/v1/users/{id}"
      methods: ["GET", "PUT", "DELETE"]
      mcp_method: "users.get"
      parameter_mapping:
        path:
          id: "user.id"
  
  # 服务器配置
  server:
    name: "API Gateway MCP Server"
    version: "1.0.0"
    capabilities:
      resources: true
      tools: true
      prompts: true
  
  # 传输配置
  transport:
    type: "sse"  # stdio, sse, custom
    sse:
      endpoint: "/mcp/sse"
      post_endpoint: "/mcp/message"
```

### 2. 启用 MCP 中间件

在网关配置中启用 MCP 中间件：

```go
// 在 gateway.go 中添加 MCP 中间件
func (g *Gateway) addMCPMiddleware() {
    if g.config.MCP.Enabled {
        mcpMiddleware := mcp.NewMiddleware(g.config.MCP, g.logger)
        g.router.Use(mcpMiddleware.Handle())
    }
}
```

### 3. 测试 MCP 转换

发送 HTTP 请求测试转换功能：

```bash
# 普通 HTTP 请求
curl -X GET "http://localhost:8080/api/v1/users?page=1&limit=10"

# MCP 格式请求
curl -X POST "http://localhost:8080/mcp/message" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "users.list",
    "params": {
      "pagination": {
        "page": 1,
        "limit": 10
      }
    }
  }'
```

## 配置说明

详细的配置说明请参考 [配置文档](docs/configuration.md)。

## API 映射规则

详细的 API 映射规则请参考 [映射规则文档](docs/mapping-rules.md)。

## 开发指南

详细的开发指南请参考 [开发文档](docs/development.md)。
