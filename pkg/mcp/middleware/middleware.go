package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"api-gateway/pkg/mcp/config"
	"api-gateway/pkg/mcp/converter"
	"api-gateway/pkg/mcp/protocol"
	"api-gateway/pkg/mcp/server"
	"api-gateway/pkg/telemetry"
)

// MCPMiddleware MCP 转换中间件
type MCPMiddleware struct {
	// MCP 配置
	config *config.MCPConfig
	
	// 转换器管理器
	converterManager converter.Converter
	
	// MCP 服务器
	mcpServer *server.MCPServer
	
	// 日志记录器
	logger *telemetry.Logger
	
	// 指标收集器
	metrics *telemetry.Metrics
	
	// 是否启用
	enabled bool
}

// NewMCPMiddleware 创建 MCP 中间件
func NewMCPMiddleware(cfg *config.MCPConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) *MCPMiddleware {
	// 创建转换器管理器
	converterManager := converter.NewConverterManager(cfg.ConversionRules, logger, metrics)
	
	// 创建 MCP 服务器
	mcpServer := server.NewMCPServer(cfg, logger, metrics)
	
	return &MCPMiddleware{
		config:           cfg,
		converterManager: converterManager,
		mcpServer:        mcpServer,
		logger:           logger,
		metrics:          metrics,
		enabled:          cfg.Enabled,
	}
}

// Handle 处理 HTTP 请求
func (m *MCPMiddleware) Handle() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用 MCP 转换
		if !m.enabled {
			c.Next()
			return
		}
		
		// 记录请求开始时间
		startTime := time.Now()
		
		// 检查是否是 MCP 相关的请求
		if m.isMCPRequest(c.Request) {
			m.handleMCPRequest(c)
			return
		}
		
		// 检查是否需要进行 API 到 MCP 转换
		if m.shouldConvertToMCP(c.Request) {
			m.handleAPIToMCPConversion(c, startTime)
			return
		}
		
		// 继续处理普通请求
		c.Next()
	}
}

// isMCPRequest 检查是否是 MCP 协议请求
func (m *MCPMiddleware) isMCPRequest(req *http.Request) bool {
	// 检查路径是否匹配 MCP 端点
	if strings.HasPrefix(req.URL.Path, m.config.Transport.SSE.Endpoint) ||
		strings.HasPrefix(req.URL.Path, m.config.Transport.SSE.PostEndpoint) {
		return true
	}
	
	// 检查请求头是否包含 MCP 标识
	if req.Header.Get("X-MCP-Protocol") != "" {
		return true
	}
	
	// 检查 Content-Type 是否为 MCP 格式
	contentType := req.Header.Get("Content-Type")
	if strings.Contains(contentType, "application/vnd.mcp") {
		return true
	}
	
	return false
}

// shouldConvertToMCP 检查是否应该转换为 MCP
func (m *MCPMiddleware) shouldConvertToMCP(req *http.Request) bool {
	// 检查请求头是否要求 MCP 转换
	if req.Header.Get("X-Convert-To-MCP") == "true" {
		return true
	}
	
	// 检查是否有匹配的转换规则
	return m.converterManager.CanConvert(req)
}

// handleMCPRequest 处理 MCP 协议请求
func (m *MCPMiddleware) handleMCPRequest(c *gin.Context) {
	if m.logger != nil {
		m.logger.Debug("处理 MCP 协议请求",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"remote_addr", c.ClientIP(),
		)
	}
	
	// 记录 MCP 请求指标
	if m.metrics != nil {
		m.metrics.IncCounter("mcp_requests_total", map[string]string{
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
		})
	}
	
	// 委托给 MCP 服务器处理
	m.mcpServer.HandleHTTPRequest(c)
}

// handleAPIToMCPConversion 处理 API 到 MCP 转换
func (m *MCPMiddleware) handleAPIToMCPConversion(c *gin.Context, startTime time.Time) {
	ctx := c.Request.Context()
	
	if m.logger != nil {
		m.logger.Debug("开始 API 到 MCP 转换",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"remote_addr", c.ClientIP(),
		)
	}
	
	// 记录转换请求指标
	if m.metrics != nil {
		m.metrics.IncCounter("mcp_api_conversions_total", map[string]string{
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
		})
	}
	
	// 转换 HTTP 请求为 MCP 请求
	mcpReq, err := m.converterManager.ConvertRequest(ctx, c.Request)
	if err != nil {
		m.handleConversionError(c, err, "请求转换失败")
		return
	}
	
	// 处理 MCP 请求
	mcpResp, err := m.processMCPRequest(ctx, mcpReq)
	if err != nil {
		m.handleConversionError(c, err, "MCP 请求处理失败")
		return
	}
	
	// 转换 MCP 响应为 HTTP 响应
	if err := m.converterManager.ConvertResponse(ctx, mcpResp, c.Writer); err != nil {
		m.handleConversionError(c, err, "响应转换失败")
		return
	}
	
	// 记录转换成功指标
	if m.metrics != nil {
		duration := time.Since(startTime)
		m.metrics.RecordHistogram("mcp_conversion_duration_seconds", duration.Seconds(), map[string]string{
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
			"status": "success",
		})
		
		m.metrics.IncCounter("mcp_api_conversions_success_total", map[string]string{
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
		})
	}
	
	if m.logger != nil {
		m.logger.Info("API 到 MCP 转换成功",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"mcp_method", mcpReq.Method,
			"request_id", mcpReq.ID,
			"duration", time.Since(startTime),
		)
	}
	
	// 阻止继续处理
	c.Abort()
}

// processMCPRequest 处理 MCP 请求
func (m *MCPMiddleware) processMCPRequest(ctx context.Context, mcpReq *protocol.Request) (*protocol.Response, error) {
	// 这里可以实现具体的 MCP 请求处理逻辑
	// 目前简单返回一个成功响应
	
	// 模拟处理时间
	time.Sleep(10 * time.Millisecond)
	
	// 创建成功响应
	result := map[string]interface{}{
		"message": "MCP 请求处理成功",
		"method":  mcpReq.Method,
		"params":  mcpReq.Params,
	}
	
	return protocol.NewResponse(mcpReq.ID, result), nil
}

// handleConversionError 处理转换错误
func (m *MCPMiddleware) handleConversionError(c *gin.Context, err error, message string) {
	if m.logger != nil {
		m.logger.Error(message,
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"error", err.Error(),
		)
	}
	
	// 记录错误指标
	if m.metrics != nil {
		m.metrics.IncCounter("mcp_conversion_errors_total", map[string]string{
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
			"error":  "conversion_failed",
		})
	}
	
	// 使用错误转换器处理错误
	if convErr := m.converterManager.ConvertError(c.Request.Context(), err, c.Writer); convErr != nil {
		// 如果错误转换也失败，返回简单的错误响应
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": gin.H{
				"code":    5000,
				"message": "内部服务器错误",
				"details": message,
			},
			"success": false,
		})
	}
	
	c.Abort()
}

// UpdateConfig 更新配置
func (m *MCPMiddleware) UpdateConfig(cfg *config.MCPConfig) error {
	m.config = cfg
	m.enabled = cfg.Enabled
	
	// 更新转换器管理器的规则
	m.converterManager.UpdateRules(cfg.ConversionRules)
	
	// 更新 MCP 服务器配置
	if err := m.mcpServer.UpdateConfig(cfg); err != nil {
		return fmt.Errorf("更新 MCP 服务器配置失败: %w", err)
	}
	
	if m.logger != nil {
		m.logger.Info("MCP 中间件配置已更新",
			"enabled", m.enabled,
			"rules_count", len(cfg.ConversionRules),
		)
	}
	
	return nil
}

// GetStats 获取统计信息
func (m *MCPMiddleware) GetStats() *MiddlewareStats {
	return &MiddlewareStats{
		Enabled:          m.enabled,
		ConfigVersion:    m.config.ProtocolVersion,
		ConverterStats:   m.converterManager.GetStats(),
		ServerStats:      m.mcpServer.GetStats(),
	}
}

// MiddlewareStats 中间件统计信息
type MiddlewareStats struct {
	// 是否启用
	Enabled bool `json:"enabled"`
	
	// 配置版本
	ConfigVersion string `json:"config_version"`
	
	// 转换器统计
	ConverterStats *converter.ConversionStats `json:"converter_stats"`
	
	// 服务器统计
	ServerStats *server.ServerStats `json:"server_stats"`
}

// Enable 启用中间件
func (m *MCPMiddleware) Enable() {
	m.enabled = true
	if m.logger != nil {
		m.logger.Info("MCP 中间件已启用")
	}
}

// Disable 禁用中间件
func (m *MCPMiddleware) Disable() {
	m.enabled = false
	if m.logger != nil {
		m.logger.Info("MCP 中间件已禁用")
	}
}

// IsEnabled 检查是否启用
func (m *MCPMiddleware) IsEnabled() bool {
	return m.enabled
}

// GetConfig 获取配置
func (m *MCPMiddleware) GetConfig() *config.MCPConfig {
	return m.config
}

// GetConverterManager 获取转换器管理器
func (m *MCPMiddleware) GetConverterManager() converter.Converter {
	return m.converterManager
}

// GetMCPServer 获取 MCP 服务器
func (m *MCPMiddleware) GetMCPServer() *server.MCPServer {
	return m.mcpServer
}

// Shutdown 关闭中间件
func (m *MCPMiddleware) Shutdown(ctx context.Context) error {
	if m.logger != nil {
		m.logger.Info("正在关闭 MCP 中间件")
	}
	
	// 关闭 MCP 服务器
	if err := m.mcpServer.Shutdown(ctx); err != nil {
		return fmt.Errorf("关闭 MCP 服务器失败: %w", err)
	}
	
	m.enabled = false
	
	if m.logger != nil {
		m.logger.Info("MCP 中间件已关闭")
	}
	
	return nil
}
