package middleware

import (
	"context"
	"net/http"
	"time"

	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
)

// Middleware interface defines the middleware contract
type Middleware interface {
	Handle() gin.HandlerFunc
	Name() string
	Priority() int
}

// RequestID middleware adds a unique request ID to each request
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// Logger middleware logs HTTP requests
func Logger(logger *telemetry.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Get request ID
		requestID, _ := c.Get("request_id")

		// Build log fields
		fields := []interface{}{
			"request_id", requestID,
			"method", c.Request.Method,
			"path", path,
			"status", c.Writer.Status(),
			"latency", latency,
			"client_ip", c.ClientIP(),
			"user_agent", c.Request.UserAgent(),
		}

		if raw != "" {
			fields = append(fields, "query", raw)
		}

		if len(c.Errors) > 0 {
			fields = append(fields, "errors", c.Errors.String())
		}

		// Log based on status code
		status := c.Writer.Status()
		if status >= 500 {
			logger.Error("HTTP request completed with server error", fields...)
		} else if status >= 400 {
			logger.Warn("HTTP request completed with client error", fields...)
		} else {
			logger.Info("HTTP request completed", fields...)
		}
	}
}

// Recovery middleware recovers from panics
func Recovery(logger *telemetry.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				requestID, _ := c.Get("request_id")
				
				logger.Error("Panic recovered",
					"request_id", requestID,
					"error", err,
					"path", c.Request.URL.Path,
					"method", c.Request.Method)

				c.JSON(http.StatusInternalServerError, gin.H{
					"error":      "internal server error",
					"message":    "An unexpected error occurred",
					"request_id": requestID,
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}

// Metrics middleware records metrics for requests
func Metrics(metrics *telemetry.Metrics) gin.HandlerFunc {
	return func(c *gin.Context) {
		if metrics == nil {
			c.Next()
			return
		}

		start := time.Now()
		
		// Track active connections
		metrics.IncActiveConnections()
		defer metrics.DecActiveConnections()

		// Process request
		c.Next()

		// Record metrics
		duration := time.Since(start)
		path := c.FullPath()
		if path == "" {
			path = c.Request.URL.Path
		}

		upstream, _ := c.Get("upstream")
		upstreamStr := ""
		if upstream != nil {
			upstreamStr = upstream.(string)
		}

		metrics.RecordRequest(
			c.Request.Method,
			path,
			string(rune(c.Writer.Status())),
			upstreamStr,
			duration,
			c.Request.ContentLength,
			int64(c.Writer.Size()),
		)
	}
}

// Tracing middleware adds distributed tracing
func Tracing(tracer opentracing.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		if tracer == nil {
			c.Next()
			return
		}

		// Extract span context from headers
		spanCtx, _ := tracer.Extract(
			opentracing.HTTPHeaders,
			opentracing.HTTPHeadersCarrier(c.Request.Header),
		)

		// Start span
		span := tracer.StartSpan(
			c.Request.Method+" "+c.Request.URL.Path,
			ext.RPCServerOption(spanCtx),
		)
		defer span.Finish()

		// Set span tags
		ext.HTTPMethod.Set(span, c.Request.Method)
		ext.HTTPUrl.Set(span, c.Request.URL.String())
		ext.Component.Set(span, "api-gateway")

		// Add span to context
		c.Set("span", span)

		// Inject span context into headers for downstream services
		tracer.Inject(
			span.Context(),
			opentracing.HTTPHeaders,
			opentracing.HTTPHeadersCarrier(c.Request.Header),
		)

		// Process request
		c.Next()

		// Set response tags
		ext.HTTPStatusCode.Set(span, uint16(c.Writer.Status()))
		if c.Writer.Status() >= 400 {
			ext.Error.Set(span, true)
		}

		// Add error information if present
		if len(c.Errors) > 0 {
			span.SetTag("error.message", c.Errors.String())
		}
	}
}

// ConnectionTracker middleware tracks active connections
func ConnectionTracker(metrics *telemetry.Metrics) gin.HandlerFunc {
	return func(c *gin.Context) {
		if metrics == nil {
			c.Next()
			return
		}

		// This is handled in the Metrics middleware
		// Keeping this separate for clarity and potential future enhancements
		c.Next()
	}
}

// Timeout middleware adds request timeout
func Timeout(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()

		// Replace request context
		c.Request = c.Request.WithContext(ctx)

		// Channel to signal completion
		done := make(chan struct{})
		
		go func() {
			defer close(done)
			c.Next()
		}()

		select {
		case <-done:
			// Request completed normally
		case <-ctx.Done():
			// Request timed out
			c.JSON(http.StatusRequestTimeout, gin.H{
				"error":   "request timeout",
				"message": "Request took too long to process",
			})
			c.Abort()
		}
	}
}

// HealthCheck middleware for health check endpoints
func HealthCheck(path string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.URL.Path == path {
			c.JSON(http.StatusOK, gin.H{
				"status": "healthy",
				"time":   time.Now().UTC(),
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// ContentType middleware sets default content type
func ContentType(contentType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.GetHeader("Content-Type") == "" {
			c.Header("Content-Type", contentType)
		}
		c.Next()
	}
}



// RealIP middleware extracts real client IP
func RealIP() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check various headers for real IP
		realIP := c.GetHeader("X-Real-IP")
		if realIP == "" {
			realIP = c.GetHeader("X-Forwarded-For")
		}
		if realIP == "" {
			realIP = c.GetHeader("CF-Connecting-IP") // Cloudflare
		}
		
		if realIP != "" {
			c.Set("real_ip", realIP)
		}
		
		c.Next()
	}
}
