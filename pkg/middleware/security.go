package middleware

import (
	"io"
	"net/http"
	"strings"

	"api-gateway/pkg/config"
	"api-gateway/pkg/security"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
)

// CORS middleware handles Cross-Origin Resource Sharing
func CORS(cfg config.CORSConfig, logger *telemetry.Logger) gin.HandlerFunc {
	corsHandler, _ := security.NewCORSHandler(cfg, logger)
	
	return func(c *gin.Context) {
		origin := c.GetHeader("Origin")
		method := c.Request.Method
		
		// Get requested headers for preflight
		var requestedHeaders []string
		if requestedHeadersStr := c.GetHeader("Access-Control-Request-Headers"); requestedHeadersStr != "" {
			requestedHeaders = strings.Split(requestedHeadersStr, ",")
			for i, header := range requestedHeaders {
				requestedHeaders[i] = strings.TrimSpace(header)
			}
		}

		// Handle CORS
		result, err := corsHandler.Handle(origin, method, requestedHeaders)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "CORS error",
				"message": "Failed to process CORS request",
			})
			c.Abort()
			return
		}

		// Set CORS headers
		for key, value := range result.Headers {
			c.Header(key, value)
		}

		// Handle preflight request
		if method == "OPTIONS" && result.PreflightResult {
			c.Status(http.StatusNoContent)
			c.Abort()
			return
		}

		// Check if request is allowed
		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "CORS policy violation",
				"message": "Origin not allowed by CORS policy",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// IPFilter middleware filters requests based on IP address
func IPFilter(cfg config.IPFilterConfig, logger *telemetry.Logger) gin.HandlerFunc {
	ipFilter, _ := security.NewIPFilter(cfg, logger)
	
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		
		// Get real IP if available
		if realIP, exists := c.Get("real_ip"); exists {
			clientIP = realIP.(string)
		}

		allowed, err := ipFilter.IsAllowed(clientIP)
		if err != nil {
			logger.Error("IP filter error", "client_ip", clientIP, "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "IP filter error",
				"message": "Failed to validate client IP",
			})
			c.Abort()
			return
		}

		if !allowed {
			logger.Warn("IP blocked by filter", "client_ip", clientIP)
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "access denied",
				"message": "Your IP address is not allowed to access this resource",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// WAF middleware provides Web Application Firewall protection
func WAF(cfg config.WAFConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) gin.HandlerFunc {
	wafEngine, _ := security.NewWAFEngine(cfg, logger, metrics)
	
	return func(c *gin.Context) {
		// Read request body for WAF analysis
		var body string
		if c.Request.Body != nil {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil {
				body = string(bodyBytes)
				// Restore body for downstream handlers
				c.Request.Body = io.NopCloser(strings.NewReader(body))
			}
		}

		// Extract headers
		headers := make(map[string]string)
		for key, values := range c.Request.Header {
			if len(values) > 0 {
				headers[key] = values[0]
			}
		}

		// Create WAF request
		wafRequest := security.CreateWAFRequestFromHTTP(
			c.Request.Method,
			c.Request.URL.Path,
			c.Request.URL.RawQuery,
			body,
			c.ClientIP(),
			c.Request.UserAgent(),
			c.GetHeader("Content-Type"),
			headers,
		)

		// Evaluate request
		result, err := wafEngine.Evaluate(wafRequest)
		if err != nil {
			logger.Error("WAF evaluation error", "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "WAF error",
				"message": "Failed to evaluate request security",
			})
			c.Abort()
			return
		}

		// Handle WAF result
		switch result.Action {
		case "block":
			logger.Warn("Request blocked by WAF",
				"rule", result.RuleName,
				"reason", result.Reason,
				"client_ip", c.ClientIP(),
				"path", c.Request.URL.Path)
			
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "request blocked",
				"message": "Request blocked by security policy",
			})
			c.Abort()
			return

		case "log":
			logger.Info("Request logged by WAF",
				"rule", result.RuleName,
				"reason", result.Reason,
				"client_ip", c.ClientIP(),
				"path", c.Request.URL.Path)
			// Continue processing

		case "allow":
			// Continue processing
		}

		c.Next()
	}
}

// RateLimit middleware provides rate limiting
func RateLimit(cfg config.RateLimitConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) gin.HandlerFunc {
	rateLimiter, _ := security.NewRateLimiter(cfg, logger, metrics)
	
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		path := c.Request.URL.Path
		method := c.Request.Method
		
		// Get real IP if available
		if realIP, exists := c.Get("real_ip"); exists {
			clientIP = realIP.(string)
		}

		// Get key type from authentication context
		keyType := "anonymous"
		if userID, exists := c.Get("user_id"); exists && userID != nil {
			keyType = userID.(string)
		}

		// Check rate limit
		allowed, err := rateLimiter.IsAllowed(clientIP, path, method, keyType)
		if err != nil {
			logger.Error("Rate limit error", "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "rate limit error",
				"message": "Failed to check rate limit",
			})
			c.Abort()
			return
		}

		if !allowed {
			logger.Warn("Rate limit exceeded",
				"client_ip", clientIP,
				"path", path,
				"method", method,
				"key_type", keyType)
			
			c.Header("X-RateLimit-Limit", "exceeded")
			c.Header("Retry-After", "60") // Suggest retry after 60 seconds
			
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "rate limit exceeded",
				"message": "Too many requests. Please try again later.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// SecurityHeaders middleware adds security headers
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Prevent MIME type sniffing
		c.Header("X-Content-Type-Options", "nosniff")
		
		// Prevent clickjacking
		c.Header("X-Frame-Options", "DENY")
		
		// Enable XSS protection
		c.Header("X-XSS-Protection", "1; mode=block")
		
		// Control referrer information
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		
		// Content Security Policy
		c.Header("Content-Security-Policy", "default-src 'self'")
		
		// Strict Transport Security (for HTTPS)
		if c.Request.TLS != nil {
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}
		
		// Prevent caching of sensitive content
		if strings.Contains(c.Request.URL.Path, "/admin") || 
		   strings.Contains(c.Request.URL.Path, "/auth") {
			c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
			c.Header("Pragma", "no-cache")
			c.Header("Expires", "0")
		}

		c.Next()
	}
}

// RequestSizeLimit middleware limits request body size
func RequestSizeLimit(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"error":   "request too large",
				"message": "Request body exceeds maximum allowed size",
			})
			c.Abort()
			return
		}

		// Limit reader to prevent memory exhaustion
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxSize)
		
		c.Next()
	}
}

// HTTPSRedirect middleware redirects HTTP requests to HTTPS
func HTTPSRedirect() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Header.Get("X-Forwarded-Proto") == "http" {
			httpsURL := "https://" + c.Request.Host + c.Request.RequestURI
			c.Redirect(http.StatusMovedPermanently, httpsURL)
			c.Abort()
			return
		}
		c.Next()
	}
}

// NoCache middleware prevents caching of responses
func NoCache() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Cache-Control", "no-cache, no-store, max-age=0, must-revalidate, value")
		c.Header("Expires", "Thu, 01 Jan 1970 00:00:00 GMT")
		c.Header("Last-Modified", "Thu, 01 Jan 1970 00:00:00 GMT")
		c.Next()
	}
}
