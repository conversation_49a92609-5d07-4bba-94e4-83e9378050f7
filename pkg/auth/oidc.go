package auth

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/coreos/go-oidc/v3/oidc"
	"golang.org/x/oauth2"
)

// OIDCAuthenticator implements OpenID Connect authentication
// OIDC认证器实现OpenID Connect认证协议
type OIDCAuthenticator struct {
	config       config.OIDCConfig
	logger       *telemetry.Logger
	provider     *oidc.Provider        // OIDC提供者
	verifier     *oidc.IDTokenVerifier // ID Token验证器
	oauth2Config *oauth2.Config        // OAuth2配置
	httpClient   *http.Client          // HTTP客户端
}

// NewOIDCAuthenticator creates a new OIDC authenticator
// 创建新的OIDC认证器
func NewOIDCAuthenticator(cfg config.OIDCConfig, logger *telemetry.Logger) (*OIDCAuthenticator, error) {
	if !cfg.Enabled {
		return nil, fmt.Errorf("OIDC认证器未启用")
	}

	// 验证必需的配置参数
	if cfg.Issuer == "" {
		return nil, fmt.Errorf("OIDC Issuer URL不能为空")
	}
	if cfg.ClientID == "" {
		return nil, fmt.Errorf("OIDC Client ID不能为空")
	}
	if cfg.ClientSecret == "" {
		return nil, fmt.Errorf("OIDC Client Secret不能为空")
	}
	if cfg.RedirectURL == "" {
		return nil, fmt.Errorf("OIDC Redirect URL不能为空")
	}

	// 创建HTTP客户端，设置超时和TLS配置
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 如果配置了跳过TLS验证，设置相应的传输配置
	if cfg.SkipVerify {
		logger.Warn("OIDC配置为跳过TLS证书验证，这在生产环境中不安全")
		// 注意：在生产环境中不建议跳过TLS验证
	}

	auth := &OIDCAuthenticator{
		config:     cfg,
		logger:     logger,
		httpClient: httpClient,
	}

	// 初始化OIDC提供者
	if err := auth.initializeProvider(); err != nil {
		return nil, fmt.Errorf("初始化OIDC提供者失败: %w", err)
	}

	logger.Info("OIDC认证器初始化成功",
		"issuer", cfg.Issuer,
		"client_id", cfg.ClientID,
		"redirect_url", cfg.RedirectURL)

	return auth, nil
}

// initializeProvider initializes the OIDC provider
// 初始化OIDC提供者
func (o *OIDCAuthenticator) initializeProvider() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 1. 发现OIDC提供者配置
	o.logger.Info("正在发现OIDC提供者配置", "issuer", o.config.Issuer)

	provider, err := oidc.NewProvider(ctx, o.config.Issuer)
	if err != nil {
		return fmt.Errorf("发现OIDC提供者配置失败: %w", err)
	}
	o.provider = provider

	// 2. 创建ID Token验证器
	oidcConfig := &oidc.Config{
		ClientID: o.config.ClientID,
	}
	o.verifier = provider.Verifier(oidcConfig)

	// 3. 设置OAuth2配置
	scopes := []string{oidc.ScopeOpenID, "profile", "email"}
	if len(o.config.Scopes) > 0 {
		// 使用配置中指定的scopes，但确保包含openid
		scopes = o.config.Scopes
		hasOpenID := false
		for _, scope := range scopes {
			if scope == oidc.ScopeOpenID {
				hasOpenID = true
				break
			}
		}
		if !hasOpenID {
			scopes = append([]string{oidc.ScopeOpenID}, scopes...)
		}
	}

	o.oauth2Config = &oauth2.Config{
		ClientID:     o.config.ClientID,
		ClientSecret: o.config.ClientSecret,
		RedirectURL:  o.config.RedirectURL,
		Endpoint:     provider.Endpoint(),
		Scopes:       scopes,
	}

	o.logger.Info("OIDC提供者初始化成功",
		"issuer", o.config.Issuer,
		"client_id", o.config.ClientID,
		"scopes", o.oauth2Config.Scopes)

	return nil
}

// Authenticate performs OIDC authentication
// 执行OIDC认证
func (o *OIDCAuthenticator) Authenticate(ctx context.Context, authCtx *AuthContext) (*AuthResult, error) {
	// 从Authorization头中提取ID Token
	idToken := o.extractIDToken(authCtx.Headers)
	if idToken == "" {
		o.logger.Debug("未找到OIDC ID Token", "request_id", authCtx.RequestID)
		return &AuthResult{
			Authenticated: false,
			Error:         "未找到OIDC ID Token",
			TokenType:     "oidc",
		}, nil
	}

	// 验证ID Token
	o.logger.Debug("开始验证OIDC ID Token", "request_id", authCtx.RequestID)
	return o.ValidateIDToken(ctx, idToken)
}

// extractIDToken extracts OIDC ID token from headers
// 从请求头中提取OIDC ID Token
func (o *OIDCAuthenticator) extractIDToken(headers map[string]string) string {
	// 检查Authorization头中的Bearer token
	authHeader := headers["Authorization"]
	if authHeader == "" {
		authHeader = headers["authorization"]
	}

	if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
		token := strings.TrimPrefix(authHeader, "Bearer ")
		token = strings.TrimSpace(token)
		return token
	}

	return ""
}

// ValidateIDToken validates an OIDC ID token
// 验证OIDC ID Token
func (o *OIDCAuthenticator) ValidateIDToken(ctx context.Context, idToken string) (*AuthResult, error) {
	// 1. 验证ID Token签名和声明
	token, err := o.verifier.Verify(ctx, idToken)
	if err != nil {
		o.logger.Warn("OIDC ID Token验证失败", "error", err)
		return &AuthResult{
			Authenticated: false,
			Error:         "无效的OIDC ID Token",
			TokenType:     "oidc",
		}, nil
	}

	// 2. 提取用户信息
	var claims struct {
		Sub           string `json:"sub"`
		Name          string `json:"name"`
		Email         string `json:"email"`
		EmailVerified bool   `json:"email_verified"`
		Picture       string `json:"picture"`
		Groups        []string `json:"groups"`
		Roles         []string `json:"roles"`
	}

	if err := token.Claims(&claims); err != nil {
		o.logger.Error("解析OIDC Token声明失败", "error", err)
		return &AuthResult{
			Authenticated: false,
			Error:         "解析Token声明失败",
			TokenType:     "oidc",
		}, nil
	}

	// 3. 构建认证结果
	result := &AuthResult{
		Authenticated: true,
		UserID:        claims.Sub,
		Username:      claims.Name,
		TokenType:     "oidc",
		Roles:         claims.Roles,
		Attributes: map[string]interface{}{
			"email":          claims.Email,
			"email_verified": claims.EmailVerified,
			"picture":        claims.Picture,
			"groups":         claims.Groups,
			"issuer":         token.Issuer,
			"audience":       token.Audience,
			"expiry":         token.Expiry,
		},
	}

	// 如果没有角色信息，尝试从组信息中推导
	if len(result.Roles) == 0 && len(claims.Groups) > 0 {
		result.Roles = claims.Groups
	}

	o.logger.Info("OIDC认证成功",
		"user_id", claims.Sub,
		"username", claims.Name,
		"email", claims.Email,
		"roles", result.Roles)

	return result, nil
}

// GetAuthorizationURL generates the OIDC authorization URL
// 生成OIDC授权URL
func (o *OIDCAuthenticator) GetAuthorizationURL(state string) string {
	// 如果没有提供state，生成一个随机state用于CSRF保护
	if state == "" {
		state = o.generateRandomState()
	}

	// 使用OAuth2配置生成授权URL
	authURL := o.oauth2Config.AuthCodeURL(state, oauth2.AccessTypeOffline)

	o.logger.Debug("生成OIDC授权URL",
		"url", authURL,
		"state", state)

	return authURL
}

// generateRandomState generates a random state for CSRF protection
// 生成随机state用于CSRF保护
func (o *OIDCAuthenticator) generateRandomState() string {
	b := make([]byte, 32)
	rand.Read(b)
	return base64.URLEncoding.EncodeToString(b)
}

// ExchangeCodeForTokens exchanges authorization code for tokens
// 交换授权码获取Token
func (o *OIDCAuthenticator) ExchangeCodeForTokens(ctx context.Context, code string) (*AuthResult, error) {
	o.logger.Debug("开始交换授权码", "code", code[:10]+"...")

	// 1. 交换授权码获取Token
	oauth2Token, err := o.oauth2Config.Exchange(ctx, code)
	if err != nil {
		o.logger.Error("交换授权码失败", "error", err)
		return &AuthResult{
			Authenticated: false,
			Error:         "授权码交换失败",
			TokenType:     "oidc",
		}, nil
	}

	// 2. 提取ID Token
	rawIDToken, ok := oauth2Token.Extra("id_token").(string)
	if !ok {
		o.logger.Error("响应中未找到ID Token")
		return &AuthResult{
			Authenticated: false,
			Error:         "未找到ID Token",
			TokenType:     "oidc",
		}, nil
	}

	// 3. 验证ID Token并获取用户信息
	result, err := o.ValidateIDToken(ctx, rawIDToken)
	if err != nil {
		return result, err
	}

	// 4. 添加OAuth2 Token信息到结果中
	if result.Authenticated {
		if result.Attributes == nil {
			result.Attributes = make(map[string]interface{})
		}
		result.Attributes["access_token"] = oauth2Token.AccessToken
		result.Attributes["refresh_token"] = oauth2Token.RefreshToken
		result.Attributes["token_expiry"] = oauth2Token.Expiry
		result.Attributes["oauth2_token_type"] = oauth2Token.TokenType

		o.logger.Info("OIDC授权码交换成功",
			"user_id", result.UserID,
			"username", result.Username)
	}

	return result, nil
}

// RefreshToken refreshes an OIDC access token
// 刷新OIDC访问Token
func (o *OIDCAuthenticator) RefreshToken(ctx context.Context, refreshToken string) (*AuthResult, error) {
	o.logger.Debug("开始刷新OIDC Token")

	// 1. 使用刷新Token获取新的访问Token
	tokenSource := o.oauth2Config.TokenSource(ctx, &oauth2.Token{
		RefreshToken: refreshToken,
	})

	newToken, err := tokenSource.Token()
	if err != nil {
		o.logger.Error("刷新Token失败", "error", err)
		return &AuthResult{
			Authenticated: false,
			Error:         "Token刷新失败",
			TokenType:     "oidc",
		}, nil
	}

	// 2. 如果有新的ID Token，验证它
	if rawIDToken, ok := newToken.Extra("id_token").(string); ok {
		result, err := o.ValidateIDToken(ctx, rawIDToken)
		if err != nil {
			return result, err
		}

		// 3. 添加新的Token信息
		if result.Authenticated {
			if result.Attributes == nil {
				result.Attributes = make(map[string]interface{})
			}
			result.Attributes["access_token"] = newToken.AccessToken
			result.Attributes["refresh_token"] = newToken.RefreshToken
			result.Attributes["token_expiry"] = newToken.Expiry
			result.Attributes["oauth2_token_type"] = newToken.TokenType

			o.logger.Info("OIDC Token刷新成功", "user_id", result.UserID)
		}

		return result, nil
	}

	// 如果没有ID Token，返回基本的成功结果
	return &AuthResult{
		Authenticated: true,
		TokenType:     "oidc",
		Attributes: map[string]interface{}{
			"access_token":      newToken.AccessToken,
			"refresh_token":     newToken.RefreshToken,
			"token_expiry":      newToken.Expiry,
			"oauth2_token_type": newToken.TokenType,
		},
	}, nil
}

// GetUserInfo retrieves user information from OIDC provider
// 从OIDC提供者获取用户信息
func (o *OIDCAuthenticator) GetUserInfo(ctx context.Context, accessToken string) (map[string]interface{}, error) {
	o.logger.Debug("开始获取OIDC用户信息")

	// 1. 调用OIDC UserInfo端点
	userInfo, err := o.provider.UserInfo(ctx, oauth2.StaticTokenSource(&oauth2.Token{
		AccessToken: accessToken,
	}))
	if err != nil {
		o.logger.Error("获取用户信息失败", "error", err)
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 2. 解析用户信息
	var claims map[string]interface{}
	if err := userInfo.Claims(&claims); err != nil {
		o.logger.Error("解析用户信息失败", "error", err)
		return nil, fmt.Errorf("解析用户信息失败: %w", err)
	}

	o.logger.Debug("OIDC用户信息获取成功",
		"sub", claims["sub"],
		"name", claims["name"],
		"email", claims["email"])

	return claims, nil
}

// RevokeToken revokes an OIDC token
// 撤销OIDC Token
func (o *OIDCAuthenticator) RevokeToken(ctx context.Context, token string) error {
	o.logger.Debug("开始撤销OIDC Token")

	// 获取撤销端点URL
	var claims struct {
		RevocationEndpoint string `json:"revocation_endpoint"`
	}
	var revocationEndpoint string
	if err := o.provider.Claims(&claims); err == nil {
		revocationEndpoint = claims.RevocationEndpoint
	}

	if revocationEndpoint == "" {
		o.logger.Warn("OIDC提供者不支持Token撤销")
		return fmt.Errorf("OIDC提供者不支持Token撤销")
	}

	// 构建撤销请求
	data := fmt.Sprintf("token=%s&client_id=%s&client_secret=%s",
		token, o.config.ClientID, o.config.ClientSecret)

	req, err := http.NewRequestWithContext(ctx, "POST", revocationEndpoint, strings.NewReader(data))
	if err != nil {
		return fmt.Errorf("创建撤销请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送撤销请求
	resp, err := o.httpClient.Do(req)
	if err != nil {
		o.logger.Error("Token撤销请求失败", "error", err)
		return fmt.Errorf("Token撤销请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		o.logger.Error("Token撤销失败", "status_code", resp.StatusCode)
		return fmt.Errorf("Token撤销失败，状态码: %d", resp.StatusCode)
	}

	o.logger.Info("OIDC Token撤销成功")
	return nil
}

// Name returns the authenticator name
func (o *OIDCAuthenticator) Name() string {
	return "oidc"
}

// Priority returns the authenticator priority
func (o *OIDCAuthenticator) Priority() int {
	return 75
}

// Enabled returns whether the authenticator is enabled
func (o *OIDCAuthenticator) Enabled() bool {
	return o.config.Enabled
}

// Close closes the OIDC authenticator
// 关闭OIDC认证器
func (o *OIDCAuthenticator) Close() error {
	o.logger.Info("关闭OIDC认证器")

	// 关闭HTTP客户端
	if o.httpClient != nil {
		o.httpClient.CloseIdleConnections()
	}

	return nil
}
