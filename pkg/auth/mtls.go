package auth

import (
	"context"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"strings"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// MTLSAuthenticator implements mutual TLS authentication
type MTLSAuthenticator struct {
	config    config.MTLSConfig
	logger    *telemetry.Logger
	caCertPool *x509.CertPool
	crlCache   map[string]*pkix.CertificateList
	crlMutex   sync.RWMutex

	// OCSP configuration
	ocspEnabled bool
	ocspTimeout time.Duration

	// Certificate validation options
	verifyOptions x509.VerifyOptions
}

// NewMTLSAuthenticator creates a new mTLS authenticator
func NewMTLSAuthenticator(cfg config.MTLSConfig, logger *telemetry.Logger) (*MTLSAuthenticator, error) {
	if !cfg.Enabled {
		return nil, fmt.Errorf("mTLS认证未启用")
	}

	auth := &MTLSAuthenticator{
		config:      cfg,
		logger:      logger.With("component", "mtls-authenticator"),
		caCertPool:  x509.NewCertPool(),
		crlCache:    make(map[string]*pkix.CertificateList),
		ocspEnabled: cfg.OCSPEnabled,
		ocspTimeout: time.Duration(cfg.OCSPTimeoutSeconds) * time.Second,
	}

	// 设置默认OCSP超时
	if auth.ocspTimeout == 0 {
		auth.ocspTimeout = 10 * time.Second
	}

	// 加载CA证书
	if cfg.CAFile != "" {
		if err := auth.loadCACertificates(); err != nil {
			return nil, fmt.Errorf("加载CA证书失败: %w", err)
		}
	}

	// 加载CRL文件
	if cfg.CRLFile != "" {
		if err := auth.loadCRL(); err != nil {
			return nil, fmt.Errorf("加载CRL失败: %w", err)
		}
	}

	// 设置证书验证选项
	auth.verifyOptions = x509.VerifyOptions{
		Roots:         auth.caCertPool,
		KeyUsages:     []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth},
		CurrentTime:   time.Now(),
	}

	auth.logger.Info("mTLS认证器初始化成功",
		"ca_file", cfg.CAFile,
		"crl_file", cfg.CRLFile,
		"ocsp_enabled", cfg.OCSPEnabled)

	return auth, nil
}

// loadCACertificates loads CA certificates for client certificate validation
func (m *MTLSAuthenticator) loadCACertificates() error {
	// 读取CA证书文件
	caCertData, err := ioutil.ReadFile(m.config.CAFile)
	if err != nil {
		return fmt.Errorf("读取CA证书文件失败: %w", err)
	}

	// 解析PEM格式的证书
	block, rest := pem.Decode(caCertData)
	if block == nil {
		return fmt.Errorf("无法解析CA证书PEM格式")
	}

	// 解析第一个证书
	caCert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return fmt.Errorf("解析CA证书失败: %w", err)
	}

	// 添加到证书池
	m.caCertPool.AddCert(caCert)

	// 处理证书链中的其他证书
	for len(rest) > 0 {
		block, rest = pem.Decode(rest)
		if block == nil {
			break
		}

		if block.Type == "CERTIFICATE" {
			cert, err := x509.ParseCertificate(block.Bytes)
			if err != nil {
				m.logger.Warn("跳过无效的CA证书", "error", err)
				continue
			}
			m.caCertPool.AddCert(cert)
		}
	}

	m.logger.Info("CA证书加载成功", "file", m.config.CAFile)
	return nil
}

// loadCRL loads Certificate Revocation List
func (m *MTLSAuthenticator) loadCRL() error {
	crlData, err := ioutil.ReadFile(m.config.CRLFile)
	if err != nil {
		return fmt.Errorf("读取CRL文件失败: %w", err)
	}

	// 解析PEM格式的CRL
	block, _ := pem.Decode(crlData)
	if block == nil {
		return fmt.Errorf("无法解析CRL PEM格式")
	}

	// 解析CRL
	crl, err := x509.ParseCRL(block.Bytes)
	if err != nil {
		return fmt.Errorf("解析CRL失败: %w", err)
	}

	// 缓存CRL
	m.crlMutex.Lock()
	m.crlCache[m.config.CRLFile] = crl
	m.crlMutex.Unlock()

	m.logger.Info("CRL加载成功",
		"file", m.config.CRLFile,
		"revoked_count", len(crl.TBSCertList.RevokedCertificates))

	return nil
}

// checkCRL checks if certificate is revoked using CRL
func (m *MTLSAuthenticator) checkCRL(cert *x509.Certificate) error {
	if m.config.CRLFile == "" {
		return nil // CRL检查未启用
	}

	m.crlMutex.RLock()
	crl, exists := m.crlCache[m.config.CRLFile]
	m.crlMutex.RUnlock()

	if !exists {
		return fmt.Errorf("CRL未加载")
	}

	// 检查证书是否在撤销列表中
	for _, revokedCert := range crl.TBSCertList.RevokedCertificates {
		if revokedCert.SerialNumber.Cmp(cert.SerialNumber) == 0 {
			return fmt.Errorf("证书已被撤销: 序列号 %s", cert.SerialNumber.String())
		}
	}

	return nil
}

// checkOCSP checks certificate status using OCSP
func (m *MTLSAuthenticator) checkOCSP(cert *x509.Certificate, issuer *x509.Certificate) error {
	if !m.ocspEnabled {
		return nil // OCSP检查未启用
	}

	// 这里应该实现OCSP检查
	// 由于OCSP实现比较复杂，这里提供基础框架
	m.logger.Debug("OCSP检查", "cert_serial", cert.SerialNumber.String())

	// TODO: 实现实际的OCSP检查
	// 1. 从证书中提取OCSP服务器URL
	// 2. 构建OCSP请求
	// 3. 发送OCSP请求
	// 4. 验证OCSP响应

	return nil
}

// parseCertificateFromPEM parses certificate from PEM format
func (m *MTLSAuthenticator) parseCertificateFromPEM(certPEM string) (*x509.Certificate, error) {
	// 解码PEM格式
	block, _ := pem.Decode([]byte(certPEM))
	if block == nil {
		return nil, fmt.Errorf("无法解析PEM格式证书")
	}

	// 解析证书
	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("解析证书失败: %w", err)
	}

	return cert, nil
}

// validateCertificateAttributes validates certificate attributes based on configuration
func (m *MTLSAuthenticator) validateCertificateAttributes(cert *x509.Certificate) error {
	// 验证CN
	if m.config.VerifyClientCertCN && len(m.config.AllowedCNs) > 0 {
		found := false
		for _, allowedCN := range m.config.AllowedCNs {
			if cert.Subject.CommonName == allowedCN {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("证书CN '%s' 不在允许列表中", cert.Subject.CommonName)
		}
	}

	// 验证组织
	if m.config.VerifyOrganization && len(m.config.AllowedOrganizations) > 0 {
		found := false
		for _, certOrg := range cert.Subject.Organization {
			for _, allowedOrg := range m.config.AllowedOrganizations {
				if certOrg == allowedOrg {
					found = true
					break
				}
			}
			if found {
				break
			}
		}
		if !found {
			return fmt.Errorf("证书组织 '%v' 不在允许列表中", cert.Subject.Organization)
		}
	}

	return nil
}

// Authenticate performs mTLS authentication
func (m *MTLSAuthenticator) Authenticate(ctx context.Context, authCtx *AuthContext) (*AuthResult, error) {
	// 检查是否有客户端证书信息在Headers中
	// 在实际实现中，这些信息应该由TLS终端（如nginx、HAProxy）设置
	clientCertPEM, exists := authCtx.Headers["X-Client-Cert"]
	if !exists {
		m.logger.Debug("未找到客户端证书头", "request_id", authCtx.RequestID)
		return &AuthResult{
			Authenticated: false,
			Error:         "未提供客户端证书",
		}, nil
	}

	// 解析客户端证书
	clientCert, err := m.parseCertificateFromPEM(clientCertPEM)
	if err != nil {
		m.logger.Warn("解析客户端证书失败", "error", err, "request_id", authCtx.RequestID)
		return &AuthResult{
			Authenticated: false,
			Error:         fmt.Sprintf("客户端证书解析失败: %v", err),
		}, nil
	}

	// 验证证书链
	if err := m.validateCertificateChain(clientCert); err != nil {
		m.logger.Warn("证书链验证失败", "error", err, "request_id", authCtx.RequestID)
		return &AuthResult{
			Authenticated: false,
			Error:         fmt.Sprintf("证书链验证失败: %v", err),
		}, nil
	}

	// 检查证书撤销状态 (CRL)
	if err := m.checkCRL(clientCert); err != nil {
		m.logger.Warn("CRL检查失败", "error", err, "request_id", authCtx.RequestID)
		return &AuthResult{
			Authenticated: false,
			Error:         fmt.Sprintf("证书撤销检查失败: %v", err),
		}, nil
	}

	// 检查OCSP状态（需要发行者证书）
	var issuer *x509.Certificate
	if issuerPEM, exists := authCtx.Headers["X-Client-Cert-Issuer"]; exists {
		if parsedIssuer, err := m.parseCertificateFromPEM(issuerPEM); err == nil {
			issuer = parsedIssuer
		}
	}
	if err := m.checkOCSP(clientCert, issuer); err != nil {
		m.logger.Warn("OCSP检查失败", "error", err, "request_id", authCtx.RequestID)
		return &AuthResult{
			Authenticated: false,
			Error:         fmt.Sprintf("OCSP检查失败: %v", err),
		}, nil
	}

	// 提取用户信息
	userInfo := m.extractUserInfo(clientCert)

	// 验证CN和组织（如果配置了）
	if err := m.validateCertificateAttributes(clientCert); err != nil {
		m.logger.Warn("证书属性验证失败", "error", err, "request_id", authCtx.RequestID)
		return &AuthResult{
			Authenticated: false,
			Error:         fmt.Sprintf("证书属性验证失败: %v", err),
		}, nil
	}

	m.logger.Info("mTLS认证成功",
		"user_id", userInfo.UserID,
		"subject", clientCert.Subject.String(),
		"serial", clientCert.SerialNumber.String(),
		"request_id", authCtx.RequestID)

	return &AuthResult{
		Authenticated: true,
		UserID:        userInfo.UserID,
		Username:      userInfo.Username,
		TokenType:     "mtls",
		Roles:         userInfo.Roles,
		Attributes: map[string]interface{}{
			"certificate_subject":      clientCert.Subject.String(),
			"certificate_issuer":       clientCert.Issuer.String(),
			"certificate_serial":       clientCert.SerialNumber.String(),
			"certificate_not_before":   clientCert.NotBefore,
			"certificate_not_after":    clientCert.NotAfter,
			"certificate_fingerprint":  m.getCertificateFingerprint(clientCert),
			"organization":             userInfo.Organization,
			"organizational_unit":      userInfo.OrganizationalUnit,
			"country":                  userInfo.Country,
			"email":                    userInfo.Email,
		},
	}, nil
}

// validateCertificateChain validates the client certificate chain
func (m *MTLSAuthenticator) validateCertificateChain(cert *x509.Certificate) error {
	// 验证证书链
	_, err := cert.Verify(m.verifyOptions)
	if err != nil {
		return fmt.Errorf("证书链验证失败: %w", err)
	}

	// 检查证书用途
	if !m.isValidClientCertificate(cert) {
		return fmt.Errorf("证书不适用于客户端认证")
	}

	// 检查证书有效期
	now := time.Now()
	if now.Before(cert.NotBefore) {
		return fmt.Errorf("证书尚未生效")
	}
	if now.After(cert.NotAfter) {
		return fmt.Errorf("证书已过期")
	}

	return nil
}

// isValidClientCertificate checks if certificate is valid for client authentication
func (m *MTLSAuthenticator) isValidClientCertificate(cert *x509.Certificate) bool {
	// 检查密钥用途
	if cert.KeyUsage&x509.KeyUsageDigitalSignature == 0 {
		return false
	}

	// 检查扩展密钥用途
	for _, usage := range cert.ExtKeyUsage {
		if usage == x509.ExtKeyUsageClientAuth {
			return true
		}
	}

	return false
}

// UserInfo contains extracted user information from certificate
type UserInfo struct {
	UserID             string
	Username           string
	Email              string
	Organization       string
	OrganizationalUnit string
	Country            string
	Roles              []string
}

// extractUserInfo extracts user information from certificate
func (m *MTLSAuthenticator) extractUserInfo(cert *x509.Certificate) *UserInfo {
	subject := cert.Subject

	userInfo := &UserInfo{
		UserID:   subject.CommonName,
		Username: subject.CommonName,
		Roles:    []string{"user"}, // 默认角色
	}

	// 提取组织信息
	if len(subject.Organization) > 0 {
		userInfo.Organization = subject.Organization[0]
	}

	if len(subject.OrganizationalUnit) > 0 {
		userInfo.OrganizationalUnit = subject.OrganizationalUnit[0]
	}

	if len(subject.Country) > 0 {
		userInfo.Country = subject.Country[0]
	}

	// 从SAN中提取邮箱
	for _, email := range cert.EmailAddresses {
		if userInfo.Email == "" {
			userInfo.Email = email
			break
		}
	}

	// 根据组织单位确定角色
	if userInfo.OrganizationalUnit != "" {
		switch strings.ToLower(userInfo.OrganizationalUnit) {
		case "admin", "administrators":
			userInfo.Roles = []string{"admin", "user"}
		case "manager", "managers":
			userInfo.Roles = []string{"manager", "user"}
		default:
			userInfo.Roles = []string{"user"}
		}
	}

	return userInfo
}

// getCertificateFingerprint calculates certificate fingerprint
func (m *MTLSAuthenticator) getCertificateFingerprint(cert *x509.Certificate) string {
	// 使用SHA256计算指纹
	hash := fmt.Sprintf("%x", cert.Raw)
	return hash
}

// ValidateClientCertificate validates a client certificate
func (m *MTLSAuthenticator) ValidateClientCertificate(cert *x509.Certificate) (*AuthResult, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Verify certificate signature against CA
	// 2. Check certificate validity period
	// 3. Validate certificate chain
	// 4. Extract identity from certificate subject/extensions
	// 5. Map certificate to user/service identity

	if cert == nil {
		return &AuthResult{
			Authenticated: false,
			Error:         "no client certificate provided",
		}, nil
	}

	// Extract identity from certificate subject
	subject := cert.Subject
	userID := subject.CommonName
	
	if userID == "" {
		return &AuthResult{
			Authenticated: false,
			Error:         "no common name in certificate",
		}, nil
	}

	// Build auth result
	result := &AuthResult{
		Authenticated: true,
		UserID:        userID,
		Username:      userID,
		TokenType:     "mtls",
		Attributes: map[string]interface{}{
			"certificate_subject": subject.String(),
			"certificate_serial":  cert.SerialNumber.String(),
		},
	}

	// Extract roles from certificate extensions or organizational units
	if len(subject.OrganizationalUnit) > 0 {
		result.Roles = subject.OrganizationalUnit
	}

	m.logger.Info("mTLS authentication successful",
		"user_id", userID,
		"certificate_serial", cert.SerialNumber.String())

	return result, nil
}

// Name returns the authenticator name
func (m *MTLSAuthenticator) Name() string {
	return "mtls"
}

// Priority returns the authenticator priority
func (m *MTLSAuthenticator) Priority() int {
	return 50 // High priority for mTLS
}

// Enabled returns whether the authenticator is enabled
func (m *MTLSAuthenticator) Enabled() bool {
	return m.config.Enabled
}

// Close closes the mTLS authenticator
func (m *MTLSAuthenticator) Close() error {
	return nil
}
