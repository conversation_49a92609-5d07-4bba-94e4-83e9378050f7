package telemetry

import (
	"encoding/json"
	"fmt"
	"time"

	"api-gateway/pkg/config"
)

// AuditEvent represents a security audit event
type AuditEvent struct {
	Timestamp   time.Time              `json:"timestamp"`
	EventType   string                 `json:"event_type"`
	EventID     string                 `json:"event_id"`
	RequestID   string                 `json:"request_id,omitempty"`
	UserID      string                 `json:"user_id,omitempty"`
	Username    string                 `json:"username,omitempty"`
	ClientIP    string                 `json:"client_ip,omitempty"`
	UserAgent   string                 `json:"user_agent,omitempty"`
	Resource    string                 `json:"resource,omitempty"`
	Action      string                 `json:"action,omitempty"`
	Result      string                 `json:"result"`
	Reason      string                 `json:"reason,omitempty"`
	Severity    string                 `json:"severity"`
	Category    string                 `json:"category"`
	Source      string                 `json:"source"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	PolicyName  string                 `json:"policy_name,omitempty"`
	RiskScore   int                    `json:"risk_score,omitempty"`
	GeoLocation map[string]interface{} `json:"geo_location,omitempty"`
	DeviceInfo  map[string]interface{} `json:"device_info,omitempty"`
}

// AuditLogger handles security audit logging
type AuditLogger struct {
	logger *Logger
	config config.LoggingConfig
}

// NewAuditLogger creates a new audit logger
func NewAuditLogger(cfg config.LoggingConfig) (*AuditLogger, error) {
	// Create a dedicated logger for audit events
	auditCfg := LoggingConfig{
		Level:      cfg.Level,
		Format:     cfg.Format,
		Output:     "file",
		Filename:   "logs/audit.log", // Separate audit log file
		MaxSize:    cfg.MaxSize,
		MaxBackups: cfg.MaxBackups,
		MaxAge:     cfg.MaxAge,
		Compress:   true,
	}

	logger, err := NewLogger(auditCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create audit logger: %w", err)
	}

	return &AuditLogger{
		logger: logger.With("component", "audit"),
		config: cfg,
	}, nil
}

// LogAuthenticationEvent logs authentication events
func (a *AuditLogger) LogAuthenticationEvent(event *AuditEvent) {
	event.Category = "authentication"
	event.Source = "api-gateway"
	event.Timestamp = time.Now()
	
	a.logEvent(event)
}

// LogAuthorizationEvent logs authorization events
func (a *AuditLogger) LogAuthorizationEvent(event *AuditEvent) {
	event.Category = "authorization"
	event.Source = "api-gateway"
	event.Timestamp = time.Now()
	
	a.logEvent(event)
}

// LogSecurityEvent logs security-related events
func (a *AuditLogger) LogSecurityEvent(event *AuditEvent) {
	event.Category = "security"
	event.Source = "api-gateway"
	event.Timestamp = time.Now()
	
	a.logEvent(event)
}

// LogAccessEvent logs resource access events
func (a *AuditLogger) LogAccessEvent(event *AuditEvent) {
	event.Category = "access"
	event.Source = "api-gateway"
	event.Timestamp = time.Now()
	
	a.logEvent(event)
}

// LogConfigurationEvent logs configuration change events
func (a *AuditLogger) LogConfigurationEvent(event *AuditEvent) {
	event.Category = "configuration"
	event.Source = "api-gateway"
	event.Timestamp = time.Now()
	
	a.logEvent(event)
}

// LogDataEvent logs data access and modification events
func (a *AuditLogger) LogDataEvent(event *AuditEvent) {
	event.Category = "data"
	event.Source = "api-gateway"
	event.Timestamp = time.Now()
	
	a.logEvent(event)
}

// logEvent logs an audit event
func (a *AuditLogger) logEvent(event *AuditEvent) {
	// Generate event ID if not provided
	if event.EventID == "" {
		event.EventID = generateEventID()
	}

	// Serialize event to JSON
	eventJSON, err := json.Marshal(event)
	if err != nil {
		a.logger.Error("Failed to serialize audit event", "error", err)
		return
	}

	// Log based on severity
	switch event.Severity {
	case "critical":
		a.logger.Error("AUDIT", "event", string(eventJSON))
	case "high":
		a.logger.Warn("AUDIT", "event", string(eventJSON))
	case "medium":
		a.logger.Info("AUDIT", "event", string(eventJSON))
	case "low":
		a.logger.Debug("AUDIT", "event", string(eventJSON))
	default:
		a.logger.Info("AUDIT", "event", string(eventJSON))
	}
}

// CreateAuthenticationSuccessEvent creates an authentication success event
func CreateAuthenticationSuccessEvent(requestID, userID, username, clientIP, userAgent, authType string) *AuditEvent {
	return &AuditEvent{
		EventType: "authentication_success",
		RequestID: requestID,
		UserID:    userID,
		Username:  username,
		ClientIP:  clientIP,
		UserAgent: userAgent,
		Result:    "success",
		Severity:  "low",
		Metadata: map[string]interface{}{
			"auth_type": authType,
		},
	}
}

// CreateAuthenticationFailureEvent creates an authentication failure event
func CreateAuthenticationFailureEvent(requestID, clientIP, userAgent, reason, authType string) *AuditEvent {
	return &AuditEvent{
		EventType: "authentication_failure",
		RequestID: requestID,
		ClientIP:  clientIP,
		UserAgent: userAgent,
		Result:    "failure",
		Reason:    reason,
		Severity:  "medium",
		RiskScore: 5,
		Metadata: map[string]interface{}{
			"auth_type": authType,
		},
	}
}

// CreateAuthorizationDeniedEvent creates an authorization denied event
func CreateAuthorizationDeniedEvent(requestID, userID, username, clientIP, resource, action, reason, policyName string) *AuditEvent {
	return &AuditEvent{
		EventType:  "authorization_denied",
		RequestID:  requestID,
		UserID:     userID,
		Username:   username,
		ClientIP:   clientIP,
		Resource:   resource,
		Action:     action,
		Result:     "denied",
		Reason:     reason,
		Severity:   "medium",
		PolicyName: policyName,
		RiskScore:  3,
	}
}

// CreateSecurityViolationEvent creates a security violation event
func CreateSecurityViolationEvent(requestID, clientIP, userAgent, violationType, description string, riskScore int) *AuditEvent {
	return &AuditEvent{
		EventType: "security_violation",
		RequestID: requestID,
		ClientIP:  clientIP,
		UserAgent: userAgent,
		Result:    "blocked",
		Reason:    description,
		Severity:  "high",
		RiskScore: riskScore,
		Metadata: map[string]interface{}{
			"violation_type": violationType,
		},
	}
}

// CreateRateLimitExceededEvent creates a rate limit exceeded event
func CreateRateLimitExceededEvent(requestID, clientIP, resource, limitType string) *AuditEvent {
	return &AuditEvent{
		EventType: "rate_limit_exceeded",
		RequestID: requestID,
		ClientIP:  clientIP,
		Resource:  resource,
		Result:    "blocked",
		Reason:    "rate limit exceeded",
		Severity:  "medium",
		RiskScore: 2,
		Metadata: map[string]interface{}{
			"limit_type": limitType,
		},
	}
}

// CreateConfigurationChangeEvent creates a configuration change event
func CreateConfigurationChangeEvent(userID, username, configType, changeType, description string) *AuditEvent {
	return &AuditEvent{
		EventType: "configuration_change",
		UserID:    userID,
		Username:  username,
		Action:    changeType,
		Result:    "success",
		Severity:  "medium",
		Metadata: map[string]interface{}{
			"config_type": configType,
			"description": description,
		},
	}
}

// CreateDataAccessEvent creates a data access event
func CreateDataAccessEvent(requestID, userID, username, clientIP, resource, action string, sensitive bool) *AuditEvent {
	severity := "low"
	riskScore := 1
	
	if sensitive {
		severity = "medium"
		riskScore = 3
	}

	return &AuditEvent{
		EventType: "data_access",
		RequestID: requestID,
		UserID:    userID,
		Username:  username,
		ClientIP:  clientIP,
		Resource:  resource,
		Action:    action,
		Result:    "success",
		Severity:  severity,
		RiskScore: riskScore,
		Metadata: map[string]interface{}{
			"sensitive": sensitive,
		},
	}
}

// CreatePrivilegeEscalationEvent creates a privilege escalation event
func CreatePrivilegeEscalationEvent(requestID, userID, username, clientIP, fromRole, toRole, reason string) *AuditEvent {
	return &AuditEvent{
		EventType: "privilege_escalation",
		RequestID: requestID,
		UserID:    userID,
		Username:  username,
		ClientIP:  clientIP,
		Result:    "attempted",
		Reason:    reason,
		Severity:  "high",
		RiskScore: 8,
		Metadata: map[string]interface{}{
			"from_role": fromRole,
			"to_role":   toRole,
		},
	}
}

// CreateSuspiciousActivityEvent creates a suspicious activity event
func CreateSuspiciousActivityEvent(requestID, clientIP, userAgent, activityType, description string, riskScore int) *AuditEvent {
	severity := "medium"
	if riskScore >= 7 {
		severity = "high"
	} else if riskScore <= 3 {
		severity = "low"
	}

	return &AuditEvent{
		EventType: "suspicious_activity",
		RequestID: requestID,
		ClientIP:  clientIP,
		UserAgent: userAgent,
		Result:    "detected",
		Reason:    description,
		Severity:  severity,
		RiskScore: riskScore,
		Metadata: map[string]interface{}{
			"activity_type": activityType,
		},
	}
}

// generateEventID generates a unique event ID
func generateEventID() string {
	return fmt.Sprintf("audit_%d", time.Now().UnixNano())
}

// GetAuditEventTypes returns all supported audit event types
func GetAuditEventTypes() []string {
	return []string{
		"authentication_success",
		"authentication_failure",
		"authorization_granted",
		"authorization_denied",
		"security_violation",
		"rate_limit_exceeded",
		"configuration_change",
		"data_access",
		"privilege_escalation",
		"suspicious_activity",
		"session_created",
		"session_terminated",
		"password_change",
		"account_locked",
		"account_unlocked",
		"policy_violation",
		"compliance_check",
	}
}

// GetSeverityLevels returns all supported severity levels
func GetSeverityLevels() []string {
	return []string{
		"critical",
		"high",
		"medium",
		"low",
	}
}

// GetCategories returns all supported event categories
func GetCategories() []string {
	return []string{
		"authentication",
		"authorization",
		"security",
		"access",
		"configuration",
		"data",
		"compliance",
		"system",
	}
}
