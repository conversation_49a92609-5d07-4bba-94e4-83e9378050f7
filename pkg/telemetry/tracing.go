package telemetry

import (
	"fmt"
	"io"

	"api-gateway/pkg/config"

	"github.com/opentracing/opentracing-go"
	"github.com/uber/jaeger-client-go"
	jaegercfg "github.com/uber/jaeger-client-go/config"
	jaegerlog "github.com/uber/jaeger-client-go/log"
	"github.com/uber/jaeger-lib/metrics"
)

// NewTracer creates a new Jaeger tracer
func NewTracer(cfg config.TracingConfig) (opentracing.Tracer, io.Closer, error) {
	if !cfg.Enabled {
		return opentracing.NoopTracer{}, &noopCloser{}, nil
	}

	// Jaeger configuration
	jaegerCfg := jaegercfg.Configuration{
		ServiceName: cfg.ServiceName,
		Sampler: &jaegercfg.SamplerConfig{
			Type:  jaeger.SamplerTypeConst,
			Param: cfg.SampleRate,
		},
		Reporter: &jaegercfg.ReporterConfig{
			LogSpans:           true,
			LocalAgentHostPort: cfg.Endpoint,
		},
	}

	// Initialize tracer
	tracer, closer, err := jaegerCfg.NewTracer(
		jaegercfg.Logger(jaegerlog.StdLogger),
		jaegercfg.Metrics(metrics.NullFactory),
	)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to initialize tracer: %w", err)
	}

	// Set global tracer
	opentracing.SetGlobalTracer(tracer)

	return tracer, closer, nil
}

// noopCloser implements io.Closer with no-op Close method
type noopCloser struct{}

func (nc *noopCloser) Close() error {
	return nil
}
