package telemetry

import (
	"fmt"
	"os"

	"api-gateway/pkg/config"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger wraps zap.Logger with additional functionality
type Logger struct {
	*zap.Logger
}

// ConfigLoggerAdapter 适配器，将 telemetry.Logger 适配到 config.Logger 接口
type ConfigLoggerAdapter struct {
	logger *Logger
}

// NewConfigLoggerAdapter 创建配置日志适配器
func NewConfigLoggerAdapter(logger *Logger) *ConfigLoggerAdapter {
	return &ConfigLoggerAdapter{logger: logger}
}

// Info 记录信息级别日志
func (a *ConfigLoggerAdapter) Info(msg string, fields ...interface{}) {
	if a.logger != nil {
		a.logger.Info(msg, fields...)
	}
}

// Error 记录错误级别日志
func (a *ConfigLoggerAdapter) Error(msg string, fields ...interface{}) {
	if a.logger != nil {
		a.logger.Error(msg, fields...)
	}
}

// Warn 记录警告级别日志
func (a *ConfigLoggerAdapter) Warn(msg string, fields ...interface{}) {
	if a.logger != nil {
		a.logger.Warn(msg, fields...)
	}
}

// Debug 记录调试级别日志
func (a *ConfigLoggerAdapter) Debug(msg string, fields ...interface{}) {
	if a.logger != nil {
		a.logger.Debug(msg, fields...)
	}
}

// With 创建带有额外字段的日志器
func (a *ConfigLoggerAdapter) With(key string, value interface{}) ConfigLogger {
	if a.logger != nil {
		newLogger := a.logger.With(key, value)
		return NewConfigLoggerAdapter(newLogger)
	}
	return a
}

// ConfigLogger 定义配置日志接口，避免循环导入
type ConfigLogger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
	With(key string, value interface{}) ConfigLogger
}

// ConfigLoggerInterface 适配器，实现 config.Logger 接口
type ConfigLoggerInterface struct {
	logger *Logger
}

// NewConfigLoggerInterface 创建配置日志接口适配器
func NewConfigLoggerInterface(logger *Logger) *ConfigLoggerInterface {
	return &ConfigLoggerInterface{logger: logger}
}

// Info 记录信息级别日志
func (c *ConfigLoggerInterface) Info(msg string, fields ...interface{}) {
	if c.logger != nil {
		c.logger.Info(msg, fields...)
	}
}

// Error 记录错误级别日志
func (c *ConfigLoggerInterface) Error(msg string, fields ...interface{}) {
	if c.logger != nil {
		c.logger.Error(msg, fields...)
	}
}

// Warn 记录警告级别日志
func (c *ConfigLoggerInterface) Warn(msg string, fields ...interface{}) {
	if c.logger != nil {
		c.logger.Warn(msg, fields...)
	}
}

// Debug 记录调试级别日志
func (c *ConfigLoggerInterface) Debug(msg string, fields ...interface{}) {
	if c.logger != nil {
		c.logger.Debug(msg, fields...)
	}
}

// With 创建带有额外字段的日志器，返回 config.Logger 接口
func (c *ConfigLoggerInterface) With(key string, value interface{}) interface{} {
	if c.logger != nil {
		newLogger := c.logger.With(key, value)
		return NewConfigLoggerInterface(newLogger)
	}
	return c
}

// NewConfigLogger 创建一个实现 config.Logger 接口的适配器
func NewConfigLogger(logger *Logger) config.Logger {
	return config.NewLoggerAdapter(
		func(msg string, fields ...interface{}) {
			logger.Info(msg, fields...)
		},
		func(msg string, fields ...interface{}) {
			logger.Error(msg, fields...)
		},
		func(msg string, fields ...interface{}) {
			logger.Warn(msg, fields...)
		},
		func(msg string, fields ...interface{}) {
			logger.Debug(msg, fields...)
		},
		func(key string, value interface{}) config.Logger {
			newLogger := logger.With(key, value)
			return NewConfigLogger(newLogger)
		},
	)
}

// ConvertLoggingConfig 将 config.LoggingConfig 转换为 telemetry.LoggingConfig
func ConvertLoggingConfig(cfg config.LoggingConfig) LoggingConfig {
	return LoggingConfig{
		Level:      cfg.Level,
		Format:     cfg.Format,
		Output:     cfg.Output,
		Filename:   cfg.File,
		MaxSize:    cfg.MaxSize,
		MaxBackups: cfg.MaxBackups,
		MaxAge:     cfg.MaxAge,
		Compress:   true,
	}
}

// convertFields 转换字段格式
func convertFields(fields ...interface{}) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields)/2)
	for i := 0; i < len(fields)-1; i += 2 {
		if key, ok := fields[i].(string); ok {
			zapFields = append(zapFields, zap.Any(key, fields[i+1]))
		}
	}
	return zapFields
}

// LoggingConfig 日志配置结构体，避免循环导入
type LoggingConfig struct {
	Level      string `yaml:"level" json:"level"`
	Format     string `yaml:"format" json:"format"`
	Output     string `yaml:"output" json:"output"`
	Filename   string `yaml:"filename" json:"filename"`
	MaxSize    int    `yaml:"max_size" json:"max_size"`
	MaxBackups int    `yaml:"max_backups" json:"max_backups"`
	MaxAge     int    `yaml:"max_age" json:"max_age"`
	Compress   bool   `yaml:"compress" json:"compress"`
}

// NewLogger creates a new logger instance based on configuration
func NewLogger(cfg LoggingConfig) (*Logger, error) {
	// Configure log level
	level, err := zapcore.ParseLevel(cfg.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}

	// Configure encoder
	var encoder zapcore.Encoder
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// Configure output
	var writeSyncer zapcore.WriteSyncer
	if cfg.Output == "file" {
		// Use lumberjack for log rotation
		lumberJackLogger := &lumberjack.Logger{
			Filename:   cfg.Filename,
			MaxSize:    cfg.MaxSize,
			MaxBackups: cfg.MaxBackups,
			MaxAge:     cfg.MaxAge,
			Compress:   cfg.Compress,
		}
		writeSyncer = zapcore.AddSync(lumberJackLogger)
	} else {
		writeSyncer = zapcore.AddSync(os.Stdout)
	}

	// Create core
	core := zapcore.NewCore(encoder, writeSyncer, level)

	// Create logger with caller information
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return &Logger{Logger: logger}, nil
}

// Info logs an info message with structured fields
func (l *Logger) Info(msg string, fields ...interface{}) {
	l.Logger.Info(msg, l.convertFields(fields...)...)
}

// Error logs an error message with structured fields
func (l *Logger) Error(msg string, fields ...interface{}) {
	l.Logger.Error(msg, l.convertFields(fields...)...)
}

// Warn logs a warning message with structured fields
func (l *Logger) Warn(msg string, fields ...interface{}) {
	l.Logger.Warn(msg, l.convertFields(fields...)...)
}

// Debug logs a debug message with structured fields
func (l *Logger) Debug(msg string, fields ...interface{}) {
	l.Logger.Debug(msg, l.convertFields(fields...)...)
}

// Fatal logs a fatal message with structured fields and exits
func (l *Logger) Fatal(msg string, fields ...interface{}) {
	l.Logger.Fatal(msg, l.convertFields(fields...)...)
}

// With creates a child logger with additional fields
func (l *Logger) With(fields ...interface{}) *Logger {
	return &Logger{Logger: l.Logger.With(l.convertFields(fields...)...)}
}

// convertFields converts key-value pairs to zap fields
func (l *Logger) convertFields(fields ...interface{}) []zap.Field {
	if len(fields)%2 != 0 {
		// If odd number of fields, add the last one as a string
		fields = append(fields, "")
	}

	zapFields := make([]zap.Field, 0, len(fields)/2)
	for i := 0; i < len(fields); i += 2 {
		key, ok := fields[i].(string)
		if !ok {
			key = fmt.Sprintf("field_%d", i/2)
		}
		value := fields[i+1]
		zapFields = append(zapFields, zap.Any(key, value))
	}

	return zapFields
}

// RequestLogger creates a logger for HTTP request logging
func (l *Logger) RequestLogger() *Logger {
	return &Logger{Logger: l.Logger.Named("request")}
}

// SecurityLogger creates a logger for security events
func (l *Logger) SecurityLogger() *Logger {
	return &Logger{Logger: l.Logger.Named("security")}
}

// AuditLogger creates a logger for audit events
func (l *Logger) AuditLogger() *Logger {
	return &Logger{Logger: l.Logger.Named("audit")}
}
