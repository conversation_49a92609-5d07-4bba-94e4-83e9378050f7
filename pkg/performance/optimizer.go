package performance

import (
	"context"
	"runtime"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"
)

// Optimizer 性能优化器
type Optimizer struct {
	logger  *telemetry.Logger
	metrics *telemetry.Metrics
	config  OptimizerConfig
	
	// 性能统计
	stats     *PerformanceStats
	statsMux  sync.RWMutex
	
	// 优化器状态
	running   bool
	stopCh    chan struct{}
	wg        sync.WaitGroup
}

// OptimizerConfig 优化器配置
type OptimizerConfig struct {
	// 是否启用性能优化
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// 监控间隔
	MonitorInterval time.Duration `yaml:"monitor_interval" json:"monitor_interval"`
	
	// GC优化配置
	GCConfig GCConfig `yaml:"gc" json:"gc"`
	
	// 内存优化配置
	MemoryConfig MemoryConfig `yaml:"memory" json:"memory"`
	
	// 连接池优化配置
	PoolConfig PoolConfig `yaml:"pool" json:"pool"`
}

// GCConfig GC优化配置
type GCConfig struct {
	// 是否启用GC优化
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// GC目标百分比
	TargetPercent int `yaml:"target_percent" json:"target_percent"`
	
	// 强制GC间隔
	ForceInterval time.Duration `yaml:"force_interval" json:"force_interval"`
}

// MemoryConfig 内存优化配置
type MemoryConfig struct {
	// 是否启用内存优化
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// 内存使用阈值(MB)
	ThresholdMB int64 `yaml:"threshold_mb" json:"threshold_mb"`
	
	// 内存清理间隔
	CleanupInterval time.Duration `yaml:"cleanup_interval" json:"cleanup_interval"`
}

// PoolConfig 连接池优化配置
type PoolConfig struct {
	// 是否启用连接池优化
	Enabled bool `yaml:"enabled" json:"enabled"`
	
	// 最大空闲连接数
	MaxIdleConns int `yaml:"max_idle_conns" json:"max_idle_conns"`
	
	// 最大连接数
	MaxOpenConns int `yaml:"max_open_conns" json:"max_open_conns"`
	
	// 连接最大生存时间
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime" json:"conn_max_lifetime"`
}

// PerformanceStats 性能统计
type PerformanceStats struct {
	// 系统统计
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage int64   `json:"memory_usage"`
	GoroutineCount int  `json:"goroutine_count"`
	
	// GC统计
	GCCount     uint32  `json:"gc_count"`
	GCPauseTime float64 `json:"gc_pause_time"`
	
	// 请求统计
	RequestCount    int64   `json:"request_count"`
	AvgResponseTime float64 `json:"avg_response_time"`
	ErrorRate       float64 `json:"error_rate"`
	
	// 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// NewOptimizer 创建性能优化器
func NewOptimizer(config OptimizerConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) *Optimizer {
	return &Optimizer{
		logger:  logger.With("component", "performance-optimizer"),
		metrics: metrics,
		config:  config,
		stats:   &PerformanceStats{},
		stopCh:  make(chan struct{}),
	}
}

// Start 启动性能优化器
func (o *Optimizer) Start(ctx context.Context) error {
	if !o.config.Enabled {
		o.logger.Info("性能优化器已禁用")
		return nil
	}

	o.running = true
	o.logger.Info("启动性能优化器", 
		"monitor_interval", o.config.MonitorInterval,
		"gc_enabled", o.config.GCConfig.Enabled,
		"memory_enabled", o.config.MemoryConfig.Enabled)

	// 启动监控协程
	o.wg.Add(1)
	go o.monitorLoop(ctx)

	// 启动GC优化协程
	if o.config.GCConfig.Enabled {
		o.wg.Add(1)
		go o.gcOptimizeLoop(ctx)
	}

	// 启动内存优化协程
	if o.config.MemoryConfig.Enabled {
		o.wg.Add(1)
		go o.memoryOptimizeLoop(ctx)
	}

	return nil
}

// Stop 停止性能优化器
func (o *Optimizer) Stop() error {
	if !o.running {
		return nil
	}

	o.logger.Info("停止性能优化器")
	o.running = false
	close(o.stopCh)
	o.wg.Wait()

	return nil
}

// GetStats 获取性能统计
func (o *Optimizer) GetStats() *PerformanceStats {
	o.statsMux.RLock()
	defer o.statsMux.RUnlock()
	
	// 返回统计数据的副本
	stats := *o.stats
	return &stats
}

// monitorLoop 监控循环
func (o *Optimizer) monitorLoop(ctx context.Context) {
	defer o.wg.Done()
	
	ticker := time.NewTicker(o.config.MonitorInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-o.stopCh:
			return
		case <-ticker.C:
			o.collectStats()
			o.reportMetrics()
		}
	}
}

// collectStats 收集性能统计
func (o *Optimizer) collectStats() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	o.statsMux.Lock()
	defer o.statsMux.Unlock()

	// 更新统计数据
	o.stats.MemoryUsage = int64(m.Alloc)
	o.stats.GoroutineCount = runtime.NumGoroutine()
	o.stats.GCCount = m.NumGC
	o.stats.GCPauseTime = float64(m.PauseNs[(m.NumGC+255)%256]) / 1e6 // 转换为毫秒
	o.stats.Timestamp = time.Now()

	o.logger.Debug("性能统计更新",
		"memory_usage_mb", o.stats.MemoryUsage/1024/1024,
		"goroutine_count", o.stats.GoroutineCount,
		"gc_count", o.stats.GCCount,
		"gc_pause_ms", o.stats.GCPauseTime)
}

// reportMetrics 报告指标
func (o *Optimizer) reportMetrics() {
	stats := o.GetStats()

	// 记录性能统计到日志（简化实现）
	o.logger.Debug("性能指标报告",
		"memory_usage_bytes", stats.MemoryUsage,
		"goroutine_count", stats.GoroutineCount,
		"gc_count", stats.GCCount,
		"gc_pause_time_ms", stats.GCPauseTime)
}

// gcOptimizeLoop GC优化循环
func (o *Optimizer) gcOptimizeLoop(ctx context.Context) {
	defer o.wg.Done()
	
	// 设置GC目标百分比（Go 1.19+中已移除SetGCPercent，使用环境变量GOGC）
	if o.config.GCConfig.TargetPercent > 0 {
		o.logger.Info("GC目标百分比配置", "percent", o.config.GCConfig.TargetPercent)
	}

	if o.config.GCConfig.ForceInterval <= 0 {
		return
	}

	ticker := time.NewTicker(o.config.GCConfig.ForceInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-o.stopCh:
			return
		case <-ticker.C:
			o.forceGC()
		}
	}
}

// forceGC 强制执行GC
func (o *Optimizer) forceGC() {
	start := time.Now()
	runtime.GC()
	duration := time.Since(start)
	
	o.logger.Debug("强制GC执行完成", "duration_ms", duration.Milliseconds())
}

// memoryOptimizeLoop 内存优化循环
func (o *Optimizer) memoryOptimizeLoop(ctx context.Context) {
	defer o.wg.Done()
	
	ticker := time.NewTicker(o.config.MemoryConfig.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-o.stopCh:
			return
		case <-ticker.C:
			o.checkMemoryUsage()
		}
	}
}

// checkMemoryUsage 检查内存使用情况
func (o *Optimizer) checkMemoryUsage() {
	stats := o.GetStats()
	thresholdBytes := o.config.MemoryConfig.ThresholdMB * 1024 * 1024
	
	if stats.MemoryUsage > thresholdBytes {
		o.logger.Warn("内存使用超过阈值，执行清理",
			"current_mb", stats.MemoryUsage/1024/1024,
			"threshold_mb", o.config.MemoryConfig.ThresholdMB)
		
		// 执行内存清理
		o.cleanupMemory()
		
		// 记录内存清理事件
		o.logger.Info("内存清理事件", "reason", "threshold_exceeded")
	}
}

// cleanupMemory 清理内存
func (o *Optimizer) cleanupMemory() {
	// 强制GC
	runtime.GC()

	o.logger.Info("内存清理完成")
}

// DefaultOptimizerConfig 默认优化器配置
func DefaultOptimizerConfig() OptimizerConfig {
	return OptimizerConfig{
		Enabled:         true,
		MonitorInterval: 30 * time.Second,
		GCConfig: GCConfig{
			Enabled:       true,
			TargetPercent: 100, // 默认GC目标
			ForceInterval: 5 * time.Minute,
		},
		MemoryConfig: MemoryConfig{
			Enabled:         true,
			ThresholdMB:     512, // 512MB阈值
			CleanupInterval: 2 * time.Minute,
		},
		PoolConfig: PoolConfig{
			Enabled:         true,
			MaxIdleConns:    100,
			MaxOpenConns:    1000,
			ConnMaxLifetime: 30 * time.Minute,
		},
	}
}
