package transform

import (
	"context"
	"fmt"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// Manager 转换管理器实现
type Manager struct {
	config config.TransformConfig
	logger *telemetry.Logger
	
	// 转换器注册表
	transformers map[string]Transformer
	transformersMux sync.RWMutex
	
	// 转换链
	chains map[string]*TransformChain
	chainsMux sync.RWMutex
	
	// 事件监听器
	listeners []TransformEventListener
	listenersMux sync.RWMutex
	
	// 统计信息
	stats *TransformStats
	statsMux sync.RWMutex
	
	// 生命周期
	running   bool
	startTime time.Time
}

// NewManager 创建新的转换管理器
func NewManager(cfg config.TransformConfig, logger *telemetry.Logger) (*Manager, error) {
	manager := &Manager{
		config:       cfg,
		logger:       logger.With("component", "transform-manager"),
		transformers: make(map[string]Transformer),
		chains:       make(map[string]*TransformChain),
		listeners:    make([]TransformEventListener, 0),
		stats: &TransformStats{
			TransformerStats: make(map[string]*TransformerStats),
			ErrorStats:       make(map[string]int64),
		},
	}
	
	// 注册内置转换器
	if err := manager.registerBuiltinTransformers(); err != nil {
		return nil, fmt.Errorf("注册内置转换器失败: %w", err)
	}
	
	manager.logger.Info("转换管理器初始化完成",
		"enabled", cfg.Enabled,
		"transformers_count", len(manager.transformers))
	
	return manager, nil
}

// Start 启动转换管理器
func (m *Manager) Start() error {
	if !m.config.Enabled {
		m.logger.Info("转换功能未启用")
		return nil
	}
	
	if m.running {
		return nil
	}
	
	m.logger.Info("启动转换管理器")
	
	m.running = true
	m.startTime = time.Now()
	
	m.logger.Info("转换管理器启动完成")
	
	return nil
}

// Stop 停止转换管理器
func (m *Manager) Stop() error {
	if !m.running {
		return nil
	}
	
	m.logger.Info("停止转换管理器")
	
	m.running = false
	
	m.logger.Info("转换管理器已停止")
	
	return nil
}

// RegisterTransformer 注册转换器
func (m *Manager) RegisterTransformer(name string, transformer Transformer) error {
	if name == "" {
		return fmt.Errorf("转换器名称不能为空")
	}
	
	if transformer == nil {
		return fmt.Errorf("转换器不能为nil")
	}
	
	m.transformersMux.Lock()
	defer m.transformersMux.Unlock()
	
	if _, exists := m.transformers[name]; exists {
		return fmt.Errorf("转换器 %s 已存在", name)
	}
	
	m.transformers[name] = transformer
	
	// 初始化统计信息
	m.statsMux.Lock()
	m.stats.TransformerStats[name] = &TransformerStats{
		Name: name,
	}
	m.statsMux.Unlock()
	
	m.logger.Info("注册转换器", "name", name, "type", transformer.Name())
	
	return nil
}

// GetTransformer 获取转换器
func (m *Manager) GetTransformer(name string) (Transformer, error) {
	m.transformersMux.RLock()
	defer m.transformersMux.RUnlock()
	
	transformer, exists := m.transformers[name]
	if !exists {
		return nil, fmt.Errorf("转换器 %s 不存在", name)
	}
	
	return transformer, nil
}

// ListTransformers 列出所有转换器
func (m *Manager) ListTransformers() []string {
	m.transformersMux.RLock()
	defer m.transformersMux.RUnlock()
	
	names := make([]string, 0, len(m.transformers))
	for name := range m.transformers {
		names = append(names, name)
	}
	
	return names
}

// ExecuteChain 执行转换链
func (m *Manager) ExecuteChain(ctx context.Context, chain []string, request *TransformRequest) (*TransformResponse, error) {
	if !m.config.Enabled {
		return &TransformResponse{
			StatusCode:  200,
			Headers:     request.Headers,
			Body:        request.Body,
			ContentType: request.ContentType,
		}, nil
	}
	
	start := time.Now()
	
	// 发送开始事件
	m.emitEvent(&TransformEvent{
		Type:      TransformEventTypeStart,
		RequestID: request.RequestID,
		Timestamp: start,
	})
	
	currentRequest := request
	var currentResponse *TransformResponse
	
	// 执行转换链
	for i, transformerName := range chain {
		transformer, err := m.GetTransformer(transformerName)
		if err != nil {
			m.recordError(transformerName, err)
			m.emitEvent(&TransformEvent{
				Type:        TransformEventTypeError,
				RequestID:   request.RequestID,
				Transformer: transformerName,
				Error:       err,
				Timestamp:   time.Now(),
			})
			return nil, fmt.Errorf("获取转换器 %s 失败: %w", transformerName, err)
		}
		
		stepStart := time.Now()
		
		if i == 0 {
			// 第一个转换器处理请求
			currentResponse, err = transformer.TransformRequest(ctx, currentRequest)
		} else {
			// 后续转换器处理响应
			currentResponse, err = transformer.TransformResponse(ctx, currentRequest, currentResponse)
		}
		
		stepDuration := time.Since(stepStart)
		
		if err != nil {
			m.recordError(transformerName, err)
			m.emitEvent(&TransformEvent{
				Type:        TransformEventTypeError,
				RequestID:   request.RequestID,
				Transformer: transformerName,
				Duration:    stepDuration,
				Error:       err,
				Timestamp:   time.Now(),
			})
			return nil, fmt.Errorf("转换器 %s 执行失败: %w", transformerName, err)
		}
		
		// 记录成功统计
		m.recordSuccess(transformerName, stepDuration)
		
		m.emitEvent(&TransformEvent{
			Type:        TransformEventTypeComplete,
			RequestID:   request.RequestID,
			Transformer: transformerName,
			Duration:    stepDuration,
			Success:     true,
			Timestamp:   time.Now(),
		})
	}
	
	// 更新全局统计
	totalDuration := time.Since(start)
	m.updateGlobalStats(totalDuration, true)
	
	if currentResponse != nil {
		currentResponse.Duration = totalDuration
	}
	
	return currentResponse, nil
}

// HealthCheck 健康检查
func (m *Manager) HealthCheck() error {
	if !m.running {
		return fmt.Errorf("转换管理器未运行")
	}
	
	// 检查所有转换器
	m.transformersMux.RLock()
	defer m.transformersMux.RUnlock()
	
	for name, transformer := range m.transformers {
		// 这里可以添加转换器特定的健康检查
		if transformer == nil {
			return fmt.Errorf("转换器 %s 为nil", name)
		}
	}
	
	return nil
}

// GetStats 获取统计信息
func (m *Manager) GetStats() (*TransformStats, error) {
	m.statsMux.RLock()
	defer m.statsMux.RUnlock()
	
	// 创建统计信息副本
	stats := &TransformStats{
		TotalTransforms:      m.stats.TotalTransforms,
		SuccessfulTransforms: m.stats.SuccessfulTransforms,
		FailedTransforms:     m.stats.FailedTransforms,
		AverageTransformTime: m.stats.AverageTransformTime,
		TransformerStats:     make(map[string]*TransformerStats),
		ErrorStats:           make(map[string]int64),
	}
	
	// 复制转换器统计
	for name, transformerStats := range m.stats.TransformerStats {
		stats.TransformerStats[name] = &TransformerStats{
			Name:              transformerStats.Name,
			TotalTransforms:   transformerStats.TotalTransforms,
			SuccessTransforms: transformerStats.SuccessTransforms,
			FailedTransforms:  transformerStats.FailedTransforms,
			AverageTime:       transformerStats.AverageTime,
			LastUsed:          transformerStats.LastUsed,
		}
	}
	
	// 复制错误统计
	for errorType, count := range m.stats.ErrorStats {
		stats.ErrorStats[errorType] = count
	}
	
	return stats, nil
}

// AddEventListener 添加事件监听器
func (m *Manager) AddEventListener(listener TransformEventListener) {
	m.listenersMux.Lock()
	defer m.listenersMux.Unlock()
	
	m.listeners = append(m.listeners, listener)
}

// RemoveEventListener 移除事件监听器
func (m *Manager) RemoveEventListener(listener TransformEventListener) {
	m.listenersMux.Lock()
	defer m.listenersMux.Unlock()
	
	for i, l := range m.listeners {
		if l == listener {
			m.listeners = append(m.listeners[:i], m.listeners[i+1:]...)
			break
		}
	}
}

// 私有方法

// registerBuiltinTransformers 注册内置转换器
func (m *Manager) registerBuiltinTransformers() error {
	// 注册JSON转换器
	if m.config.Request.Body.JSON.Add != nil || m.config.Response.Body.JSON.Add != nil {
		jsonTransformer := NewJSONTransformer(m.config.Request.Body.JSON, m.logger)
		if err := m.RegisterTransformer("json", jsonTransformer); err != nil {
			return err
		}
	}
	
	// 注册协议转换器
	if m.config.Protocol.Enabled {
		protocolTransformer := NewProtocolTransformer(m.config.Protocol, m.logger)
		if err := m.RegisterTransformer("protocol", protocolTransformer); err != nil {
			return err
		}
	}
	
	// 注册GraphQL转换器
	if m.config.GraphQL.Enabled {
		graphqlTransformer := NewGraphQLTransformer(m.config.GraphQL, m.logger)
		if err := m.RegisterTransformer("graphql", graphqlTransformer); err != nil {
			return err
		}
	}
	
	// 注册格式转换器
	if m.config.Format.Enabled {
		formatTransformer := NewFormatTransformer(m.config.Format, m.logger)
		if err := m.RegisterTransformer("format", formatTransformer); err != nil {
			return err
		}
	}
	
	return nil
}

// emitEvent 发送事件
func (m *Manager) emitEvent(event *TransformEvent) {
	m.listenersMux.RLock()
	listeners := make([]TransformEventListener, len(m.listeners))
	copy(listeners, m.listeners)
	m.listenersMux.RUnlock()
	
	if len(listeners) == 0 {
		return
	}
	
	// 异步发送事件
	go func() {
		for _, listener := range listeners {
			// 检查监听器是否关心这种事件类型
			eventTypes := listener.GetEventTypes()
			if len(eventTypes) > 0 {
				found := false
				for _, et := range eventTypes {
					if et == event.Type {
						found = true
						break
					}
				}
				if !found {
					continue
				}
			}
			
			if err := listener.OnTransformEvent(event); err != nil {
				m.logger.Error("转换事件处理失败", "event_type", event.Type, "error", err)
			}
		}
	}()
}

// recordSuccess 记录成功统计
func (m *Manager) recordSuccess(transformerName string, duration time.Duration) {
	m.statsMux.Lock()
	defer m.statsMux.Unlock()
	
	if stats, exists := m.stats.TransformerStats[transformerName]; exists {
		stats.TotalTransforms++
		stats.SuccessTransforms++
		stats.LastUsed = time.Now()
		
		// 更新平均时间
		if stats.AverageTime == 0 {
			stats.AverageTime = duration
		} else {
			stats.AverageTime = (stats.AverageTime + duration) / 2
		}
	}
}

// recordError 记录错误统计
func (m *Manager) recordError(transformerName string, err error) {
	m.statsMux.Lock()
	defer m.statsMux.Unlock()
	
	if stats, exists := m.stats.TransformerStats[transformerName]; exists {
		stats.TotalTransforms++
		stats.FailedTransforms++
		stats.LastUsed = time.Now()
	}
	
	// 记录错误类型统计
	errorType := fmt.Sprintf("%T", err)
	m.stats.ErrorStats[errorType]++
}

// updateGlobalStats 更新全局统计
func (m *Manager) updateGlobalStats(duration time.Duration, success bool) {
	m.statsMux.Lock()
	defer m.statsMux.Unlock()
	
	m.stats.TotalTransforms++
	
	if success {
		m.stats.SuccessfulTransforms++
	} else {
		m.stats.FailedTransforms++
	}
	
	// 更新平均时间
	if m.stats.AverageTransformTime == 0 {
		m.stats.AverageTransformTime = duration
	} else {
		m.stats.AverageTransformTime = (m.stats.AverageTransformTime + duration) / 2
	}
}
