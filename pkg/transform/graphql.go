package transform

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/graphql-go/graphql"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// GraphQLTransformerImpl GraphQL转换器实现
type GraphQLTransformerImpl struct {
	config config.GraphQLTransformConfig
	logger *telemetry.Logger
	schema *graphql.Schema
	
	// 统计信息
	stats *TransformerStats
}

// NewGraphQLTransformer 创建新的GraphQL转换器
func NewGraphQLTransformer(cfg config.GraphQLTransformConfig, logger *telemetry.Logger) *GraphQLTransformerImpl {
	return &GraphQLTransformerImpl{
		config: cfg,
		logger: logger.With("component", "graphql-transformer"),
		stats: &TransformerStats{
			Name: "graphql",
		},
	}
}

// Name 获取转换器名称
func (gt *GraphQLTransformerImpl) Name() string {
	return "graphql"
}

// Version 获取转换器版本
func (gt *GraphQLTransformerImpl) Version() string {
	return "1.0.0"
}

// SupportedContentTypes 获取支持的内容类型
func (gt *GraphQLTransformerImpl) SupportedContentTypes() []string {
	return []string{
		"application/json",
		"application/graphql",
		"text/plain",
	}
}

// ValidateConfig 验证配置
func (gt *GraphQLTransformerImpl) ValidateConfig(cfg interface{}) error {
	_, ok := cfg.(config.GraphQLTransformConfig)
	if !ok {
		return fmt.Errorf("无效的GraphQL转换器配置类型")
	}
	return nil
}

// Configure 配置转换器
func (gt *GraphQLTransformerImpl) Configure(cfg interface{}) error {
	graphqlConfig, ok := cfg.(config.GraphQLTransformConfig)
	if !ok {
		return fmt.Errorf("无效的GraphQL转换器配置类型")
	}

	gt.config = graphqlConfig
	
	// 加载GraphQL Schema（如果配置了）
	if graphqlConfig.SchemaPath != "" {
		if err := gt.loadSchema(graphqlConfig.SchemaPath); err != nil {
			gt.logger.Error("加载GraphQL Schema失败", "error", err)
		}
	}
	
	gt.logger.Info("GraphQL转换器配置已更新")
	
	return nil
}

// TransformRequest 转换请求
func (gt *GraphQLTransformerImpl) TransformRequest(ctx context.Context, request *TransformRequest) (*TransformResponse, error) {
	start := time.Now()
	defer func() {
		gt.updateStats(time.Since(start), true)
	}()
	
	// 检查是否需要REST到GraphQL转换
	if gt.config.RESTToGraphQL.QueryMappings != nil || gt.config.RESTToGraphQL.MutationMappings != nil {
		return gt.transformRESTToGraphQL(ctx, request)
	}
	
	// 默认不转换
	return &TransformResponse{
		StatusCode:  200,
		Headers:     request.Headers,
		Body:        request.Body,
		ContentType: request.ContentType,
		Duration:    time.Since(start),
	}, nil
}

// TransformResponse 转换响应
func (gt *GraphQLTransformerImpl) TransformResponse(ctx context.Context, request *TransformRequest, response *TransformResponse) (*TransformResponse, error) {
	start := time.Now()
	defer func() {
		gt.updateStats(time.Since(start), true)
	}()
	
	// 检查是否需要GraphQL到REST转换
	if gt.config.GraphQLToREST.QueryRules != nil {
		return gt.transformGraphQLToREST(ctx, request, response)
	}
	
	return response, nil
}

// RESTToGraphQL REST到GraphQL转换
func (gt *GraphQLTransformerImpl) RESTToGraphQL(ctx context.Context, request *TransformRequest) (*GraphQLRequest, error) {
	// 查找匹配的查询映射
	for _, mapping := range gt.config.RESTToGraphQL.QueryMappings {
		if gt.matchesRESTPath(request.Path, mapping.RESTPath) {
			return gt.buildGraphQLQuery(request, mapping)
		}
	}
	
	// 查找匹配的变更映射
	for _, mapping := range gt.config.RESTToGraphQL.MutationMappings {
		if gt.matchesRESTPath(request.Path, mapping.RESTPath) && 
		   gt.matchesHTTPMethod(request.Method, mapping.HTTPMethod) {
			return gt.buildGraphQLMutation(request, mapping)
		}
	}
	
	return nil, fmt.Errorf("未找到匹配的GraphQL映射")
}

// GraphQLToREST GraphQL到REST转换
func (gt *GraphQLTransformerImpl) GraphQLToREST(ctx context.Context, response *GraphQLResponse) (*TransformResponse, error) {
	restResponse := &TransformResponse{
		StatusCode:  200,
		Headers:     map[string]string{"Content-Type": "application/json"},
		ContentType: "application/json",
	}
	
	// 处理GraphQL错误
	if len(response.Errors) > 0 {
		restResponse.StatusCode = 400
		errorResponse := map[string]interface{}{
			"errors": response.Errors,
		}
		
		body, err := json.Marshal(errorResponse)
		if err != nil {
			return nil, fmt.Errorf("序列化GraphQL错误失败: %w", err)
		}
		
		restResponse.Body = body
		restResponse.Size = int64(len(body))
		return restResponse, nil
	}
	
	// 处理成功响应
	body, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("序列化GraphQL响应失败: %w", err)
	}
	
	restResponse.Body = body
	restResponse.Size = int64(len(body))
	
	return restResponse, nil
}

// ParseQuery 解析GraphQL查询
func (gt *GraphQLTransformerImpl) ParseQuery(query string) (*GraphQLQuery, error) {
	// 简化的GraphQL查询解析
	parsedQuery := &GraphQLQuery{
		Variables: make(map[string]interface{}),
		Fields:    make([]GraphQLField, 0),
	}
	
	// 检测查询类型
	if strings.Contains(query, "mutation") {
		parsedQuery.Type = "mutation"
	} else if strings.Contains(query, "subscription") {
		parsedQuery.Type = "subscription"
	} else {
		parsedQuery.Type = "query"
	}
	
	// 提取操作名称
	nameRegex := regexp.MustCompile(`(?:query|mutation|subscription)\s+(\w+)`)
	if matches := nameRegex.FindStringSubmatch(query); len(matches) > 1 {
		parsedQuery.Name = matches[1]
	}
	
	// 这里应该实现更完整的GraphQL解析逻辑
	// 可以使用专门的GraphQL解析库
	
	return parsedQuery, nil
}

// ValidateQuery 验证GraphQL查询
func (gt *GraphQLTransformerImpl) ValidateQuery(query string, schema string) error {
	if gt.schema == nil {
		return fmt.Errorf("GraphQL Schema未加载")
	}
	
	// 使用graphql-go库验证查询
	result := graphql.Do(graphql.Params{
		Schema:        *gt.schema,
		RequestString: query,
	})
	
	if len(result.Errors) > 0 {
		return fmt.Errorf("GraphQL查询验证失败: %v", result.Errors)
	}
	
	return nil
}

// 私有方法

// transformRESTToGraphQL 转换REST到GraphQL
func (gt *GraphQLTransformerImpl) transformRESTToGraphQL(ctx context.Context, request *TransformRequest) (*TransformResponse, error) {
	graphqlRequest, err := gt.RESTToGraphQL(ctx, request)
	if err != nil {
		return nil, err
	}
	
	// 执行GraphQL查询（这里是模拟实现）
	response := &TransformResponse{
		StatusCode:  200,
		Headers:     map[string]string{"Content-Type": "application/json"},
		ContentType: "application/json",
		Metadata: map[string]interface{}{
			"graphql_query": graphqlRequest.Query,
			"graphql_variables": graphqlRequest.Variables,
		},
	}
	
	// 序列化GraphQL请求作为响应体（演示用）
	body, err := json.Marshal(graphqlRequest)
	if err != nil {
		return nil, fmt.Errorf("序列化GraphQL请求失败: %w", err)
	}
	
	response.Body = body
	response.Size = int64(len(body))
	
	return response, nil
}

// transformGraphQLToREST 转换GraphQL到REST
func (gt *GraphQLTransformerImpl) transformGraphQLToREST(ctx context.Context, request *TransformRequest, response *TransformResponse) (*TransformResponse, error) {
	// 模拟GraphQL响应
	graphqlResponse := &GraphQLResponse{
		Data: map[string]interface{}{
			"result": "success",
			"data":   json.RawMessage(response.Body),
		},
	}
	
	return gt.GraphQLToREST(ctx, graphqlResponse)
}

// buildGraphQLQuery 构建GraphQL查询
func (gt *GraphQLTransformerImpl) buildGraphQLQuery(request *TransformRequest, mapping config.GraphQLQueryMapping) (*GraphQLRequest, error) {
	graphqlRequest := &GraphQLRequest{
		Query:     mapping.GraphQLQuery,
		Variables: make(map[string]interface{}),
	}
	
	// 应用变量映射
	pathParams := gt.extractPathParameters(request.Path, mapping.RESTPath)
	
	for restParam, graphqlVar := range mapping.VariableMapping {
		if value, exists := pathParams[restParam]; exists {
			graphqlRequest.Variables[graphqlVar] = value
		} else if value, exists := request.Query[restParam]; exists {
			graphqlRequest.Variables[graphqlVar] = value
		}
	}
	
	return graphqlRequest, nil
}

// buildGraphQLMutation 构建GraphQL变更
func (gt *GraphQLTransformerImpl) buildGraphQLMutation(request *TransformRequest, mapping config.GraphQLMutationMapping) (*GraphQLRequest, error) {
	graphqlRequest := &GraphQLRequest{
		Query:     mapping.GraphQLMutation,
		Variables: make(map[string]interface{}),
	}
	
	// 应用变量映射
	pathParams := gt.extractPathParameters(request.Path, mapping.RESTPath)
	
	for restParam, graphqlVar := range mapping.VariableMapping {
		if value, exists := pathParams[restParam]; exists {
			graphqlRequest.Variables[graphqlVar] = value
		} else if value, exists := request.Query[restParam]; exists {
			graphqlRequest.Variables[graphqlVar] = value
		}
	}
	
	// 处理请求体
	if len(request.Body) > 0 {
		var bodyData map[string]interface{}
		if err := json.Unmarshal(request.Body, &bodyData); err == nil {
			for k, v := range bodyData {
				if graphqlVar, exists := mapping.VariableMapping[k]; exists {
					graphqlRequest.Variables[graphqlVar] = v
				} else {
					graphqlRequest.Variables[k] = v
				}
			}
		}
	}
	
	return graphqlRequest, nil
}

// matchesRESTPath 检查REST路径是否匹配
func (gt *GraphQLTransformerImpl) matchesRESTPath(path, pattern string) bool {
	if pattern == "*" {
		return true
	}
	
	// 支持路径参数
	if strings.Contains(pattern, "{") {
		regex := gt.convertPathToRegex(pattern)
		matched, _ := regexp.MatchString(regex, path)
		return matched
	}
	
	return path == pattern
}

// matchesHTTPMethod 检查HTTP方法是否匹配
func (gt *GraphQLTransformerImpl) matchesHTTPMethod(method, pattern string) bool {
	return strings.EqualFold(method, pattern) || pattern == "*"
}

// convertPathToRegex 将路径模式转换为正则表达式
func (gt *GraphQLTransformerImpl) convertPathToRegex(pattern string) string {
	regex := regexp.MustCompile(`\{[^}]+\}`).ReplaceAllString(pattern, `([^/]+)`)
	return "^" + regex + "$"
}

// extractPathParameters 提取路径参数
func (gt *GraphQLTransformerImpl) extractPathParameters(path, pattern string) map[string]string {
	params := make(map[string]string)
	
	if !strings.Contains(pattern, "{") {
		return params
	}
	
	paramNames := regexp.MustCompile(`\{([^}]+)\}`).FindAllStringSubmatch(pattern, -1)
	regex := gt.convertPathToRegex(pattern)
	
	re := regexp.MustCompile(regex)
	matches := re.FindStringSubmatch(path)
	
	if len(matches) > 1 && len(paramNames) == len(matches)-1 {
		for i, paramName := range paramNames {
			if len(paramName) > 1 {
				params[paramName[1]] = matches[i+1]
			}
		}
	}
	
	return params
}

// loadSchema 加载GraphQL Schema
func (gt *GraphQLTransformerImpl) loadSchema(schemaPath string) error {
	// 这里应该从文件加载Schema
	// 为了演示，我们创建一个简单的Schema
	
	userType := graphql.NewObject(graphql.ObjectConfig{
		Name: "User",
		Fields: graphql.Fields{
			"id":   &graphql.Field{Type: graphql.String},
			"name": &graphql.Field{Type: graphql.String},
		},
	})
	
	queryType := graphql.NewObject(graphql.ObjectConfig{
		Name: "Query",
		Fields: graphql.Fields{
			"user": &graphql.Field{
				Type: userType,
				Args: graphql.FieldConfigArgument{
					"id": &graphql.ArgumentConfig{Type: graphql.String},
				},
			},
		},
	})
	
	schema, err := graphql.NewSchema(graphql.SchemaConfig{
		Query: queryType,
	})
	
	if err != nil {
		return fmt.Errorf("创建GraphQL Schema失败: %w", err)
	}
	
	gt.schema = &schema
	return nil
}

// updateStats 更新统计信息
func (gt *GraphQLTransformerImpl) updateStats(duration time.Duration, success bool) {
	gt.stats.TotalTransforms++
	gt.stats.LastUsed = time.Now()
	
	if success {
		gt.stats.SuccessTransforms++
	} else {
		gt.stats.FailedTransforms++
	}
	
	// 更新平均时间
	if gt.stats.AverageTime == 0 {
		gt.stats.AverageTime = duration
	} else {
		gt.stats.AverageTime = (gt.stats.AverageTime + duration) / 2
	}
}
