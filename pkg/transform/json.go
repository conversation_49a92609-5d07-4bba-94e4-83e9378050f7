package transform

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// JSONTransformerImpl JSON转换器实现
type JSONTransformerImpl struct {
	config config.JSONTransformConfig
	logger *telemetry.Logger
	
	// 统计信息
	stats *TransformerStats
}

// NewJSONTransformer 创建新的JSON转换器
func NewJSONTransformer(cfg config.JSONTransformConfig, logger *telemetry.Logger) *JSONTransformerImpl {
	return &JSONTransformerImpl{
		config: cfg,
		logger: logger.With("component", "json-transformer"),
		stats: &TransformerStats{
			Name: "json",
		},
	}
}

// Name 获取转换器名称
func (jt *JSONTransformerImpl) Name() string {
	return "json"
}

// Version 获取转换器版本
func (jt *JSONTransformerImpl) Version() string {
	return "1.0.0"
}

// SupportedContentTypes 获取支持的内容类型
func (jt *JSONTransformerImpl) SupportedContentTypes() []string {
	return []string{
		"application/json",
		"application/json; charset=utf-8",
		"text/json",
	}
}

// ValidateConfig 验证配置
func (jt *JSONTransformerImpl) ValidateConfig(cfg interface{}) error {
	_, ok := cfg.(config.JSONTransformConfig)
	if !ok {
		return fmt.Errorf("无效的JSON转换器配置类型")
	}
	return nil
}

// Configure 配置转换器
func (jt *JSONTransformerImpl) Configure(cfg interface{}) error {
	jsonConfig, ok := cfg.(config.JSONTransformConfig)
	if !ok {
		return fmt.Errorf("无效的JSON转换器配置类型")
	}
	
	jt.config = jsonConfig
	jt.logger.Info("JSON转换器配置已更新")
	
	return nil
}

// TransformRequest 转换请求
func (jt *JSONTransformerImpl) TransformRequest(ctx context.Context, request *TransformRequest) (*TransformResponse, error) {
	start := time.Now()
	defer func() {
		jt.updateStats(time.Since(start), true)
	}()
	
	// 检查内容类型
	if !jt.isJSONContentType(request.ContentType) {
		return &TransformResponse{
			StatusCode:  200,
			Headers:     request.Headers,
			Body:        request.Body,
			ContentType: request.ContentType,
		}, nil
	}
	
	// 转换JSON请求体
	transformedBody, err := jt.transformJSONBody(request.Body, jt.config)
	if err != nil {
		jt.updateStats(time.Since(start), false)
		return nil, fmt.Errorf("JSON请求转换失败: %w", err)
	}
	
	return &TransformResponse{
		StatusCode:  200,
		Headers:     request.Headers,
		Body:        transformedBody,
		ContentType: request.ContentType,
		Duration:    time.Since(start),
		Size:        int64(len(transformedBody)),
	}, nil
}

// TransformResponse 转换响应
func (jt *JSONTransformerImpl) TransformResponse(ctx context.Context, request *TransformRequest, response *TransformResponse) (*TransformResponse, error) {
	start := time.Now()
	defer func() {
		jt.updateStats(time.Since(start), true)
	}()
	
	// 检查内容类型
	if !jt.isJSONContentType(response.ContentType) {
		return response, nil
	}
	
	// 转换JSON响应体
	transformedBody, err := jt.transformJSONBody(response.Body, jt.config)
	if err != nil {
		jt.updateStats(time.Since(start), false)
		return nil, fmt.Errorf("JSON响应转换失败: %w", err)
	}
	
	response.Body = transformedBody
	response.Size = int64(len(transformedBody))
	
	return response, nil
}

// GetValue 获取JSON值
func (jt *JSONTransformerImpl) GetValue(data []byte, path string) (interface{}, error) {
	result := gjson.GetBytes(data, path)
	if !result.Exists() {
		return nil, fmt.Errorf("路径 %s 不存在", path)
	}
	
	return result.Value(), nil
}

// SetValue 设置JSON值
func (jt *JSONTransformerImpl) SetValue(data []byte, path string, value interface{}) ([]byte, error) {
	result, err := sjson.SetBytes(data, path, value)
	if err != nil {
		return nil, fmt.Errorf("设置路径 %s 失败: %w", path, err)
	}
	
	return result, nil
}

// DeleteValue 删除JSON值
func (jt *JSONTransformerImpl) DeleteValue(data []byte, path string) ([]byte, error) {
	result, err := sjson.DeleteBytes(data, path)
	if err != nil {
		return nil, fmt.Errorf("删除路径 %s 失败: %w", path, err)
	}
	
	return result, nil
}

// Merge 合并JSON
func (jt *JSONTransformerImpl) Merge(data1, data2 []byte) ([]byte, error) {
	var obj1, obj2 map[string]interface{}
	
	if err := json.Unmarshal(data1, &obj1); err != nil {
		return nil, fmt.Errorf("解析第一个JSON失败: %w", err)
	}
	
	if err := json.Unmarshal(data2, &obj2); err != nil {
		return nil, fmt.Errorf("解析第二个JSON失败: %w", err)
	}
	
	// 合并对象
	merged := jt.mergeObjects(obj1, obj2)
	
	result, err := json.Marshal(merged)
	if err != nil {
		return nil, fmt.Errorf("序列化合并结果失败: %w", err)
	}
	
	return result, nil
}

// Validate 验证JSON
func (jt *JSONTransformerImpl) Validate(data []byte, schema []byte) error {
	// 简单的JSON格式验证
	var obj interface{}
	if err := json.Unmarshal(data, &obj); err != nil {
		return fmt.Errorf("无效的JSON格式: %w", err)
	}
	
	// 这里可以集成JSON Schema验证库
	// 例如：github.com/xeipuuv/gojsonschema
	
	return nil
}

// 私有方法

// isJSONContentType 检查是否为JSON内容类型
func (jt *JSONTransformerImpl) isJSONContentType(contentType string) bool {
	for _, supportedType := range jt.SupportedContentTypes() {
		if strings.Contains(strings.ToLower(contentType), strings.ToLower(supportedType)) {
			return true
		}
	}
	return false
}

// transformJSONBody 转换JSON请求体
func (jt *JSONTransformerImpl) transformJSONBody(body []byte, config config.JSONTransformConfig) ([]byte, error) {
	if len(body) == 0 {
		return body, nil
	}
	
	// 验证JSON格式
	if !gjson.ValidBytes(body) {
		return nil, fmt.Errorf("无效的JSON格式")
	}
	
	result := body
	var err error
	
	// 应用添加操作
	for key, value := range config.Add {
		result, err = sjson.SetBytes(result, key, value)
		if err != nil {
			return nil, fmt.Errorf("添加字段 %s 失败: %w", key, err)
		}
	}
	
	// 应用删除操作
	for _, path := range config.Remove {
		result, err = sjson.DeleteBytes(result, path)
		if err != nil {
			jt.logger.Warn("删除字段失败", "path", path, "error", err)
			// 继续处理其他操作
		}
	}
	
	// 应用重命名操作
	for oldPath, newPath := range config.Rename {
		// 获取旧值
		oldValue := gjson.GetBytes(result, oldPath)
		if oldValue.Exists() {
			// 设置新值
			result, err = sjson.SetBytes(result, newPath, oldValue.Value())
			if err != nil {
				return nil, fmt.Errorf("重命名字段 %s 到 %s 失败: %w", oldPath, newPath, err)
			}
			
			// 删除旧值
			result, err = sjson.DeleteBytes(result, oldPath)
			if err != nil {
				jt.logger.Warn("删除重命名的旧字段失败", "path", oldPath, "error", err)
			}
		}
	}
	
	// 应用替换操作
	for path, value := range config.Replace {
		// 只有当路径存在时才替换
		if gjson.GetBytes(result, path).Exists() {
			result, err = sjson.SetBytes(result, path, value)
			if err != nil {
				return nil, fmt.Errorf("替换字段 %s 失败: %w", path, err)
			}
		}
	}
	
	// 应用转换规则
	for _, rule := range config.Transform {
		result, err = jt.applyTransformRule(result, rule)
		if err != nil {
			return nil, fmt.Errorf("应用转换规则失败: %w", err)
		}
	}
	
	return result, nil
}

// applyTransformRule 应用转换规则
func (jt *JSONTransformerImpl) applyTransformRule(data []byte, rule config.JSONTransformRule) ([]byte, error) {
	// 检查条件（如果有）
	if !jt.evaluateCondition(data, rule.Condition) {
		return data, nil
	}
	
	switch rule.Operation {
	case "add":
		return sjson.SetBytes(data, rule.Path, rule.Value)
	case "remove":
		return sjson.DeleteBytes(data, rule.Path)
	case "replace":
		if gjson.GetBytes(data, rule.Path).Exists() {
			return sjson.SetBytes(data, rule.Path, rule.Value)
		}
		return data, nil
	case "rename":
		if newPath, ok := rule.Value.(string); ok {
			oldValue := gjson.GetBytes(data, rule.Path)
			if oldValue.Exists() {
				result, err := sjson.SetBytes(data, newPath, oldValue.Value())
				if err != nil {
					return nil, err
				}
				return sjson.DeleteBytes(result, rule.Path)
			}
		}
		return data, nil
	case "transform":
		// 这里可以实现更复杂的转换逻辑
		// 例如：应用表达式、函数等
		return data, nil
	default:
		return data, fmt.Errorf("不支持的操作类型: %s", rule.Operation)
	}
}

// evaluateCondition 评估条件
func (jt *JSONTransformerImpl) evaluateCondition(data []byte, condition config.TransformCondition) bool {
	if condition.Type == "" {
		return true // 没有条件，总是为真
	}
	
	// 简化的条件评估实现
	switch condition.Type {
	case "path":
		value := gjson.GetBytes(data, condition.Key)
		return jt.compareValues(value.Value(), condition.Value, condition.Operator)
	case "exists":
		return gjson.GetBytes(data, condition.Key).Exists()
	default:
		return true
	}
}

// compareValues 比较值
func (jt *JSONTransformerImpl) compareValues(actual, expected interface{}, operator string) bool {
	switch operator {
	case "eq", "":
		return actual == expected
	case "ne":
		return actual != expected
	case "contains":
		if actualStr, ok := actual.(string); ok {
			if expectedStr, ok := expected.(string); ok {
				return strings.Contains(actualStr, expectedStr)
			}
		}
		return false
	default:
		return false
	}
}

// mergeObjects 合并对象
func (jt *JSONTransformerImpl) mergeObjects(obj1, obj2 map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	
	// 复制第一个对象
	for k, v := range obj1 {
		result[k] = v
	}
	
	// 合并第二个对象
	for k, v := range obj2 {
		if existing, exists := result[k]; exists {
			// 如果两个值都是对象，递归合并
			if existingMap, ok := existing.(map[string]interface{}); ok {
				if vMap, ok := v.(map[string]interface{}); ok {
					result[k] = jt.mergeObjects(existingMap, vMap)
					continue
				}
			}
		}
		result[k] = v
	}
	
	return result
}

// updateStats 更新统计信息
func (jt *JSONTransformerImpl) updateStats(duration time.Duration, success bool) {
	jt.stats.TotalTransforms++
	jt.stats.LastUsed = time.Now()
	
	if success {
		jt.stats.SuccessTransforms++
	} else {
		jt.stats.FailedTransforms++
	}
	
	// 更新平均时间
	if jt.stats.AverageTime == 0 {
		jt.stats.AverageTime = duration
	} else {
		jt.stats.AverageTime = (jt.stats.AverageTime + duration) / 2
	}
}
