package transform

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// ProtocolTransformerImpl 协议转换器实现
type ProtocolTransformerImpl struct {
	config config.ProtocolTransformConfig
	logger *telemetry.Logger
	
	// 统计信息
	stats *TransformerStats
}

// NewProtocolTransformer 创建新的协议转换器
func NewProtocolTransformer(cfg config.ProtocolTransformConfig, logger *telemetry.Logger) *ProtocolTransformerImpl {
	return &ProtocolTransformerImpl{
		config: cfg,
		logger: logger.With("component", "protocol-transformer"),
		stats: &TransformerStats{
			Name: "protocol",
		},
	}
}

// Name 获取转换器名称
func (pt *ProtocolTransformerImpl) Name() string {
	return "protocol"
}

// Version 获取转换器版本
func (pt *ProtocolTransformerImpl) Version() string {
	return "1.0.0"
}

// SupportedContentTypes 获取支持的内容类型
func (pt *ProtocolTransformerImpl) SupportedContentTypes() []string {
	return []string{
		"application/json",
		"application/grpc",
		"application/x-protobuf",
		"text/plain",
	}
}

// ValidateConfig 验证配置
func (pt *ProtocolTransformerImpl) ValidateConfig(cfg interface{}) error {
	_, ok := cfg.(config.ProtocolTransformConfig)
	if !ok {
		return fmt.Errorf("无效的协议转换器配置类型")
	}
	return nil
}

// Configure 配置转换器
func (pt *ProtocolTransformerImpl) Configure(cfg interface{}) error {
	protocolConfig, ok := cfg.(config.ProtocolTransformConfig)
	if !ok {
		return fmt.Errorf("无效的协议转换器配置类型")
	}

	pt.config = protocolConfig
	pt.logger.Info("协议转换器配置已更新")
	
	return nil
}

// TransformRequest 转换请求
func (pt *ProtocolTransformerImpl) TransformRequest(ctx context.Context, request *TransformRequest) (*TransformResponse, error) {
	start := time.Now()
	defer func() {
		pt.updateStats(time.Since(start), true)
	}()
	
	// 根据配置决定转换类型
	if pt.config.RESTToGRPC.Enabled {
		return pt.transformRESTToGRPC(ctx, request)
	}
	
	// 默认不转换
	return &TransformResponse{
		StatusCode:  200,
		Headers:     request.Headers,
		Body:        request.Body,
		ContentType: request.ContentType,
		Duration:    time.Since(start),
	}, nil
}

// TransformResponse 转换响应
func (pt *ProtocolTransformerImpl) TransformResponse(ctx context.Context, request *TransformRequest, response *TransformResponse) (*TransformResponse, error) {
	start := time.Now()
	defer func() {
		pt.updateStats(time.Since(start), true)
	}()
	
	// 根据配置决定转换类型
	if pt.config.GRPCToREST.Enabled {
		return pt.transformGRPCToREST(ctx, request, response)
	}
	
	return response, nil
}

// RESTToGRPC REST到gRPC转换
func (pt *ProtocolTransformerImpl) RESTToGRPC(ctx context.Context, request *TransformRequest) (*GRPCRequest, error) {
	// 查找匹配的服务配置
	service := pt.findMatchingRESTToGRPCService(request.Path, request.Method)
	if service == nil {
		return nil, fmt.Errorf("未找到匹配的gRPC服务配置")
	}
	
	// 构建gRPC请求
	grpcRequest := &GRPCRequest{
		Service: service.GRPCService,
		Method:  service.GRPCMethod,
		Headers: make(map[string]string),
		Timeout: 30 * time.Second,
	}
	
	// 解析超时
	if pt.config.RESTToGRPC.DefaultTimeout != "" {
		if timeout, err := time.ParseDuration(pt.config.RESTToGRPC.DefaultTimeout); err == nil {
			grpcRequest.Timeout = timeout
		}
	}
	
	// 复制相关头部
	for key, value := range request.Headers {
		if pt.shouldForwardHeader(key) {
			grpcRequest.Headers[key] = value
		}
	}
	
	// 构建gRPC消息
	message, err := pt.buildGRPCMessage(request, service)
	if err != nil {
		return nil, fmt.Errorf("构建gRPC消息失败: %w", err)
	}
	
	grpcRequest.Message = message
	
	return grpcRequest, nil
}

// GRPCToREST gRPC到REST转换
func (pt *ProtocolTransformerImpl) GRPCToREST(ctx context.Context, response *GRPCResponse) (*TransformResponse, error) {
	// 构建REST响应
	restResponse := &TransformResponse{
		StatusCode:  pt.mapGRPCStatusToHTTP(response.StatusCode),
		Headers:     make(map[string]string),
		ContentType: "application/json",
	}
	
	// 复制头部
	for key, value := range response.Headers {
		if pt.shouldForwardHeader(key) {
			restResponse.Headers[key] = value
		}
	}
	
	// 设置内容类型
	restResponse.Headers["Content-Type"] = "application/json"
	
	// 转换响应体
	if response.Error != nil {
		// 错误响应
		errorResponse := map[string]interface{}{
			"error": map[string]interface{}{
				"code":    response.StatusCode,
				"message": response.Error.Error(),
			},
		}
		
		body, err := json.Marshal(errorResponse)
		if err != nil {
			return nil, fmt.Errorf("序列化错误响应失败: %w", err)
		}
		
		restResponse.Body = body
	} else {
		// 成功响应
		body, err := json.Marshal(response.Message)
		if err != nil {
			return nil, fmt.Errorf("序列化gRPC响应失败: %w", err)
		}
		
		restResponse.Body = body
	}
	
	restResponse.Size = int64(len(restResponse.Body))
	
	return restResponse, nil
}

// TransformWebSocketMessage WebSocket消息转换
func (pt *ProtocolTransformerImpl) TransformWebSocketMessage(ctx context.Context, message *WebSocketMessage) (*WebSocketMessage, error) {
	if !pt.config.WebSocket.Enabled {
		return message, nil
	}
	
	// 查找匹配的消息规则
	for _, rule := range pt.config.WebSocket.MessageRules {
		if pt.matchesMessageType(message, rule.Transform.Type) {
			return pt.applyWebSocketRule(message, rule)
		}
	}
	
	return message, nil
}

// 私有方法

// transformRESTToGRPC 转换REST到gRPC
func (pt *ProtocolTransformerImpl) transformRESTToGRPC(ctx context.Context, request *TransformRequest) (*TransformResponse, error) {
	grpcRequest, err := pt.RESTToGRPC(ctx, request)
	if err != nil {
		return nil, err
	}
	
	// 这里应该调用实际的gRPC客户端
	// 为了演示，我们返回一个模拟的响应
	response := &TransformResponse{
		StatusCode:  200,
		Headers:     map[string]string{"Content-Type": "application/json"},
		ContentType: "application/json",
		Metadata: map[string]interface{}{
			"grpc_service": grpcRequest.Service,
			"grpc_method":  grpcRequest.Method,
		},
	}
	
	// 序列化gRPC请求作为响应体（演示用）
	body, err := json.Marshal(grpcRequest)
	if err != nil {
		return nil, fmt.Errorf("序列化gRPC请求失败: %w", err)
	}
	
	response.Body = body
	response.Size = int64(len(body))
	
	return response, nil
}

// transformGRPCToREST 转换gRPC到REST
func (pt *ProtocolTransformerImpl) transformGRPCToREST(ctx context.Context, request *TransformRequest, response *TransformResponse) (*TransformResponse, error) {
	// 模拟gRPC响应
	grpcResponse := &GRPCResponse{
		Message:    map[string]interface{}{"result": "success"},
		Headers:    response.Headers,
		StatusCode: response.StatusCode,
	}
	
	return pt.GRPCToREST(ctx, grpcResponse)
}

// findMatchingRESTToGRPCService 查找匹配的REST到gRPC服务
func (pt *ProtocolTransformerImpl) findMatchingRESTToGRPCService(path, method string) *config.RESTToGRPCService {
	for _, service := range pt.config.RESTToGRPC.Services {
		if pt.matchesRESTPath(path, service.RESTPath) {
			return &service
		}
	}
	return nil
}

// matchesRESTPath 检查REST路径是否匹配
func (pt *ProtocolTransformerImpl) matchesRESTPath(path, pattern string) bool {
	// 简单的路径匹配，支持通配符
	if pattern == "*" {
		return true
	}
	
	// 支持路径参数，例如 /users/{id}
	if strings.Contains(pattern, "{") {
		regex := pt.convertPathToRegex(pattern)
		matched, _ := regexp.MatchString(regex, path)
		return matched
	}
	
	return path == pattern
}

// convertPathToRegex 将路径模式转换为正则表达式
func (pt *ProtocolTransformerImpl) convertPathToRegex(pattern string) string {
	// 将 {param} 转换为 ([^/]+)
	regex := regexp.MustCompile(`\{[^}]+\}`).ReplaceAllString(pattern, `([^/]+)`)
	return "^" + regex + "$"
}

// buildGRPCMessage 构建gRPC消息
func (pt *ProtocolTransformerImpl) buildGRPCMessage(request *TransformRequest, service *config.RESTToGRPCService) (map[string]interface{}, error) {
	message := make(map[string]interface{})
	
	// 解析路径参数
	pathParams := pt.extractPathParameters(request.Path, service.RESTPath)
	
	// 应用参数映射
	for restParam, grpcParam := range service.ParameterMapping {
		if value, exists := pathParams[restParam]; exists {
			message[grpcParam] = value
		} else if value, exists := request.Query[restParam]; exists {
			message[grpcParam] = value
		}
	}
	
	// 处理请求体
	if len(request.Body) > 0 && request.Method != "GET" {
		if service.RequestMapping != "" {
			// 使用自定义映射
			var bodyData interface{}
			if err := json.Unmarshal(request.Body, &bodyData); err == nil {
				message[service.RequestMapping] = bodyData
			}
		} else {
			// 直接合并JSON
			var bodyData map[string]interface{}
			if err := json.Unmarshal(request.Body, &bodyData); err == nil {
				for k, v := range bodyData {
					message[k] = v
				}
			}
		}
	}
	
	return message, nil
}

// extractPathParameters 提取路径参数
func (pt *ProtocolTransformerImpl) extractPathParameters(path, pattern string) map[string]string {
	params := make(map[string]string)
	
	if !strings.Contains(pattern, "{") {
		return params
	}
	
	// 构建正则表达式来提取参数
	paramNames := regexp.MustCompile(`\{([^}]+)\}`).FindAllStringSubmatch(pattern, -1)
	regex := pt.convertPathToRegex(pattern)
	
	re := regexp.MustCompile(regex)
	matches := re.FindStringSubmatch(path)
	
	if len(matches) > 1 && len(paramNames) == len(matches)-1 {
		for i, paramName := range paramNames {
			if len(paramName) > 1 {
				params[paramName[1]] = matches[i+1]
			}
		}
	}
	
	return params
}

// shouldForwardHeader 检查是否应该转发头部
func (pt *ProtocolTransformerImpl) shouldForwardHeader(header string) bool {
	// 排除一些不应该转发的头部
	excludeHeaders := []string{
		"content-length",
		"transfer-encoding",
		"connection",
		"upgrade",
	}
	
	headerLower := strings.ToLower(header)
	for _, exclude := range excludeHeaders {
		if headerLower == exclude {
			return false
		}
	}
	
	return true
}

// mapGRPCStatusToHTTP 映射gRPC状态码到HTTP状态码
func (pt *ProtocolTransformerImpl) mapGRPCStatusToHTTP(grpcStatus int) int {
	// gRPC状态码到HTTP状态码的映射
	statusMap := map[int]int{
		0:  200, // OK
		1:  499, // CANCELLED
		2:  500, // UNKNOWN
		3:  400, // INVALID_ARGUMENT
		4:  504, // DEADLINE_EXCEEDED
		5:  404, // NOT_FOUND
		6:  409, // ALREADY_EXISTS
		7:  403, // PERMISSION_DENIED
		8:  429, // RESOURCE_EXHAUSTED
		9:  400, // FAILED_PRECONDITION
		10: 409, // ABORTED
		11: 400, // OUT_OF_RANGE
		12: 501, // UNIMPLEMENTED
		13: 500, // INTERNAL
		14: 503, // UNAVAILABLE
		15: 500, // DATA_LOSS
		16: 401, // UNAUTHENTICATED
	}
	
	if httpStatus, exists := statusMap[grpcStatus]; exists {
		return httpStatus
	}
	
	return 500 // 默认为内部服务器错误
}

// matchesMessageType 检查WebSocket消息类型是否匹配
func (pt *ProtocolTransformerImpl) matchesMessageType(message *WebSocketMessage, messageType string) bool {
	switch messageType {
	case "text":
		return message.Type == 1 // WebSocket文本消息
	case "binary":
		return message.Type == 2 // WebSocket二进制消息
	case "all":
		return true
	default:
		// 尝试解析为数字
		if typeNum, err := strconv.Atoi(messageType); err == nil {
			return message.Type == typeNum
		}
		return false
	}
}

// applyWebSocketRule 应用WebSocket规则
func (pt *ProtocolTransformerImpl) applyWebSocketRule(message *WebSocketMessage, rule config.WebSocketMessageTransformRule) (*WebSocketMessage, error) {
	// 简化实现，实际应该支持更复杂的转换
	transformedMessage := &WebSocketMessage{
		Type:    message.Type,
		Data:    message.Data,
		Headers: make(map[string]string),
	}

	// 复制头部
	for k, v := range message.Headers {
		transformedMessage.Headers[k] = v
	}

	// 应用模板转换（如果有）
	if rule.Transform.Content.Template != "" {
		// 这里可以实现模板转换逻辑
		pt.logger.Debug("应用WebSocket消息模板", "template", rule.Transform.Content.Template)
	}

	return transformedMessage, nil
}

// updateStats 更新统计信息
func (pt *ProtocolTransformerImpl) updateStats(duration time.Duration, success bool) {
	pt.stats.TotalTransforms++
	pt.stats.LastUsed = time.Now()
	
	if success {
		pt.stats.SuccessTransforms++
	} else {
		pt.stats.FailedTransforms++
	}
	
	// 更新平均时间
	if pt.stats.AverageTime == 0 {
		pt.stats.AverageTime = duration
	} else {
		pt.stats.AverageTime = (pt.stats.AverageTime + duration) / 2
	}
}
