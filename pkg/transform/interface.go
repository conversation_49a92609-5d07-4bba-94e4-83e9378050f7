package transform

import (
	"context"
	"net/http"
	"time"
)

// Transformer 转换器接口
type Transformer interface {
	// 转换请求
	TransformRequest(ctx context.Context, request *TransformRequest) (*TransformResponse, error)
	
	// 转换响应
	TransformResponse(ctx context.Context, request *TransformRequest, response *TransformResponse) (*TransformResponse, error)
	
	// 获取转换器名称
	Name() string
	
	// 获取转换器版本
	Version() string
	
	// 获取支持的内容类型
	SupportedContentTypes() []string
	
	// 验证配置
	ValidateConfig(config interface{}) error
	
	// 配置转换器
	Configure(config interface{}) error
}

// TransformRequest 转换请求
type TransformRequest struct {
	// HTTP请求信息
	Method      string            `json:"method"`
	Path        string            `json:"path"`
	Query       map[string]string `json:"query"`
	Headers     map[string]string `json:"headers"`
	Body        []byte            `json:"body"`
	ContentType string            `json:"content_type"`
	
	// 上下文信息
	RequestID   string                 `json:"request_id"`
	UserID      string                 `json:"user_id,omitempty"`
	SessionID   string                 `json:"session_id,omitempty"`
	ServiceName string                 `json:"service_name,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	
	// 时间信息
	Timestamp time.Time `json:"timestamp"`
	
	// 原始HTTP请求（可选）
	HTTPRequest *http.Request `json:"-"`
}

// TransformResponse 转换响应
type TransformResponse struct {
	// HTTP响应信息
	StatusCode  int               `json:"status_code"`
	Headers     map[string]string `json:"headers"`
	Body        []byte            `json:"body"`
	ContentType string            `json:"content_type"`
	
	// 错误信息
	Error   error  `json:"error,omitempty"`
	Message string `json:"message,omitempty"`
	
	// 元数据
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	
	// 性能信息
	Duration time.Duration `json:"duration"`
	Size     int64         `json:"size"`
}

// TransformManager 转换管理器接口
type TransformManager interface {
	// 注册转换器
	RegisterTransformer(name string, transformer Transformer) error
	
	// 获取转换器
	GetTransformer(name string) (Transformer, error)
	
	// 列出所有转换器
	ListTransformers() []string
	
	// 执行转换链
	ExecuteChain(ctx context.Context, chain []string, request *TransformRequest) (*TransformResponse, error)
	
	// 生命周期管理
	Start() error
	Stop() error
	
	// 健康检查
	HealthCheck() error
	
	// 获取统计信息
	GetStats() (*TransformStats, error)
}

// TransformStats 转换统计信息
type TransformStats struct {
	// 总转换次数
	TotalTransforms int64 `json:"total_transforms"`
	
	// 成功转换次数
	SuccessfulTransforms int64 `json:"successful_transforms"`
	
	// 失败转换次数
	FailedTransforms int64 `json:"failed_transforms"`
	
	// 平均转换时间
	AverageTransformTime time.Duration `json:"average_transform_time"`
	
	// 转换器统计
	TransformerStats map[string]*TransformerStats `json:"transformer_stats"`
	
	// 错误统计
	ErrorStats map[string]int64 `json:"error_stats"`
}

// TransformerStats 单个转换器统计
type TransformerStats struct {
	Name             string        `json:"name"`
	TotalTransforms  int64         `json:"total_transforms"`
	SuccessTransforms int64        `json:"success_transforms"`
	FailedTransforms int64         `json:"failed_transforms"`
	AverageTime      time.Duration `json:"average_time"`
	LastUsed         time.Time     `json:"last_used"`
}

// JSONTransformer JSON转换器接口
type JSONTransformer interface {
	Transformer
	
	// JSONPath操作
	GetValue(data []byte, path string) (interface{}, error)
	SetValue(data []byte, path string, value interface{}) ([]byte, error)
	DeleteValue(data []byte, path string) ([]byte, error)
	
	// JSON合并
	Merge(data1, data2 []byte) ([]byte, error)
	
	// JSON验证
	Validate(data []byte, schema []byte) error
}

// XMLTransformer XML转换器接口
type XMLTransformer interface {
	Transformer
	
	// XPath操作
	GetValue(data []byte, xpath string) (interface{}, error)
	SetValue(data []byte, xpath string, value interface{}) ([]byte, error)
	DeleteValue(data []byte, xpath string) ([]byte, error)
	
	// XML验证
	Validate(data []byte, schema []byte) error
	
	// 命名空间处理
	SetNamespaces(namespaces map[string]string)
}

// ProtocolTransformer 协议转换器接口
type ProtocolTransformer interface {
	Transformer
	
	// REST到gRPC转换
	RESTToGRPC(ctx context.Context, request *TransformRequest) (*GRPCRequest, error)
	
	// gRPC到REST转换
	GRPCToREST(ctx context.Context, response *GRPCResponse) (*TransformResponse, error)
	
	// WebSocket消息转换
	TransformWebSocketMessage(ctx context.Context, message *WebSocketMessage) (*WebSocketMessage, error)
}

// GRPCRequest gRPC请求
type GRPCRequest struct {
	Service string                 `json:"service"`
	Method  string                 `json:"method"`
	Message map[string]interface{} `json:"message"`
	Headers map[string]string      `json:"headers"`
	Timeout time.Duration          `json:"timeout"`
}

// GRPCResponse gRPC响应
type GRPCResponse struct {
	Message    map[string]interface{} `json:"message"`
	Headers    map[string]string      `json:"headers"`
	StatusCode int                    `json:"status_code"`
	Error      error                  `json:"error,omitempty"`
}

// WebSocketMessage WebSocket消息
type WebSocketMessage struct {
	Type    int    `json:"type"`    // 消息类型
	Data    []byte `json:"data"`    // 消息数据
	Headers map[string]string `json:"headers,omitempty"`
}

// GraphQLTransformer GraphQL转换器接口
type GraphQLTransformer interface {
	Transformer
	
	// REST到GraphQL转换
	RESTToGraphQL(ctx context.Context, request *TransformRequest) (*GraphQLRequest, error)
	
	// GraphQL到REST转换
	GraphQLToREST(ctx context.Context, response *GraphQLResponse) (*TransformResponse, error)
	
	// 解析GraphQL查询
	ParseQuery(query string) (*GraphQLQuery, error)
	
	// 验证GraphQL查询
	ValidateQuery(query string, schema string) error
}

// GraphQLRequest GraphQL请求
type GraphQLRequest struct {
	Query     string                 `json:"query"`
	Variables map[string]interface{} `json:"variables,omitempty"`
	Operation string                 `json:"operationName,omitempty"`
}

// GraphQLResponse GraphQL响应
type GraphQLResponse struct {
	Data   interface{}            `json:"data,omitempty"`
	Errors []GraphQLError         `json:"errors,omitempty"`
	Extensions map[string]interface{} `json:"extensions,omitempty"`
}

// GraphQLError GraphQL错误
type GraphQLError struct {
	Message   string                 `json:"message"`
	Locations []GraphQLLocation      `json:"locations,omitempty"`
	Path      []interface{}          `json:"path,omitempty"`
	Extensions map[string]interface{} `json:"extensions,omitempty"`
}

// GraphQLLocation GraphQL位置
type GraphQLLocation struct {
	Line   int `json:"line"`
	Column int `json:"column"`
}

// GraphQLQuery 解析后的GraphQL查询
type GraphQLQuery struct {
	Type      string                 `json:"type"`      // query, mutation, subscription
	Name      string                 `json:"name"`
	Fields    []GraphQLField         `json:"fields"`
	Variables map[string]interface{} `json:"variables"`
}

// GraphQLField GraphQL字段
type GraphQLField struct {
	Name      string                 `json:"name"`
	Alias     string                 `json:"alias,omitempty"`
	Arguments map[string]interface{} `json:"arguments,omitempty"`
	Fields    []GraphQLField         `json:"fields,omitempty"`
}

// TemplateTransformer 模板转换器接口
type TemplateTransformer interface {
	Transformer
	
	// 渲染模板
	Render(template string, data interface{}) ([]byte, error)
	
	// 注册模板函数
	RegisterFunction(name string, fn interface{}) error
	
	// 加载模板
	LoadTemplate(name, content string) error
	
	// 获取模板
	GetTemplate(name string) (string, error)
}

// FormatTransformer 格式转换器接口
type FormatTransformer interface {
	Transformer
	
	// JSON到XML转换
	JSONToXML(data []byte) ([]byte, error)
	
	// XML到JSON转换
	XMLToJSON(data []byte) ([]byte, error)
	
	// JSON到YAML转换
	JSONToYAML(data []byte) ([]byte, error)
	
	// YAML到JSON转换
	YAMLToJSON(data []byte) ([]byte, error)
	
	// CSV到JSON转换
	CSVToJSON(data []byte) ([]byte, error)
	
	// JSON到CSV转换
	JSONToCSV(data []byte) ([]byte, error)
}

// TransformChain 转换链
type TransformChain struct {
	Name         string              `json:"name"`
	Description  string              `json:"description"`
	Transformers []TransformStep     `json:"transformers"`
	Conditions   []TransformCondition `json:"conditions,omitempty"`
	Enabled      bool                `json:"enabled"`
}

// TransformStep 转换步骤
type TransformStep struct {
	Name        string                 `json:"name"`
	Transformer string                 `json:"transformer"`
	Config      map[string]interface{} `json:"config,omitempty"`
	Condition   *TransformCondition    `json:"condition,omitempty"`
	OnError     string                 `json:"on_error,omitempty"` // continue, stop, retry
}

// TransformCondition 转换条件
type TransformCondition struct {
	Type     string      `json:"type"`     // path, method, header, content_type, size
	Key      string      `json:"key,omitempty"`
	Value    interface{} `json:"value"`
	Operator string      `json:"operator"` // eq, ne, gt, lt, contains, regex
}

// TransformEvent 转换事件
type TransformEvent struct {
	Type        TransformEventType     `json:"type"`
	RequestID   string                 `json:"request_id"`
	Transformer string                 `json:"transformer"`
	Duration    time.Duration          `json:"duration"`
	Success     bool                   `json:"success"`
	Error       error                  `json:"error,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
}

// TransformEventType 转换事件类型
type TransformEventType string

const (
	TransformEventTypeStart    TransformEventType = "start"
	TransformEventTypeComplete TransformEventType = "complete"
	TransformEventTypeError    TransformEventType = "error"
	TransformEventTypeSkip     TransformEventType = "skip"
)

// TransformEventListener 转换事件监听器
type TransformEventListener interface {
	// 处理转换事件
	OnTransformEvent(event *TransformEvent) error
	
	// 获取监听的事件类型
	GetEventTypes() []TransformEventType
}
