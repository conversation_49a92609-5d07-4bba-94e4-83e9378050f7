package transform

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/clbanning/mxj/v2"
	"gopkg.in/yaml.v3"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// FormatTransformerImpl 格式转换器实现
type FormatTransformerImpl struct {
	config config.FormatTransformConfig
	logger *telemetry.Logger
	
	// 统计信息
	stats *TransformerStats
}

// NewFormatTransformer 创建新的格式转换器
func NewFormatTransformer(cfg config.FormatTransformConfig, logger *telemetry.Logger) *FormatTransformerImpl {
	return &FormatTransformerImpl{
		config: cfg,
		logger: logger.With("component", "format-transformer"),
		stats: &TransformerStats{
			Name: "format",
		},
	}
}

// Name 获取转换器名称
func (ft *FormatTransformerImpl) Name() string {
	return "format"
}

// Version 获取转换器版本
func (ft *FormatTransformerImpl) Version() string {
	return "1.0.0"
}

// SupportedContentTypes 获取支持的内容类型
func (ft *FormatTransformerImpl) SupportedContentTypes() []string {
	return []string{
		"application/json",
		"application/xml",
		"text/xml",
		"application/yaml",
		"text/yaml",
		"text/csv",
		"application/csv",
	}
}

// ValidateConfig 验证配置
func (ft *FormatTransformerImpl) ValidateConfig(cfg interface{}) error {
	_, ok := cfg.(config.FormatTransformConfig)
	if !ok {
		return fmt.Errorf("无效的格式转换器配置类型")
	}
	return nil
}

// Configure 配置转换器
func (ft *FormatTransformerImpl) Configure(cfg interface{}) error {
	formatConfig, ok := cfg.(config.FormatTransformConfig)
	if !ok {
		return fmt.Errorf("无效的格式转换器配置类型")
	}

	ft.config = formatConfig
	ft.logger.Info("格式转换器配置已更新")
	
	return nil
}

// TransformRequest 转换请求
func (ft *FormatTransformerImpl) TransformRequest(ctx context.Context, request *TransformRequest) (*TransformResponse, error) {
	start := time.Now()
	defer func() {
		ft.updateStats(time.Since(start), true)
	}()
	
	if !ft.config.Enabled {
		return &TransformResponse{
			StatusCode:  200,
			Headers:     request.Headers,
			Body:        request.Body,
			ContentType: request.ContentType,
			Duration:    time.Since(start),
		}, nil
	}
	
	// 根据内容类型进行转换
	transformedBody, newContentType, err := ft.transformByContentType(request.Body, request.ContentType)
	if err != nil {
		ft.updateStats(time.Since(start), false)
		return nil, fmt.Errorf("格式转换失败: %w", err)
	}
	
	// 更新头部
	headers := make(map[string]string)
	for k, v := range request.Headers {
		headers[k] = v
	}
	if newContentType != "" {
		headers["Content-Type"] = newContentType
	}
	
	return &TransformResponse{
		StatusCode:  200,
		Headers:     headers,
		Body:        transformedBody,
		ContentType: newContentType,
		Duration:    time.Since(start),
		Size:        int64(len(transformedBody)),
	}, nil
}

// TransformResponse 转换响应
func (ft *FormatTransformerImpl) TransformResponse(ctx context.Context, request *TransformRequest, response *TransformResponse) (*TransformResponse, error) {
	start := time.Now()
	defer func() {
		ft.updateStats(time.Since(start), true)
	}()
	
	if !ft.config.Enabled {
		return response, nil
	}
	
	// 根据内容类型进行转换
	transformedBody, newContentType, err := ft.transformByContentType(response.Body, response.ContentType)
	if err != nil {
		ft.updateStats(time.Since(start), false)
		return nil, fmt.Errorf("响应格式转换失败: %w", err)
	}
	
	// 更新响应
	if newContentType != "" {
		response.ContentType = newContentType
		if response.Headers == nil {
			response.Headers = make(map[string]string)
		}
		response.Headers["Content-Type"] = newContentType
	}
	
	response.Body = transformedBody
	response.Size = int64(len(transformedBody))
	
	return response, nil
}

// JSONToXML JSON到XML转换
func (ft *FormatTransformerImpl) JSONToXML(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}
	
	// 解析JSON
	var jsonData interface{}
	if err := json.Unmarshal(data, &jsonData); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %w", err)
	}
	
	// 使用mxj库转换为XML
	mv := mxj.Map(jsonData.(map[string]interface{}))
	
	// 配置XML选项
	rootElement := ft.config.JSONToXML.RootElement
	if rootElement == "" {
		rootElement = "root"
	}
	
	xmlData, err := mv.XmlIndent("", "  ", rootElement)
	if err != nil {
		return nil, fmt.Errorf("转换为XML失败: %w", err)
	}
	
	return xmlData, nil
}

// XMLToJSON XML到JSON转换
func (ft *FormatTransformerImpl) XMLToJSON(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}
	
	// 使用mxj库解析XML
	mv, err := mxj.NewMapXml(data)
	if err != nil {
		return nil, fmt.Errorf("解析XML失败: %w", err)
	}
	
	// 转换为JSON
	jsonData, err := mv.Json()
	if err != nil {
		return nil, fmt.Errorf("转换为JSON失败: %w", err)
	}
	
	return jsonData, nil
}

// JSONToYAML JSON到YAML转换
func (ft *FormatTransformerImpl) JSONToYAML(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}
	
	// 解析JSON
	var jsonData interface{}
	if err := json.Unmarshal(data, &jsonData); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %w", err)
	}
	
	// 转换为YAML
	yamlData, err := yaml.Marshal(jsonData)
	if err != nil {
		return nil, fmt.Errorf("转换为YAML失败: %w", err)
	}
	
	return yamlData, nil
}

// YAMLToJSON YAML到JSON转换
func (ft *FormatTransformerImpl) YAMLToJSON(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}
	
	// 解析YAML
	var yamlData interface{}
	if err := yaml.Unmarshal(data, &yamlData); err != nil {
		return nil, fmt.Errorf("解析YAML失败: %w", err)
	}
	
	// 转换为JSON
	jsonData, err := json.Marshal(yamlData)
	if err != nil {
		return nil, fmt.Errorf("转换为JSON失败: %w", err)
	}
	
	return jsonData, nil
}

// CSVToJSON CSV到JSON转换
func (ft *FormatTransformerImpl) CSVToJSON(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}
	
	// 解析CSV
	reader := csv.NewReader(strings.NewReader(string(data)))
	
	// 设置分隔符
	if ft.config.CSV.Delimiter != "" {
		reader.Comma = rune(ft.config.CSV.Delimiter[0])
	}
	
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("解析CSV失败: %w", err)
	}
	
	if len(records) == 0 {
		return []byte("[]"), nil
	}
	
	var result []map[string]interface{}
	
	// 处理头部
	var headers []string
	startRow := 0
	
	if ft.config.CSV.HasHeader && len(records) > 0 {
		headers = records[0]
		startRow = 1
	} else {
		// 生成默认头部
		for i := 0; i < len(records[0]); i++ {
			headers = append(headers, fmt.Sprintf("column_%d", i+1))
		}
	}
	
	// 转换数据行
	for i := startRow; i < len(records); i++ {
		row := make(map[string]interface{})
		for j, value := range records[i] {
			if j < len(headers) {
				headerName := headers[j]
				
				// 应用字段映射
				if mappedName, exists := ft.config.CSV.FieldMapping[headerName]; exists {
					headerName = mappedName
				}
				
				row[headerName] = value
			}
		}
		result = append(result, row)
	}
	
	// 序列化为JSON
	jsonData, err := json.Marshal(result)
	if err != nil {
		return nil, fmt.Errorf("序列化JSON失败: %w", err)
	}
	
	return jsonData, nil
}

// JSONToCSV JSON到CSV转换
func (ft *FormatTransformerImpl) JSONToCSV(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}
	
	// 解析JSON
	var jsonData []map[string]interface{}
	if err := json.Unmarshal(data, &jsonData); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %w", err)
	}
	
	if len(jsonData) == 0 {
		return []byte(""), nil
	}
	
	// 收集所有字段名
	fieldSet := make(map[string]bool)
	for _, row := range jsonData {
		for field := range row {
			fieldSet[field] = true
		}
	}
	
	// 转换为有序列表
	var fields []string
	for field := range fieldSet {
		fields = append(fields, field)
	}
	
	// 构建CSV
	var csvBuilder strings.Builder
	writer := csv.NewWriter(&csvBuilder)
	
	// 设置分隔符
	if ft.config.CSV.Delimiter != "" {
		writer.Comma = rune(ft.config.CSV.Delimiter[0])
	}
	
	// 写入头部
	if ft.config.CSV.HasHeader {
		writer.Write(fields)
	}
	
	// 写入数据行
	for _, row := range jsonData {
		var record []string
		for _, field := range fields {
			if value, exists := row[field]; exists {
				record = append(record, fmt.Sprintf("%v", value))
			} else {
				record = append(record, "")
			}
		}
		writer.Write(record)
	}
	
	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, fmt.Errorf("写入CSV失败: %w", err)
	}
	
	return []byte(csvBuilder.String()), nil
}

// 私有方法

// transformByContentType 根据内容类型进行转换
func (ft *FormatTransformerImpl) transformByContentType(data []byte, contentType string) ([]byte, string, error) {
	if len(data) == 0 {
		return data, contentType, nil
	}
	
	contentTypeLower := strings.ToLower(contentType)
	
	// JSON相关转换
	if strings.Contains(contentTypeLower, "json") {
		// JSON到XML
		if ft.config.JSONToXML.RootElement != "" {
			xmlData, err := ft.JSONToXML(data)
			if err == nil {
				return xmlData, "application/xml", nil
			}
		}
		
		// JSON到YAML
		if ft.config.YAML.Indent > 0 {
			yamlData, err := ft.JSONToYAML(data)
			if err == nil {
				return yamlData, "application/yaml", nil
			}
		}
	}
	
	// XML相关转换
	if strings.Contains(contentTypeLower, "xml") {
		// XML到JSON
		if ft.config.XMLToJSON.AttributePrefix != "" || ft.config.XMLToJSON.ContentField != "" {
			jsonData, err := ft.XMLToJSON(data)
			if err == nil {
				return jsonData, "application/json", nil
			}
		}
	}
	
	// YAML相关转换
	if strings.Contains(contentTypeLower, "yaml") {
		// YAML到JSON
		jsonData, err := ft.YAMLToJSON(data)
		if err == nil {
			return jsonData, "application/json", nil
		}
	}
	
	// CSV相关转换
	if strings.Contains(contentTypeLower, "csv") {
		// CSV到JSON
		jsonData, err := ft.CSVToJSON(data)
		if err == nil {
			return jsonData, "application/json", nil
		}
	}
	
	// 没有匹配的转换，返回原数据
	return data, contentType, nil
}

// updateStats 更新统计信息
func (ft *FormatTransformerImpl) updateStats(duration time.Duration, success bool) {
	ft.stats.TotalTransforms++
	ft.stats.LastUsed = time.Now()
	
	if success {
		ft.stats.SuccessTransforms++
	} else {
		ft.stats.FailedTransforms++
	}
	
	// 更新平均时间
	if ft.stats.AverageTime == 0 {
		ft.stats.AverageTime = duration
	} else {
		ft.stats.AverageTime = (ft.stats.AverageTime + duration) / 2
	}
}
