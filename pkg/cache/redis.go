package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// RedisCache Redis缓存实现
type RedisCache struct {
	config   config.CacheRedisConfig
	logger   *telemetry.Logger
	client   *redis.Client
	
	// 统计信息
	stats    *CacheStats
	statsMux sync.RWMutex
	
	// 生命周期
	startTime time.Time
	
	// 事件监听器
	listeners []CacheEventListener
	listenersMux sync.RWMutex
}

// redisItem Redis缓存项结构
type redisItem struct {
	Value []byte   `json:"value"`
	Tags  []string `json:"tags,omitempty"`
}

// NewRedisCache 创建新的Redis缓存
func NewRedisCache(cfg config.CacheRedisConfig, logger *telemetry.Logger) (*RedisCache, error) {
	// 解析超时配置
	dialTimeout, _ := time.ParseDuration(cfg.DialTimeout)
	if dialTimeout == 0 {
		dialTimeout = 5 * time.Second
	}
	
	readTimeout, _ := time.ParseDuration(cfg.ReadTimeout)
	if readTimeout == 0 {
		readTimeout = 3 * time.Second
	}
	
	writeTimeout, _ := time.ParseDuration(cfg.WriteTimeout)
	if writeTimeout == 0 {
		writeTimeout = 3 * time.Second
	}
	
	retryDelay, _ := time.ParseDuration(cfg.RetryDelay)
	if retryDelay == 0 {
		retryDelay = 100 * time.Millisecond
	}
	
	// 创建Redis客户端
	client := redis.NewClient(&redis.Options{
		Addr:         cfg.Address,
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		MaxRetries:   cfg.MaxRetries,
		DialTimeout:  dialTimeout,
		ReadTimeout:  readTimeout,
		WriteTimeout: writeTimeout,
	})
	
	cache := &RedisCache{
		config:    cfg,
		logger:    logger.With("component", "redis-cache"),
		client:    client,
		stats:     &CacheStats{},
		startTime: time.Now(),
		listeners: make([]CacheEventListener, 0),
	}
	
	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("Redis连接失败: %w", err)
	}
	
	cache.logger.Info("Redis缓存初始化完成",
		"address", cfg.Address,
		"db", cfg.DB,
		"pool_size", cfg.PoolSize)
	
	return cache, nil
}

// Get 获取缓存值
func (rc *RedisCache) Get(ctx context.Context, key string) ([]byte, error) {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		rc.updateAvgGetTime(duration)
	}()
	
	fullKey := rc.buildKey(key)
	
	result, err := rc.client.Get(ctx, fullKey).Result()
	if err != nil {
		if err == redis.Nil {
			rc.recordMiss(key)
			rc.emitEvent(CacheEventTypeMiss, key, nil, 0, nil)
			return nil, ErrCacheKeyNotFound
		}
		rc.recordError("get", err)
		return nil, fmt.Errorf("Redis GET失败: %w", err)
	}
	
	// 解析Redis项
	var item redisItem
	if err := json.Unmarshal([]byte(result), &item); err != nil {
		// 如果解析失败，可能是旧格式的数据，直接返回
		rc.recordHit(key)
		rc.emitEvent(CacheEventTypeHit, key, []byte(result), 0, nil)
		return []byte(result), nil
	}
	
	rc.recordHit(key)
	rc.emitEvent(CacheEventTypeHit, key, item.Value, 0, item.Tags)
	
	return item.Value, nil
}

// Set 设置缓存值
func (rc *RedisCache) Set(ctx context.Context, key string, value []byte, ttl time.Duration) error {
	return rc.SetWithTags(ctx, key, value, ttl, nil)
}

// SetWithTags 设置带标签的缓存值
func (rc *RedisCache) SetWithTags(ctx context.Context, key string, value []byte, ttl time.Duration, tags []string) error {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		rc.updateAvgSetTime(duration)
	}()
	
	fullKey := rc.buildKey(key)
	
	// 创建Redis项
	item := redisItem{
		Value: value,
		Tags:  tags,
	}
	
	// 序列化
	data, err := json.Marshal(item)
	if err != nil {
		return fmt.Errorf("序列化缓存项失败: %w", err)
	}
	
	// 设置到Redis
	err = rc.client.Set(ctx, fullKey, data, ttl).Err()
	if err != nil {
		rc.recordError("set", err)
		return fmt.Errorf("Redis SET失败: %w", err)
	}
	
	// 如果有标签，建立标签索引
	if len(tags) > 0 {
		rc.indexTags(ctx, key, tags)
	}
	
	rc.recordSet(key, int64(len(value)))
	rc.emitEvent(CacheEventTypeSet, key, value, ttl, tags)
	
	return nil
}

// Delete 删除缓存值
func (rc *RedisCache) Delete(ctx context.Context, key string) error {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		rc.updateAvgDeleteTime(duration)
	}()
	
	fullKey := rc.buildKey(key)
	
	// 先获取项目以获取标签信息
	item, _ := rc.getRedisItem(ctx, fullKey)
	
	result, err := rc.client.Del(ctx, fullKey).Result()
	if err != nil {
		rc.recordError("delete", err)
		return fmt.Errorf("Redis DEL失败: %w", err)
	}
	
	if result > 0 {
		// 清理标签索引
		if item != nil && len(item.Tags) > 0 {
			rc.cleanupTagIndex(ctx, key, item.Tags)
		}
		
		rc.recordDelete(key)
		rc.emitEvent(CacheEventTypeDelete, key, nil, 0, nil)
	}
	
	return nil
}

// Exists 检查键是否存在
func (rc *RedisCache) Exists(ctx context.Context, key string) (bool, error) {
	fullKey := rc.buildKey(key)
	
	result, err := rc.client.Exists(ctx, fullKey).Result()
	if err != nil {
		return false, fmt.Errorf("Redis EXISTS失败: %w", err)
	}
	
	return result > 0, nil
}

// GetMulti 批量获取
func (rc *RedisCache) GetMulti(ctx context.Context, keys []string) (map[string][]byte, error) {
	if len(keys) == 0 {
		return make(map[string][]byte), nil
	}
	
	// 构建完整键名
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = rc.buildKey(key)
	}
	
	// 批量获取
	results, err := rc.client.MGet(ctx, fullKeys...).Result()
	if err != nil {
		return nil, fmt.Errorf("Redis MGET失败: %w", err)
	}
	
	// 解析结果
	resultMap := make(map[string][]byte)
	for i, result := range results {
		if result != nil {
			if str, ok := result.(string); ok {
				// 尝试解析为Redis项
				var item redisItem
				if err := json.Unmarshal([]byte(str), &item); err == nil {
					resultMap[keys[i]] = item.Value
				} else {
					// 旧格式数据
					resultMap[keys[i]] = []byte(str)
				}
				rc.recordHit(keys[i])
			}
		} else {
			rc.recordMiss(keys[i])
		}
	}
	
	return resultMap, nil
}

// SetMulti 批量设置
func (rc *RedisCache) SetMulti(ctx context.Context, items map[string]CacheItem) error {
	pipe := rc.client.Pipeline()
	
	for key, item := range items {
		fullKey := rc.buildKey(key)
		
		// 创建Redis项
		redisItem := redisItem{
			Value: item.Value,
			Tags:  item.Tags,
		}
		
		// 序列化
		data, err := json.Marshal(redisItem)
		if err != nil {
			return fmt.Errorf("序列化缓存项失败: %w", err)
		}
		
		pipe.Set(ctx, fullKey, data, item.TTL)
		
		// 建立标签索引
		if len(item.Tags) > 0 {
			rc.indexTagsInPipeline(pipe, ctx, key, item.Tags)
		}
	}
	
	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("Redis批量设置失败: %w", err)
	}
	
	// 记录统计
	for key, item := range items {
		rc.recordSet(key, int64(len(item.Value)))
		rc.emitEvent(CacheEventTypeSet, key, item.Value, item.TTL, item.Tags)
	}
	
	return nil
}

// DeleteMulti 批量删除
func (rc *RedisCache) DeleteMulti(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}
	
	// 构建完整键名
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = rc.buildKey(key)
	}
	
	result, err := rc.client.Del(ctx, fullKeys...).Result()
	if err != nil {
		return fmt.Errorf("Redis批量删除失败: %w", err)
	}
	
	// 记录统计
	for i := int64(0); i < result; i++ {
		if int(i) < len(keys) {
			rc.recordDelete(keys[i])
			rc.emitEvent(CacheEventTypeDelete, keys[i], nil, 0, nil)
		}
	}
	
	return nil
}

// GetWithTTL 获取值和剩余TTL
func (rc *RedisCache) GetWithTTL(ctx context.Context, key string) ([]byte, time.Duration, error) {
	fullKey := rc.buildKey(key)
	
	// 使用管道同时获取值和TTL
	pipe := rc.client.Pipeline()
	getCmd := pipe.Get(ctx, fullKey)
	ttlCmd := pipe.TTL(ctx, fullKey)
	
	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, 0, fmt.Errorf("Redis GET WITH TTL失败: %w", err)
	}
	
	// 检查值是否存在
	value, err := getCmd.Result()
	if err != nil {
		if err == redis.Nil {
			return nil, 0, ErrCacheKeyNotFound
		}
		return nil, 0, err
	}
	
	// 获取TTL
	ttl, err := ttlCmd.Result()
	if err != nil {
		ttl = -1 // 表示没有过期时间
	}
	
	// 解析值
	var item redisItem
	if err := json.Unmarshal([]byte(value), &item); err == nil {
		return item.Value, ttl, nil
	}
	
	// 旧格式数据
	return []byte(value), ttl, nil
}

// SetNX 仅当键不存在时设置
func (rc *RedisCache) SetNX(ctx context.Context, key string, value []byte, ttl time.Duration) (bool, error) {
	fullKey := rc.buildKey(key)

	// 创建Redis项
	item := redisItem{
		Value: value,
	}

	data, err := json.Marshal(item)
	if err != nil {
		return false, fmt.Errorf("序列化缓存项失败: %w", err)
	}

	result, err := rc.client.SetNX(ctx, fullKey, data, ttl).Result()
	if err != nil {
		return false, fmt.Errorf("Redis SETNX失败: %w", err)
	}

	if result {
		rc.recordSet(key, int64(len(value)))
		rc.emitEvent(CacheEventTypeSet, key, value, ttl, nil)
	}

	return result, nil
}

// Increment 增加数值
func (rc *RedisCache) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	fullKey := rc.buildKey(key)

	result, err := rc.client.IncrBy(ctx, fullKey, delta).Result()
	if err != nil {
		return 0, fmt.Errorf("Redis INCRBY失败: %w", err)
	}

	return result, nil
}

// Decrement 减少数值
func (rc *RedisCache) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	fullKey := rc.buildKey(key)

	result, err := rc.client.DecrBy(ctx, fullKey, delta).Result()
	if err != nil {
		return 0, fmt.Errorf("Redis DECRBY失败: %w", err)
	}

	return result, nil
}

// Keys 获取匹配模式的键
func (rc *RedisCache) Keys(ctx context.Context, pattern string) ([]string, error) {
	fullPattern := rc.buildKey(pattern)

	keys, err := rc.client.Keys(ctx, fullPattern).Result()
	if err != nil {
		return nil, fmt.Errorf("Redis KEYS失败: %w", err)
	}

	// 移除键前缀
	result := make([]string, len(keys))
	for i, key := range keys {
		result[i] = rc.stripKeyPrefix(key)
	}

	return result, nil
}

// DeleteByPattern 按模式删除
func (rc *RedisCache) DeleteByPattern(ctx context.Context, pattern string) error {
	keys, err := rc.Keys(ctx, pattern)
	if err != nil {
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	return rc.DeleteMulti(ctx, keys)
}

// InvalidateByTag 按标签失效
func (rc *RedisCache) InvalidateByTag(ctx context.Context, tag string) error {
	tagKey := rc.buildTagKey(tag)

	// 获取所有带有此标签的键
	keys, err := rc.client.SMembers(ctx, tagKey).Result()
	if err != nil {
		return fmt.Errorf("获取标签键失败: %w", err)
	}

	if len(keys) == 0 {
		return nil
	}

	// 删除所有相关的键
	pipe := rc.client.Pipeline()

	// 删除缓存键
	for _, key := range keys {
		fullKey := rc.buildKey(key)
		pipe.Del(ctx, fullKey)
	}

	// 删除标签索引
	pipe.Del(ctx, tagKey)

	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("按标签删除失败: %w", err)
	}

	// 记录统计和事件
	for _, key := range keys {
		rc.recordDelete(key)
		rc.emitEvent(CacheEventTypeInvalidate, key, nil, 0, []string{tag})
	}

	return nil
}

// Stats 获取统计信息
func (rc *RedisCache) Stats(ctx context.Context) (*CacheStats, error) {
	rc.statsMux.RLock()
	defer rc.statsMux.RUnlock()

	// 获取Redis信息
	info, err := rc.client.Info(ctx, "memory", "stats").Result()
	if err != nil {
		rc.logger.Warn("获取Redis信息失败", "error", err)
	}

	// 解析Redis信息
	var usedMemory int64
	var totalKeys int64

	if info != "" {
		lines := strings.Split(info, "\r\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "used_memory:") {
				if val, err := strconv.ParseInt(strings.TrimPrefix(line, "used_memory:"), 10, 64); err == nil {
					usedMemory = val
				}
			}
			if strings.HasPrefix(line, "db"+strconv.Itoa(rc.config.DB)+":") {
				// 解析 db0:keys=123,expires=456,avg_ttl=789
				parts := strings.Split(line, ",")
				if len(parts) > 0 {
					keysPart := strings.Split(parts[0], "=")
					if len(keysPart) == 2 {
						if val, err := strconv.ParseInt(keysPart[1], 10, 64); err == nil {
							totalKeys = val
						}
					}
				}
			}
		}
	}

	stats := &CacheStats{
		Hits:        rc.stats.Hits,
		Misses:      rc.stats.Misses,
		Sets:        rc.stats.Sets,
		Deletes:     rc.stats.Deletes,
		Evictions:   rc.stats.Evictions,
		HitRatio:    rc.calculateHitRatio(),
		AvgGetTime:  rc.stats.AvgGetTime,
		AvgSetTime:  rc.stats.AvgSetTime,
		ItemCount:   totalKeys,
		UsedMemory:  usedMemory,
		Uptime:      time.Since(rc.startTime),
		LastAccess:  rc.stats.LastAccess,
		Errors:      rc.stats.Errors,
		Timeouts:    rc.stats.Timeouts,
		Metadata: map[string]interface{}{
			"redis_address": rc.config.Address,
			"redis_db":      rc.config.DB,
		},
	}

	return stats, nil
}

// Close 关闭缓存
func (rc *RedisCache) Close() error {
	err := rc.client.Close()
	if err != nil {
		rc.logger.Error("关闭Redis客户端失败", "error", err)
		return err
	}

	rc.logger.Info("Redis缓存已关闭")
	return nil
}

// Ping 检查缓存是否可用
func (rc *RedisCache) Ping(ctx context.Context) error {
	return rc.client.Ping(ctx).Err()
}

// 辅助方法

// buildKey 构建完整的键名
func (rc *RedisCache) buildKey(key string) string {
	if rc.config.KeyPrefix == "" {
		return key
	}
	return rc.config.KeyPrefix + ":" + key
}

// stripKeyPrefix 移除键前缀
func (rc *RedisCache) stripKeyPrefix(key string) string {
	if rc.config.KeyPrefix == "" {
		return key
	}
	prefix := rc.config.KeyPrefix + ":"
	if strings.HasPrefix(key, prefix) {
		return strings.TrimPrefix(key, prefix)
	}
	return key
}

// buildTagKey 构建标签键名
func (rc *RedisCache) buildTagKey(tag string) string {
	return rc.buildKey("tag:" + tag)
}

// getRedisItem 获取Redis项
func (rc *RedisCache) getRedisItem(ctx context.Context, fullKey string) (*redisItem, error) {
	result, err := rc.client.Get(ctx, fullKey).Result()
	if err != nil {
		return nil, err
	}

	var item redisItem
	if err := json.Unmarshal([]byte(result), &item); err != nil {
		return nil, err
	}

	return &item, nil
}

// indexTags 建立标签索引
func (rc *RedisCache) indexTags(ctx context.Context, key string, tags []string) {
	for _, tag := range tags {
		tagKey := rc.buildTagKey(tag)
		rc.client.SAdd(ctx, tagKey, key)
	}
}

// indexTagsInPipeline 在管道中建立标签索引
func (rc *RedisCache) indexTagsInPipeline(pipe redis.Pipeliner, ctx context.Context, key string, tags []string) {
	for _, tag := range tags {
		tagKey := rc.buildTagKey(tag)
		pipe.SAdd(ctx, tagKey, key)
	}
}

// cleanupTagIndex 清理标签索引
func (rc *RedisCache) cleanupTagIndex(ctx context.Context, key string, tags []string) {
	for _, tag := range tags {
		tagKey := rc.buildTagKey(tag)
		rc.client.SRem(ctx, tagKey, key)
	}
}

// 统计方法

// recordHit 记录缓存命中
func (rc *RedisCache) recordHit(key string) {
	rc.statsMux.Lock()
	defer rc.statsMux.Unlock()

	rc.stats.Hits++
	rc.stats.LastAccess = time.Now()
}

// recordMiss 记录缓存未命中
func (rc *RedisCache) recordMiss(key string) {
	rc.statsMux.Lock()
	defer rc.statsMux.Unlock()

	rc.stats.Misses++
	rc.stats.LastAccess = time.Now()
}

// recordSet 记录缓存设置
func (rc *RedisCache) recordSet(key string, size int64) {
	rc.statsMux.Lock()
	defer rc.statsMux.Unlock()

	rc.stats.Sets++
}

// recordDelete 记录缓存删除
func (rc *RedisCache) recordDelete(key string) {
	rc.statsMux.Lock()
	defer rc.statsMux.Unlock()

	rc.stats.Deletes++
}

// recordError 记录错误
func (rc *RedisCache) recordError(operation string, err error) {
	rc.statsMux.Lock()
	defer rc.statsMux.Unlock()

	rc.stats.Errors++
	rc.logger.Error("Redis操作失败", "operation", operation, "error", err)
}

// calculateHitRatio 计算命中率
func (rc *RedisCache) calculateHitRatio() float64 {
	total := rc.stats.Hits + rc.stats.Misses
	if total == 0 {
		return 0
	}
	return float64(rc.stats.Hits) / float64(total)
}

// updateAvgGetTime 更新平均获取时间
func (rc *RedisCache) updateAvgGetTime(duration time.Duration) {
	rc.statsMux.Lock()
	defer rc.statsMux.Unlock()

	if rc.stats.AvgGetTime == 0 {
		rc.stats.AvgGetTime = float64(duration.Nanoseconds()) / 1e6
	} else {
		rc.stats.AvgGetTime = (rc.stats.AvgGetTime + float64(duration.Nanoseconds())/1e6) / 2
	}
}

// updateAvgSetTime 更新平均设置时间
func (rc *RedisCache) updateAvgSetTime(duration time.Duration) {
	rc.statsMux.Lock()
	defer rc.statsMux.Unlock()

	if rc.stats.AvgSetTime == 0 {
		rc.stats.AvgSetTime = float64(duration.Nanoseconds()) / 1e6
	} else {
		rc.stats.AvgSetTime = (rc.stats.AvgSetTime + float64(duration.Nanoseconds())/1e6) / 2
	}
}

// updateAvgDeleteTime 更新平均删除时间
func (rc *RedisCache) updateAvgDeleteTime(duration time.Duration) {
	// 可以添加删除时间统计
}

// 事件相关方法

// AddEventListener 添加事件监听器
func (rc *RedisCache) AddEventListener(listener CacheEventListener) {
	rc.listenersMux.Lock()
	defer rc.listenersMux.Unlock()

	rc.listeners = append(rc.listeners, listener)
}

// RemoveEventListener 移除事件监听器
func (rc *RedisCache) RemoveEventListener(listener CacheEventListener) {
	rc.listenersMux.Lock()
	defer rc.listenersMux.Unlock()

	for i, l := range rc.listeners {
		if l == listener {
			rc.listeners = append(rc.listeners[:i], rc.listeners[i+1:]...)
			break
		}
	}
}

// emitEvent 发送事件
func (rc *RedisCache) emitEvent(eventType CacheEventType, key string, value []byte, ttl time.Duration, tags []string) {
	rc.listenersMux.RLock()
	listeners := make([]CacheEventListener, len(rc.listeners))
	copy(listeners, rc.listeners)
	rc.listenersMux.RUnlock()

	if len(listeners) == 0 {
		return
	}

	event := &CacheEvent{
		Type:      eventType,
		Key:       key,
		Value:     value,
		TTL:       ttl,
		Tags:      tags,
		Timestamp: time.Now(),
	}

	// 异步发送事件
	go func() {
		for _, listener := range listeners {
			// 检查监听器是否关心这种事件类型
			eventTypes := listener.GetEventTypes()
			if len(eventTypes) > 0 {
				found := false
				for _, et := range eventTypes {
					if et == eventType {
						found = true
						break
					}
				}
				if !found {
					continue
				}
			}

			if err := listener.OnCacheEvent(event); err != nil {
				rc.logger.Error("缓存事件处理失败", "event_type", eventType, "key", key, "error", err)
			}
		}
	}()
}
