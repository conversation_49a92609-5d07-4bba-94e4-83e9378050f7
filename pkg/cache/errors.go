package cache

import "errors"

// 缓存相关错误定义
var (
	// ErrCacheKeyNotFound 缓存键未找到
	ErrCacheKeyNotFound = errors.New("缓存键未找到")
	
	// ErrCacheKeyExists 缓存键已存在
	ErrCacheKeyExists = errors.New("缓存键已存在")
	
	// ErrCacheKeyInvalid 缓存键无效
	ErrCacheKeyInvalid = errors.New("缓存键无效")
	
	// ErrCacheValueTooLarge 缓存值过大
	ErrCacheValueTooLarge = errors.New("缓存值过大")
	
	// ErrCacheFull 缓存已满
	ErrCacheFull = errors.New("缓存已满")
	
	// ErrCacheTimeout 缓存操作超时
	ErrCacheTimeout = errors.New("缓存操作超时")
	
	// ErrCacheConnectionFailed 缓存连接失败
	ErrCacheConnectionFailed = errors.New("缓存连接失败")
	
	// ErrCacheNotSupported 缓存操作不支持
	ErrCacheNotSupported = errors.New("缓存操作不支持")
	
	// ErrCacheConfigInvalid 缓存配置无效
	ErrCacheConfigInvalid = errors.New("缓存配置无效")
	
	// ErrCacheNotInitialized 缓存未初始化
	ErrCacheNotInitialized = errors.New("缓存未初始化")
	
	// ErrCacheAlreadyExists 缓存已存在
	ErrCacheAlreadyExists = errors.New("缓存已存在")
	
	// ErrCacheNotFound 缓存未找到
	ErrCacheNotFound = errors.New("缓存未找到")
	
	// ErrCacheSerializationFailed 缓存序列化失败
	ErrCacheSerializationFailed = errors.New("缓存序列化失败")
	
	// ErrCacheDeserializationFailed 缓存反序列化失败
	ErrCacheDeserializationFailed = errors.New("缓存反序列化失败")
	
	// ErrCacheCompressionFailed 缓存压缩失败
	ErrCacheCompressionFailed = errors.New("缓存压缩失败")
	
	// ErrCacheDecompressionFailed 缓存解压失败
	ErrCacheDecompressionFailed = errors.New("缓存解压失败")
	
	// ErrCacheInvalidTTL 无效的TTL
	ErrCacheInvalidTTL = errors.New("无效的TTL")
	
	// ErrCacheInvalidTag 无效的标签
	ErrCacheInvalidTag = errors.New("无效的标签")
	
	// ErrCacheTagNotFound 标签未找到
	ErrCacheTagNotFound = errors.New("标签未找到")
	
	// ErrCachePolicyNotFound 缓存策略未找到
	ErrCachePolicyNotFound = errors.New("缓存策略未找到")
	
	// ErrCachePolicyInvalid 缓存策略无效
	ErrCachePolicyInvalid = errors.New("缓存策略无效")
	
	// ErrCacheWarmupFailed 缓存预热失败
	ErrCacheWarmupFailed = errors.New("缓存预热失败")
	
	// ErrCacheInvalidationFailed 缓存失效失败
	ErrCacheInvalidationFailed = errors.New("缓存失效失败")
	
	// ErrCacheMetricsNotAvailable 缓存指标不可用
	ErrCacheMetricsNotAvailable = errors.New("缓存指标不可用")
	
	// ErrCacheEventListenerFailed 缓存事件监听器失败
	ErrCacheEventListenerFailed = errors.New("缓存事件监听器失败")
)

// CacheError 缓存错误结构
type CacheError struct {
	Operation string // 操作类型
	Key       string // 缓存键
	Cause     error  // 原因
	Code      string // 错误代码
	Message   string // 错误消息
}

// Error 实现error接口
func (e *CacheError) Error() string {
	if e.Message != "" {
		return e.Message
	}
	
	if e.Cause != nil {
		return e.Cause.Error()
	}
	
	return "缓存操作失败"
}

// Unwrap 解包错误
func (e *CacheError) Unwrap() error {
	return e.Cause
}

// NewCacheError 创建缓存错误
func NewCacheError(operation, key string, cause error) *CacheError {
	return &CacheError{
		Operation: operation,
		Key:       key,
		Cause:     cause,
	}
}

// NewCacheErrorWithCode 创建带错误代码的缓存错误
func NewCacheErrorWithCode(operation, key, code string, cause error) *CacheError {
	return &CacheError{
		Operation: operation,
		Key:       key,
		Code:      code,
		Cause:     cause,
	}
}

// NewCacheErrorWithMessage 创建带消息的缓存错误
func NewCacheErrorWithMessage(operation, key, message string) *CacheError {
	return &CacheError{
		Operation: operation,
		Key:       key,
		Message:   message,
	}
}

// IsCacheError 检查是否为缓存错误
func IsCacheError(err error) bool {
	_, ok := err.(*CacheError)
	return ok
}

// IsKeyNotFoundError 检查是否为键未找到错误
func IsKeyNotFoundError(err error) bool {
	return errors.Is(err, ErrCacheKeyNotFound)
}

// IsTimeoutError 检查是否为超时错误
func IsTimeoutError(err error) bool {
	return errors.Is(err, ErrCacheTimeout)
}

// IsConnectionError 检查是否为连接错误
func IsConnectionError(err error) bool {
	return errors.Is(err, ErrCacheConnectionFailed)
}

// IsConfigError 检查是否为配置错误
func IsConfigError(err error) bool {
	return errors.Is(err, ErrCacheConfigInvalid)
}

// IsSerializationError 检查是否为序列化错误
func IsSerializationError(err error) bool {
	return errors.Is(err, ErrCacheSerializationFailed) || 
		   errors.Is(err, ErrCacheDeserializationFailed)
}

// IsCompressionError 检查是否为压缩错误
func IsCompressionError(err error) bool {
	return errors.Is(err, ErrCacheCompressionFailed) || 
		   errors.Is(err, ErrCacheDecompressionFailed)
}

// WrapError 包装错误
func WrapError(operation, key string, err error) error {
	if err == nil {
		return nil
	}
	
	if IsCacheError(err) {
		return err
	}
	
	return NewCacheError(operation, key, err)
}

// WrapErrorWithCode 包装错误并添加错误代码
func WrapErrorWithCode(operation, key, code string, err error) error {
	if err == nil {
		return nil
	}
	
	if IsCacheError(err) {
		return err
	}
	
	return NewCacheErrorWithCode(operation, key, code, err)
}

// ErrorCode 错误代码常量
const (
	ErrorCodeKeyNotFound      = "KEY_NOT_FOUND"
	ErrorCodeKeyExists        = "KEY_EXISTS"
	ErrorCodeKeyInvalid       = "KEY_INVALID"
	ErrorCodeValueTooLarge    = "VALUE_TOO_LARGE"
	ErrorCodeCacheFull        = "CACHE_FULL"
	ErrorCodeTimeout          = "TIMEOUT"
	ErrorCodeConnectionFailed = "CONNECTION_FAILED"
	ErrorCodeNotSupported     = "NOT_SUPPORTED"
	ErrorCodeConfigInvalid    = "CONFIG_INVALID"
	ErrorCodeNotInitialized   = "NOT_INITIALIZED"
	ErrorCodeSerialization    = "SERIALIZATION_FAILED"
	ErrorCodeDeserialization  = "DESERIALIZATION_FAILED"
	ErrorCodeCompression      = "COMPRESSION_FAILED"
	ErrorCodeDecompression    = "DECOMPRESSION_FAILED"
	ErrorCodeInvalidTTL       = "INVALID_TTL"
	ErrorCodeInvalidTag       = "INVALID_TAG"
	ErrorCodeTagNotFound      = "TAG_NOT_FOUND"
	ErrorCodePolicyNotFound   = "POLICY_NOT_FOUND"
	ErrorCodePolicyInvalid    = "POLICY_INVALID"
	ErrorCodeWarmupFailed     = "WARMUP_FAILED"
	ErrorCodeInvalidationFailed = "INVALIDATION_FAILED"
	ErrorCodeMetricsNotAvailable = "METRICS_NOT_AVAILABLE"
	ErrorCodeEventListenerFailed = "EVENT_LISTENER_FAILED"
)

// GetErrorCode 获取错误代码
func GetErrorCode(err error) string {
	if cacheErr, ok := err.(*CacheError); ok {
		if cacheErr.Code != "" {
			return cacheErr.Code
		}
	}
	
	// 根据错误类型返回对应的错误代码
	switch {
	case errors.Is(err, ErrCacheKeyNotFound):
		return ErrorCodeKeyNotFound
	case errors.Is(err, ErrCacheKeyExists):
		return ErrorCodeKeyExists
	case errors.Is(err, ErrCacheKeyInvalid):
		return ErrorCodeKeyInvalid
	case errors.Is(err, ErrCacheValueTooLarge):
		return ErrorCodeValueTooLarge
	case errors.Is(err, ErrCacheFull):
		return ErrorCodeCacheFull
	case errors.Is(err, ErrCacheTimeout):
		return ErrorCodeTimeout
	case errors.Is(err, ErrCacheConnectionFailed):
		return ErrorCodeConnectionFailed
	case errors.Is(err, ErrCacheNotSupported):
		return ErrorCodeNotSupported
	case errors.Is(err, ErrCacheConfigInvalid):
		return ErrorCodeConfigInvalid
	case errors.Is(err, ErrCacheNotInitialized):
		return ErrorCodeNotInitialized
	case errors.Is(err, ErrCacheSerializationFailed):
		return ErrorCodeSerialization
	case errors.Is(err, ErrCacheDeserializationFailed):
		return ErrorCodeDeserialization
	case errors.Is(err, ErrCacheCompressionFailed):
		return ErrorCodeCompression
	case errors.Is(err, ErrCacheDecompressionFailed):
		return ErrorCodeDecompression
	case errors.Is(err, ErrCacheInvalidTTL):
		return ErrorCodeInvalidTTL
	case errors.Is(err, ErrCacheInvalidTag):
		return ErrorCodeInvalidTag
	case errors.Is(err, ErrCacheTagNotFound):
		return ErrorCodeTagNotFound
	case errors.Is(err, ErrCachePolicyNotFound):
		return ErrorCodePolicyNotFound
	case errors.Is(err, ErrCachePolicyInvalid):
		return ErrorCodePolicyInvalid
	case errors.Is(err, ErrCacheWarmupFailed):
		return ErrorCodeWarmupFailed
	case errors.Is(err, ErrCacheInvalidationFailed):
		return ErrorCodeInvalidationFailed
	case errors.Is(err, ErrCacheMetricsNotAvailable):
		return ErrorCodeMetricsNotAvailable
	case errors.Is(err, ErrCacheEventListenerFailed):
		return ErrorCodeEventListenerFailed
	default:
		return "UNKNOWN"
	}
}
