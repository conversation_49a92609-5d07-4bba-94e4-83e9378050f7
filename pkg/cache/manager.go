package cache

import (
	"context"
	"fmt"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// Manager 缓存管理器实现
type Manager struct {
	config    config.CacheConfig
	logger    *telemetry.Logger
	
	// 缓存实例
	caches    map[string]Cache
	cacheMux  sync.RWMutex
	
	// 默认缓存
	defaultCache Cache
	
	// 组件
	keyGenerator CacheKeyGenerator
	policies     map[string]CachePolicy
	warmer       CacheWarmer
	invalidator  CacheInvalidator
	metrics      CacheMetrics
	
	// 生命周期
	startTime time.Time
	running   bool
	stopChan  chan struct{}
	wg        sync.WaitGroup
}

// NewManager 创建新的缓存管理器
func NewManager(cfg config.CacheConfig, logger *telemetry.Logger) (*Manager, error) {
	if !cfg.Enabled {
		return &Manager{
			config:  cfg,
			logger:  logger.With("component", "cache-manager"),
			caches:  make(map[string]Cache),
			running: false,
		}, nil
	}
	
	manager := &Manager{
		config:    cfg,
		logger:    logger.With("component", "cache-manager"),
		caches:    make(map[string]Cache),
		policies:  make(map[string]CachePolicy),
		startTime: time.Now(),
		stopChan:  make(chan struct{}),
	}
	
	// 创建默认缓存
	defaultCache, err := manager.createCacheInstance("default", cfg)
	if err != nil {
		return nil, fmt.Errorf("创建默认缓存失败: %w", err)
	}
	
	manager.defaultCache = defaultCache
	manager.caches["default"] = defaultCache
	
	// 初始化组件
	if err := manager.initializeComponents(); err != nil {
		return nil, fmt.Errorf("初始化缓存组件失败: %w", err)
	}
	
	manager.logger.Info("缓存管理器初始化完成",
		"type", cfg.Type,
		"default_ttl", cfg.DefaultTTL,
		"policies_count", len(cfg.Policies))
	
	return manager, nil
}

// Start 启动缓存管理器
func (m *Manager) Start() error {
	if !m.config.Enabled {
		m.logger.Info("缓存未启用")
		return nil
	}
	
	if m.running {
		return nil
	}
	
	m.logger.Info("启动缓存管理器")
	
	// 启动预热
	if m.config.Warmup.Enabled && m.warmer != nil {
		go m.startWarmup()
	}
	
	// 启动监控
	m.wg.Add(1)
	go m.monitoringLoop()
	
	m.running = true
	m.logger.Info("缓存管理器启动完成")
	
	return nil
}

// Stop 停止缓存管理器
func (m *Manager) Stop() error {
	if !m.running {
		return nil
	}
	
	m.logger.Info("停止缓存管理器")
	
	close(m.stopChan)
	m.wg.Wait()
	
	// 关闭所有缓存
	m.cacheMux.Lock()
	for name, cache := range m.caches {
		if err := cache.Close(); err != nil {
			m.logger.Error("关闭缓存失败", "name", name, "error", err)
		}
	}
	m.cacheMux.Unlock()
	
	m.running = false
	m.logger.Info("缓存管理器已停止")
	
	return nil
}

// GetCache 获取缓存实例
func (m *Manager) GetCache(name string) (Cache, error) {
	if !m.config.Enabled {
		return nil, ErrCacheNotInitialized
	}
	
	if name == "" || name == "default" {
		return m.defaultCache, nil
	}
	
	m.cacheMux.RLock()
	cache, exists := m.caches[name]
	m.cacheMux.RUnlock()
	
	if !exists {
		return nil, ErrCacheNotFound
	}
	
	return cache, nil
}

// CreateCache 创建缓存实例
func (m *Manager) CreateCache(name string, config interface{}) (Cache, error) {
	if !m.config.Enabled {
		return nil, ErrCacheNotInitialized
	}
	
	m.cacheMux.Lock()
	defer m.cacheMux.Unlock()
	
	if _, exists := m.caches[name]; exists {
		return nil, ErrCacheAlreadyExists
	}
	
	// 简化实现，暂时只支持默认配置
	cache, err := m.createCacheInstance(name, m.config)
	
	if err != nil {
		return nil, err
	}
	
	m.caches[name] = cache
	m.logger.Info("创建缓存实例", "name", name)
	
	return cache, nil
}

// DeleteCache 删除缓存实例
func (m *Manager) DeleteCache(name string) error {
	if name == "default" {
		return fmt.Errorf("不能删除默认缓存")
	}
	
	m.cacheMux.Lock()
	defer m.cacheMux.Unlock()
	
	cache, exists := m.caches[name]
	if !exists {
		return ErrCacheNotFound
	}
	
	if err := cache.Close(); err != nil {
		m.logger.Error("关闭缓存失败", "name", name, "error", err)
	}
	
	delete(m.caches, name)
	m.logger.Info("删除缓存实例", "name", name)
	
	return nil
}

// ListCaches 列出所有缓存
func (m *Manager) ListCaches() []string {
	m.cacheMux.RLock()
	defer m.cacheMux.RUnlock()
	
	names := make([]string, 0, len(m.caches))
	for name := range m.caches {
		names = append(names, name)
	}
	
	return names
}

// GlobalStats 获取全局统计
func (m *Manager) GlobalStats() (*GlobalCacheStats, error) {
	if !m.config.Enabled {
		return nil, ErrCacheNotInitialized
	}
	
	m.cacheMux.RLock()
	defer m.cacheMux.RUnlock()
	
	globalStats := &GlobalCacheStats{
		TotalCaches: len(m.caches),
		CacheStats:  make(map[string]*CacheStats),
		Uptime:      time.Since(m.startTime),
	}
	
	ctx := context.Background()
	for name, cache := range m.caches {
		stats, err := cache.Stats(ctx)
		if err != nil {
			m.logger.Error("获取缓存统计失败", "name", name, "error", err)
			continue
		}
		
		globalStats.CacheStats[name] = stats
		globalStats.TotalHits += stats.Hits
		globalStats.TotalMisses += stats.Misses
		globalStats.TotalSize += stats.TotalSize
	}
	
	return globalStats, nil
}

// WarmupCache 预热缓存
func (m *Manager) WarmupCache(ctx context.Context, name string, urls []string) error {
	if !m.config.Enabled {
		return ErrCacheNotInitialized
	}
	
	if m.warmer == nil {
		return ErrCacheNotSupported
	}
	
	warmupConfig := &WarmupConfig{
		URLs:        urls,
		Concurrency: m.config.Warmup.Concurrency,
		Timeout:     30 * time.Second,
	}
	
	if m.config.Warmup.Timeout != "" {
		if timeout, err := time.ParseDuration(m.config.Warmup.Timeout); err == nil {
			warmupConfig.Timeout = timeout
		}
	}
	
	return m.warmer.WarmupCache(ctx, warmupConfig)
}

// FlushAll 清空所有缓存
func (m *Manager) FlushAll(ctx context.Context) error {
	if !m.config.Enabled {
		return ErrCacheNotInitialized
	}
	
	m.cacheMux.RLock()
	defer m.cacheMux.RUnlock()
	
	for name, cache := range m.caches {
		// 获取所有键并删除
		keys, err := cache.Keys(ctx, "*")
		if err != nil {
			m.logger.Error("获取缓存键失败", "name", name, "error", err)
			continue
		}
		
		if len(keys) > 0 {
			if err := cache.DeleteMulti(ctx, keys); err != nil {
				m.logger.Error("清空缓存失败", "name", name, "error", err)
			} else {
				m.logger.Info("清空缓存完成", "name", name, "keys_count", len(keys))
			}
		}
	}
	
	return nil
}

// GetKeyGenerator 获取键生成器
func (m *Manager) GetKeyGenerator() CacheKeyGenerator {
	return m.keyGenerator
}

// GetPolicy 获取缓存策略
func (m *Manager) GetPolicy(name string) (CachePolicy, error) {
	policy, exists := m.policies[name]
	if !exists {
		return nil, ErrCachePolicyNotFound
	}
	return policy, nil
}

// GetWarmer 获取预热器
func (m *Manager) GetWarmer() CacheWarmer {
	return m.warmer
}

// GetInvalidator 获取失效器
func (m *Manager) GetInvalidator() CacheInvalidator {
	return m.invalidator
}

// GetMetrics 获取指标收集器
func (m *Manager) GetMetrics() CacheMetrics {
	return m.metrics
}

// IsRunning 检查是否正在运行
func (m *Manager) IsRunning() bool {
	return m.running
}

// 私有方法

// createCacheInstance 创建缓存实例
func (m *Manager) createCacheInstance(name string, cfg config.CacheConfig) (Cache, error) {
	switch cfg.Type {
	case "memory":
		return NewMemoryCache(cfg.Memory, m.logger)
	case "redis":
		return NewRedisCache(cfg.Redis, m.logger)
	case "multi_level":
		return m.createMultiLevelCache(cfg.MultiLevel)
	default:
		return nil, fmt.Errorf("不支持的缓存类型: %s", cfg.Type)
	}
}

// createMultiLevelCache 创建多级缓存
func (m *Manager) createMultiLevelCache(cfg config.CacheMultiLevelConfig) (Cache, error) {
	// 多级缓存的实现将在后续添加
	return nil, fmt.Errorf("多级缓存暂未实现")
}

// initializeComponents 初始化组件
func (m *Manager) initializeComponents() error {
	// 初始化键生成器
	m.keyGenerator = NewDefaultKeyGenerator(m.config.KeyGenerator, m.logger)

	// 初始化策略
	for _, policyConfig := range m.config.Policies {
		policy := NewDefaultPolicy(policyConfig, m.logger)
		m.policies[policyConfig.Name] = policy
	}

	// 初始化预热器
	if m.config.Warmup.Enabled {
		m.warmer = NewDefaultWarmer(m.config.Warmup, m.defaultCache, m.logger)
	}

	// 初始化失效器
	m.invalidator = NewDefaultInvalidator(m.defaultCache, m.logger)

	// 初始化指标收集器
	m.metrics = NewDefaultMetrics(m.logger)

	return nil
}

// startWarmup 启动预热
func (m *Manager) startWarmup() {
	if m.warmer == nil {
		return
	}

	m.logger.Info("开始缓存预热")

	ctx := context.Background()
	warmupConfig := &WarmupConfig{
		URLs:        m.config.Warmup.URLs,
		Concurrency: m.config.Warmup.Concurrency,
		Timeout:     30 * time.Second,
	}

	if m.config.Warmup.Timeout != "" {
		if timeout, err := time.ParseDuration(m.config.Warmup.Timeout); err == nil {
			warmupConfig.Timeout = timeout
		}
	}

	if err := m.warmer.WarmupCache(ctx, warmupConfig); err != nil {
		m.logger.Error("缓存预热失败", "error", err)
	} else {
		m.logger.Info("缓存预热完成")
	}
}

// monitoringLoop 监控循环
func (m *Manager) monitoringLoop() {
	defer m.wg.Done()

	ticker := time.NewTicker(30 * time.Second) // 每30秒收集一次统计
	defer ticker.Stop()

	for {
		select {
		case <-m.stopChan:
			return
		case <-ticker.C:
			m.collectMetrics()
		}
	}
}

// collectMetrics 收集指标
func (m *Manager) collectMetrics() {
	if m.metrics == nil {
		return
	}

	ctx := context.Background()

	m.cacheMux.RLock()
	for name, cache := range m.caches {
		stats, err := cache.Stats(ctx)
		if err != nil {
			m.logger.Error("获取缓存统计失败", "name", name, "error", err)
			continue
		}

		// 记录指标
		m.recordCacheMetrics(name, stats)
	}
	m.cacheMux.RUnlock()
}

// recordCacheMetrics 记录缓存指标
func (m *Manager) recordCacheMetrics(cacheName string, stats *CacheStats) {
	// 这里可以将指标发送到监控系统
	m.logger.Debug("缓存统计",
		"cache", cacheName,
		"hits", stats.Hits,
		"misses", stats.Misses,
		"hit_ratio", stats.HitRatio,
		"item_count", stats.ItemCount,
		"used_memory", stats.UsedMemory)
}
