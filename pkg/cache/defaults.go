package cache

import (
	"context"
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"fmt"
	"hash"
	"net/http"
	"sort"
	"strings"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// DefaultKeyGenerator 默认键生成器
type DefaultKeyGenerator struct {
	config config.CacheKeyConfig
	logger *telemetry.Logger
}

// NewDefaultKeyGenerator 创建默认键生成器
func NewDefaultKeyGenerator(cfg config.CacheKeyConfig, logger *telemetry.Logger) *DefaultKeyGenerator {
	return &DefaultKeyGenerator{
		config: cfg,
		logger: logger.With("component", "cache-key-generator"),
	}
}

// GenerateKey 生成缓存键
func (kg *DefaultKeyGenerator) GenerateKey(ctx context.Context, request *CacheKeyRequest) (string, error) {
	var keyParts []string
	
	// 添加基本路径
	if request.Path != "" {
		keyParts = append(keyParts, request.Path)
	}
	
	// 添加方法
	if request.Method != "" {
		keyParts = append(keyParts, request.Method)
	}
	
	// 添加查询参数
	if kg.config.IncludeQueryParams && len(request.Query) > 0 {
		queryParts := kg.buildQueryString(request.Query)
		if queryParts != "" {
			keyParts = append(keyParts, queryParts)
		}
	}
	
	// 添加头部
	if len(kg.config.IncludeHeaders) > 0 && len(request.Headers) > 0 {
		headerParts := kg.buildHeaderString(request.Headers)
		if headerParts != "" {
			keyParts = append(keyParts, headerParts)
		}
	}
	
	// 添加用户ID
	if request.UserID != "" {
		keyParts = append(keyParts, "user:"+request.UserID)
	}
	
	// 添加服务名
	if request.ServiceName != "" {
		keyParts = append(keyParts, "service:"+request.ServiceName)
	}
	
	// 添加版本
	if request.Version != "" {
		keyParts = append(keyParts, "version:"+request.Version)
	}
	
	// 构建最终键
	key := strings.Join(keyParts, "|")
	
	// 使用模板（如果配置了）
	if kg.config.Template != "" {
		key = kg.applyTemplate(kg.config.Template, request)
	}
	
	// 应用哈希（如果配置了）
	if kg.config.HashAlgorithm != "" {
		hashedKey, err := kg.hashKey(key, kg.config.HashAlgorithm)
		if err != nil {
			return "", err
		}
		key = hashedKey
	}
	
	return key, nil
}

// GenerateTags 生成标签
func (kg *DefaultKeyGenerator) GenerateTags(ctx context.Context, request *CacheKeyRequest) ([]string, error) {
	var tags []string
	
	// 基于路径的标签
	if request.Path != "" {
		pathParts := strings.Split(strings.Trim(request.Path, "/"), "/")
		if len(pathParts) > 0 {
			tags = append(tags, "path:"+pathParts[0])
		}
	}
	
	// 基于方法的标签
	if request.Method != "" {
		tags = append(tags, "method:"+request.Method)
	}
	
	// 基于服务的标签
	if request.ServiceName != "" {
		tags = append(tags, "service:"+request.ServiceName)
	}
	
	// 基于版本的标签
	if request.Version != "" {
		tags = append(tags, "version:"+request.Version)
	}
	
	// 基于用户的标签
	if request.UserID != "" {
		tags = append(tags, "user:"+request.UserID)
	}
	
	return tags, nil
}

// ValidateKey 验证键
func (kg *DefaultKeyGenerator) ValidateKey(key string) error {
	if key == "" {
		return ErrCacheKeyInvalid
	}
	
	// 检查键长度
	if len(key) > 250 { // Redis键长度限制
		return fmt.Errorf("缓存键过长: %d > 250", len(key))
	}
	
	// 检查非法字符
	if strings.ContainsAny(key, " \t\n\r") {
		return fmt.Errorf("缓存键包含非法字符")
	}
	
	return nil
}

// buildQueryString 构建查询字符串
func (kg *DefaultKeyGenerator) buildQueryString(query map[string]string) string {
	if len(query) == 0 {
		return ""
	}
	
	// 排序键以确保一致性
	keys := make([]string, 0, len(query))
	for k := range query {
		// 跳过排除的参数
		if kg.isExcludedParam(k) {
			continue
		}
		keys = append(keys, k)
	}
	sort.Strings(keys)
	
	var parts []string
	for _, k := range keys {
		parts = append(parts, k+"="+query[k])
	}
	
	return strings.Join(parts, "&")
}

// buildHeaderString 构建头部字符串
func (kg *DefaultKeyGenerator) buildHeaderString(headers map[string]string) string {
	if len(headers) == 0 {
		return ""
	}
	
	var parts []string
	for _, headerName := range kg.config.IncludeHeaders {
		if value, exists := headers[headerName]; exists && !kg.isExcludedHeader(headerName) {
			parts = append(parts, headerName+":"+value)
		}
	}
	
	sort.Strings(parts)
	return strings.Join(parts, "|")
}

// isExcludedParam 检查是否为排除的参数
func (kg *DefaultKeyGenerator) isExcludedParam(param string) bool {
	for _, excluded := range kg.config.ExcludeParams {
		if param == excluded {
			return true
		}
	}
	return false
}

// isExcludedHeader 检查是否为排除的头部
func (kg *DefaultKeyGenerator) isExcludedHeader(header string) bool {
	for _, excluded := range kg.config.ExcludeHeaders {
		if strings.EqualFold(header, excluded) {
			return true
		}
	}
	return false
}

// applyTemplate 应用模板
func (kg *DefaultKeyGenerator) applyTemplate(template string, request *CacheKeyRequest) string {
	// 简单的模板替换
	result := template
	result = strings.ReplaceAll(result, "{method}", request.Method)
	result = strings.ReplaceAll(result, "{path}", request.Path)
	result = strings.ReplaceAll(result, "{user_id}", request.UserID)
	result = strings.ReplaceAll(result, "{service_name}", request.ServiceName)
	result = strings.ReplaceAll(result, "{version}", request.Version)
	
	return result
}

// hashKey 哈希键
func (kg *DefaultKeyGenerator) hashKey(key, algorithm string) (string, error) {
	var h hash.Hash
	
	switch strings.ToLower(algorithm) {
	case "md5":
		h = md5.New()
	case "sha1":
		h = sha1.New()
	case "sha256":
		h = sha256.New()
	default:
		return "", fmt.Errorf("不支持的哈希算法: %s", algorithm)
	}
	
	h.Write([]byte(key))
	return fmt.Sprintf("%x", h.Sum(nil)), nil
}

// DefaultPolicy 默认缓存策略
type DefaultPolicy struct {
	config config.CachePolicy
	logger *telemetry.Logger
}

// NewDefaultPolicy 创建默认策略
func NewDefaultPolicy(cfg config.CachePolicy, logger *telemetry.Logger) *DefaultPolicy {
	return &DefaultPolicy{
		config: cfg,
		logger: logger.With("component", "cache-policy", "policy", cfg.Name),
	}
}

// ShouldCache 是否应该缓存
func (p *DefaultPolicy) ShouldCache(ctx context.Context, request *CachePolicyRequest) (bool, error) {
	if !p.config.Enabled {
		return false, nil
	}
	
	// 检查路径匹配
	if !p.matchesPath(request.Path) {
		return false, nil
	}
	
	// 检查方法匹配
	if !p.matchesMethod(request.Method) {
		return false, nil
	}
	
	// 检查状态码
	if request.StatusCode >= 400 {
		return false, nil // 不缓存错误响应
	}
	
	// 检查条件
	for _, condition := range p.config.Conditions {
		if !p.evaluateCondition(condition, request) {
			return false, nil
		}
	}
	
	return true, nil
}

// GetTTL 获取TTL
func (p *DefaultPolicy) GetTTL(ctx context.Context, request *CachePolicyRequest) (time.Duration, error) {
	if p.config.TTL != "" {
		return time.ParseDuration(p.config.TTL)
	}
	
	// 默认TTL
	return 5 * time.Minute, nil
}

// GetTags 获取标签
func (p *DefaultPolicy) GetTags(ctx context.Context, request *CachePolicyRequest) ([]string, error) {
	var tags []string
	
	// 添加策略标签
	tags = append(tags, "policy:"+p.config.Name)
	
	// 基于VaryBy生成标签
	for _, varyBy := range p.config.VaryBy {
		switch varyBy {
		case "user_id":
			if request.UserID != "" {
				tags = append(tags, "user:"+request.UserID)
			}
		case "service":
			if request.ServiceName != "" {
				tags = append(tags, "service:"+request.ServiceName)
			}
		case "path":
			tags = append(tags, "path:"+request.Path)
		case "method":
			tags = append(tags, "method:"+request.Method)
		}
	}
	
	return tags, nil
}

// ShouldInvalidate 是否应该失效
func (p *DefaultPolicy) ShouldInvalidate(ctx context.Context, request *CachePolicyRequest) (bool, error) {
	// 写操作应该失效缓存
	if request.Method == http.MethodPost || 
	   request.Method == http.MethodPut || 
	   request.Method == http.MethodDelete || 
	   request.Method == http.MethodPatch {
		return true, nil
	}
	
	return false, nil
}

// matchesPath 检查路径匹配
func (p *DefaultPolicy) matchesPath(path string) bool {
	if len(p.config.Paths) == 0 {
		return true
	}
	
	for _, pattern := range p.config.Paths {
		if p.matchPattern(pattern, path) {
			return true
		}
	}
	
	return false
}

// matchesMethod 检查方法匹配
func (p *DefaultPolicy) matchesMethod(method string) bool {
	if len(p.config.Methods) == 0 {
		return true
	}
	
	for _, m := range p.config.Methods {
		if strings.EqualFold(m, method) || m == "*" {
			return true
		}
	}
	
	return false
}

// matchPattern 模式匹配
func (p *DefaultPolicy) matchPattern(pattern, text string) bool {
	// 简单的通配符匹配
	if pattern == "*" {
		return true
	}
	
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(text, prefix)
	}
	
	if strings.HasPrefix(pattern, "*") {
		suffix := strings.TrimPrefix(pattern, "*")
		return strings.HasSuffix(text, suffix)
	}
	
	return pattern == text
}

// evaluateCondition 评估条件
func (p *DefaultPolicy) evaluateCondition(condition config.CacheCondition, request *CachePolicyRequest) bool {
	var value string
	
	switch condition.Type {
	case "header":
		value = request.Headers[condition.Key]
	case "query":
		value = request.Query[condition.Key]
	case "status_code":
		value = fmt.Sprintf("%d", request.StatusCode)
	case "content_type":
		value = request.ResponseHeaders["Content-Type"]
	default:
		return false
	}
	
	switch condition.Operator {
	case "eq", "":
		return value == condition.Value
	case "ne":
		return value != condition.Value
	case "contains":
		return strings.Contains(value, condition.Value)
	case "regex":
		// 简化实现，实际应该使用正则表达式
		return strings.Contains(value, condition.Value)
	default:
		return false
	}
}

// DefaultWarmer 默认缓存预热器
type DefaultWarmer struct {
	config config.CacheWarmupConfig
	cache  Cache
	logger *telemetry.Logger

	status *WarmupStatus
	mutex  sync.RWMutex
}

// NewDefaultWarmer 创建默认预热器
func NewDefaultWarmer(cfg config.CacheWarmupConfig, cache Cache, logger *telemetry.Logger) *DefaultWarmer {
	return &DefaultWarmer{
		config: cfg,
		cache:  cache,
		logger: logger.With("component", "cache-warmer"),
		status: &WarmupStatus{},
	}
}

// WarmupCache 预热缓存
func (w *DefaultWarmer) WarmupCache(ctx context.Context, config *WarmupConfig) error {
	w.mutex.Lock()
	if w.status.IsRunning {
		w.mutex.Unlock()
		return fmt.Errorf("预热已在进行中")
	}

	w.status = &WarmupStatus{
		IsRunning:     true,
		TotalURLs:     len(config.URLs),
		CompletedURLs: 0,
		FailedURLs:    0,
		StartTime:     time.Now(),
		Errors:        make([]string, 0),
	}
	w.mutex.Unlock()

	defer func() {
		w.mutex.Lock()
		w.status.IsRunning = false
		w.status.Duration = time.Since(w.status.StartTime)
		w.mutex.Unlock()
	}()

	// 使用工作池进行并发预热
	semaphore := make(chan struct{}, config.Concurrency)
	var wg sync.WaitGroup

	for _, url := range config.URLs {
		wg.Add(1)
		go func(url string) {
			defer wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			if err := w.WarmupURL(ctx, url); err != nil {
				w.mutex.Lock()
				w.status.FailedURLs++
				w.status.Errors = append(w.status.Errors, fmt.Sprintf("%s: %v", url, err))
				w.mutex.Unlock()
				w.logger.Error("预热URL失败", "url", url, "error", err)
			} else {
				w.mutex.Lock()
				w.status.CompletedURLs++
				w.mutex.Unlock()
			}
		}(url)
	}

	wg.Wait()

	w.logger.Info("缓存预热完成",
		"total", w.status.TotalURLs,
		"completed", w.status.CompletedURLs,
		"failed", w.status.FailedURLs,
		"duration", w.status.Duration)

	return nil
}

// WarmupURL 预热单个URL
func (w *DefaultWarmer) WarmupURL(ctx context.Context, url string) error {
	// 这里应该发起HTTP请求来预热缓存
	// 简化实现，实际应该集成HTTP客户端
	w.logger.Debug("预热URL", "url", url)

	// 模拟预热过程
	time.Sleep(100 * time.Millisecond)

	return nil
}

// WarmupURLs 预热多个URL
func (w *DefaultWarmer) WarmupURLs(ctx context.Context, urls []string) error {
	config := &WarmupConfig{
		URLs:        urls,
		Concurrency: w.config.Concurrency,
		Timeout:     30 * time.Second,
	}

	return w.WarmupCache(ctx, config)
}

// GetWarmupStatus 获取预热状态
func (w *DefaultWarmer) GetWarmupStatus() (*WarmupStatus, error) {
	w.mutex.RLock()
	defer w.mutex.RUnlock()

	// 返回状态副本
	status := &WarmupStatus{
		IsRunning:     w.status.IsRunning,
		TotalURLs:     w.status.TotalURLs,
		CompletedURLs: w.status.CompletedURLs,
		FailedURLs:    w.status.FailedURLs,
		StartTime:     w.status.StartTime,
		Duration:      w.status.Duration,
		Errors:        make([]string, len(w.status.Errors)),
	}
	copy(status.Errors, w.status.Errors)

	return status, nil
}

// StopWarmup 停止预热
func (w *DefaultWarmer) StopWarmup() error {
	w.mutex.Lock()
	defer w.mutex.Unlock()

	if !w.status.IsRunning {
		return fmt.Errorf("预热未在进行中")
	}

	// 简化实现，实际应该有停止机制
	w.status.IsRunning = false

	return nil
}

// DefaultInvalidator 默认缓存失效器
type DefaultInvalidator struct {
	cache  Cache
	logger *telemetry.Logger
}

// NewDefaultInvalidator 创建默认失效器
func NewDefaultInvalidator(cache Cache, logger *telemetry.Logger) *DefaultInvalidator {
	return &DefaultInvalidator{
		cache:  cache,
		logger: logger.With("component", "cache-invalidator"),
	}
}

// InvalidateKey 按键失效
func (i *DefaultInvalidator) InvalidateKey(ctx context.Context, key string) error {
	return i.cache.Delete(ctx, key)
}

// InvalidatePattern 按模式失效
func (i *DefaultInvalidator) InvalidatePattern(ctx context.Context, pattern string) error {
	return i.cache.DeleteByPattern(ctx, pattern)
}

// InvalidateTag 按标签失效
func (i *DefaultInvalidator) InvalidateTag(ctx context.Context, tag string) error {
	return i.cache.InvalidateByTag(ctx, tag)
}

// InvalidateByCondition 按条件失效
func (i *DefaultInvalidator) InvalidateByCondition(ctx context.Context, condition *InvalidationCondition) error {
	var keysToDelete []string

	// 获取所有键
	allKeys, err := i.cache.Keys(ctx, "*")
	if err != nil {
		return err
	}

	// 过滤符合条件的键
	for _, key := range allKeys {
		if i.matchesCondition(key, condition) {
			keysToDelete = append(keysToDelete, key)
		}
	}

	// 批量删除
	if len(keysToDelete) > 0 {
		return i.cache.DeleteMulti(ctx, keysToDelete)
	}

	return nil
}

// GetInvalidationStatus 获取失效状态
func (i *DefaultInvalidator) GetInvalidationStatus() (*InvalidationStatus, error) {
	// 简化实现
	return &InvalidationStatus{
		TotalInvalidated: 0,
		Duration:         0,
		Errors:           nil,
	}, nil
}

// matchesCondition 检查是否匹配条件
func (i *DefaultInvalidator) matchesCondition(key string, condition *InvalidationCondition) bool {
	// 检查键模式
	if condition.KeyPattern != "" {
		if !i.matchPattern(condition.KeyPattern, key) {
			return false
		}
	}

	// 其他条件检查可以在这里添加

	return true
}

// matchPattern 模式匹配
func (i *DefaultInvalidator) matchPattern(pattern, text string) bool {
	if pattern == "*" {
		return true
	}

	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(text, prefix)
	}

	if strings.HasPrefix(pattern, "*") {
		suffix := strings.TrimPrefix(pattern, "*")
		return strings.HasSuffix(text, suffix)
	}

	return pattern == text
}

// DefaultMetrics 默认指标收集器
type DefaultMetrics struct {
	logger  *telemetry.Logger
	metrics map[string]interface{}
	mutex   sync.RWMutex
}

// NewDefaultMetrics 创建默认指标收集器
func NewDefaultMetrics(logger *telemetry.Logger) *DefaultMetrics {
	return &DefaultMetrics{
		logger:  logger.With("component", "cache-metrics"),
		metrics: make(map[string]interface{}),
	}
}

// RecordHit 记录缓存命中
func (m *DefaultMetrics) RecordHit(cacheName string, key string, duration time.Duration) {
	m.logger.Debug("缓存命中", "cache", cacheName, "key", key, "duration", duration)
}

// RecordMiss 记录缓存未命中
func (m *DefaultMetrics) RecordMiss(cacheName string, key string, duration time.Duration) {
	m.logger.Debug("缓存未命中", "cache", cacheName, "key", key, "duration", duration)
}

// RecordSet 记录缓存设置
func (m *DefaultMetrics) RecordSet(cacheName string, key string, size int64, duration time.Duration) {
	m.logger.Debug("缓存设置", "cache", cacheName, "key", key, "size", size, "duration", duration)
}

// RecordDelete 记录缓存删除
func (m *DefaultMetrics) RecordDelete(cacheName string, key string, duration time.Duration) {
	m.logger.Debug("缓存删除", "cache", cacheName, "key", key, "duration", duration)
}

// RecordError 记录缓存错误
func (m *DefaultMetrics) RecordError(cacheName string, operation string, err error) {
	m.logger.Error("缓存错误", "cache", cacheName, "operation", operation, "error", err)
}

// GetMetrics 获取指标
func (m *DefaultMetrics) GetMetrics() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make(map[string]interface{})
	for k, v := range m.metrics {
		result[k] = v
	}

	return result
}
