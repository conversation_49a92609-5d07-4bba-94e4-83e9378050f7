package cache

import (
	"context"
	"time"
)

// Cache 高级缓存接口
type Cache interface {
	// 基本操作
	Get(ctx context.Context, key string) ([]byte, error)
	Set(ctx context.Context, key string, value []byte, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	
	// 批量操作
	GetMulti(ctx context.Context, keys []string) (map[string][]byte, error)
	SetMulti(ctx context.Context, items map[string]CacheItem) error
	DeleteMulti(ctx context.Context, keys []string) error
	
	// 高级操作
	GetWithTTL(ctx context.Context, key string) ([]byte, time.Duration, error)
	SetNX(ctx context.Context, key string, value []byte, ttl time.Duration) (bool, error)
	Increment(ctx context.Context, key string, delta int64) (int64, error)
	Decrement(ctx context.Context, key string, delta int64) (int64, error)
	
	// 模式操作
	Keys(ctx context.Context, pattern string) ([]string, error)
	DeleteByPattern(ctx context.Context, pattern string) error
	
	// 标签操作
	SetWithTags(ctx context.Context, key string, value []byte, ttl time.Duration, tags []string) error
	InvalidateByTag(ctx context.Context, tag string) error
	
	// 统计信息
	Stats(ctx context.Context) (*CacheStats, error)
	
	// 生命周期
	Close() error
	Ping(ctx context.Context) error
}

// CacheItem 缓存项
type CacheItem struct {
	Key   string
	Value []byte
	TTL   time.Duration
	Tags  []string
}

// CacheStats 缓存统计信息
type CacheStats struct {
	// 基本统计
	Hits        int64 `json:"hits"`
	Misses      int64 `json:"misses"`
	Sets        int64 `json:"sets"`
	Deletes     int64 `json:"deletes"`
	Evictions   int64 `json:"evictions"`
	
	// 性能统计
	HitRatio    float64 `json:"hit_ratio"`
	AvgGetTime  float64 `json:"avg_get_time_ms"`
	AvgSetTime  float64 `json:"avg_set_time_ms"`
	
	// 容量统计
	ItemCount   int64 `json:"item_count"`
	TotalSize   int64 `json:"total_size_bytes"`
	MaxSize     int64 `json:"max_size_bytes"`
	UsedMemory  int64 `json:"used_memory_bytes"`
	
	// 时间统计
	Uptime      time.Duration `json:"uptime"`
	LastAccess  time.Time     `json:"last_access"`
	
	// 错误统计
	Errors      int64 `json:"errors"`
	Timeouts    int64 `json:"timeouts"`
	
	// 扩展统计
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// CacheManager 缓存管理器接口
type CacheManager interface {
	// 获取缓存实例
	GetCache(name string) (Cache, error)
	
	// 创建缓存实例
	CreateCache(name string, config interface{}) (Cache, error)
	
	// 删除缓存实例
	DeleteCache(name string) error
	
	// 列出所有缓存
	ListCaches() []string
	
	// 全局统计
	GlobalStats() (*GlobalCacheStats, error)
	
	// 预热缓存
	WarmupCache(ctx context.Context, name string, urls []string) error
	
	// 清空所有缓存
	FlushAll(ctx context.Context) error
	
	// 生命周期
	Start() error
	Stop() error
}

// GlobalCacheStats 全局缓存统计
type GlobalCacheStats struct {
	TotalCaches int                    `json:"total_caches"`
	CacheStats  map[string]*CacheStats `json:"cache_stats"`
	TotalHits   int64                  `json:"total_hits"`
	TotalMisses int64                  `json:"total_misses"`
	TotalSize   int64                  `json:"total_size_bytes"`
	Uptime      time.Duration          `json:"uptime"`
}

// CacheKeyGenerator 缓存键生成器接口
type CacheKeyGenerator interface {
	// 生成缓存键
	GenerateKey(ctx context.Context, request *CacheKeyRequest) (string, error)
	
	// 生成标签
	GenerateTags(ctx context.Context, request *CacheKeyRequest) ([]string, error)
	
	// 验证键
	ValidateKey(key string) error
}

// CacheKeyRequest 缓存键生成请求
type CacheKeyRequest struct {
	// HTTP相关
	Method      string            `json:"method"`
	Path        string            `json:"path"`
	Query       map[string]string `json:"query"`
	Headers     map[string]string `json:"headers"`
	Body        []byte            `json:"body,omitempty"`
	
	// 用户相关
	UserID      string            `json:"user_id,omitempty"`
	SessionID   string            `json:"session_id,omitempty"`
	
	// 服务相关
	ServiceName string            `json:"service_name,omitempty"`
	Version     string            `json:"version,omitempty"`
	
	// 自定义属性
	Attributes  map[string]interface{} `json:"attributes,omitempty"`
}

// CachePolicy 缓存策略接口
type CachePolicy interface {
	// 是否应该缓存
	ShouldCache(ctx context.Context, request *CachePolicyRequest) (bool, error)
	
	// 获取TTL
	GetTTL(ctx context.Context, request *CachePolicyRequest) (time.Duration, error)
	
	// 获取标签
	GetTags(ctx context.Context, request *CachePolicyRequest) ([]string, error)
	
	// 是否应该失效
	ShouldInvalidate(ctx context.Context, request *CachePolicyRequest) (bool, error)
}

// CachePolicyRequest 缓存策略请求
type CachePolicyRequest struct {
	// 请求信息
	Method      string            `json:"method"`
	Path        string            `json:"path"`
	Headers     map[string]string `json:"headers"`
	Query       map[string]string `json:"query"`
	
	// 响应信息
	StatusCode  int               `json:"status_code"`
	ResponseHeaders map[string]string `json:"response_headers"`
	ResponseSize    int64         `json:"response_size"`
	
	// 上下文信息
	UserID      string            `json:"user_id,omitempty"`
	ServiceName string            `json:"service_name,omitempty"`
	
	// 自定义属性
	Attributes  map[string]interface{} `json:"attributes,omitempty"`
}

// CacheWarmer 缓存预热器接口
type CacheWarmer interface {
	// 预热缓存
	WarmupCache(ctx context.Context, config *WarmupConfig) error
	
	// 预热单个URL
	WarmupURL(ctx context.Context, url string) error
	
	// 预热多个URL
	WarmupURLs(ctx context.Context, urls []string) error
	
	// 获取预热状态
	GetWarmupStatus() (*WarmupStatus, error)
	
	// 停止预热
	StopWarmup() error
}

// WarmupConfig 预热配置
type WarmupConfig struct {
	URLs        []string      `json:"urls"`
	Concurrency int           `json:"concurrency"`
	Timeout     time.Duration `json:"timeout"`
	Headers     map[string]string `json:"headers,omitempty"`
}

// WarmupStatus 预热状态
type WarmupStatus struct {
	IsRunning     bool          `json:"is_running"`
	TotalURLs     int           `json:"total_urls"`
	CompletedURLs int           `json:"completed_urls"`
	FailedURLs    int           `json:"failed_urls"`
	StartTime     time.Time     `json:"start_time"`
	Duration      time.Duration `json:"duration"`
	Errors        []string      `json:"errors,omitempty"`
}

// CacheInvalidator 缓存失效器接口
type CacheInvalidator interface {
	// 按键失效
	InvalidateKey(ctx context.Context, key string) error
	
	// 按模式失效
	InvalidatePattern(ctx context.Context, pattern string) error
	
	// 按标签失效
	InvalidateTag(ctx context.Context, tag string) error
	
	// 按条件失效
	InvalidateByCondition(ctx context.Context, condition *InvalidationCondition) error
	
	// 获取失效状态
	GetInvalidationStatus() (*InvalidationStatus, error)
}

// InvalidationCondition 失效条件
type InvalidationCondition struct {
	// 匹配条件
	KeyPattern    string            `json:"key_pattern,omitempty"`
	Tags          []string          `json:"tags,omitempty"`
	MaxAge        time.Duration     `json:"max_age,omitempty"`
	MinSize       int64             `json:"min_size,omitempty"`
	MaxSize       int64             `json:"max_size,omitempty"`
	
	// 自定义条件
	CustomFilter  func(key string, item *CacheItem) bool `json:"-"`
}

// InvalidationStatus 失效状态
type InvalidationStatus struct {
	TotalInvalidated int           `json:"total_invalidated"`
	Duration         time.Duration `json:"duration"`
	Errors           []string      `json:"errors,omitempty"`
}

// CacheEvent 缓存事件
type CacheEvent struct {
	Type      CacheEventType `json:"type"`
	Key       string         `json:"key"`
	Value     []byte         `json:"value,omitempty"`
	TTL       time.Duration  `json:"ttl,omitempty"`
	Tags      []string       `json:"tags,omitempty"`
	Timestamp time.Time      `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// CacheEventType 缓存事件类型
type CacheEventType string

const (
	CacheEventTypeSet       CacheEventType = "set"
	CacheEventTypeGet       CacheEventType = "get"
	CacheEventTypeDelete    CacheEventType = "delete"
	CacheEventTypeExpire    CacheEventType = "expire"
	CacheEventTypeEvict     CacheEventType = "evict"
	CacheEventTypeHit       CacheEventType = "hit"
	CacheEventTypeMiss      CacheEventType = "miss"
	CacheEventTypeInvalidate CacheEventType = "invalidate"
)

// CacheEventListener 缓存事件监听器
type CacheEventListener interface {
	// 处理缓存事件
	OnCacheEvent(event *CacheEvent) error
	
	// 获取监听的事件类型
	GetEventTypes() []CacheEventType
}

// CacheMetrics 缓存指标收集器
type CacheMetrics interface {
	// 记录缓存命中
	RecordHit(cacheName string, key string, duration time.Duration)
	
	// 记录缓存未命中
	RecordMiss(cacheName string, key string, duration time.Duration)
	
	// 记录缓存设置
	RecordSet(cacheName string, key string, size int64, duration time.Duration)
	
	// 记录缓存删除
	RecordDelete(cacheName string, key string, duration time.Duration)
	
	// 记录缓存错误
	RecordError(cacheName string, operation string, err error)
	
	// 获取指标
	GetMetrics() map[string]interface{}
}
