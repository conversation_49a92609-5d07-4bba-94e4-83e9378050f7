package cache

import (
	"context"
	"fmt"
	"sync"
	"time"

	lru "github.com/hashicorp/golang-lru/v2"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// MemoryCache 内存缓存实现
type MemoryCache struct {
	config   config.CacheMemoryConfig
	logger   *telemetry.Logger
	
	// LRU缓存
	lruCache *lru.Cache[string, *memoryCacheItem]
	
	// 统计信息
	stats    *CacheStats
	statsMux sync.RWMutex
	
	// 生命周期
	startTime time.Time
	stopChan  chan struct{}
	wg        sync.WaitGroup
	
	// 事件监听器
	listeners []CacheEventListener
	listenersMux sync.RWMutex
}

// memoryCacheItem 内存缓存项
type memoryCacheItem struct {
	Value     []byte
	ExpiresAt time.Time
	Tags      []string
	Size      int64
	CreatedAt time.Time
	AccessCount int64
	LastAccess  time.Time
}

// NewMemoryCache 创建新的内存缓存
func NewMemoryCache(cfg config.CacheMemoryConfig, logger *telemetry.Logger) (*MemoryCache, error) {
	// 创建LRU缓存
	maxItems := cfg.MaxItems
	if maxItems <= 0 {
		maxItems = 1000 // 默认最大1000个项目
	}
	
	lruCache, err := lru.New[string, *memoryCacheItem](maxItems)
	if err != nil {
		return nil, fmt.Errorf("创建LRU缓存失败: %w", err)
	}
	
	cache := &MemoryCache{
		config:    cfg,
		logger:    logger.With("component", "memory-cache"),
		lruCache:  lruCache,
		stats:     &CacheStats{},
		startTime: time.Now(),
		stopChan:  make(chan struct{}),
		listeners: make([]CacheEventListener, 0),
	}
	
	// 启动清理协程
	cache.startCleanupRoutine()
	
	cache.logger.Info("内存缓存初始化完成",
		"max_items", maxItems,
		"max_size", cfg.MaxSize,
		"eviction_policy", cfg.EvictionPolicy)
	
	return cache, nil
}

// Get 获取缓存值
func (mc *MemoryCache) Get(ctx context.Context, key string) ([]byte, error) {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		mc.updateAvgGetTime(duration)
	}()
	
	item, exists := mc.lruCache.Get(key)
	if !exists {
		mc.recordMiss(key)
		mc.emitEvent(CacheEventTypeMiss, key, nil, 0, nil)
		return nil, ErrCacheKeyNotFound
	}
	
	// 检查是否过期
	if mc.isExpired(item) {
		mc.lruCache.Remove(key)
		mc.recordMiss(key)
		mc.emitEvent(CacheEventTypeExpire, key, nil, 0, nil)
		return nil, ErrCacheKeyNotFound
	}
	
	// 更新访问统计
	item.AccessCount++
	item.LastAccess = time.Now()
	
	mc.recordHit(key)
	mc.emitEvent(CacheEventTypeHit, key, item.Value, 0, item.Tags)
	
	return item.Value, nil
}

// Set 设置缓存值
func (mc *MemoryCache) Set(ctx context.Context, key string, value []byte, ttl time.Duration) error {
	return mc.SetWithTags(ctx, key, value, ttl, nil)
}

// SetWithTags 设置带标签的缓存值
func (mc *MemoryCache) SetWithTags(ctx context.Context, key string, value []byte, ttl time.Duration, tags []string) error {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		mc.updateAvgSetTime(duration)
	}()
	
	// 检查大小限制
	size := int64(len(value))
	if mc.config.MaxSize > 0 && size > mc.config.MaxSize {
		return fmt.Errorf("缓存项大小 %d 超过最大限制 %d", size, mc.config.MaxSize)
	}
	
	// 创建缓存项
	item := &memoryCacheItem{
		Value:       make([]byte, len(value)),
		Tags:        make([]string, len(tags)),
		Size:        size,
		CreatedAt:   time.Now(),
		AccessCount: 0,
		LastAccess:  time.Now(),
	}
	
	copy(item.Value, value)
	copy(item.Tags, tags)
	
	// 设置过期时间
	if ttl > 0 {
		item.ExpiresAt = time.Now().Add(ttl)
	}
	
	// 存储到LRU缓存
	evicted := mc.lruCache.Add(key, item)
	if evicted {
		mc.recordEviction()
	}
	
	mc.recordSet(key, size)
	mc.emitEvent(CacheEventTypeSet, key, value, ttl, tags)
	
	return nil
}

// Delete 删除缓存值
func (mc *MemoryCache) Delete(ctx context.Context, key string) error {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		mc.updateAvgDeleteTime(duration)
	}()
	
	existed := mc.lruCache.Remove(key)
	if existed {
		mc.recordDelete(key)
		mc.emitEvent(CacheEventTypeDelete, key, nil, 0, nil)
	}
	
	return nil
}

// Exists 检查键是否存在
func (mc *MemoryCache) Exists(ctx context.Context, key string) (bool, error) {
	item, exists := mc.lruCache.Peek(key)
	if !exists {
		return false, nil
	}
	
	// 检查是否过期
	if mc.isExpired(item) {
		mc.lruCache.Remove(key)
		return false, nil
	}
	
	return true, nil
}

// GetMulti 批量获取
func (mc *MemoryCache) GetMulti(ctx context.Context, keys []string) (map[string][]byte, error) {
	result := make(map[string][]byte)
	
	for _, key := range keys {
		if value, err := mc.Get(ctx, key); err == nil {
			result[key] = value
		}
	}
	
	return result, nil
}

// SetMulti 批量设置
func (mc *MemoryCache) SetMulti(ctx context.Context, items map[string]CacheItem) error {
	for key, item := range items {
		if err := mc.SetWithTags(ctx, key, item.Value, item.TTL, item.Tags); err != nil {
			return err
		}
	}
	return nil
}

// DeleteMulti 批量删除
func (mc *MemoryCache) DeleteMulti(ctx context.Context, keys []string) error {
	for _, key := range keys {
		mc.Delete(ctx, key)
	}
	return nil
}

// GetWithTTL 获取值和剩余TTL
func (mc *MemoryCache) GetWithTTL(ctx context.Context, key string) ([]byte, time.Duration, error) {
	item, exists := mc.lruCache.Get(key)
	if !exists {
		return nil, 0, ErrCacheKeyNotFound
	}
	
	// 检查是否过期
	if mc.isExpired(item) {
		mc.lruCache.Remove(key)
		return nil, 0, ErrCacheKeyNotFound
	}
	
	var remainingTTL time.Duration
	if !item.ExpiresAt.IsZero() {
		remainingTTL = time.Until(item.ExpiresAt)
		if remainingTTL < 0 {
			remainingTTL = 0
		}
	}
	
	return item.Value, remainingTTL, nil
}

// SetNX 仅当键不存在时设置
func (mc *MemoryCache) SetNX(ctx context.Context, key string, value []byte, ttl time.Duration) (bool, error) {
	if exists, _ := mc.Exists(ctx, key); exists {
		return false, nil
	}
	
	return true, mc.Set(ctx, key, value, ttl)
}

// Increment 增加数值
func (mc *MemoryCache) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	// 内存缓存的简单实现
	// 在实际应用中可能需要更复杂的数值处理
	return 0, fmt.Errorf("内存缓存不支持数值操作")
}

// Decrement 减少数值
func (mc *MemoryCache) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	return 0, fmt.Errorf("内存缓存不支持数值操作")
}

// Keys 获取匹配模式的键
func (mc *MemoryCache) Keys(ctx context.Context, pattern string) ([]string, error) {
	var keys []string
	
	// 遍历所有键（这在大型缓存中可能很慢）
	for _, key := range mc.lruCache.Keys() {
		// 简单的模式匹配（可以使用更复杂的正则表达式）
		if pattern == "*" || key == pattern {
			keys = append(keys, key)
		}
	}
	
	return keys, nil
}

// DeleteByPattern 按模式删除
func (mc *MemoryCache) DeleteByPattern(ctx context.Context, pattern string) error {
	keys, err := mc.Keys(ctx, pattern)
	if err != nil {
		return err
	}
	
	return mc.DeleteMulti(ctx, keys)
}

// InvalidateByTag 按标签失效
func (mc *MemoryCache) InvalidateByTag(ctx context.Context, tag string) error {
	var keysToDelete []string
	
	// 遍历所有项目查找匹配的标签
	for _, key := range mc.lruCache.Keys() {
		if item, exists := mc.lruCache.Peek(key); exists {
			for _, itemTag := range item.Tags {
				if itemTag == tag {
					keysToDelete = append(keysToDelete, key)
					break
				}
			}
		}
	}
	
	return mc.DeleteMulti(ctx, keysToDelete)
}

// Stats 获取统计信息
func (mc *MemoryCache) Stats(ctx context.Context) (*CacheStats, error) {
	mc.statsMux.RLock()
	defer mc.statsMux.RUnlock()
	
	// 计算当前使用的内存
	var totalSize int64
	for _, key := range mc.lruCache.Keys() {
		if item, exists := mc.lruCache.Peek(key); exists {
			totalSize += item.Size
		}
	}
	
	stats := &CacheStats{
		Hits:        mc.stats.Hits,
		Misses:      mc.stats.Misses,
		Sets:        mc.stats.Sets,
		Deletes:     mc.stats.Deletes,
		Evictions:   mc.stats.Evictions,
		HitRatio:    mc.calculateHitRatio(),
		AvgGetTime:  mc.stats.AvgGetTime,
		AvgSetTime:  mc.stats.AvgSetTime,
		ItemCount:   int64(mc.lruCache.Len()),
		TotalSize:   totalSize,
		MaxSize:     mc.config.MaxSize,
		UsedMemory:  totalSize,
		Uptime:      time.Since(mc.startTime),
		LastAccess:  mc.stats.LastAccess,
		Errors:      mc.stats.Errors,
		Timeouts:    mc.stats.Timeouts,
	}
	
	return stats, nil
}

// Close 关闭缓存
func (mc *MemoryCache) Close() error {
	close(mc.stopChan)
	mc.wg.Wait()
	mc.lruCache.Purge()
	mc.logger.Info("内存缓存已关闭")
	return nil
}

// Ping 检查缓存是否可用
func (mc *MemoryCache) Ping(ctx context.Context) error {
	return nil // 内存缓存总是可用的
}

// 辅助方法

// isExpired 检查项目是否过期
func (mc *MemoryCache) isExpired(item *memoryCacheItem) bool {
	if item.ExpiresAt.IsZero() {
		return false
	}
	return time.Now().After(item.ExpiresAt)
}

// startCleanupRoutine 启动清理协程
func (mc *MemoryCache) startCleanupRoutine() {
	if mc.config.CleanupInterval == "" {
		return
	}

	interval, err := time.ParseDuration(mc.config.CleanupInterval)
	if err != nil {
		mc.logger.Warn("无效的清理间隔", "interval", mc.config.CleanupInterval, "error", err)
		return
	}

	mc.wg.Add(1)
	go func() {
		defer mc.wg.Done()

		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			select {
			case <-mc.stopChan:
				return
			case <-ticker.C:
				mc.cleanup()
			}
		}
	}()
}

// cleanup 清理过期项目
func (mc *MemoryCache) cleanup() {
	var expiredKeys []string

	// 查找过期的键
	for _, key := range mc.lruCache.Keys() {
		if item, exists := mc.lruCache.Peek(key); exists && mc.isExpired(item) {
			expiredKeys = append(expiredKeys, key)
		}
	}

	// 删除过期的键
	for _, key := range expiredKeys {
		mc.lruCache.Remove(key)
		mc.emitEvent(CacheEventTypeExpire, key, nil, 0, nil)
	}

	if len(expiredKeys) > 0 {
		mc.logger.Debug("清理过期缓存项", "count", len(expiredKeys))
	}
}

// 统计方法

// recordHit 记录缓存命中
func (mc *MemoryCache) recordHit(key string) {
	mc.statsMux.Lock()
	defer mc.statsMux.Unlock()

	mc.stats.Hits++
	mc.stats.LastAccess = time.Now()
}

// recordMiss 记录缓存未命中
func (mc *MemoryCache) recordMiss(key string) {
	mc.statsMux.Lock()
	defer mc.statsMux.Unlock()

	mc.stats.Misses++
	mc.stats.LastAccess = time.Now()
}

// recordSet 记录缓存设置
func (mc *MemoryCache) recordSet(key string, size int64) {
	mc.statsMux.Lock()
	defer mc.statsMux.Unlock()

	mc.stats.Sets++
}

// recordDelete 记录缓存删除
func (mc *MemoryCache) recordDelete(key string) {
	mc.statsMux.Lock()
	defer mc.statsMux.Unlock()

	mc.stats.Deletes++
}

// recordEviction 记录缓存淘汰
func (mc *MemoryCache) recordEviction() {
	mc.statsMux.Lock()
	defer mc.statsMux.Unlock()

	mc.stats.Evictions++
}

// calculateHitRatio 计算命中率
func (mc *MemoryCache) calculateHitRatio() float64 {
	total := mc.stats.Hits + mc.stats.Misses
	if total == 0 {
		return 0
	}
	return float64(mc.stats.Hits) / float64(total)
}

// updateAvgGetTime 更新平均获取时间
func (mc *MemoryCache) updateAvgGetTime(duration time.Duration) {
	mc.statsMux.Lock()
	defer mc.statsMux.Unlock()

	// 简单的移动平均
	if mc.stats.AvgGetTime == 0 {
		mc.stats.AvgGetTime = float64(duration.Nanoseconds()) / 1e6 // 转换为毫秒
	} else {
		mc.stats.AvgGetTime = (mc.stats.AvgGetTime + float64(duration.Nanoseconds())/1e6) / 2
	}
}

// updateAvgSetTime 更新平均设置时间
func (mc *MemoryCache) updateAvgSetTime(duration time.Duration) {
	mc.statsMux.Lock()
	defer mc.statsMux.Unlock()

	// 简单的移动平均
	if mc.stats.AvgSetTime == 0 {
		mc.stats.AvgSetTime = float64(duration.Nanoseconds()) / 1e6 // 转换为毫秒
	} else {
		mc.stats.AvgSetTime = (mc.stats.AvgSetTime + float64(duration.Nanoseconds())/1e6) / 2
	}
}

// updateAvgDeleteTime 更新平均删除时间
func (mc *MemoryCache) updateAvgDeleteTime(duration time.Duration) {
	// 可以添加删除时间统计
}

// 事件相关方法

// AddEventListener 添加事件监听器
func (mc *MemoryCache) AddEventListener(listener CacheEventListener) {
	mc.listenersMux.Lock()
	defer mc.listenersMux.Unlock()

	mc.listeners = append(mc.listeners, listener)
}

// RemoveEventListener 移除事件监听器
func (mc *MemoryCache) RemoveEventListener(listener CacheEventListener) {
	mc.listenersMux.Lock()
	defer mc.listenersMux.Unlock()

	for i, l := range mc.listeners {
		if l == listener {
			mc.listeners = append(mc.listeners[:i], mc.listeners[i+1:]...)
			break
		}
	}
}

// emitEvent 发送事件
func (mc *MemoryCache) emitEvent(eventType CacheEventType, key string, value []byte, ttl time.Duration, tags []string) {
	mc.listenersMux.RLock()
	listeners := make([]CacheEventListener, len(mc.listeners))
	copy(listeners, mc.listeners)
	mc.listenersMux.RUnlock()

	if len(listeners) == 0 {
		return
	}

	event := &CacheEvent{
		Type:      eventType,
		Key:       key,
		Value:     value,
		TTL:       ttl,
		Tags:      tags,
		Timestamp: time.Now(),
	}

	// 异步发送事件
	go func() {
		for _, listener := range listeners {
			// 检查监听器是否关心这种事件类型
			eventTypes := listener.GetEventTypes()
			if len(eventTypes) > 0 {
				found := false
				for _, et := range eventTypes {
					if et == eventType {
						found = true
						break
					}
				}
				if !found {
					continue
				}
			}

			if err := listener.OnCacheEvent(event); err != nil {
				mc.logger.Error("缓存事件处理失败", "event_type", eventType, "key", key, "error", err)
			}
		}
	}()
}
