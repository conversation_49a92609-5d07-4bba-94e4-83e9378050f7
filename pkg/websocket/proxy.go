package websocket

import (
	"context"
	"fmt"
	"net"
	"net/url"
	"sync"
	"time"

	"github.com/gobwas/ws"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// Proxy WebSocket代理实现
type Proxy struct {
	config config.WebSocketProxyConfig
	logger *telemetry.Logger
	
	// 代理连接管理
	proxies    map[string]*ProxyConnection
	proxiesMux sync.RWMutex
	
	// 统计信息
	stats *ProxyStats
	statsMux sync.RWMutex
	
	// 生命周期
	running bool
	
	// 上下文
	ctx    context.Context
	cancel context.CancelFunc
	
	// 等待组
	wg sync.WaitGroup
}

// ProxyConnection 代理连接
type ProxyConnection struct {
	ID           string
	ClientConn   Connection
	UpstreamConn net.Conn
	UpstreamURL  string
	StartTime    time.Time
	BytesIn      int64
	BytesOut     int64
	MessagesIn   int64
	MessagesOut  int64
	Active       bool
	
	ctx    context.Context
	cancel context.CancelFunc
	logger *telemetry.Logger
}

// NewProxy 创建新的WebSocket代理
func NewProxy(cfg config.WebSocketProxyConfig, logger *telemetry.Logger) (*Proxy, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	proxy := &Proxy{
		config:  cfg,
		logger:  logger.With("component", "websocket-proxy"),
		proxies: make(map[string]*ProxyConnection),
		stats: &ProxyStats{},
		ctx:     ctx,
		cancel:  cancel,
	}
	
	proxy.logger.Info("WebSocket代理初始化完成",
		"enabled", cfg.Enabled,
		"mode", cfg.Mode,
		"buffer_size", cfg.BufferSize)
	
	return proxy, nil
}

// ProxyConnection 代理WebSocket连接
func (p *Proxy) ProxyConnection(clientConn Connection, upstreamURL string) error {
	if !p.config.Enabled {
		return fmt.Errorf("WebSocket代理未启用")
	}
	
	// 解析上游URL
	u, err := url.Parse(upstreamURL)
	if err != nil {
		return fmt.Errorf("解析上游URL失败: %w", err)
	}
	
	// 建立上游连接
	upstreamConn, err := p.connectUpstream(u)
	if err != nil {
		return fmt.Errorf("连接上游失败: %w", err)
	}
	
	// 创建代理连接
	proxyConn := &ProxyConnection{
		ID:           generateProxyID(),
		ClientConn:   clientConn,
		UpstreamConn: upstreamConn,
		UpstreamURL:  upstreamURL,
		StartTime:    time.Now(),
		Active:       true,
		logger:       p.logger.With("proxy_id", generateProxyID()),
	}
	
	proxyConn.ctx, proxyConn.cancel = context.WithCancel(p.ctx)
	
	// 注册代理连接
	p.registerProxy(proxyConn)
	
	// 启动代理
	p.wg.Add(1)
	go p.runProxy(proxyConn)
	
	p.logger.Info("启动WebSocket代理", "proxy_id", proxyConn.ID, "upstream", upstreamURL)
	
	return nil
}

// GetProxyStats 获取代理统计
func (p *Proxy) GetProxyStats() (*ProxyStats, error) {
	p.statsMux.RLock()
	defer p.statsMux.RUnlock()
	
	p.proxiesMux.RLock()
	activeProxies := int64(0)
	for _, proxy := range p.proxies {
		if proxy.Active {
			activeProxies++
		}
	}
	p.proxiesMux.RUnlock()
	
	return &ProxyStats{
		ProxiedConnections: p.stats.ProxiedConnections,
		ActiveProxies:      activeProxies,
		ProxiedMessages:    p.stats.ProxiedMessages,
		ProxiedBytes:       p.stats.ProxiedBytes,
		ProxyErrors:        p.stats.ProxyErrors,
		AvgProxyLatency:    p.stats.AvgProxyLatency,
	}, nil
}

// HealthCheck 健康检查
func (p *Proxy) HealthCheck() error {
	if !p.running {
		return fmt.Errorf("WebSocket代理未运行")
	}
	
	return nil
}

// Start 启动代理
func (p *Proxy) Start() error {
	if p.running {
		return nil
	}
	
	p.running = true
	
	// 启动清理协程
	p.wg.Add(1)
	go p.cleanupLoop()
	
	p.logger.Info("WebSocket代理已启动")
	
	return nil
}

// Stop 停止代理
func (p *Proxy) Stop() error {
	if !p.running {
		return nil
	}
	
	p.logger.Info("停止WebSocket代理")
	
	// 取消上下文
	p.cancel()
	
	// 关闭所有代理连接
	p.proxiesMux.Lock()
	for _, proxy := range p.proxies {
		proxy.cancel()
		proxy.UpstreamConn.Close()
	}
	p.proxiesMux.Unlock()
	
	// 等待协程结束
	p.wg.Wait()
	
	p.running = false
	
	p.logger.Info("WebSocket代理已停止")
	
	return nil
}

// 私有方法

// connectUpstream 连接上游
func (p *Proxy) connectUpstream(u *url.URL) (net.Conn, error) {
	// 构建WebSocket连接
	dialer := ws.Dialer{
		Timeout: 10 * time.Second,
	}
	
	conn, _, _, err := dialer.Dial(p.ctx, u.String())
	if err != nil {
		return nil, fmt.Errorf("WebSocket拨号失败: %w", err)
	}
	
	return conn, nil
}

// registerProxy 注册代理
func (p *Proxy) registerProxy(proxy *ProxyConnection) {
	p.proxiesMux.Lock()
	defer p.proxiesMux.Unlock()
	
	p.proxies[proxy.ID] = proxy
	
	// 更新统计
	p.statsMux.Lock()
	p.stats.ProxiedConnections++
	p.statsMux.Unlock()
	
	p.logger.Debug("注册代理连接", "proxy_id", proxy.ID)
}

// unregisterProxy 注销代理
func (p *Proxy) unregisterProxy(proxyID string) {
	p.proxiesMux.Lock()
	defer p.proxiesMux.Unlock()
	
	delete(p.proxies, proxyID)
	
	p.logger.Debug("注销代理连接", "proxy_id", proxyID)
}

// runProxy 运行代理
func (p *Proxy) runProxy(proxy *ProxyConnection) {
	defer p.wg.Done()
	defer func() {
		proxy.Active = false
		proxy.UpstreamConn.Close()
		p.unregisterProxy(proxy.ID)
		
		proxy.logger.Info("代理连接已关闭",
			"duration", time.Since(proxy.StartTime),
			"bytes_in", proxy.BytesIn,
			"bytes_out", proxy.BytesOut,
			"messages_in", proxy.MessagesIn,
			"messages_out", proxy.MessagesOut)
	}()
	
	// 启动双向数据转发
	var wg sync.WaitGroup
	
	// 客户端到上游
	wg.Add(1)
	go func() {
		defer wg.Done()
		p.forwardClientToUpstream(proxy)
	}()
	
	// 上游到客户端
	wg.Add(1)
	go func() {
		defer wg.Done()
		p.forwardUpstreamToClient(proxy)
	}()
	
	// 等待任一方向结束
	wg.Wait()
}

// forwardClientToUpstream 转发客户端到上游
func (p *Proxy) forwardClientToUpstream(proxy *ProxyConnection) {
	for {
		select {
		case <-proxy.ctx.Done():
			return
		default:
			// 接收客户端消息
			message, err := proxy.ClientConn.ReceiveMessage()
			if err != nil {
				proxy.logger.Error("接收客户端消息失败", "error", err)
				return
			}
			
			// 转发到上游
			if err := p.writeUpstreamMessage(proxy, message); err != nil {
				proxy.logger.Error("转发消息到上游失败", "error", err)
				p.recordProxyError()
				return
			}
			
			// 更新统计
			proxy.MessagesOut++
			proxy.BytesOut += int64(len(message.Data))
			p.updateProxyStats(message)
		}
	}
}

// forwardUpstreamToClient 转发上游到客户端
func (p *Proxy) forwardUpstreamToClient(proxy *ProxyConnection) {
	for {
		select {
		case <-proxy.ctx.Done():
			return
		default:
			// 接收上游消息
			message, err := p.readUpstreamMessage(proxy)
			if err != nil {
				proxy.logger.Error("接收上游消息失败", "error", err)
				return
			}
			
			// 转发到客户端
			if err := proxy.ClientConn.SendMessage(message); err != nil {
				proxy.logger.Error("转发消息到客户端失败", "error", err)
				p.recordProxyError()
				return
			}
			
			// 更新统计
			proxy.MessagesIn++
			proxy.BytesIn += int64(len(message.Data))
			p.updateProxyStats(message)
		}
	}
}

// writeUpstreamMessage 写入上游消息
func (p *Proxy) writeUpstreamMessage(proxy *ProxyConnection, message *Message) error {
	var opCode ws.OpCode
	
	switch message.Type {
	case MessageTypeText:
		opCode = ws.OpText
	case MessageTypeBinary:
		opCode = ws.OpBinary
	case MessageTypePing:
		opCode = ws.OpPing
	case MessageTypePong:
		opCode = ws.OpPong
	case MessageTypeClose:
		opCode = ws.OpClose
	default:
		return fmt.Errorf("不支持的消息类型: %d", message.Type)
	}
	
	frame := ws.NewFrame(opCode, true, message.Data)
	return ws.WriteFrame(proxy.UpstreamConn, frame)
}

// readUpstreamMessage 读取上游消息
func (p *Proxy) readUpstreamMessage(proxy *ProxyConnection) (*Message, error) {
	frame, err := ws.ReadFrame(proxy.UpstreamConn)
	if err != nil {
		return nil, err
	}
	
	message := &Message{
		ID:        generateMessageID(),
		Data:      frame.Payload,
		Timestamp: time.Now(),
	}
	
	switch frame.Header.OpCode {
	case ws.OpText:
		message.Type = MessageTypeText
	case ws.OpBinary:
		message.Type = MessageTypeBinary
	case ws.OpPing:
		message.Type = MessageTypePing
	case ws.OpPong:
		message.Type = MessageTypePong
	case ws.OpClose:
		message.Type = MessageTypeClose
	default:
		return nil, fmt.Errorf("不支持的上游消息类型: %s", frame.Header.OpCode)
	}
	
	return message, nil
}

// updateProxyStats 更新代理统计
func (p *Proxy) updateProxyStats(message *Message) {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	
	p.stats.ProxiedMessages++
	p.stats.ProxiedBytes += int64(len(message.Data))
}

// recordProxyError 记录代理错误
func (p *Proxy) recordProxyError() {
	p.statsMux.Lock()
	defer p.statsMux.Unlock()
	
	p.stats.ProxyErrors++
}

// cleanupLoop 清理循环
func (p *Proxy) cleanupLoop() {
	defer p.wg.Done()
	
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-p.ctx.Done():
			return
		case <-ticker.C:
			p.cleanupInactiveProxies()
		}
	}
}

// cleanupInactiveProxies 清理非活跃代理
func (p *Proxy) cleanupInactiveProxies() {
	p.proxiesMux.Lock()
	defer p.proxiesMux.Unlock()
	
	var toRemove []string
	
	for id, proxy := range p.proxies {
		if !proxy.Active {
			toRemove = append(toRemove, id)
		}
	}
	
	for _, id := range toRemove {
		delete(p.proxies, id)
	}
	
	if len(toRemove) > 0 {
		p.logger.Info("清理非活跃代理完成", "removed_count", len(toRemove))
	}
}

// generateProxyID 生成代理ID
func generateProxyID() string {
	return fmt.Sprintf("proxy_%d", time.Now().UnixNano())
}
