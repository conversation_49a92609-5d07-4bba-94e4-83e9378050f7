package websocket

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"github.com/gobwas/ws"

	"api-gateway/pkg/telemetry"
)

// ConnectionImpl WebSocket连接实现
type ConnectionImpl struct {
	// 连接ID
	id string
	
	// 底层网络连接
	conn net.Conn
	
	// 远程地址
	remoteAddr string
	
	// 用户ID
	userID string
	
	// 连接时间
	connectedAt time.Time
	
	// 最后活跃时间
	lastActivity time.Time
	
	// 连接元数据
	metadata map[string]interface{}
	metadataMux sync.RWMutex
	
	// 连接状态
	active bool
	activeMux sync.RWMutex
	
	// 统计信息
	stats *ConnectionStats
	statsMux sync.RWMutex
	
	// 发送通道
	sendChan chan *Message
	
	// 接收通道
	receiveChan chan *Message
	
	// 关闭通道
	closeChan chan struct{}
	
	// 错误通道
	errorChan chan error
	
	// 日志器
	logger *telemetry.Logger
	
	// 上下文
	ctx    context.Context
	cancel context.CancelFunc
	
	// 等待组
	wg sync.WaitGroup
}

// NewConnection 创建新的WebSocket连接
func NewConnection(id string, conn net.Conn, userID string, logger *telemetry.Logger) *ConnectionImpl {
	ctx, cancel := context.WithCancel(context.Background())
	
	c := &ConnectionImpl{
		id:           id,
		conn:         conn,
		remoteAddr:   conn.RemoteAddr().String(),
		userID:       userID,
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
		metadata:     make(map[string]interface{}),
		active:       true,
		stats: &ConnectionStats{
			ConnectionID: id,
			ConnectedAt:  time.Now(),
			LastActivity: time.Now(),
		},
		sendChan:    make(chan *Message, 256),
		receiveChan: make(chan *Message, 256),
		closeChan:   make(chan struct{}),
		errorChan:   make(chan error, 16),
		logger:      logger.With("connection_id", id, "remote_addr", conn.RemoteAddr().String()),
		ctx:         ctx,
		cancel:      cancel,
	}
	
	// 启动消息处理协程
	c.wg.Add(2)
	go c.sendLoop()
	go c.receiveLoop()
	
	c.logger.Info("WebSocket连接已建立", "user_id", userID)
	
	return c
}

// ID 获取连接ID
func (c *ConnectionImpl) ID() string {
	return c.id
}

// RemoteAddr 获取远程地址
func (c *ConnectionImpl) RemoteAddr() string {
	return c.remoteAddr
}

// UserID 获取用户ID
func (c *ConnectionImpl) UserID() string {
	return c.userID
}

// ConnectedAt 获取连接时间
func (c *ConnectionImpl) ConnectedAt() time.Time {
	return c.connectedAt
}

// SendMessage 发送消息
func (c *ConnectionImpl) SendMessage(message *Message) error {
	if !c.IsActive() {
		return fmt.Errorf("连接已关闭")
	}
	
	select {
	case c.sendChan <- message:
		return nil
	case <-c.ctx.Done():
		return fmt.Errorf("连接已关闭")
	default:
		return fmt.Errorf("发送队列已满")
	}
}

// ReceiveMessage 接收消息
func (c *ConnectionImpl) ReceiveMessage() (*Message, error) {
	select {
	case message := <-c.receiveChan:
		return message, nil
	case err := <-c.errorChan:
		return nil, err
	case <-c.ctx.Done():
		return nil, fmt.Errorf("连接已关闭")
	}
}

// Close 关闭连接
func (c *ConnectionImpl) Close(reason string) error {
	c.activeMux.Lock()
	if !c.active {
		c.activeMux.Unlock()
		return nil
	}
	c.active = false
	c.activeMux.Unlock()
	
	c.logger.Info("关闭WebSocket连接", "reason", reason)
	
	// 发送关闭帧
	closeFrame := ws.NewCloseFrame(ws.NewCloseFrameBody(ws.StatusNormalClosure, reason))
	if err := ws.WriteFrame(c.conn, closeFrame); err != nil {
		c.logger.Error("发送关闭帧失败", "error", err)
	}
	
	// 取消上下文
	c.cancel()
	
	// 关闭底层连接
	if err := c.conn.Close(); err != nil {
		c.logger.Error("关闭底层连接失败", "error", err)
	}
	
	// 等待协程结束
	c.wg.Wait()
	
	return nil
}

// IsActive 检查连接是否活跃
func (c *ConnectionImpl) IsActive() bool {
	c.activeMux.RLock()
	defer c.activeMux.RUnlock()
	return c.active
}

// Metadata 获取连接元数据
func (c *ConnectionImpl) Metadata() map[string]interface{} {
	c.metadataMux.RLock()
	defer c.metadataMux.RUnlock()
	
	result := make(map[string]interface{})
	for k, v := range c.metadata {
		result[k] = v
	}
	return result
}

// SetMetadata 设置连接元数据
func (c *ConnectionImpl) SetMetadata(key string, value interface{}) {
	c.metadataMux.Lock()
	defer c.metadataMux.Unlock()
	
	c.metadata[key] = value
}

// Ping Ping连接
func (c *ConnectionImpl) Ping() error {
	if !c.IsActive() {
		return fmt.Errorf("连接已关闭")
	}
	
	pingFrame := ws.NewPingFrame([]byte("ping"))
	if err := ws.WriteFrame(c.conn, pingFrame); err != nil {
		c.logger.Error("发送Ping帧失败", "error", err)
		return err
	}
	
	return nil
}

// Stats 获取连接统计
func (c *ConnectionImpl) Stats() *ConnectionStats {
	c.statsMux.RLock()
	defer c.statsMux.RUnlock()
	
	return &ConnectionStats{
		ConnectionID:     c.stats.ConnectionID,
		SentMessages:     c.stats.SentMessages,
		ReceivedMessages: c.stats.ReceivedMessages,
		SentBytes:        c.stats.SentBytes,
		ReceivedBytes:    c.stats.ReceivedBytes,
		ConnectedAt:      c.stats.ConnectedAt,
		LastActivity:     c.stats.LastActivity,
		Errors:           c.stats.Errors,
	}
}

// 私有方法

// sendLoop 发送循环
func (c *ConnectionImpl) sendLoop() {
	defer c.wg.Done()
	
	for {
		select {
		case message := <-c.sendChan:
			if err := c.writeMessage(message); err != nil {
				c.logger.Error("发送消息失败", "error", err)
				c.recordError()
				c.errorChan <- err
				return
			}
			c.recordSentMessage(message)
			
		case <-c.ctx.Done():
			return
		}
	}
}

// receiveLoop 接收循环
func (c *ConnectionImpl) receiveLoop() {
	defer c.wg.Done()
	
	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			message, err := c.readMessage()
			if err != nil {
				c.logger.Error("接收消息失败", "error", err)
				c.recordError()
				c.errorChan <- err
				return
			}
			
			if message != nil {
				c.recordReceivedMessage(message)
				c.receiveChan <- message
			}
		}
	}
}

// writeMessage 写入消息
func (c *ConnectionImpl) writeMessage(message *Message) error {
	var opCode ws.OpCode
	
	switch message.Type {
	case MessageTypeText:
		opCode = ws.OpText
	case MessageTypeBinary:
		opCode = ws.OpBinary
	case MessageTypePing:
		opCode = ws.OpPing
	case MessageTypePong:
		opCode = ws.OpPong
	case MessageTypeClose:
		opCode = ws.OpClose
	default:
		return fmt.Errorf("不支持的消息类型: %d", message.Type)
	}
	
	frame := ws.NewFrame(opCode, true, message.Data)
	return ws.WriteFrame(c.conn, frame)
}

// readMessage 读取消息
func (c *ConnectionImpl) readMessage() (*Message, error) {
	// 设置读取超时
	c.conn.SetReadDeadline(time.Now().Add(30 * time.Second))
	
	frame, err := ws.ReadFrame(c.conn)
	if err != nil {
		return nil, err
	}
	
	// 处理控制帧
	if frame.Header.OpCode.IsControl() {
		return c.handleControlFrame(frame)
	}
	
	// 处理数据帧
	message := &Message{
		ID:        generateMessageID(),
		Data:      frame.Payload,
		Timestamp: time.Now(),
		SenderID:  c.userID,
	}
	
	switch frame.Header.OpCode {
	case ws.OpText:
		message.Type = MessageTypeText
	case ws.OpBinary:
		message.Type = MessageTypeBinary
	default:
		return nil, fmt.Errorf("不支持的数据帧类型: %s", frame.Header.OpCode)
	}
	
	return message, nil
}

// handleControlFrame 处理控制帧
func (c *ConnectionImpl) handleControlFrame(frame ws.Frame) (*Message, error) {
	switch frame.Header.OpCode {
	case ws.OpPing:
		// 响应Pong
		pongFrame := ws.NewPongFrame(frame.Payload)
		if err := ws.WriteFrame(c.conn, pongFrame); err != nil {
			return nil, err
		}
		
		return &Message{
			ID:        generateMessageID(),
			Type:      MessageTypePing,
			Data:      frame.Payload,
			Timestamp: time.Now(),
			SenderID:  c.userID,
		}, nil
		
	case ws.OpPong:
		return &Message{
			ID:        generateMessageID(),
			Type:      MessageTypePong,
			Data:      frame.Payload,
			Timestamp: time.Now(),
			SenderID:  c.userID,
		}, nil
		
	case ws.OpClose:
		// 处理关闭帧
		closeCode, reason := ws.ParseCloseFrameData(frame.Payload)
		c.logger.Info("收到关闭帧", "code", closeCode, "reason", reason)
		
		// 响应关闭帧
		closeFrame := ws.NewCloseFrame(ws.NewCloseFrameBody(ws.StatusNormalClosure, ""))
		ws.WriteFrame(c.conn, closeFrame)
		
		// 关闭连接
		go c.Close("客户端关闭连接")
		
		return &Message{
			ID:        generateMessageID(),
			Type:      MessageTypeClose,
			Data:      frame.Payload,
			Timestamp: time.Now(),
			SenderID:  c.userID,
		}, nil
		
	default:
		return nil, fmt.Errorf("未知的控制帧类型: %s", frame.Header.OpCode)
	}
}

// recordSentMessage 记录发送消息
func (c *ConnectionImpl) recordSentMessage(message *Message) {
	c.statsMux.Lock()
	defer c.statsMux.Unlock()
	
	c.stats.SentMessages++
	c.stats.SentBytes += int64(len(message.Data))
	c.stats.LastActivity = time.Now()
	c.lastActivity = time.Now()
}

// recordReceivedMessage 记录接收消息
func (c *ConnectionImpl) recordReceivedMessage(message *Message) {
	c.statsMux.Lock()
	defer c.statsMux.Unlock()
	
	c.stats.ReceivedMessages++
	c.stats.ReceivedBytes += int64(len(message.Data))
	c.stats.LastActivity = time.Now()
	c.lastActivity = time.Now()
}

// recordError 记录错误
func (c *ConnectionImpl) recordError() {
	c.statsMux.Lock()
	defer c.statsMux.Unlock()
	
	c.stats.Errors++
}



// generateMessageID 生成消息ID
func generateMessageID() string {
	return fmt.Sprintf("msg_%d", time.Now().UnixNano())
}
