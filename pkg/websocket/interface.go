package websocket

import (
	"net/http"
	"time"
)

// WebSocketManager WebSocket管理器接口
type WebSocketManager interface {
	// 启动WebSocket服务
	Start() error
	
	// 停止WebSocket服务
	Stop() error
	
	// 处理WebSocket连接
	HandleConnection(w http.ResponseWriter, r *http.Request) error
	
	// 获取连接统计
	GetStats() (*WebSocketStats, error)
	
	// 获取活跃连接
	GetActiveConnections() []*Connection
	
	// 广播消息
	Broadcast(message *Message, filter ConnectionFilter) error
	
	// 关闭连接
	CloseConnection(connectionID string, reason string) error
	
	// 健康检查
	HealthCheck() error
}

// Connection WebSocket连接接口
type Connection interface {
	// 获取连接ID
	ID() string
	
	// 获取远程地址
	RemoteAddr() string
	
	// 获取用户ID
	UserID() string
	
	// 获取连接时间
	ConnectedAt() time.Time
	
	// 发送消息
	SendMessage(message *Message) error
	
	// 接收消息
	ReceiveMessage() (*Message, error)
	
	// 关闭连接
	Close(reason string) error
	
	// 检查连接是否活跃
	IsActive() bool
	
	// 获取连接元数据
	Metadata() map[string]interface{}
	
	// 设置连接元数据
	SetMetadata(key string, value interface{})
	
	// Ping连接
	Ping() error
	
	// 获取连接统计
	Stats() *ConnectionStats
}

// Message WebSocket消息
type Message struct {
	// 消息ID
	ID string `json:"id"`
	
	// 消息类型：text, binary, ping, pong, close
	Type MessageType `json:"type"`
	
	// 消息数据
	Data []byte `json:"data"`
	
	// 消息头部
	Headers map[string]string `json:"headers,omitempty"`
	
	// 发送者ID
	SenderID string `json:"sender_id,omitempty"`
	
	// 接收者ID
	ReceiverID string `json:"receiver_id,omitempty"`
	
	// 消息时间戳
	Timestamp time.Time `json:"timestamp"`
	
	// 消息元数据
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// MessageType 消息类型
type MessageType int

const (
	MessageTypeText   MessageType = 1
	MessageTypeBinary MessageType = 2
	MessageTypePing   MessageType = 9
	MessageTypePong   MessageType = 10
	MessageTypeClose  MessageType = 8
)

// ConnectionFilter 连接过滤器
type ConnectionFilter interface {
	// 检查连接是否匹配过滤条件
	Match(conn Connection) bool
}

// WebSocketProxy WebSocket代理接口
type WebSocketProxy interface {
	// 代理WebSocket连接
	ProxyConnection(clientConn Connection, upstreamURL string) error
	
	// 获取代理统计
	GetProxyStats() (*ProxyStats, error)
	
	// 健康检查
	HealthCheck() error
}

// WebSocketRouter WebSocket路由器接口
type WebSocketRouter interface {
	// 添加路由
	AddRoute(route *Route) error
	
	// 删除路由
	RemoveRoute(name string) error
	
	// 匹配路由
	MatchRoute(path string) (*Route, error)
	
	// 获取所有路由
	GetRoutes() []*Route
}

// Route WebSocket路由
type Route struct {
	// 路由名称
	Name string `json:"name"`
	
	// 路径模式
	Path string `json:"path"`
	
	// 上游配置
	Upstream *Upstream `json:"upstream"`
	
	// 中间件
	Middlewares []Middleware `json:"middlewares"`
	
	// 认证配置
	Auth *AuthConfig `json:"auth"`
	
	// 转换配置
	Transform *TransformConfig `json:"transform"`
	
	// 是否启用
	Enabled bool `json:"enabled"`
}

// Upstream 上游配置
type Upstream struct {
	// 上游类型
	Type string `json:"type"`
	
	// 上游URL
	URL string `json:"url"`
	
	// 服务名称
	ServiceName string `json:"service_name"`
	
	// 负载均衡策略
	LoadBalancer string `json:"load_balancer"`
	
	// 健康检查
	HealthCheck *HealthCheckConfig `json:"health_check"`
	
	// 超时配置
	Timeout *TimeoutConfig `json:"timeout"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	// 是否需要认证
	Required bool `json:"required"`
	
	// 认证方法
	Methods []string `json:"methods"`
	
	// JWT配置
	JWT *JWTConfig `json:"jwt"`
	
	// API Key配置
	APIKey *APIKeyConfig `json:"api_key"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	// 密钥
	Secret string `json:"secret"`
	
	// 算法
	Algorithm string `json:"algorithm"`
	
	// Token来源
	TokenSource string `json:"token_source"`
	
	// Token参数名
	TokenParam string `json:"token_param"`
}

// APIKeyConfig API Key配置
type APIKeyConfig struct {
	// API Key来源
	Source string `json:"source"`
	
	// 参数名
	Param string `json:"param"`
	
	// 有效的API Keys
	ValidKeys []string `json:"valid_keys"`
}

// TransformConfig 转换配置
type TransformConfig struct {
	// 是否启用转换
	Enabled bool `json:"enabled"`
	
	// 消息转换规则
	MessageRules []*MessageTransformRule `json:"message_rules"`
	
	// 连接转换规则
	ConnectionRules []*ConnectionTransformRule `json:"connection_rules"`
}

// MessageTransformRule 消息转换规则
type MessageTransformRule struct {
	// 规则名称
	Name string `json:"name"`
	
	// 匹配条件
	Condition *FilterCondition `json:"condition"`
	
	// 转换操作
	Transform *MessageTransform `json:"transform"`
	
	// 是否启用
	Enabled bool `json:"enabled"`
}

// FilterCondition 过滤条件
type FilterCondition struct {
	// 消息类型
	MessageType string `json:"message_type"`
	
	// 大小范围
	SizeRange *SizeRange `json:"size_range"`
	
	// 内容匹配
	ContentMatch *ContentMatch `json:"content_match"`
	
	// 频率限制
	RateLimit *RateLimit `json:"rate_limit"`
}

// SizeRange 大小范围
type SizeRange struct {
	Min int64 `json:"min"`
	Max int64 `json:"max"`
}

// ContentMatch 内容匹配
type ContentMatch struct {
	Type          string `json:"type"`
	Value         string `json:"value"`
	CaseSensitive bool   `json:"case_sensitive"`
}

// RateLimit 频率限制
type RateLimit struct {
	Requests int    `json:"requests"`
	Window   string `json:"window"`
	Key      string `json:"key"`
}

// MessageTransform 消息转换
type MessageTransform struct {
	// 转换类型
	Type string `json:"type"`
	
	// 格式转换
	Format *FormatTransform `json:"format"`
	
	// 内容转换
	Content *ContentTransform `json:"content"`
	
	// 头部转换
	Headers map[string]string `json:"headers"`
}

// FormatTransform 格式转换
type FormatTransform struct {
	From    string                 `json:"from"`
	To      string                 `json:"to"`
	Options map[string]interface{} `json:"options"`
}

// ContentTransform 内容转换
type ContentTransform struct {
	Type     string                `json:"type"`
	Template string                `json:"template"`
	Regex    *RegexTransform       `json:"regex"`
	JSONPath []*JSONPathTransform  `json:"jsonpath"`
}

// RegexTransform 正则转换
type RegexTransform struct {
	Pattern     string `json:"pattern"`
	Replacement string `json:"replacement"`
	Global      bool   `json:"global"`
}

// JSONPathTransform JSONPath转换
type JSONPathTransform struct {
	Path       string      `json:"path"`
	Operation  string      `json:"operation"`
	Value      interface{} `json:"value"`
	TargetPath string      `json:"target_path"`
}

// ConnectionTransformRule 连接转换规则
type ConnectionTransformRule struct {
	Name      string                   `json:"name"`
	Condition *ConnectionCondition     `json:"condition"`
	Transform *ConnectionTransform     `json:"transform"`
	Enabled   bool                     `json:"enabled"`
}

// ConnectionCondition 连接条件
type ConnectionCondition struct {
	Path        string            `json:"path"`
	Origin      string            `json:"origin"`
	Subprotocol string            `json:"subprotocol"`
	Headers     map[string]string `json:"headers"`
}

// ConnectionTransform 连接转换
type ConnectionTransform struct {
	PathRewrite   string            `json:"path_rewrite"`
	AddHeaders    map[string]string `json:"add_headers"`
	RemoveHeaders []string          `json:"remove_headers"`
	Subprotocol   string            `json:"subprotocol"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled          bool   `json:"enabled"`
	Interval         string `json:"interval"`
	Timeout          string `json:"timeout"`
	Path             string `json:"path"`
	Message          string `json:"message"`
	ExpectedResponse string `json:"expected_response"`
}

// TimeoutConfig 超时配置
type TimeoutConfig struct {
	Connect string `json:"connect"`
	Read    string `json:"read"`
	Write   string `json:"write"`
	Idle    string `json:"idle"`
}

// Middleware WebSocket中间件接口
type Middleware interface {
	// 处理连接
	HandleConnection(conn Connection, next MiddlewareHandler) error
	
	// 处理消息
	HandleMessage(conn Connection, message *Message, next MessageHandler) error
	
	// 获取中间件名称
	Name() string
}

// MiddlewareHandler 中间件处理器
type MiddlewareHandler func(conn Connection) error

// MessageHandler 消息处理器
type MessageHandler func(conn Connection, message *Message) error

// WebSocketStats WebSocket统计信息
type WebSocketStats struct {
	// 总连接数
	TotalConnections int64 `json:"total_connections"`
	
	// 活跃连接数
	ActiveConnections int64 `json:"active_connections"`
	
	// 总消息数
	TotalMessages int64 `json:"total_messages"`
	
	// 发送消息数
	SentMessages int64 `json:"sent_messages"`
	
	// 接收消息数
	ReceivedMessages int64 `json:"received_messages"`
	
	// 错误数
	Errors int64 `json:"errors"`
	
	// 平均连接时长
	AvgConnectionDuration time.Duration `json:"avg_connection_duration"`
	
	// 平均消息大小
	AvgMessageSize int64 `json:"avg_message_size"`
	
	// 启动时间
	StartTime time.Time `json:"start_time"`
	
	// 最后更新时间
	LastUpdate time.Time `json:"last_update"`
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	// 连接ID
	ConnectionID string `json:"connection_id"`
	
	// 发送消息数
	SentMessages int64 `json:"sent_messages"`
	
	// 接收消息数
	ReceivedMessages int64 `json:"received_messages"`
	
	// 发送字节数
	SentBytes int64 `json:"sent_bytes"`
	
	// 接收字节数
	ReceivedBytes int64 `json:"received_bytes"`
	
	// 连接时间
	ConnectedAt time.Time `json:"connected_at"`
	
	// 最后活跃时间
	LastActivity time.Time `json:"last_activity"`
	
	// 错误数
	Errors int64 `json:"errors"`
}

// ProxyStats 代理统计信息
type ProxyStats struct {
	// 代理连接数
	ProxiedConnections int64 `json:"proxied_connections"`
	
	// 活跃代理数
	ActiveProxies int64 `json:"active_proxies"`
	
	// 代理消息数
	ProxiedMessages int64 `json:"proxied_messages"`
	
	// 代理字节数
	ProxiedBytes int64 `json:"proxied_bytes"`
	
	// 代理错误数
	ProxyErrors int64 `json:"proxy_errors"`
	
	// 平均代理延迟
	AvgProxyLatency time.Duration `json:"avg_proxy_latency"`
}

// WebSocketEvent WebSocket事件
type WebSocketEvent struct {
	// 事件类型
	Type EventType `json:"type"`
	
	// 连接ID
	ConnectionID string `json:"connection_id"`
	
	// 消息ID
	MessageID string `json:"message_id,omitempty"`
	
	// 事件数据
	Data interface{} `json:"data,omitempty"`
	
	// 事件时间戳
	Timestamp time.Time `json:"timestamp"`
	
	// 事件元数据
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// EventType 事件类型
type EventType string

const (
	EventTypeConnectionOpen    EventType = "connection_open"
	EventTypeConnectionClose   EventType = "connection_close"
	EventTypeMessageReceived   EventType = "message_received"
	EventTypeMessageSent       EventType = "message_sent"
	EventTypeError            EventType = "error"
	EventTypeProxyStart       EventType = "proxy_start"
	EventTypeProxyEnd         EventType = "proxy_end"
)

// WebSocketEventListener WebSocket事件监听器
type WebSocketEventListener interface {
	// 处理WebSocket事件
	OnWebSocketEvent(event *WebSocketEvent) error
	
	// 获取监听的事件类型
	GetEventTypes() []EventType
}
