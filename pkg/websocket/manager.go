package websocket

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gobwas/ws"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// Manager WebSocket管理器实现
type Manager struct {
	config config.WebSocketConfig
	logger *telemetry.Logger
	
	// 连接管理
	connections    map[string]Connection
	connectionsMux sync.RWMutex
	
	// 路由器
	router WebSocketRouter
	
	// 代理
	proxy WebSocketProxy
	
	// 中间件
	middlewares []Middleware
	
	// 事件监听器
	listeners []WebSocketEventListener
	listenersMux sync.RWMutex
	
	// 统计信息
	stats *WebSocketStats
	statsMux sync.RWMutex
	
	// 生命周期
	running   bool
	startTime time.Time
	
	// 上下文
	ctx    context.Context
	cancel context.CancelFunc
	
	// 等待组
	wg sync.WaitGroup
}

// NewManager 创建新的WebSocket管理器
func NewManager(cfg config.WebSocketConfig, logger *telemetry.Logger) (*Manager, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	manager := &Manager{
		config:      cfg,
		logger:      logger.With("component", "websocket-manager"),
		connections: make(map[string]Connection),
		middlewares: make([]Middleware, 0),
		listeners:   make([]WebSocketEventListener, 0),
		stats: &WebSocketStats{
			StartTime: time.Now(),
		},
		ctx:    ctx,
		cancel: cancel,
	}
	
	// 初始化路由器
	router, err := NewRouter(cfg.Routes, logger)
	if err != nil {
		return nil, fmt.Errorf("初始化WebSocket路由器失败: %w", err)
	}
	manager.router = router
	
	// 初始化代理
	if cfg.Proxy.Enabled {
		proxy, err := NewProxy(cfg.Proxy, logger)
		if err != nil {
			return nil, fmt.Errorf("初始化WebSocket代理失败: %w", err)
		}
		manager.proxy = proxy
	}
	
	manager.logger.Info("WebSocket管理器初始化完成",
		"enabled", cfg.Enabled,
		"proxy_enabled", cfg.Proxy.Enabled,
		"routes_count", len(cfg.Routes))
	
	return manager, nil
}

// Start 启动WebSocket服务
func (m *Manager) Start() error {
	if !m.config.Enabled {
		m.logger.Info("WebSocket功能未启用")
		return nil
	}
	
	if m.running {
		return nil
	}
	
	m.logger.Info("启动WebSocket管理器")
	
	// 启动监控协程
	m.wg.Add(1)
	go m.monitoringLoop()
	
	// 启动清理协程
	m.wg.Add(1)
	go m.cleanupLoop()
	
	m.running = true
	m.startTime = time.Now()
	
	m.logger.Info("WebSocket管理器启动完成")
	
	return nil
}

// Stop 停止WebSocket服务
func (m *Manager) Stop() error {
	if !m.running {
		return nil
	}
	
	m.logger.Info("停止WebSocket管理器")
	
	// 取消上下文
	m.cancel()
	
	// 关闭所有连接
	m.connectionsMux.Lock()
	for _, conn := range m.connections {
		conn.Close("服务器关闭")
	}
	m.connectionsMux.Unlock()
	
	// 等待协程结束
	m.wg.Wait()
	
	m.running = false
	
	m.logger.Info("WebSocket管理器已停止")
	
	return nil
}

// HandleConnection 处理WebSocket连接
func (m *Manager) HandleConnection(w http.ResponseWriter, r *http.Request) error {
	if !m.config.Enabled {
		http.Error(w, "WebSocket功能未启用", http.StatusServiceUnavailable)
		return fmt.Errorf("WebSocket功能未启用")
	}
	
	// 检查Origin
	if m.config.Connection.CheckOrigin {
		origin := r.Header.Get("Origin")
		if !m.isOriginAllowed(origin) {
			http.Error(w, "Origin不被允许", http.StatusForbidden)
			return fmt.Errorf("Origin不被允许: %s", origin)
		}
	}
	
	// 升级连接
	conn, _, _, err := ws.UpgradeHTTP(r, w)
	if err != nil {
		m.logger.Error("WebSocket升级失败", "error", err)
		return err
	}
	
	// 创建连接对象
	connectionID := generateConnectionID()
	userID := m.extractUserID(r)
	
	wsConn := NewConnection(connectionID, conn, userID, m.logger)
	
	// 注册连接
	m.registerConnection(wsConn)
	
	// 发送连接打开事件
	m.emitEvent(&WebSocketEvent{
		Type:         EventTypeConnectionOpen,
		ConnectionID: connectionID,
		Timestamp:    time.Now(),
		Data: map[string]interface{}{
			"remote_addr": conn.RemoteAddr().String(),
			"user_id":     userID,
		},
	})
	
	// 处理连接
	go m.handleConnectionLifecycle(wsConn, r)
	
	return nil
}

// GetStats 获取连接统计
func (m *Manager) GetStats() (*WebSocketStats, error) {
	m.statsMux.RLock()
	defer m.statsMux.RUnlock()
	
	m.connectionsMux.RLock()
	activeConnections := int64(len(m.connections))
	m.connectionsMux.RUnlock()
	
	return &WebSocketStats{
		TotalConnections:          m.stats.TotalConnections,
		ActiveConnections:         activeConnections,
		TotalMessages:             m.stats.TotalMessages,
		SentMessages:              m.stats.SentMessages,
		ReceivedMessages:          m.stats.ReceivedMessages,
		Errors:                    m.stats.Errors,
		AvgConnectionDuration:     m.stats.AvgConnectionDuration,
		AvgMessageSize:            m.stats.AvgMessageSize,
		StartTime:                 m.stats.StartTime,
		LastUpdate:                time.Now(),
	}, nil
}

// GetActiveConnections 获取活跃连接
func (m *Manager) GetActiveConnections() []*Connection {
	m.connectionsMux.RLock()
	defer m.connectionsMux.RUnlock()
	
	connections := make([]*Connection, 0, len(m.connections))
	for _, conn := range m.connections {
		if conn.IsActive() {
			connCopy := conn
			connections = append(connections, &connCopy)
		}
	}
	
	return connections
}

// Broadcast 广播消息
func (m *Manager) Broadcast(message *Message, filter ConnectionFilter) error {
	m.connectionsMux.RLock()
	defer m.connectionsMux.RUnlock()
	
	var errors []error
	sentCount := 0
	
	for _, conn := range m.connections {
		if !conn.IsActive() {
			continue
		}
		
		if filter != nil && !filter.Match(conn) {
			continue
		}
		
		if err := conn.SendMessage(message); err != nil {
			errors = append(errors, fmt.Errorf("发送到连接 %s 失败: %w", conn.ID(), err))
		} else {
			sentCount++
		}
	}
	
	m.logger.Info("广播消息完成", "sent_count", sentCount, "errors_count", len(errors))
	
	if len(errors) > 0 {
		return fmt.Errorf("广播过程中发生 %d 个错误", len(errors))
	}
	
	return nil
}

// CloseConnection 关闭连接
func (m *Manager) CloseConnection(connectionID string, reason string) error {
	m.connectionsMux.RLock()
	conn, exists := m.connections[connectionID]
	m.connectionsMux.RUnlock()
	
	if !exists {
		return fmt.Errorf("连接 %s 不存在", connectionID)
	}
	
	return conn.Close(reason)
}

// HealthCheck 健康检查
func (m *Manager) HealthCheck() error {
	if !m.running {
		return fmt.Errorf("WebSocket管理器未运行")
	}
	
	// 检查路由器
	if m.router == nil {
		return fmt.Errorf("WebSocket路由器未初始化")
	}
	
	// 检查代理（如果启用）
	if m.config.Proxy.Enabled && m.proxy != nil {
		if err := m.proxy.HealthCheck(); err != nil {
			return fmt.Errorf("WebSocket代理健康检查失败: %w", err)
		}
	}
	
	return nil
}

// AddMiddleware 添加中间件
func (m *Manager) AddMiddleware(middleware Middleware) {
	m.middlewares = append(m.middlewares, middleware)
	m.logger.Info("添加WebSocket中间件", "name", middleware.Name())
}

// AddEventListener 添加事件监听器
func (m *Manager) AddEventListener(listener WebSocketEventListener) {
	m.listenersMux.Lock()
	defer m.listenersMux.Unlock()
	
	m.listeners = append(m.listeners, listener)
}

// 私有方法

// registerConnection 注册连接
func (m *Manager) registerConnection(conn Connection) {
	m.connectionsMux.Lock()
	defer m.connectionsMux.Unlock()
	
	m.connections[conn.ID()] = conn
	
	// 更新统计
	m.statsMux.Lock()
	m.stats.TotalConnections++
	m.statsMux.Unlock()
	
	m.logger.Info("注册WebSocket连接", "connection_id", conn.ID(), "user_id", conn.UserID())
}

// unregisterConnection 注销连接
func (m *Manager) unregisterConnection(connectionID string) {
	m.connectionsMux.Lock()
	defer m.connectionsMux.Unlock()
	
	delete(m.connections, connectionID)
	
	m.logger.Info("注销WebSocket连接", "connection_id", connectionID)
}

// handleConnectionLifecycle 处理连接生命周期
func (m *Manager) handleConnectionLifecycle(conn Connection, r *http.Request) {
	defer func() {
		// 注销连接
		m.unregisterConnection(conn.ID())

		// 发送连接关闭事件
		m.emitEvent(&WebSocketEvent{
			Type:         EventTypeConnectionClose,
			ConnectionID: conn.ID(),
			Timestamp:    time.Now(),
		})
	}()

	// 应用中间件
	for _, middleware := range m.middlewares {
		if err := middleware.HandleConnection(conn, func(c Connection) error {
			return nil
		}); err != nil {
			m.logger.Error("中间件处理连接失败", "middleware", middleware.Name(), "error", err)
			conn.Close("中间件错误")
			return
		}
	}

	// 查找路由
	route, err := m.router.MatchRoute(r.URL.Path)
	if err != nil {
		m.logger.Error("路由匹配失败", "path", r.URL.Path, "error", err)
		conn.Close("路由不存在")
		return
	}

	// 处理消息循环
	for {
		message, err := conn.ReceiveMessage()
		if err != nil {
			m.logger.Error("接收消息失败", "connection_id", conn.ID(), "error", err)
			break
		}

		// 发送消息接收事件
		m.emitEvent(&WebSocketEvent{
			Type:         EventTypeMessageReceived,
			ConnectionID: conn.ID(),
			MessageID:    message.ID,
			Timestamp:    time.Now(),
			Data:         message,
		})

		// 处理消息
		if err := m.handleMessage(conn, message, route); err != nil {
			m.logger.Error("处理消息失败", "connection_id", conn.ID(), "message_id", message.ID, "error", err)
		}

		// 更新统计
		m.updateMessageStats(message)
	}
}

// handleMessage 处理消息
func (m *Manager) handleMessage(conn Connection, message *Message, route *Route) error {
	// 应用消息中间件
	for _, middleware := range m.middlewares {
		if err := middleware.HandleMessage(conn, message, func(c Connection, msg *Message) error {
			return nil
		}); err != nil {
			return fmt.Errorf("中间件处理消息失败: %w", err)
		}
	}

	// 如果启用了代理，进行代理处理
	if m.config.Proxy.Enabled && m.proxy != nil && route.Upstream != nil {
		return m.proxy.ProxyConnection(conn, route.Upstream.URL)
	}

	// 默认回显消息
	response := &Message{
		ID:        generateMessageID(),
		Type:      message.Type,
		Data:      message.Data,
		Timestamp: time.Now(),
		SenderID:  "gateway",
		ReceiverID: message.SenderID,
	}

	return conn.SendMessage(response)
}

// isOriginAllowed 检查Origin是否被允许
func (m *Manager) isOriginAllowed(origin string) bool {
	if len(m.config.Connection.AllowedOrigins) == 0 {
		return true
	}

	for _, allowed := range m.config.Connection.AllowedOrigins {
		if allowed == "*" || allowed == origin {
			return true
		}
	}

	return false
}

// extractUserID 提取用户ID
func (m *Manager) extractUserID(r *http.Request) string {
	// 从查询参数提取
	if userID := r.URL.Query().Get("user_id"); userID != "" {
		return userID
	}

	// 从头部提取
	if userID := r.Header.Get("X-User-ID"); userID != "" {
		return userID
	}

	// 从JWT Token提取（简化实现）
	if token := r.Header.Get("Authorization"); token != "" {
		// 这里应该解析JWT Token获取用户ID
		return "anonymous"
	}

	return "anonymous"
}

// emitEvent 发送事件
func (m *Manager) emitEvent(event *WebSocketEvent) {
	m.listenersMux.RLock()
	listeners := make([]WebSocketEventListener, len(m.listeners))
	copy(listeners, m.listeners)
	m.listenersMux.RUnlock()

	if len(listeners) == 0 {
		return
	}

	// 异步发送事件
	go func() {
		for _, listener := range listeners {
			// 检查监听器是否关心这种事件类型
			eventTypes := listener.GetEventTypes()
			if len(eventTypes) > 0 {
				found := false
				for _, et := range eventTypes {
					if et == event.Type {
						found = true
						break
					}
				}
				if !found {
					continue
				}
			}

			if err := listener.OnWebSocketEvent(event); err != nil {
				m.logger.Error("WebSocket事件处理失败", "event_type", event.Type, "error", err)
			}
		}
	}()
}

// updateMessageStats 更新消息统计
func (m *Manager) updateMessageStats(message *Message) {
	m.statsMux.Lock()
	defer m.statsMux.Unlock()

	m.stats.TotalMessages++
	m.stats.ReceivedMessages++

	// 更新平均消息大小
	messageSize := int64(len(message.Data))
	if m.stats.AvgMessageSize == 0 {
		m.stats.AvgMessageSize = messageSize
	} else {
		m.stats.AvgMessageSize = (m.stats.AvgMessageSize + messageSize) / 2
	}
}

// monitoringLoop 监控循环
func (m *Manager) monitoringLoop() {
	defer m.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.collectMetrics()
		}
	}
}

// cleanupLoop 清理循环
func (m *Manager) cleanupLoop() {
	defer m.wg.Done()

	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.cleanupInactiveConnections()
		}
	}
}

// collectMetrics 收集指标
func (m *Manager) collectMetrics() {
	m.connectionsMux.RLock()
	activeCount := len(m.connections)
	m.connectionsMux.RUnlock()

	m.logger.Debug("WebSocket指标",
		"active_connections", activeCount,
		"total_connections", m.stats.TotalConnections,
		"total_messages", m.stats.TotalMessages)
}

// cleanupInactiveConnections 清理非活跃连接
func (m *Manager) cleanupInactiveConnections() {
	m.connectionsMux.Lock()
	defer m.connectionsMux.Unlock()

	var toRemove []string

	for id, conn := range m.connections {
		if !conn.IsActive() {
			toRemove = append(toRemove, id)
		}
	}

	for _, id := range toRemove {
		delete(m.connections, id)
		m.logger.Debug("清理非活跃连接", "connection_id", id)
	}

	if len(toRemove) > 0 {
		m.logger.Info("清理非活跃连接完成", "removed_count", len(toRemove))
	}
}

// generateConnectionID 生成连接ID
func generateConnectionID() string {
	return fmt.Sprintf("conn_%d", time.Now().UnixNano())
}
