package websocket

import (
	"fmt"
	"regexp"
	"strings"
	"sync"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// Router WebSocket路由器实现
type Router struct {
	routes    []*Route
	routesMux sync.RWMutex
	logger    *telemetry.Logger
}

// NewRouter 创建新的WebSocket路由器
func NewRouter(routeConfigs []config.WebSocketRouteConfig, logger *telemetry.Logger) (*Router, error) {
	router := &Router{
		routes: make([]*Route, 0, len(routeConfigs)),
		logger: logger.With("component", "websocket-router"),
	}
	
	// 转换配置为路由
	for _, routeConfig := range routeConfigs {
		route, err := router.configToRoute(routeConfig)
		if err != nil {
			return nil, fmt.Errorf("转换路由配置失败: %w", err)
		}
		
		router.routes = append(router.routes, route)
	}
	
	router.logger.Info("WebSocket路由器初始化完成", "routes_count", len(router.routes))
	
	return router, nil
}

// AddRoute 添加路由
func (r *Router) AddRoute(route *Route) error {
	if route == nil {
		return fmt.Errorf("路由不能为nil")
	}
	
	if route.Name == "" {
		return fmt.Errorf("路由名称不能为空")
	}
	
	if route.Path == "" {
		return fmt.Errorf("路由路径不能为空")
	}
	
	r.routesMux.Lock()
	defer r.routesMux.Unlock()
	
	// 检查是否已存在同名路由
	for _, existingRoute := range r.routes {
		if existingRoute.Name == route.Name {
			return fmt.Errorf("路由 %s 已存在", route.Name)
		}
	}
	
	r.routes = append(r.routes, route)
	
	r.logger.Info("添加WebSocket路由", "name", route.Name, "path", route.Path)
	
	return nil
}

// RemoveRoute 删除路由
func (r *Router) RemoveRoute(name string) error {
	r.routesMux.Lock()
	defer r.routesMux.Unlock()
	
	for i, route := range r.routes {
		if route.Name == name {
			r.routes = append(r.routes[:i], r.routes[i+1:]...)
			r.logger.Info("删除WebSocket路由", "name", name)
			return nil
		}
	}
	
	return fmt.Errorf("路由 %s 不存在", name)
}

// MatchRoute 匹配路由
func (r *Router) MatchRoute(path string) (*Route, error) {
	r.routesMux.RLock()
	defer r.routesMux.RUnlock()
	
	for _, route := range r.routes {
		if !route.Enabled {
			continue
		}
		
		if r.matchPath(path, route.Path) {
			r.logger.Debug("匹配到WebSocket路由", "path", path, "route", route.Name)
			return route, nil
		}
	}
	
	return nil, fmt.Errorf("未找到匹配的路由: %s", path)
}

// GetRoutes 获取所有路由
func (r *Router) GetRoutes() []*Route {
	r.routesMux.RLock()
	defer r.routesMux.RUnlock()
	
	routes := make([]*Route, len(r.routes))
	copy(routes, r.routes)
	
	return routes
}

// 私有方法

// configToRoute 配置转换为路由
func (r *Router) configToRoute(config config.WebSocketRouteConfig) (*Route, error) {
	route := &Route{
		Name:    config.Name,
		Path:    config.Path,
		Enabled: config.Enabled,
	}
	
	// 转换上游配置
	if config.Upstream.Type != "" {
		route.Upstream = &Upstream{
			Type:         config.Upstream.Type,
			URL:          config.Upstream.URL,
			ServiceName:  config.Upstream.ServiceName,
			LoadBalancer: config.Upstream.LoadBalancer,
		}
		
		// 转换健康检查配置
		if config.Upstream.HealthCheck.Enabled {
			route.Upstream.HealthCheck = &HealthCheckConfig{
				Enabled:          config.Upstream.HealthCheck.Enabled,
				Interval:         config.Upstream.HealthCheck.Interval,
				Timeout:          config.Upstream.HealthCheck.Timeout,
				Path:             config.Upstream.HealthCheck.Path,
				Message:          config.Upstream.HealthCheck.Message,
				ExpectedResponse: config.Upstream.HealthCheck.ExpectedResponse,
			}
		}
		
		// 转换超时配置
		route.Upstream.Timeout = &TimeoutConfig{
			Connect: config.Upstream.Timeout.Connect,
			Read:    config.Upstream.Timeout.Read,
			Write:   config.Upstream.Timeout.Write,
			Idle:    config.Upstream.Timeout.Idle,
		}
	}
	
	// 转换认证配置
	if config.Auth.Required {
		route.Auth = &AuthConfig{
			Required: config.Auth.Required,
			Methods:  config.Auth.Methods,
		}
	}
	
	// 转换转换配置
	if config.Transform.Enabled {
		route.Transform = &TransformConfig{
			Enabled: config.Transform.Enabled,
		}
		
		// 转换消息转换规则
		for _, ruleConfig := range config.Transform.MessageRules {
			rule := &MessageTransformRule{
				Name:    ruleConfig.Name,
				Enabled: ruleConfig.Enabled,
			}
			
			// 转换条件
			if ruleConfig.Condition.MessageType != "" {
				rule.Condition = &FilterCondition{
					MessageType: ruleConfig.Condition.MessageType,
				}
				
				// 转换大小范围
				if ruleConfig.Condition.SizeRange.Min > 0 || ruleConfig.Condition.SizeRange.Max > 0 {
					rule.Condition.SizeRange = &SizeRange{
						Min: ruleConfig.Condition.SizeRange.Min,
						Max: ruleConfig.Condition.SizeRange.Max,
					}
				}
				
				// 转换内容匹配
				if ruleConfig.Condition.ContentMatch.Type != "" {
					rule.Condition.ContentMatch = &ContentMatch{
						Type:          ruleConfig.Condition.ContentMatch.Type,
						Value:         ruleConfig.Condition.ContentMatch.Value,
						CaseSensitive: ruleConfig.Condition.ContentMatch.CaseSensitive,
					}
				}
				
				// 转换频率限制
				if ruleConfig.Condition.RateLimit.Requests > 0 {
					rule.Condition.RateLimit = &RateLimit{
						Requests: ruleConfig.Condition.RateLimit.Requests,
						Window:   ruleConfig.Condition.RateLimit.Window,
						Key:      ruleConfig.Condition.RateLimit.Key,
					}
				}
			}
			
			// 转换转换操作
			if ruleConfig.Transform.Type != "" {
				rule.Transform = &MessageTransform{
					Type:    ruleConfig.Transform.Type,
					Headers: ruleConfig.Transform.Headers,
				}
				
				// 转换格式转换
				if ruleConfig.Transform.Format.From != "" {
					rule.Transform.Format = &FormatTransform{
						From:    ruleConfig.Transform.Format.From,
						To:      ruleConfig.Transform.Format.To,
						Options: ruleConfig.Transform.Format.Options,
					}
				}
				
				// 转换内容转换
				if ruleConfig.Transform.Content.Type != "" {
					rule.Transform.Content = &ContentTransform{
						Type:     ruleConfig.Transform.Content.Type,
						Template: ruleConfig.Transform.Content.Template,
					}
					
					// 转换正则转换
					if ruleConfig.Transform.Content.Regex.Pattern != "" {
						rule.Transform.Content.Regex = &RegexTransform{
							Pattern:     ruleConfig.Transform.Content.Regex.Pattern,
							Replacement: ruleConfig.Transform.Content.Regex.Replacement,
							Global:      ruleConfig.Transform.Content.Regex.Global,
						}
					}
					
					// 转换JSONPath转换
					for _, jpConfig := range ruleConfig.Transform.Content.JSONPath {
						jp := &JSONPathTransform{
							Path:       jpConfig.Path,
							Operation:  jpConfig.Operation,
							Value:      jpConfig.Value,
							TargetPath: jpConfig.TargetPath,
						}
						rule.Transform.Content.JSONPath = append(rule.Transform.Content.JSONPath, jp)
					}
				}
			}
			
			route.Transform.MessageRules = append(route.Transform.MessageRules, rule)
		}
		
		// 转换连接转换规则
		for _, ruleConfig := range config.Transform.ConnectionRules {
			rule := &ConnectionTransformRule{
				Name:    ruleConfig.Name,
				Enabled: ruleConfig.Enabled,
			}
			
			// 转换条件
			if ruleConfig.Condition.Path != "" {
				rule.Condition = &ConnectionCondition{
					Path:        ruleConfig.Condition.Path,
					Origin:      ruleConfig.Condition.Origin,
					Subprotocol: ruleConfig.Condition.Subprotocol,
					Headers:     ruleConfig.Condition.Headers,
				}
			}
			
			// 转换转换操作
			rule.Transform = &ConnectionTransform{
				PathRewrite:   ruleConfig.Transform.PathRewrite,
				AddHeaders:    ruleConfig.Transform.AddHeaders,
				RemoveHeaders: ruleConfig.Transform.RemoveHeaders,
				Subprotocol:   ruleConfig.Transform.Subprotocol,
			}
			
			route.Transform.ConnectionRules = append(route.Transform.ConnectionRules, rule)
		}
	}
	
	// 设置中间件
	route.Middlewares = make([]Middleware, 0, len(config.Middlewares))
	
	return route, nil
}

// matchPath 匹配路径
func (r *Router) matchPath(path, pattern string) bool {
	// 精确匹配
	if pattern == path {
		return true
	}
	
	// 通配符匹配
	if pattern == "*" {
		return true
	}
	
	// 前缀匹配
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(path, prefix)
	}
	
	// 后缀匹配
	if strings.HasPrefix(pattern, "*") {
		suffix := strings.TrimPrefix(pattern, "*")
		return strings.HasSuffix(path, suffix)
	}
	
	// 正则表达式匹配
	if strings.Contains(pattern, "{") && strings.Contains(pattern, "}") {
		return r.matchRegexPath(path, pattern)
	}
	
	return false
}

// matchRegexPath 正则表达式路径匹配
func (r *Router) matchRegexPath(path, pattern string) bool {
	// 将路径参数转换为正则表达式
	regexPattern := regexp.MustCompile(`\{[^}]+\}`).ReplaceAllString(pattern, `([^/]+)`)
	regexPattern = "^" + regexPattern + "$"
	
	matched, err := regexp.MatchString(regexPattern, path)
	if err != nil {
		r.logger.Error("正则表达式匹配失败", "pattern", regexPattern, "path", path, "error", err)
		return false
	}
	
	return matched
}
