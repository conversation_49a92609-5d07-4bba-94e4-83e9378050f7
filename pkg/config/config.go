package config

import (
	"time"
)

// Config represents the complete gateway configuration
type Config struct {
	Server    ServerConfig    `yaml:"server" mapstructure:"server"`
	Logging   LoggingConfig   `yaml:"logging" mapstructure:"logging"`
	Metrics   MetricsConfig   `yaml:"metrics" mapstructure:"metrics"`
	Tracing   TracingConfig   `yaml:"tracing" mapstructure:"tracing"`
	Auth      AuthConfig      `yaml:"auth" mapstructure:"auth"`
	Security  SecurityConfig  `yaml:"security" mapstructure:"security"`
	Routes    []RouteConfig   `yaml:"routes" mapstructure:"routes"`
	Plugins   PluginConfig    `yaml:"plugins" mapstructure:"plugins"`
	Discovery DiscoveryConfig `yaml:"discovery" mapstructure:"discovery"`
	GRPC      GRPCConfig      `yaml:"grpc" mapstructure:"grpc"`
	Cache      CacheConfig      `yaml:"cache" mapstructure:"cache"`
	Transform  TransformConfig  `yaml:"transform" mapstructure:"transform"`
	WebSocket  WebSocketConfig  `yaml:"websocket" mapstructure:"websocket"`
	Serverless ServerlessConfig `yaml:"serverless" mapstructure:"serverless"`
}

// ServerConfig contains HTTP server configuration
type ServerConfig struct {
	Address      string    `yaml:"address" mapstructure:"address"`
	ReadTimeout  int       `yaml:"read_timeout" mapstructure:"read_timeout"`
	WriteTimeout int       `yaml:"write_timeout" mapstructure:"write_timeout"`
	IdleTimeout  int       `yaml:"idle_timeout" mapstructure:"idle_timeout"`
	TLS          TLSConfig `yaml:"tls" mapstructure:"tls"`
}

// TLSConfig contains TLS configuration
type TLSConfig struct {
	Enabled  bool   `yaml:"enabled" mapstructure:"enabled"`
	CertFile string `yaml:"cert_file" mapstructure:"cert_file"`
	KeyFile  string `yaml:"key_file" mapstructure:"key_file"`
	MTLSMode string `yaml:"mtls_mode" mapstructure:"mtls_mode"` // none, request, require
}

// LoggingConfig contains logging configuration
type LoggingConfig struct {
	Level      string `yaml:"level" mapstructure:"level"`
	Format     string `yaml:"format" mapstructure:"format"` // json, text
	Output     string `yaml:"output" mapstructure:"output"` // stdout, file
	File       string `yaml:"file" mapstructure:"file"`
	MaxSize    int    `yaml:"max_size" mapstructure:"max_size"`
	MaxBackups int    `yaml:"max_backups" mapstructure:"max_backups"`
	MaxAge     int    `yaml:"max_age" mapstructure:"max_age"`
}

// MetricsConfig contains metrics configuration
type MetricsConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	Path    string `yaml:"path" mapstructure:"path"`
	Port    int    `yaml:"port" mapstructure:"port"`
}

// TracingConfig contains distributed tracing configuration
type TracingConfig struct {
	Enabled     bool   `yaml:"enabled" mapstructure:"enabled"`
	ServiceName string `yaml:"service_name" mapstructure:"service_name"`
	Endpoint    string `yaml:"endpoint" mapstructure:"endpoint"`
	SampleRate  float64 `yaml:"sample_rate" mapstructure:"sample_rate"`
}

// AuthConfig contains authentication configuration
type AuthConfig struct {
	JWT      JWTConfig      `yaml:"jwt" mapstructure:"jwt"`
	OIDC     OIDCConfig     `yaml:"oidc" mapstructure:"oidc"`
	APIKey   APIKeyConfig   `yaml:"api_key" mapstructure:"api_key"`
	MTLS     MTLSConfig     `yaml:"mtls" mapstructure:"mtls"`
	Policies PolicyConfig   `yaml:"policies" mapstructure:"policies"`
}

// JWTConfig contains JWT authentication configuration
type JWTConfig struct {
	Enabled    bool   `yaml:"enabled" mapstructure:"enabled"`
	Secret     string `yaml:"secret" mapstructure:"secret"`
	PublicKey  string `yaml:"public_key" mapstructure:"public_key"`
	Algorithm  string `yaml:"algorithm" mapstructure:"algorithm"`
	Expiration int    `yaml:"expiration" mapstructure:"expiration"`
}

// OIDCConfig contains OIDC configuration
// OIDC配置结构
type OIDCConfig struct {
	Enabled      bool     `yaml:"enabled" mapstructure:"enabled"`           // 是否启用OIDC认证
	Issuer       string   `yaml:"issuer" mapstructure:"issuer"`             // OIDC提供者URL
	ClientID     string   `yaml:"client_id" mapstructure:"client_id"`       // 客户端ID
	ClientSecret string   `yaml:"client_secret" mapstructure:"client_secret"` // 客户端密钥
	RedirectURL  string   `yaml:"redirect_url" mapstructure:"redirect_url"` // 重定向URL
	Scopes       []string `yaml:"scopes" mapstructure:"scopes"`             // 请求的权限范围
	SkipVerify   bool     `yaml:"skip_verify" mapstructure:"skip_verify"`   // 是否跳过TLS证书验证
}

// APIKeyConfig contains API key authentication configuration
type APIKeyConfig struct {
	Enabled    bool   `yaml:"enabled" mapstructure:"enabled"`
	HeaderName string `yaml:"header_name" mapstructure:"header_name"`
	QueryParam string `yaml:"query_param" mapstructure:"query_param"`
}

// MTLSConfig contains mutual TLS configuration
type MTLSConfig struct {
	// 是否启用mTLS认证
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// CA证书文件路径
	CAFile string `yaml:"ca_file" mapstructure:"ca_file"`

	// CRL文件路径（可选）
	CRLFile string `yaml:"crl_file" mapstructure:"crl_file"`

	// 是否启用OCSP检查
	OCSPEnabled bool `yaml:"ocsp_enabled" mapstructure:"ocsp_enabled"`

	// OCSP超时时间（秒）
	OCSPTimeoutSeconds int `yaml:"ocsp_timeout_seconds" mapstructure:"ocsp_timeout_seconds"`

	// 是否验证客户端证书的CN字段
	VerifyClientCertCN bool `yaml:"verify_client_cert_cn" mapstructure:"verify_client_cert_cn"`

	// 允许的客户端证书CN列表（如果启用CN验证）
	AllowedCNs []string `yaml:"allowed_cns" mapstructure:"allowed_cns"`

	// 是否验证客户端证书的组织字段
	VerifyOrganization bool `yaml:"verify_organization" mapstructure:"verify_organization"`

	// 允许的组织列表（如果启用组织验证）
	AllowedOrganizations []string `yaml:"allowed_organizations" mapstructure:"allowed_organizations"`
}

// PolicyConfig contains authorization policy configuration
type PolicyConfig struct {
	Engine   string `yaml:"engine" mapstructure:"engine"` // opa, builtin
	OPAPath  string `yaml:"opa_path" mapstructure:"opa_path"`
	Policies []PolicyRule `yaml:"policies" mapstructure:"policies"`
}

// PolicyRule represents a single authorization rule
type PolicyRule struct {
	Name        string            `yaml:"name" mapstructure:"name"`
	Path        string            `yaml:"path" mapstructure:"path"`
	Method      string            `yaml:"method" mapstructure:"method"`
	Roles       []string          `yaml:"roles" mapstructure:"roles"`
	Permissions []string          `yaml:"permissions" mapstructure:"permissions"`
	Conditions  map[string]string `yaml:"conditions" mapstructure:"conditions"`
}

// SecurityConfig contains security-related configuration
type SecurityConfig struct {
	RateLimit RateLimitConfig `yaml:"rate_limit" mapstructure:"rate_limit"`
	CORS      CORSConfig      `yaml:"cors" mapstructure:"cors"`
	WAF       WAFConfig       `yaml:"waf" mapstructure:"waf"`
	IPFilter  IPFilterConfig  `yaml:"ip_filter" mapstructure:"ip_filter"`
}

// RateLimitConfig contains rate limiting configuration
type RateLimitConfig struct {
	Enabled   bool          `yaml:"enabled" mapstructure:"enabled"`
	Algorithm string        `yaml:"algorithm" mapstructure:"algorithm"` // token_bucket, leaky_bucket
	Rules     []RateLimitRule `yaml:"rules" mapstructure:"rules"`
}

// RateLimitRule represents a rate limiting rule
type RateLimitRule struct {
	Path     string        `yaml:"path" mapstructure:"path"`
	Method   string        `yaml:"method" mapstructure:"method"`
	Rate     int           `yaml:"rate" mapstructure:"rate"`
	Burst    int           `yaml:"burst" mapstructure:"burst"`
	Window   time.Duration `yaml:"window" mapstructure:"window"`
	KeyBy    string        `yaml:"key_by" mapstructure:"key_by"` // ip, user, api_key
}

// CORSConfig contains CORS configuration
type CORSConfig struct {
	Enabled          bool     `yaml:"enabled" mapstructure:"enabled"`
	AllowedOrigins   []string `yaml:"allowed_origins" mapstructure:"allowed_origins"`
	AllowedMethods   []string `yaml:"allowed_methods" mapstructure:"allowed_methods"`
	AllowedHeaders   []string `yaml:"allowed_headers" mapstructure:"allowed_headers"`
	ExposedHeaders   []string `yaml:"exposed_headers" mapstructure:"exposed_headers"`
	AllowCredentials bool     `yaml:"allow_credentials" mapstructure:"allow_credentials"`
	MaxAge           int      `yaml:"max_age" mapstructure:"max_age"`
}

// WAFConfig contains Web Application Firewall configuration
type WAFConfig struct {
	Enabled bool      `yaml:"enabled" mapstructure:"enabled"`
	Rules   []WAFRule `yaml:"rules" mapstructure:"rules"`
}

// WAFRule represents a WAF rule
type WAFRule struct {
	Name        string `yaml:"name" mapstructure:"name"`
	Pattern     string `yaml:"pattern" mapstructure:"pattern"`
	Action      string `yaml:"action" mapstructure:"action"` // block, log, allow
	Description string `yaml:"description" mapstructure:"description"`
}

// IPFilterConfig contains IP filtering configuration
type IPFilterConfig struct {
	Enabled   bool     `yaml:"enabled" mapstructure:"enabled"`
	Whitelist []string `yaml:"whitelist" mapstructure:"whitelist"`
	Blacklist []string `yaml:"blacklist" mapstructure:"blacklist"`
}

// RouteConfig represents a routing configuration
type RouteConfig struct {
	Name         string            `yaml:"name" mapstructure:"name"`
	Path         string            `yaml:"path" mapstructure:"path"`
	Method       string            `yaml:"method" mapstructure:"method"`
	Upstream     UpstreamConfig    `yaml:"upstream" mapstructure:"upstream"`
	Rewrite      RewriteConfig     `yaml:"rewrite" mapstructure:"rewrite"`
	Timeout      time.Duration     `yaml:"timeout" mapstructure:"timeout"`
	Retries      int               `yaml:"retries" mapstructure:"retries"`
	Headers      map[string]string `yaml:"headers" mapstructure:"headers"`
	Plugins      []string          `yaml:"plugins" mapstructure:"plugins"`
}

// UpstreamConfig contains upstream service configuration
type UpstreamConfig struct {
	Type            string              `yaml:"type" mapstructure:"type"` // static, discovery
	LoadBalancer    string              `yaml:"load_balancer" mapstructure:"load_balancer"`
	HealthCheck     HealthCheckConfig   `yaml:"health_check" mapstructure:"health_check"`
	Servers         []ServerTarget      `yaml:"servers" mapstructure:"servers"`
	ServiceName     string              `yaml:"service_name" mapstructure:"service_name"`
	DiscoveryConfig DiscoveryConfig     `yaml:"discovery_config" mapstructure:"discovery_config"`
}

// ServerTarget represents an upstream server
type ServerTarget struct {
	Host   string `yaml:"host" mapstructure:"host"`
	Port   int    `yaml:"port" mapstructure:"port"`
	Weight int    `yaml:"weight" mapstructure:"weight"`
	Backup bool   `yaml:"backup" mapstructure:"backup"`
}

// HealthCheckConfig contains health check configuration
type HealthCheckConfig struct {
	Enabled  bool          `yaml:"enabled" mapstructure:"enabled"`
	Path     string        `yaml:"path" mapstructure:"path"`
	Interval time.Duration `yaml:"interval" mapstructure:"interval"`
	Timeout  time.Duration `yaml:"timeout" mapstructure:"timeout"`
	Retries  int           `yaml:"retries" mapstructure:"retries"`
}

// RewriteConfig contains URL rewrite configuration
type RewriteConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	From    string `yaml:"from" mapstructure:"from"`
	To      string `yaml:"to" mapstructure:"to"`
}

// PluginConfig contains plugin configuration
type PluginConfig struct {
	Directory string                 `yaml:"directory" mapstructure:"directory"`
	Plugins   map[string]interface{} `yaml:"plugins" mapstructure:"plugins"`
}

// DiscoveryConfig contains service discovery configuration
type DiscoveryConfig struct {
	Type   string      `yaml:"type" mapstructure:"type"` // consul, nacos, eureka
	Consul ConsulConfig `yaml:"consul" mapstructure:"consul"`
	Nacos  NacosConfig  `yaml:"nacos" mapstructure:"nacos"`
}

// ConsulConfig contains Consul configuration
type ConsulConfig struct {
	Address    string `yaml:"address" mapstructure:"address"`
	Datacenter string `yaml:"datacenter" mapstructure:"datacenter"`
	Token      string `yaml:"token" mapstructure:"token"`
}

// NacosConfig contains Nacos configuration
type NacosConfig struct {
	ServerConfigs []NacosServerConfig `yaml:"server_configs" mapstructure:"server_configs"`
	ClientConfig  NacosClientConfig   `yaml:"client_config" mapstructure:"client_config"`
}

// NacosServerConfig represents Nacos server configuration
type NacosServerConfig struct {
	IpAddr string `yaml:"ip_addr" mapstructure:"ip_addr"`
	Port   uint64 `yaml:"port" mapstructure:"port"`
}

// NacosClientConfig represents Nacos client configuration
type NacosClientConfig struct {
	NamespaceId         string `yaml:"namespace_id" mapstructure:"namespace_id"`
	TimeoutMs           uint64 `yaml:"timeout_ms" mapstructure:"timeout_ms"`
	NotLoadCacheAtStart bool   `yaml:"not_load_cache_at_start" mapstructure:"not_load_cache_at_start"`
	LogDir              string `yaml:"log_dir" mapstructure:"log_dir"`
	CacheDir            string `yaml:"cache_dir" mapstructure:"cache_dir"`
	LogLevel            string `yaml:"log_level" mapstructure:"log_level"`
}

// GRPCConfig contains gRPC configuration
type GRPCConfig struct {
	// 是否启用gRPC支持
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// gRPC服务器配置
	Server GRPCServerConfig `yaml:"server" mapstructure:"server"`

	// gRPC客户端配置
	Client GRPCClientConfig `yaml:"client" mapstructure:"client"`

	// HTTP到gRPC的转换配置
	Gateway GRPCGatewayConfig `yaml:"gateway" mapstructure:"gateway"`

	// 健康检查配置
	HealthCheck GRPCHealthConfig `yaml:"health_check" mapstructure:"health_check"`
}

// GRPCServerConfig gRPC服务器配置
type GRPCServerConfig struct {
	// 监听地址
	Address string `yaml:"address" mapstructure:"address"`

	// 是否启用反射
	Reflection bool `yaml:"reflection" mapstructure:"reflection"`

	// 最大接收消息大小
	MaxRecvMsgSize int `yaml:"max_recv_msg_size" mapstructure:"max_recv_msg_size"`

	// 最大发送消息大小
	MaxSendMsgSize int `yaml:"max_send_msg_size" mapstructure:"max_send_msg_size"`

	// 连接超时
	ConnectionTimeout int `yaml:"connection_timeout" mapstructure:"connection_timeout"`

	// Keep-alive配置
	KeepAlive GRPCKeepAliveConfig `yaml:"keep_alive" mapstructure:"keep_alive"`
}

// GRPCClientConfig gRPC客户端配置
type GRPCClientConfig struct {
	// 默认超时时间
	DefaultTimeout int `yaml:"default_timeout" mapstructure:"default_timeout"`

	// 最大接收消息大小
	MaxRecvMsgSize int `yaml:"max_recv_msg_size" mapstructure:"max_recv_msg_size"`

	// 最大发送消息大小
	MaxSendMsgSize int `yaml:"max_send_msg_size" mapstructure:"max_send_msg_size"`

	// 重试配置
	Retry GRPCRetryConfig `yaml:"retry" mapstructure:"retry"`

	// 负载均衡策略
	LoadBalancer string `yaml:"load_balancer" mapstructure:"load_balancer"`

	// Keep-alive配置
	KeepAlive GRPCKeepAliveConfig `yaml:"keep_alive" mapstructure:"keep_alive"`
}

// GRPCGatewayConfig HTTP到gRPC网关配置
type GRPCGatewayConfig struct {
	// 是否启用HTTP到gRPC转换
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 路径前缀
	PathPrefix string `yaml:"path_prefix" mapstructure:"path_prefix"`

	// 是否允许删除请求体
	AllowDeleteBody bool `yaml:"allow_delete_body" mapstructure:"allow_delete_body"`

	// 是否允许PATCH功能
	AllowPatchFeature bool `yaml:"allow_patch_feature" mapstructure:"allow_patch_feature"`

	// 元数据映射
	MetadataMapping map[string]string `yaml:"metadata_mapping" mapstructure:"metadata_mapping"`
}

// GRPCHealthConfig gRPC健康检查配置
type GRPCHealthConfig struct {
	// 是否启用健康检查
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 检查间隔（秒）
	Interval int `yaml:"interval" mapstructure:"interval"`

	// 超时时间（秒）
	Timeout int `yaml:"timeout" mapstructure:"timeout"`

	// 不健康阈值
	UnhealthyThreshold int `yaml:"unhealthy_threshold" mapstructure:"unhealthy_threshold"`

	// 健康阈值
	HealthyThreshold int `yaml:"healthy_threshold" mapstructure:"healthy_threshold"`
}

// GRPCKeepAliveConfig Keep-alive配置
type GRPCKeepAliveConfig struct {
	// Keep-alive时间
	Time int `yaml:"time" mapstructure:"time"`

	// Keep-alive超时
	Timeout int `yaml:"timeout" mapstructure:"timeout"`

	// 是否允许没有流时的keep-alive
	PermitWithoutStream bool `yaml:"permit_without_stream" mapstructure:"permit_without_stream"`
}

// GRPCRetryConfig gRPC重试配置
type GRPCRetryConfig struct {
	// 最大重试次数
	MaxAttempts int `yaml:"max_attempts" mapstructure:"max_attempts"`

	// 初始退避时间（毫秒）
	InitialBackoff int `yaml:"initial_backoff" mapstructure:"initial_backoff"`

	// 最大退避时间（毫秒）
	MaxBackoff int `yaml:"max_backoff" mapstructure:"max_backoff"`

	// 退避倍数
	BackoffMultiplier float64 `yaml:"backoff_multiplier" mapstructure:"backoff_multiplier"`

	// 可重试的状态码
	RetryableStatusCodes []string `yaml:"retryable_status_codes" mapstructure:"retryable_status_codes"`
}

// CacheConfig 高级缓存系统配置
type CacheConfig struct {
	// 是否启用缓存
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 缓存类型：memory, redis, multi_level
	Type string `yaml:"type" mapstructure:"type"`

	// 默认TTL
	DefaultTTL string `yaml:"default_ttl" mapstructure:"default_ttl"`

	// 最大TTL
	MaxTTL string `yaml:"max_ttl" mapstructure:"max_ttl"`

	// 内存缓存配置
	Memory CacheMemoryConfig `yaml:"memory" mapstructure:"memory"`

	// Redis缓存配置
	Redis CacheRedisConfig `yaml:"redis" mapstructure:"redis"`

	// 多级缓存配置
	MultiLevel CacheMultiLevelConfig `yaml:"multi_level" mapstructure:"multi_level"`

	// 缓存策略
	Policies []CachePolicy `yaml:"policies" mapstructure:"policies"`

	// 缓存键生成器配置
	KeyGenerator CacheKeyConfig `yaml:"key_generator" mapstructure:"key_generator"`

	// 缓存预热配置
	Warmup CacheWarmupConfig `yaml:"warmup" mapstructure:"warmup"`

	// 缓存一致性配置
	Consistency CacheConsistencyConfig `yaml:"consistency" mapstructure:"consistency"`

	// 压缩配置
	Compression CacheCompressionConfig `yaml:"compression" mapstructure:"compression"`
}

// CacheMemoryConfig 内存缓存配置
type CacheMemoryConfig struct {
	// 最大大小（字节）
	MaxSize int64 `yaml:"max_size" mapstructure:"max_size"`

	// 最大条目数
	MaxItems int `yaml:"max_items" mapstructure:"max_items"`

	// 清理间隔
	CleanupInterval string `yaml:"cleanup_interval" mapstructure:"cleanup_interval"`

	// 淘汰策略：lru, lfu, fifo, random
	EvictionPolicy string `yaml:"eviction_policy" mapstructure:"eviction_policy"`
}

// CacheRedisConfig Redis缓存配置
type CacheRedisConfig struct {
	// Redis地址
	Address string `yaml:"address" mapstructure:"address"`

	// 密码
	Password string `yaml:"password" mapstructure:"password"`

	// 数据库
	DB int `yaml:"db" mapstructure:"db"`

	// 连接池大小
	PoolSize int `yaml:"pool_size" mapstructure:"pool_size"`

	// 最小空闲连接
	MinIdleConns int `yaml:"min_idle_conns" mapstructure:"min_idle_conns"`

	// 最大重试次数
	MaxRetries int `yaml:"max_retries" mapstructure:"max_retries"`

	// 重试延迟
	RetryDelay string `yaml:"retry_delay" mapstructure:"retry_delay"`

	// 连接超时
	DialTimeout string `yaml:"dial_timeout" mapstructure:"dial_timeout"`

	// 读超时
	ReadTimeout string `yaml:"read_timeout" mapstructure:"read_timeout"`

	// 写超时
	WriteTimeout string `yaml:"write_timeout" mapstructure:"write_timeout"`

	// 键前缀
	KeyPrefix string `yaml:"key_prefix" mapstructure:"key_prefix"`
}

// CacheMultiLevelConfig 多级缓存配置
type CacheMultiLevelConfig struct {
	// 缓存级别
	Levels []CacheLevel `yaml:"levels" mapstructure:"levels"`

	// 写入策略：write_through, write_back, write_around
	WritePolicy string `yaml:"write_policy" mapstructure:"write_policy"`

	// 读取策略：read_through, read_around
	ReadPolicy string `yaml:"read_policy" mapstructure:"read_policy"`
}

// CacheLevel 缓存级别配置
type CacheLevel struct {
	// 级别名称
	Name string `yaml:"name" mapstructure:"name"`

	// 缓存类型
	Type string `yaml:"type" mapstructure:"type"`

	// 大小限制
	Size int64 `yaml:"size" mapstructure:"size"`

	// TTL
	TTL string `yaml:"ttl" mapstructure:"ttl"`

	// 优先级
	Priority int `yaml:"priority" mapstructure:"priority"`

	// 配置选项
	Options map[string]interface{} `yaml:"options" mapstructure:"options"`
}

// CachePolicy 缓存策略配置
type CachePolicy struct {
	// 策略名称
	Name string `yaml:"name" mapstructure:"name"`

	// 匹配路径
	Paths []string `yaml:"paths" mapstructure:"paths"`

	// 匹配方法
	Methods []string `yaml:"methods" mapstructure:"methods"`

	// TTL
	TTL string `yaml:"ttl" mapstructure:"ttl"`

	// 缓存条件
	Conditions []CacheCondition `yaml:"conditions" mapstructure:"conditions"`

	// 变化因子（影响缓存键的因素）
	VaryBy []string `yaml:"vary_by" mapstructure:"vary_by"`

	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 优先级
	Priority int `yaml:"priority" mapstructure:"priority"`
}

// CacheCondition 缓存条件
type CacheCondition struct {
	// 条件类型：header, query, status_code, content_type
	Type string `yaml:"type" mapstructure:"type"`

	// 条件键
	Key string `yaml:"key" mapstructure:"key"`

	// 条件值
	Value string `yaml:"value" mapstructure:"value"`

	// 操作符：eq, ne, contains, regex
	Operator string `yaml:"operator" mapstructure:"operator"`
}

// CacheKeyConfig 缓存键生成器配置
type CacheKeyConfig struct {
	// 是否包含查询参数
	IncludeQueryParams bool `yaml:"include_query_params" mapstructure:"include_query_params"`

	// 包含的头部
	IncludeHeaders []string `yaml:"include_headers" mapstructure:"include_headers"`

	// 排除的参数
	ExcludeParams []string `yaml:"exclude_params" mapstructure:"exclude_params"`

	// 排除的头部
	ExcludeHeaders []string `yaml:"exclude_headers" mapstructure:"exclude_headers"`

	// 自定义键模板
	Template string `yaml:"template" mapstructure:"template"`

	// 哈希算法：md5, sha1, sha256
	HashAlgorithm string `yaml:"hash_algorithm" mapstructure:"hash_algorithm"`
}

// CacheWarmupConfig 缓存预热配置
type CacheWarmupConfig struct {
	// 是否启用预热
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 预热策略：startup, scheduled, manual
	Strategy string `yaml:"strategy" mapstructure:"strategy"`

	// 预热URL列表
	URLs []string `yaml:"urls" mapstructure:"urls"`

	// 预热文件路径
	URLFile string `yaml:"url_file" mapstructure:"url_file"`

	// 预热间隔
	Interval string `yaml:"interval" mapstructure:"interval"`

	// 并发数
	Concurrency int `yaml:"concurrency" mapstructure:"concurrency"`

	// 超时时间
	Timeout string `yaml:"timeout" mapstructure:"timeout"`
}

// CacheConsistencyConfig 缓存一致性配置
type CacheConsistencyConfig struct {
	// 一致性模式：eventual, strong, weak
	Mode string `yaml:"mode" mapstructure:"mode"`

	// 失效策略：ttl, tag, manual
	InvalidationStrategy string `yaml:"invalidation_strategy" mapstructure:"invalidation_strategy"`

	// 失效标签
	Tags []string `yaml:"tags" mapstructure:"tags"`

	// 失效通知
	Notifications CacheNotificationConfig `yaml:"notifications" mapstructure:"notifications"`
}

// CacheNotificationConfig 缓存通知配置
type CacheNotificationConfig struct {
	// 是否启用通知
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 通知类型：redis_pubsub, webhook, kafka
	Type string `yaml:"type" mapstructure:"type"`

	// Redis发布订阅配置
	RedisPubSub CacheRedisPubSubConfig `yaml:"redis_pubsub" mapstructure:"redis_pubsub"`

	// Webhook配置
	Webhook CacheWebhookConfig `yaml:"webhook" mapstructure:"webhook"`
}

// CacheRedisPubSubConfig Redis发布订阅配置
type CacheRedisPubSubConfig struct {
	// 频道名称
	Channel string `yaml:"channel" mapstructure:"channel"`

	// 频道模式
	Pattern string `yaml:"pattern" mapstructure:"pattern"`
}

// CacheWebhookConfig Webhook配置
type CacheWebhookConfig struct {
	// Webhook URL
	URL string `yaml:"url" mapstructure:"url"`

	// 超时时间
	Timeout string `yaml:"timeout" mapstructure:"timeout"`

	// 重试次数
	Retries int `yaml:"retries" mapstructure:"retries"`
}

// CacheCompressionConfig 缓存压缩配置
type CacheCompressionConfig struct {
	// 是否启用压缩
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 压缩算法：gzip, lz4, snappy, zstd
	Algorithm string `yaml:"algorithm" mapstructure:"algorithm"`

	// 压缩级别
	Level int `yaml:"level" mapstructure:"level"`

	// 最小压缩大小
	MinSize int64 `yaml:"min_size" mapstructure:"min_size"`
}

// TransformConfig 请求响应转换配置
type TransformConfig struct {
	// 是否启用转换
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 请求转换配置
	Request RequestTransformConfig `yaml:"request" mapstructure:"request"`

	// 响应转换配置
	Response ResponseTransformConfig `yaml:"response" mapstructure:"response"`

	// 协议转换配置
	Protocol ProtocolTransformConfig `yaml:"protocol" mapstructure:"protocol"`

	// GraphQL转换配置
	GraphQL GraphQLTransformConfig `yaml:"graphql" mapstructure:"graphql"`

	// 数据格式转换配置
	Format FormatTransformConfig `yaml:"format" mapstructure:"format"`

	// 模板配置
	Templates TemplateConfig `yaml:"templates" mapstructure:"templates"`
}

// RequestTransformConfig 请求转换配置
type RequestTransformConfig struct {
	// 是否启用请求转换
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 头部转换规则
	Headers HeaderTransformConfig `yaml:"headers" mapstructure:"headers"`

	// 查询参数转换规则
	Query QueryTransformConfig `yaml:"query" mapstructure:"query"`

	// 请求体转换规则
	Body BodyTransformConfig `yaml:"body" mapstructure:"body"`

	// 路径转换规则
	Path PathTransformConfig `yaml:"path" mapstructure:"path"`

	// 方法转换规则
	Method MethodTransformConfig `yaml:"method" mapstructure:"method"`
}

// ResponseTransformConfig 响应转换配置
type ResponseTransformConfig struct {
	// 是否启用响应转换
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 头部转换规则
	Headers HeaderTransformConfig `yaml:"headers" mapstructure:"headers"`

	// 响应体转换规则
	Body BodyTransformConfig `yaml:"body" mapstructure:"body"`

	// 状态码转换规则
	Status StatusTransformConfig `yaml:"status" mapstructure:"status"`

	// 错误处理配置
	Error ErrorTransformConfig `yaml:"error" mapstructure:"error"`
}

// HeaderTransformConfig 头部转换配置
type HeaderTransformConfig struct {
	// 添加头部
	Add map[string]string `yaml:"add" mapstructure:"add"`

	// 删除头部
	Remove []string `yaml:"remove" mapstructure:"remove"`

	// 重命名头部
	Rename map[string]string `yaml:"rename" mapstructure:"rename"`

	// 替换头部值
	Replace map[string]string `yaml:"replace" mapstructure:"replace"`

	// 条件转换规则
	Rules []HeaderTransformRule `yaml:"rules" mapstructure:"rules"`
}

// HeaderTransformRule 头部转换规则
type HeaderTransformRule struct {
	// 匹配条件
	Condition TransformCondition `yaml:"condition" mapstructure:"condition"`

	// 转换操作
	Action TransformAction `yaml:"action" mapstructure:"action"`
}

// QueryTransformConfig 查询参数转换配置
type QueryTransformConfig struct {
	// 添加参数
	Add map[string]string `yaml:"add" mapstructure:"add"`

	// 删除参数
	Remove []string `yaml:"remove" mapstructure:"remove"`

	// 重命名参数
	Rename map[string]string `yaml:"rename" mapstructure:"rename"`

	// 替换参数值
	Replace map[string]string `yaml:"replace" mapstructure:"replace"`

	// 条件转换规则
	Rules []QueryTransformRule `yaml:"rules" mapstructure:"rules"`
}

// QueryTransformRule 查询参数转换规则
type QueryTransformRule struct {
	// 匹配条件
	Condition TransformCondition `yaml:"condition" mapstructure:"condition"`

	// 转换操作
	Action TransformAction `yaml:"action" mapstructure:"action"`
}

// BodyTransformConfig 请求体转换配置
type BodyTransformConfig struct {
	// 转换类型：json, xml, form, template, jsonpath
	Type string `yaml:"type" mapstructure:"type"`

	// JSON转换规则
	JSON JSONTransformConfig `yaml:"json" mapstructure:"json"`

	// XML转换规则
	XML XMLTransformConfig `yaml:"xml" mapstructure:"xml"`

	// 模板转换
	Template string `yaml:"template" mapstructure:"template"`

	// JSONPath转换规则
	JSONPath []JSONPathTransformRule `yaml:"jsonpath" mapstructure:"jsonpath"`

	// 条件转换规则
	Rules []BodyTransformRule `yaml:"rules" mapstructure:"rules"`
}

// JSONTransformConfig JSON转换配置
type JSONTransformConfig struct {
	// 添加字段
	Add map[string]interface{} `yaml:"add" mapstructure:"add"`

	// 删除字段
	Remove []string `yaml:"remove" mapstructure:"remove"`

	// 重命名字段
	Rename map[string]string `yaml:"rename" mapstructure:"rename"`

	// 替换字段值
	Replace map[string]interface{} `yaml:"replace" mapstructure:"replace"`

	// 转换规则
	Transform []JSONTransformRule `yaml:"transform" mapstructure:"transform"`
}

// JSONTransformRule JSON转换规则
type JSONTransformRule struct {
	// JSONPath表达式
	Path string `yaml:"path" mapstructure:"path"`

	// 操作类型：add, remove, replace, rename, transform
	Operation string `yaml:"operation" mapstructure:"operation"`

	// 新值或新路径
	Value interface{} `yaml:"value" mapstructure:"value"`

	// 条件
	Condition TransformCondition `yaml:"condition" mapstructure:"condition"`
}

// XMLTransformConfig XML转换配置
type XMLTransformConfig struct {
	// 命名空间映射
	Namespaces map[string]string `yaml:"namespaces" mapstructure:"namespaces"`

	// XPath转换规则
	XPath []XPathTransformRule `yaml:"xpath" mapstructure:"xpath"`
}

// XPathTransformRule XPath转换规则
type XPathTransformRule struct {
	// XPath表达式
	Path string `yaml:"path" mapstructure:"path"`

	// 操作类型
	Operation string `yaml:"operation" mapstructure:"operation"`

	// 新值
	Value interface{} `yaml:"value" mapstructure:"value"`

	// 条件
	Condition TransformCondition `yaml:"condition" mapstructure:"condition"`
}

// JSONPathTransformRule JSONPath转换规则
type JSONPathTransformRule struct {
	// JSONPath表达式
	Path string `yaml:"path" mapstructure:"path"`

	// 操作类型
	Operation string `yaml:"operation" mapstructure:"operation"`

	// 新值
	Value interface{} `yaml:"value" mapstructure:"value"`

	// 目标路径（用于移动操作）
	TargetPath string `yaml:"target_path" mapstructure:"target_path"`

	// 条件
	Condition TransformCondition `yaml:"condition" mapstructure:"condition"`
}

// PathTransformConfig 路径转换配置
type PathTransformConfig struct {
	// 重写规则
	Rewrite []PathRewriteRule `yaml:"rewrite" mapstructure:"rewrite"`

	// 前缀添加
	AddPrefix string `yaml:"add_prefix" mapstructure:"add_prefix"`

	// 前缀删除
	RemovePrefix string `yaml:"remove_prefix" mapstructure:"remove_prefix"`
}

// PathRewriteRule 路径重写规则
type PathRewriteRule struct {
	// 匹配模式（正则表达式）
	Pattern string `yaml:"pattern" mapstructure:"pattern"`

	// 替换模板
	Replacement string `yaml:"replacement" mapstructure:"replacement"`

	// 条件
	Condition TransformCondition `yaml:"condition" mapstructure:"condition"`
}

// MethodTransformConfig 方法转换配置
type MethodTransformConfig struct {
	// 方法映射
	Mapping map[string]string `yaml:"mapping" mapstructure:"mapping"`

	// 条件转换规则
	Rules []MethodTransformRule `yaml:"rules" mapstructure:"rules"`
}

// MethodTransformRule 方法转换规则
type MethodTransformRule struct {
	// 原方法
	From string `yaml:"from" mapstructure:"from"`

	// 目标方法
	To string `yaml:"to" mapstructure:"to"`

	// 条件
	Condition TransformCondition `yaml:"condition" mapstructure:"condition"`
}

// StatusTransformConfig 状态码转换配置
type StatusTransformConfig struct {
	// 状态码映射
	Mapping map[int]int `yaml:"mapping" mapstructure:"mapping"`

	// 条件转换规则
	Rules []StatusTransformRule `yaml:"rules" mapstructure:"rules"`
}

// StatusTransformRule 状态码转换规则
type StatusTransformRule struct {
	// 原状态码
	From int `yaml:"from" mapstructure:"from"`

	// 目标状态码
	To int `yaml:"to" mapstructure:"to"`

	// 条件
	Condition TransformCondition `yaml:"condition" mapstructure:"condition"`
}

// ErrorTransformConfig 错误转换配置
type ErrorTransformConfig struct {
	// 是否启用错误转换
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 错误格式：json, xml, plain
	Format string `yaml:"format" mapstructure:"format"`

	// 错误模板
	Template string `yaml:"template" mapstructure:"template"`

	// 错误映射规则
	Rules []ErrorTransformRule `yaml:"rules" mapstructure:"rules"`
}

// ErrorTransformRule 错误转换规则
type ErrorTransformRule struct {
	// 匹配的状态码
	StatusCode int `yaml:"status_code" mapstructure:"status_code"`

	// 错误消息模板
	Message string `yaml:"message" mapstructure:"message"`

	// 错误代码
	Code string `yaml:"code" mapstructure:"code"`

	// 额外字段
	Fields map[string]interface{} `yaml:"fields" mapstructure:"fields"`
}

// ProtocolTransformConfig 协议转换配置
type ProtocolTransformConfig struct {
	// 是否启用协议转换
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// REST到gRPC转换
	RESTToGRPC RESTToGRPCConfig `yaml:"rest_to_grpc" mapstructure:"rest_to_grpc"`

	// gRPC到REST转换
	GRPCToREST GRPCToRESTConfig `yaml:"grpc_to_rest" mapstructure:"grpc_to_rest"`

	// WebSocket转换
	WebSocket WebSocketTransformConfig `yaml:"websocket" mapstructure:"websocket"`
}

// RESTToGRPCConfig REST到gRPC转换配置
type RESTToGRPCConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 服务映射
	Services []RESTToGRPCService `yaml:"services" mapstructure:"services"`

	// 默认超时
	DefaultTimeout string `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// RESTToGRPCService REST到gRPC服务映射
type RESTToGRPCService struct {
	// REST路径模式
	RESTPath string `yaml:"rest_path" mapstructure:"rest_path"`

	// gRPC服务名
	GRPCService string `yaml:"grpc_service" mapstructure:"grpc_service"`

	// gRPC方法名
	GRPCMethod string `yaml:"grpc_method" mapstructure:"grpc_method"`

	// 参数映射
	ParameterMapping map[string]string `yaml:"parameter_mapping" mapstructure:"parameter_mapping"`

	// 请求体映射
	RequestMapping string `yaml:"request_mapping" mapstructure:"request_mapping"`

	// 响应体映射
	ResponseMapping string `yaml:"response_mapping" mapstructure:"response_mapping"`
}

// GRPCToRESTConfig gRPC到REST转换配置
type GRPCToRESTConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 路径前缀
	PathPrefix string `yaml:"path_prefix" mapstructure:"path_prefix"`

	// 服务映射
	Services []GRPCToRESTService `yaml:"services" mapstructure:"services"`
}

// GRPCToRESTService gRPC到REST服务映射
type GRPCToRESTService struct {
	// gRPC服务名
	GRPCService string `yaml:"grpc_service" mapstructure:"grpc_service"`

	// REST路径模板
	RESTPath string `yaml:"rest_path" mapstructure:"rest_path"`

	// HTTP方法
	HTTPMethod string `yaml:"http_method" mapstructure:"http_method"`

	// 参数映射
	ParameterMapping map[string]string `yaml:"parameter_mapping" mapstructure:"parameter_mapping"`
}



// GraphQLTransformConfig GraphQL转换配置
type GraphQLTransformConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// GraphQL端点
	Endpoint string `yaml:"endpoint" mapstructure:"endpoint"`

	// Schema文件路径
	SchemaPath string `yaml:"schema_path" mapstructure:"schema_path"`

	// REST到GraphQL转换
	RESTToGraphQL RESTToGraphQLConfig `yaml:"rest_to_graphql" mapstructure:"rest_to_graphql"`

	// GraphQL到REST转换
	GraphQLToREST GraphQLToRESTConfig `yaml:"graphql_to_rest" mapstructure:"graphql_to_rest"`
}

// RESTToGraphQLConfig REST到GraphQL转换配置
type RESTToGraphQLConfig struct {
	// 查询映射
	QueryMappings []GraphQLQueryMapping `yaml:"query_mappings" mapstructure:"query_mappings"`

	// 变更映射
	MutationMappings []GraphQLMutationMapping `yaml:"mutation_mappings" mapstructure:"mutation_mappings"`
}

// GraphQLQueryMapping GraphQL查询映射
type GraphQLQueryMapping struct {
	// REST路径
	RESTPath string `yaml:"rest_path" mapstructure:"rest_path"`

	// GraphQL查询
	GraphQLQuery string `yaml:"graphql_query" mapstructure:"graphql_query"`

	// 变量映射
	VariableMapping map[string]string `yaml:"variable_mapping" mapstructure:"variable_mapping"`
}

// GraphQLMutationMapping GraphQL变更映射
type GraphQLMutationMapping struct {
	// REST路径
	RESTPath string `yaml:"rest_path" mapstructure:"rest_path"`

	// HTTP方法
	HTTPMethod string `yaml:"http_method" mapstructure:"http_method"`

	// GraphQL变更
	GraphQLMutation string `yaml:"graphql_mutation" mapstructure:"graphql_mutation"`

	// 变量映射
	VariableMapping map[string]string `yaml:"variable_mapping" mapstructure:"variable_mapping"`
}

// GraphQLToRESTConfig GraphQL到REST转换配置
type GraphQLToRESTConfig struct {
	// 查询转换规则
	QueryRules []GraphQLToRESTRule `yaml:"query_rules" mapstructure:"query_rules"`
}

// GraphQLToRESTRule GraphQL到REST转换规则
type GraphQLToRESTRule struct {
	// GraphQL操作名
	OperationName string `yaml:"operation_name" mapstructure:"operation_name"`

	// REST端点
	RESTEndpoint string `yaml:"rest_endpoint" mapstructure:"rest_endpoint"`

	// HTTP方法
	HTTPMethod string `yaml:"http_method" mapstructure:"http_method"`

	// 参数映射
	ParameterMapping map[string]string `yaml:"parameter_mapping" mapstructure:"parameter_mapping"`
}

// FormatTransformConfig 数据格式转换配置
type FormatTransformConfig struct {
	// 是否启用格式转换
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// JSON到XML转换
	JSONToXML JSONToXMLConfig `yaml:"json_to_xml" mapstructure:"json_to_xml"`

	// XML到JSON转换
	XMLToJSON XMLToJSONConfig `yaml:"xml_to_json" mapstructure:"xml_to_json"`

	// CSV转换
	CSV CSVTransformConfig `yaml:"csv" mapstructure:"csv"`

	// YAML转换
	YAML YAMLTransformConfig `yaml:"yaml" mapstructure:"yaml"`
}

// JSONToXMLConfig JSON到XML转换配置
type JSONToXMLConfig struct {
	// 根元素名称
	RootElement string `yaml:"root_element" mapstructure:"root_element"`

	// 数组元素名称
	ArrayElement string `yaml:"array_element" mapstructure:"array_element"`

	// 属性前缀
	AttributePrefix string `yaml:"attribute_prefix" mapstructure:"attribute_prefix"`

	// 内容字段名
	ContentField string `yaml:"content_field" mapstructure:"content_field"`
}

// XMLToJSONConfig XML到JSON转换配置
type XMLToJSONConfig struct {
	// 属性前缀
	AttributePrefix string `yaml:"attribute_prefix" mapstructure:"attribute_prefix"`

	// 内容字段名
	ContentField string `yaml:"content_field" mapstructure:"content_field"`

	// 是否保留命名空间
	PreserveNamespace bool `yaml:"preserve_namespace" mapstructure:"preserve_namespace"`
}

// CSVTransformConfig CSV转换配置
type CSVTransformConfig struct {
	// 分隔符
	Delimiter string `yaml:"delimiter" mapstructure:"delimiter"`

	// 是否包含头部
	HasHeader bool `yaml:"has_header" mapstructure:"has_header"`

	// 字段映射
	FieldMapping map[string]string `yaml:"field_mapping" mapstructure:"field_mapping"`
}

// YAMLTransformConfig YAML转换配置
type YAMLTransformConfig struct {
	// 缩进大小
	Indent int `yaml:"indent" mapstructure:"indent"`

	// 是否使用流式格式
	FlowStyle bool `yaml:"flow_style" mapstructure:"flow_style"`
}

// TemplateConfig 模板配置
type TemplateConfig struct {
	// 模板引擎：go, mustache, handlebars
	Engine string `yaml:"engine" mapstructure:"engine"`

	// 模板目录
	Directory string `yaml:"directory" mapstructure:"directory"`

	// 预定义模板
	Templates map[string]string `yaml:"templates" mapstructure:"templates"`

	// 模板函数
	Functions map[string]string `yaml:"functions" mapstructure:"functions"`
}

// TransformCondition 转换条件
type TransformCondition struct {
	// 条件类型：path, method, header, query, body, status
	Type string `yaml:"type" mapstructure:"type"`

	// 条件键
	Key string `yaml:"key" mapstructure:"key"`

	// 条件值
	Value interface{} `yaml:"value" mapstructure:"value"`

	// 操作符：eq, ne, gt, lt, gte, lte, in, not_in, contains, regex
	Operator string `yaml:"operator" mapstructure:"operator"`

	// 逻辑操作符：and, or
	Logic string `yaml:"logic" mapstructure:"logic"`

	// 子条件
	Conditions []TransformCondition `yaml:"conditions" mapstructure:"conditions"`
}

// TransformAction 转换操作
type TransformAction struct {
	// 操作类型：add, remove, replace, rename, transform
	Type string `yaml:"type" mapstructure:"type"`

	// 目标键或路径
	Target string `yaml:"target" mapstructure:"target"`

	// 新值
	Value interface{} `yaml:"value" mapstructure:"value"`

	// 源键或路径（用于重命名）
	Source string `yaml:"source" mapstructure:"source"`

	// 转换表达式
	Expression string `yaml:"expression" mapstructure:"expression"`

	// 模板
	Template string `yaml:"template" mapstructure:"template"`
}

// BodyTransformRule 请求体转换规则
type BodyTransformRule struct {
	// 匹配条件
	Condition TransformCondition `yaml:"condition" mapstructure:"condition"`

	// 转换操作
	Action TransformAction `yaml:"action" mapstructure:"action"`

	// 优先级
	Priority int `yaml:"priority" mapstructure:"priority"`
}

// WebSocketConfig WebSocket配置
type WebSocketConfig struct {
	// 是否启用WebSocket支持
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 连接配置
	Connection WebSocketConnectionConfig `yaml:"connection" mapstructure:"connection"`

	// 代理配置
	Proxy WebSocketProxyConfig `yaml:"proxy" mapstructure:"proxy"`

	// 路由配置
	Routes []WebSocketRouteConfig `yaml:"routes" mapstructure:"routes"`

	// 认证配置
	Auth WebSocketAuthConfig `yaml:"auth" mapstructure:"auth"`

	// 监控配置
	Monitoring WebSocketMonitoringConfig `yaml:"monitoring" mapstructure:"monitoring"`

	// 负载均衡配置
	LoadBalancer WebSocketLoadBalancerConfig `yaml:"load_balancer" mapstructure:"load_balancer"`
}

// WebSocketConnectionConfig WebSocket连接配置
type WebSocketConnectionConfig struct {
	// 读缓冲区大小
	ReadBufferSize int `yaml:"read_buffer_size" mapstructure:"read_buffer_size"`

	// 写缓冲区大小
	WriteBufferSize int `yaml:"write_buffer_size" mapstructure:"write_buffer_size"`

	// 是否检查Origin
	CheckOrigin bool `yaml:"check_origin" mapstructure:"check_origin"`

	// 允许的Origin列表
	AllowedOrigins []string `yaml:"allowed_origins" mapstructure:"allowed_origins"`

	// 子协议
	Subprotocols []string `yaml:"subprotocols" mapstructure:"subprotocols"`

	// 连接超时
	HandshakeTimeout string `yaml:"handshake_timeout" mapstructure:"handshake_timeout"`

	// 读取超时
	ReadTimeout string `yaml:"read_timeout" mapstructure:"read_timeout"`

	// 写入超时
	WriteTimeout string `yaml:"write_timeout" mapstructure:"write_timeout"`

	// Ping间隔
	PingInterval string `yaml:"ping_interval" mapstructure:"ping_interval"`

	// Pong超时
	PongTimeout string `yaml:"pong_timeout" mapstructure:"pong_timeout"`

	// 最大消息大小
	MaxMessageSize int64 `yaml:"max_message_size" mapstructure:"max_message_size"`

	// 压缩配置
	Compression WebSocketCompressionConfig `yaml:"compression" mapstructure:"compression"`
}

// WebSocketCompressionConfig WebSocket压缩配置
type WebSocketCompressionConfig struct {
	// 是否启用压缩
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 压缩级别
	Level int `yaml:"level" mapstructure:"level"`

	// 压缩阈值
	Threshold int `yaml:"threshold" mapstructure:"threshold"`
}

// WebSocketProxyConfig WebSocket代理配置
type WebSocketProxyConfig struct {
	// 是否启用代理
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 代理模式：transparent, tunnel, transform
	Mode string `yaml:"mode" mapstructure:"mode"`

	// 缓冲区大小
	BufferSize int `yaml:"buffer_size" mapstructure:"buffer_size"`

	// 是否保持连接活跃
	KeepAlive bool `yaml:"keep_alive" mapstructure:"keep_alive"`

	// 重连配置
	Reconnect WebSocketReconnectConfig `yaml:"reconnect" mapstructure:"reconnect"`

	// 消息过滤
	MessageFilter WebSocketMessageFilterConfig `yaml:"message_filter" mapstructure:"message_filter"`
}

// WebSocketReconnectConfig WebSocket重连配置
type WebSocketReconnectConfig struct {
	// 是否启用自动重连
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 最大重连次数
	MaxRetries int `yaml:"max_retries" mapstructure:"max_retries"`

	// 重连间隔
	Interval string `yaml:"interval" mapstructure:"interval"`

	// 退避策略：fixed, exponential, linear
	BackoffStrategy string `yaml:"backoff_strategy" mapstructure:"backoff_strategy"`

	// 最大退避时间
	MaxBackoff string `yaml:"max_backoff" mapstructure:"max_backoff"`
}

// WebSocketMessageFilterConfig WebSocket消息过滤配置
type WebSocketMessageFilterConfig struct {
	// 是否启用过滤
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 过滤规则
	Rules []WebSocketFilterRule `yaml:"rules" mapstructure:"rules"`

	// 默认动作：allow, deny
	DefaultAction string `yaml:"default_action" mapstructure:"default_action"`
}

// WebSocketFilterRule WebSocket过滤规则
type WebSocketFilterRule struct {
	// 规则名称
	Name string `yaml:"name" mapstructure:"name"`

	// 匹配条件
	Condition WebSocketFilterCondition `yaml:"condition" mapstructure:"condition"`

	// 动作：allow, deny, transform, rate_limit
	Action string `yaml:"action" mapstructure:"action"`

	// 动作参数
	ActionParams map[string]interface{} `yaml:"action_params" mapstructure:"action_params"`
}

// WebSocketFilterCondition WebSocket过滤条件
type WebSocketFilterCondition struct {
	// 消息类型：text, binary, ping, pong, close
	MessageType string `yaml:"message_type" mapstructure:"message_type"`

	// 消息大小范围
	SizeRange WebSocketSizeRange `yaml:"size_range" mapstructure:"size_range"`

	// 消息内容匹配
	ContentMatch WebSocketContentMatch `yaml:"content_match" mapstructure:"content_match"`

	// 频率限制
	RateLimit WebSocketRateLimit `yaml:"rate_limit" mapstructure:"rate_limit"`
}

// WebSocketSizeRange WebSocket大小范围
type WebSocketSizeRange struct {
	// 最小大小
	Min int64 `yaml:"min" mapstructure:"min"`

	// 最大大小
	Max int64 `yaml:"max" mapstructure:"max"`
}

// WebSocketContentMatch WebSocket内容匹配
type WebSocketContentMatch struct {
	// 匹配类型：exact, contains, regex, json_path
	Type string `yaml:"type" mapstructure:"type"`

	// 匹配值
	Value string `yaml:"value" mapstructure:"value"`

	// 是否大小写敏感
	CaseSensitive bool `yaml:"case_sensitive" mapstructure:"case_sensitive"`
}

// WebSocketRateLimit WebSocket频率限制
type WebSocketRateLimit struct {
	// 请求数量
	Requests int `yaml:"requests" mapstructure:"requests"`

	// 时间窗口
	Window string `yaml:"window" mapstructure:"window"`

	// 限制键：ip, user, connection
	Key string `yaml:"key" mapstructure:"key"`
}

// WebSocketRouteConfig WebSocket路由配置
type WebSocketRouteConfig struct {
	// 路由名称
	Name string `yaml:"name" mapstructure:"name"`

	// 路径匹配
	Path string `yaml:"path" mapstructure:"path"`

	// 上游配置
	Upstream WebSocketUpstreamConfig `yaml:"upstream" mapstructure:"upstream"`

	// 认证配置
	Auth WebSocketRouteAuthConfig `yaml:"auth" mapstructure:"auth"`

	// 中间件
	Middlewares []string `yaml:"middlewares" mapstructure:"middlewares"`

	// 转换配置
	Transform WebSocketTransformConfig `yaml:"transform" mapstructure:"transform"`

	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`
}

// WebSocketUpstreamConfig WebSocket上游配置
type WebSocketUpstreamConfig struct {
	// 上游类型：static, service_discovery
	Type string `yaml:"type" mapstructure:"type"`

	// 静态上游地址
	URL string `yaml:"url" mapstructure:"url"`

	// 服务发现配置
	ServiceName string `yaml:"service_name" mapstructure:"service_name"`

	// 负载均衡策略
	LoadBalancer string `yaml:"load_balancer" mapstructure:"load_balancer"`

	// 健康检查
	HealthCheck WebSocketHealthCheckConfig `yaml:"health_check" mapstructure:"health_check"`

	// 超时配置
	Timeout WebSocketTimeoutConfig `yaml:"timeout" mapstructure:"timeout"`
}

// WebSocketHealthCheckConfig WebSocket健康检查配置
type WebSocketHealthCheckConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 检查间隔
	Interval string `yaml:"interval" mapstructure:"interval"`

	// 超时时间
	Timeout string `yaml:"timeout" mapstructure:"timeout"`

	// 检查路径
	Path string `yaml:"path" mapstructure:"path"`

	// 检查消息
	Message string `yaml:"message" mapstructure:"message"`

	// 期望响应
	ExpectedResponse string `yaml:"expected_response" mapstructure:"expected_response"`
}

// WebSocketTimeoutConfig WebSocket超时配置
type WebSocketTimeoutConfig struct {
	// 连接超时
	Connect string `yaml:"connect" mapstructure:"connect"`

	// 读取超时
	Read string `yaml:"read" mapstructure:"read"`

	// 写入超时
	Write string `yaml:"write" mapstructure:"write"`

	// 空闲超时
	Idle string `yaml:"idle" mapstructure:"idle"`
}

// WebSocketAuthConfig WebSocket认证配置
type WebSocketAuthConfig struct {
	// 是否启用认证
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 认证方法
	Methods []string `yaml:"methods" mapstructure:"methods"`

	// JWT配置
	JWT WebSocketJWTConfig `yaml:"jwt" mapstructure:"jwt"`

	// API Key配置
	APIKey WebSocketAPIKeyConfig `yaml:"api_key" mapstructure:"api_key"`

	// 自定义认证
	Custom WebSocketCustomAuthConfig `yaml:"custom" mapstructure:"custom"`
}

// WebSocketJWTConfig WebSocket JWT配置
type WebSocketJWTConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 密钥
	Secret string `yaml:"secret" mapstructure:"secret"`

	// 算法
	Algorithm string `yaml:"algorithm" mapstructure:"algorithm"`

	// Token来源：query, header, subprotocol
	TokenSource string `yaml:"token_source" mapstructure:"token_source"`

	// Token参数名
	TokenParam string `yaml:"token_param" mapstructure:"token_param"`
}

// WebSocketAPIKeyConfig WebSocket API Key配置
type WebSocketAPIKeyConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// API Key来源：query, header
	Source string `yaml:"source" mapstructure:"source"`

	// 参数名
	Param string `yaml:"param" mapstructure:"param"`

	// 有效的API Keys
	ValidKeys []string `yaml:"valid_keys" mapstructure:"valid_keys"`
}

// WebSocketCustomAuthConfig WebSocket自定义认证配置
type WebSocketCustomAuthConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 认证URL
	URL string `yaml:"url" mapstructure:"url"`

	// 超时时间
	Timeout string `yaml:"timeout" mapstructure:"timeout"`

	// 请求头
	Headers map[string]string `yaml:"headers" mapstructure:"headers"`
}

// WebSocketRouteAuthConfig WebSocket路由认证配置
type WebSocketRouteAuthConfig struct {
	// 是否需要认证
	Required bool `yaml:"required" mapstructure:"required"`

	// 认证方法
	Methods []string `yaml:"methods" mapstructure:"methods"`

	// 角色要求
	Roles []string `yaml:"roles" mapstructure:"roles"`

	// 权限要求
	Permissions []string `yaml:"permissions" mapstructure:"permissions"`
}

// WebSocketTransformConfig WebSocket转换配置
type WebSocketTransformConfig struct {
	// 是否启用转换
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 消息转换规则
	MessageRules []WebSocketMessageTransformRule `yaml:"message_rules" mapstructure:"message_rules"`

	// 连接转换规则
	ConnectionRules []WebSocketConnectionTransformRule `yaml:"connection_rules" mapstructure:"connection_rules"`
}

// WebSocketMessageTransformRule WebSocket消息转换规则
type WebSocketMessageTransformRule struct {
	// 规则名称
	Name string `yaml:"name" mapstructure:"name"`

	// 匹配条件
	Condition WebSocketFilterCondition `yaml:"condition" mapstructure:"condition"`

	// 转换操作
	Transform WebSocketMessageTransform `yaml:"transform" mapstructure:"transform"`

	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`
}

// WebSocketMessageTransform WebSocket消息转换
type WebSocketMessageTransform struct {
	// 转换类型：format, content, headers
	Type string `yaml:"type" mapstructure:"type"`

	// 格式转换
	Format WebSocketFormatTransform `yaml:"format" mapstructure:"format"`

	// 内容转换
	Content WebSocketContentTransform `yaml:"content" mapstructure:"content"`

	// 头部转换
	Headers map[string]string `yaml:"headers" mapstructure:"headers"`
}

// WebSocketFormatTransform WebSocket格式转换
type WebSocketFormatTransform struct {
	// 源格式
	From string `yaml:"from" mapstructure:"from"`

	// 目标格式
	To string `yaml:"to" mapstructure:"to"`

	// 转换选项
	Options map[string]interface{} `yaml:"options" mapstructure:"options"`
}

// WebSocketContentTransform WebSocket内容转换
type WebSocketContentTransform struct {
	// 转换类型：template, regex, jsonpath
	Type string `yaml:"type" mapstructure:"type"`

	// 模板
	Template string `yaml:"template" mapstructure:"template"`

	// 正则表达式
	Regex WebSocketRegexTransform `yaml:"regex" mapstructure:"regex"`

	// JSONPath转换
	JSONPath []WebSocketJSONPathTransform `yaml:"jsonpath" mapstructure:"jsonpath"`
}

// WebSocketRegexTransform WebSocket正则转换
type WebSocketRegexTransform struct {
	// 匹配模式
	Pattern string `yaml:"pattern" mapstructure:"pattern"`

	// 替换模板
	Replacement string `yaml:"replacement" mapstructure:"replacement"`

	// 是否全局替换
	Global bool `yaml:"global" mapstructure:"global"`
}

// WebSocketJSONPathTransform WebSocket JSONPath转换
type WebSocketJSONPathTransform struct {
	// JSONPath表达式
	Path string `yaml:"path" mapstructure:"path"`

	// 操作类型：add, remove, replace, move
	Operation string `yaml:"operation" mapstructure:"operation"`

	// 新值
	Value interface{} `yaml:"value" mapstructure:"value"`

	// 目标路径（用于移动操作）
	TargetPath string `yaml:"target_path" mapstructure:"target_path"`
}

// WebSocketConnectionTransformRule WebSocket连接转换规则
type WebSocketConnectionTransformRule struct {
	// 规则名称
	Name string `yaml:"name" mapstructure:"name"`

	// 匹配条件
	Condition WebSocketConnectionCondition `yaml:"condition" mapstructure:"condition"`

	// 转换操作
	Transform WebSocketConnectionTransform `yaml:"transform" mapstructure:"transform"`

	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`
}

// WebSocketConnectionCondition WebSocket连接条件
type WebSocketConnectionCondition struct {
	// 路径匹配
	Path string `yaml:"path" mapstructure:"path"`

	// Origin匹配
	Origin string `yaml:"origin" mapstructure:"origin"`

	// 子协议匹配
	Subprotocol string `yaml:"subprotocol" mapstructure:"subprotocol"`

	// 头部匹配
	Headers map[string]string `yaml:"headers" mapstructure:"headers"`
}

// WebSocketConnectionTransform WebSocket连接转换
type WebSocketConnectionTransform struct {
	// 路径重写
	PathRewrite string `yaml:"path_rewrite" mapstructure:"path_rewrite"`

	// 头部添加
	AddHeaders map[string]string `yaml:"add_headers" mapstructure:"add_headers"`

	// 头部删除
	RemoveHeaders []string `yaml:"remove_headers" mapstructure:"remove_headers"`

	// 子协议设置
	Subprotocol string `yaml:"subprotocol" mapstructure:"subprotocol"`
}

// WebSocketMonitoringConfig WebSocket监控配置
type WebSocketMonitoringConfig struct {
	// 是否启用监控
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 指标收集
	Metrics WebSocketMetricsConfig `yaml:"metrics" mapstructure:"metrics"`

	// 日志配置
	Logging WebSocketLoggingConfig `yaml:"logging" mapstructure:"logging"`

	// 追踪配置
	Tracing WebSocketTracingConfig `yaml:"tracing" mapstructure:"tracing"`
}

// WebSocketMetricsConfig WebSocket指标配置
type WebSocketMetricsConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 指标前缀
	Prefix string `yaml:"prefix" mapstructure:"prefix"`

	// 收集间隔
	Interval string `yaml:"interval" mapstructure:"interval"`

	// 指标标签
	Labels map[string]string `yaml:"labels" mapstructure:"labels"`
}

// WebSocketLoggingConfig WebSocket日志配置
type WebSocketLoggingConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 日志级别
	Level string `yaml:"level" mapstructure:"level"`

	// 是否记录消息内容
	LogMessages bool `yaml:"log_messages" mapstructure:"log_messages"`

	// 最大消息长度
	MaxMessageLength int `yaml:"max_message_length" mapstructure:"max_message_length"`
}

// WebSocketTracingConfig WebSocket追踪配置
type WebSocketTracingConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 采样率
	SampleRate float64 `yaml:"sample_rate" mapstructure:"sample_rate"`

	// 追踪标签
	Tags map[string]string `yaml:"tags" mapstructure:"tags"`
}

// WebSocketLoadBalancerConfig WebSocket负载均衡配置
type WebSocketLoadBalancerConfig struct {
	// 负载均衡策略：round_robin, least_connections, ip_hash, consistent_hash
	Strategy string `yaml:"strategy" mapstructure:"strategy"`

	// 会话保持
	SessionAffinity WebSocketSessionAffinityConfig `yaml:"session_affinity" mapstructure:"session_affinity"`

	// 健康检查
	HealthCheck WebSocketHealthCheckConfig `yaml:"health_check" mapstructure:"health_check"`
}

// WebSocketSessionAffinityConfig WebSocket会话保持配置
type WebSocketSessionAffinityConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 保持策略：cookie, ip, header
	Strategy string `yaml:"strategy" mapstructure:"strategy"`

	// Cookie配置
	Cookie WebSocketCookieConfig `yaml:"cookie" mapstructure:"cookie"`

	// 头部名称
	HeaderName string `yaml:"header_name" mapstructure:"header_name"`
}

// WebSocketCookieConfig WebSocket Cookie配置
type WebSocketCookieConfig struct {
	// Cookie名称
	Name string `yaml:"name" mapstructure:"name"`

	// 过期时间
	MaxAge int `yaml:"max_age" mapstructure:"max_age"`

	// 路径
	Path string `yaml:"path" mapstructure:"path"`

	// 域名
	Domain string `yaml:"domain" mapstructure:"domain"`

	// 是否安全
	Secure bool `yaml:"secure" mapstructure:"secure"`

	// 是否HttpOnly
	HttpOnly bool `yaml:"http_only" mapstructure:"http_only"`
}

// ServerlessConfig Serverless配置
type ServerlessConfig struct {
	// 是否启用Serverless支持
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// AWS Lambda配置
	AWSLambda ServerlessAWSLambdaConfig `yaml:"aws_lambda" mapstructure:"aws_lambda"`

	// Azure Functions配置
	AzureFunctions ServerlessAzureFunctionsConfig `yaml:"azure_functions" mapstructure:"azure_functions"`

	// Google Cloud Functions配置
	GoogleCloudFunctions ServerlessGoogleCloudFunctionsConfig `yaml:"google_cloud_functions" mapstructure:"google_cloud_functions"`

	// OpenFaaS配置
	OpenFaaS ServerlessOpenFaaSConfig `yaml:"openfaas" mapstructure:"openfaas"`

	// Fn Project配置
	FnProject ServerlessFnProjectConfig `yaml:"fn_project" mapstructure:"fn_project"`

	// WebAssembly配置
	WebAssembly ServerlessWebAssemblyConfig `yaml:"webassembly" mapstructure:"webassembly"`

	// LocalStack配置
	LocalStack ServerlessLocalStackConfig `yaml:"localstack" mapstructure:"localstack"`

	// Supabase Functions配置
	SupabaseFunctions ServerlessSupabaseFunctionsConfig `yaml:"supabase_functions" mapstructure:"supabase_functions"`

	// Appwrite Functions配置
	AppwriteFunctions ServerlessAppwriteFunctionsConfig `yaml:"appwrite_functions" mapstructure:"appwrite_functions"`

	// 路由配置
	Routes []ServerlessRouteConfig `yaml:"routes" mapstructure:"routes"`

	// 监控配置
	Monitoring ServerlessMonitoringConfig `yaml:"monitoring" mapstructure:"monitoring"`
}

// ServerlessAWSLambdaConfig AWS Lambda配置
type ServerlessAWSLambdaConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// AWS区域
	Region string `yaml:"region" mapstructure:"region"`

	// 访问密钥ID
	AccessKeyID string `yaml:"access_key_id" mapstructure:"access_key_id"`

	// 秘密访问密钥
	SecretAccessKey string `yaml:"secret_access_key" mapstructure:"secret_access_key"`

	// 会话令牌
	SessionToken string `yaml:"session_token" mapstructure:"session_token"`

	// 端点URL
	EndpointURL string `yaml:"endpoint_url" mapstructure:"endpoint_url"`

	// 默认超时时间
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`

	// 最大重试次数
	MaxRetries int `yaml:"max_retries" mapstructure:"max_retries"`

	// 函数缓存TTL
	FunctionCacheTTL time.Duration `yaml:"function_cache_ttl" mapstructure:"function_cache_ttl"`
}

// ServerlessAzureFunctionsConfig Azure Functions配置
type ServerlessAzureFunctionsConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 订阅ID
	SubscriptionID string `yaml:"subscription_id" mapstructure:"subscription_id"`

	// 资源组
	ResourceGroup string `yaml:"resource_group" mapstructure:"resource_group"`

	// 函数应用名称
	FunctionAppName string `yaml:"function_app_name" mapstructure:"function_app_name"`

	// 客户端ID
	ClientID string `yaml:"client_id" mapstructure:"client_id"`

	// 客户端密钥
	ClientSecret string `yaml:"client_secret" mapstructure:"client_secret"`

	// 租户ID
	TenantID string `yaml:"tenant_id" mapstructure:"tenant_id"`

	// 默认超时时间
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// ServerlessGoogleCloudFunctionsConfig Google Cloud Functions配置
type ServerlessGoogleCloudFunctionsConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 项目ID
	ProjectID string `yaml:"project_id" mapstructure:"project_id"`

	// 区域
	Region string `yaml:"region" mapstructure:"region"`

	// 凭据文件路径
	CredentialsFile string `yaml:"credentials_file" mapstructure:"credentials_file"`

	// 默认超时时间
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// ServerlessOpenFaaSConfig OpenFaaS配置
type ServerlessOpenFaaSConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 网关URL
	GatewayURL string `yaml:"gateway_url" mapstructure:"gateway_url"`

	// 用户名
	Username string `yaml:"username" mapstructure:"username"`

	// 密码
	Password string `yaml:"password" mapstructure:"password"`

	// 默认超时时间
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// ServerlessFnProjectConfig Fn Project配置
type ServerlessFnProjectConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// API URL
	APIURL string `yaml:"api_url" mapstructure:"api_url"`

	// 应用名称
	AppName string `yaml:"app_name" mapstructure:"app_name"`

	// 访问令牌
	Token string `yaml:"token" mapstructure:"token"`

	// 默认超时时间
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// ServerlessWebAssemblyConfig WebAssembly配置
type ServerlessWebAssemblyConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 运行时路径
	RuntimePath string `yaml:"runtime_path" mapstructure:"runtime_path"`

	// 模块路径
	ModulesPath string `yaml:"modules_path" mapstructure:"modules_path"`

	// 最大实例数
	MaxInstances int `yaml:"max_instances" mapstructure:"max_instances"`

	// 默认超时时间
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// ServerlessLocalStackConfig LocalStack配置
type ServerlessLocalStackConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 端点URL
	EndpointURL string `yaml:"endpoint_url" mapstructure:"endpoint_url"`

	// 区域
	Region string `yaml:"region" mapstructure:"region"`

	// 访问密钥ID
	AccessKeyID string `yaml:"access_key_id" mapstructure:"access_key_id"`

	// 秘密密钥
	SecretKey string `yaml:"secret_key" mapstructure:"secret_key"`

	// 默认超时时间
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// ServerlessSupabaseFunctionsConfig Supabase Functions配置
type ServerlessSupabaseFunctionsConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 项目URL
	ProjectURL string `yaml:"project_url" mapstructure:"project_url"`

	// 匿名密钥
	AnonKey string `yaml:"anon_key" mapstructure:"anon_key"`

	// 服务密钥
	ServiceKey string `yaml:"service_key" mapstructure:"service_key"`

	// 默认超时时间
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// ServerlessAppwriteFunctionsConfig Appwrite Functions配置
type ServerlessAppwriteFunctionsConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 端点
	Endpoint string `yaml:"endpoint" mapstructure:"endpoint"`

	// 项目ID
	ProjectID string `yaml:"project_id" mapstructure:"project_id"`

	// API密钥
	APIKey string `yaml:"api_key" mapstructure:"api_key"`

	// 默认超时时间
	DefaultTimeout time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`
}

// ServerlessRouteConfig Serverless路由配置
type ServerlessRouteConfig struct {
	// 路由名称
	Name string `yaml:"name" mapstructure:"name"`

	// 路径匹配
	Path string `yaml:"path" mapstructure:"path"`

	// HTTP方法
	Methods []string `yaml:"methods" mapstructure:"methods"`

	// 提供者名称
	Provider string `yaml:"provider" mapstructure:"provider"`

	// 函数名称
	FunctionName string `yaml:"function_name" mapstructure:"function_name"`

	// 调用类型
	InvocationType string `yaml:"invocation_type" mapstructure:"invocation_type"`

	// 超时配置
	Timeout time.Duration `yaml:"timeout" mapstructure:"timeout"`

	// 重试配置
	RetryConfig ServerlessRetryConfig `yaml:"retry_config" mapstructure:"retry_config"`

	// 转换配置
	Transform ServerlessTransformConfig `yaml:"transform" mapstructure:"transform"`

	// 认证配置
	Auth ServerlessAuthConfig `yaml:"auth" mapstructure:"auth"`

	// 缓存配置
	Cache ServerlessCacheConfig `yaml:"cache" mapstructure:"cache"`

	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`
}

// ServerlessRetryConfig Serverless重试配置
type ServerlessRetryConfig struct {
	// 最大重试次数
	MaxRetries int `yaml:"max_retries" mapstructure:"max_retries"`

	// 重试间隔
	RetryInterval time.Duration `yaml:"retry_interval" mapstructure:"retry_interval"`

	// 退避策略
	BackoffStrategy string `yaml:"backoff_strategy" mapstructure:"backoff_strategy"`

	// 最大退避时间
	MaxBackoff time.Duration `yaml:"max_backoff" mapstructure:"max_backoff"`

	// 可重试的错误码
	RetryableStatusCodes []int `yaml:"retryable_status_codes" mapstructure:"retryable_status_codes"`
}

// ServerlessTransformConfig Serverless转换配置
type ServerlessTransformConfig struct {
	// 请求转换
	Request ServerlessRequestTransform `yaml:"request" mapstructure:"request"`

	// 响应转换
	Response ServerlessResponseTransform `yaml:"response" mapstructure:"response"`
}

// ServerlessRequestTransform Serverless请求转换
type ServerlessRequestTransform struct {
	// 头部映射
	HeaderMapping map[string]string `yaml:"header_mapping" mapstructure:"header_mapping"`

	// 查询参数映射
	QueryMapping map[string]string `yaml:"query_mapping" mapstructure:"query_mapping"`

	// 路径参数映射
	PathMapping map[string]string `yaml:"path_mapping" mapstructure:"path_mapping"`

	// 负载转换模板
	PayloadTemplate string `yaml:"payload_template" mapstructure:"payload_template"`

	// 内容类型转换
	ContentType string `yaml:"content_type" mapstructure:"content_type"`
}

// ServerlessResponseTransform Serverless响应转换
type ServerlessResponseTransform struct {
	// 状态码映射
	StatusCodeMapping map[int]int `yaml:"status_code_mapping" mapstructure:"status_code_mapping"`

	// 头部映射
	HeaderMapping map[string]string `yaml:"header_mapping" mapstructure:"header_mapping"`

	// 负载转换模板
	PayloadTemplate string `yaml:"payload_template" mapstructure:"payload_template"`

	// 内容类型转换
	ContentType string `yaml:"content_type" mapstructure:"content_type"`
}

// ServerlessAuthConfig Serverless认证配置
type ServerlessAuthConfig struct {
	// 是否需要认证
	Required bool `yaml:"required" mapstructure:"required"`

	// 认证方法
	Methods []string `yaml:"methods" mapstructure:"methods"`

	// 角色要求
	Roles []string `yaml:"roles" mapstructure:"roles"`

	// 权限要求
	Permissions []string `yaml:"permissions" mapstructure:"permissions"`
}

// ServerlessCacheConfig Serverless缓存配置
type ServerlessCacheConfig struct {
	// 是否启用缓存
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 缓存TTL
	TTL time.Duration `yaml:"ttl" mapstructure:"ttl"`

	// 缓存键模板
	KeyTemplate string `yaml:"key_template" mapstructure:"key_template"`

	// 缓存条件
	Conditions []string `yaml:"conditions" mapstructure:"conditions"`
}

// ServerlessMonitoringConfig Serverless监控配置
type ServerlessMonitoringConfig struct {
	// 是否启用监控
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 指标收集
	Metrics ServerlessMetricsConfig `yaml:"metrics" mapstructure:"metrics"`

	// 日志配置
	Logging ServerlessLoggingConfig `yaml:"logging" mapstructure:"logging"`

	// 追踪配置
	Tracing ServerlessTracingConfig `yaml:"tracing" mapstructure:"tracing"`
}

// ServerlessMetricsConfig Serverless指标配置
type ServerlessMetricsConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 指标前缀
	Prefix string `yaml:"prefix" mapstructure:"prefix"`

	// 收集间隔
	Interval time.Duration `yaml:"interval" mapstructure:"interval"`

	// 指标标签
	Labels map[string]string `yaml:"labels" mapstructure:"labels"`
}

// ServerlessLoggingConfig Serverless日志配置
type ServerlessLoggingConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 日志级别
	Level string `yaml:"level" mapstructure:"level"`

	// 是否记录请求负载
	LogPayload bool `yaml:"log_payload" mapstructure:"log_payload"`

	// 最大负载长度
	MaxPayloadLength int `yaml:"max_payload_length" mapstructure:"max_payload_length"`
}

// ServerlessTracingConfig Serverless追踪配置
type ServerlessTracingConfig struct {
	// 是否启用
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`

	// 采样率
	SampleRate float64 `yaml:"sample_rate" mapstructure:"sample_rate"`

	// 追踪标签
	Tags map[string]string `yaml:"tags" mapstructure:"tags"`
}

// Load loads configuration from file

