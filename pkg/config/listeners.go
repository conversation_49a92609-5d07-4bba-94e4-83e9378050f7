package config

import (
	"context"
	"fmt"
	"sync"
)

// RouteConfigListener handles route configuration changes
type RouteConfigListener struct {
	name     string
	logger   Logger
	callback func(ctx context.Context, change ConfigChange) error
	mu       sync.RWMutex
}

// NewRouteConfigListener creates a new route configuration listener
func NewRouteConfigListener(name string, logger Logger, callback func(ctx context.Context, change ConfigChange) error) *RouteConfigListener {
	return &RouteConfigListener{
		name:     name,
		logger:   logger,
		callback: callback,
	}
}

// OnConfigChange handles configuration changes for routes
func (l *RouteConfigListener) OnConfigChange(ctx context.Context, change ConfigChange) error {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	l.logger.Info("Route configuration change received", 
		"listener", l.name,
		"type", change.Type,
		"key", change.Key)
	
	if l.callback != nil {
		return l.callback(ctx, change)
	}
	
	return nil
}

// GetName returns the listener name
func (l *RouteConfigListener) GetName() string {
	return l.name
}

// SecurityConfigListener handles security configuration changes
type SecurityConfigListener struct {
	name     string
	logger   Logger
	callback func(ctx context.Context, change ConfigChange) error
	mu       sync.RWMutex
}

// NewSecurityConfigListener creates a new security configuration listener
func NewSecurityConfigListener(name string, logger Logger, callback func(ctx context.Context, change ConfigChange) error) *SecurityConfigListener {
	return &SecurityConfigListener{
		name:     name,
		logger:   logger,
		callback: callback,
	}
}

// OnConfigChange handles configuration changes for security
func (l *SecurityConfigListener) OnConfigChange(ctx context.Context, change ConfigChange) error {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	l.logger.Info("Security configuration change received", 
		"listener", l.name,
		"type", change.Type,
		"key", change.Key)
	
	if l.callback != nil {
		return l.callback(ctx, change)
	}
	
	return nil
}

// GetName returns the listener name
func (l *SecurityConfigListener) GetName() string {
	return l.name
}

// PluginConfigListener handles plugin configuration changes
type PluginConfigListener struct {
	name     string
	logger   Logger
	callback func(ctx context.Context, change ConfigChange) error
	mu       sync.RWMutex
}

// NewPluginConfigListener creates a new plugin configuration listener
func NewPluginConfigListener(name string, logger Logger, callback func(ctx context.Context, change ConfigChange) error) *PluginConfigListener {
	return &PluginConfigListener{
		name:     name,
		logger:   logger,
		callback: callback,
	}
}

// OnConfigChange handles configuration changes for plugins
func (l *PluginConfigListener) OnConfigChange(ctx context.Context, change ConfigChange) error {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	l.logger.Info("Plugin configuration change received", 
		"listener", l.name,
		"type", change.Type,
		"key", change.Key)
	
	if l.callback != nil {
		return l.callback(ctx, change)
	}
	
	return nil
}

// GetName returns the listener name
func (l *PluginConfigListener) GetName() string {
	return l.name
}

// AuthConfigListener handles authentication configuration changes
type AuthConfigListener struct {
	name     string
	logger   Logger
	callback func(ctx context.Context, change ConfigChange) error
	mu       sync.RWMutex
}

// NewAuthConfigListener creates a new authentication configuration listener
func NewAuthConfigListener(name string, logger Logger, callback func(ctx context.Context, change ConfigChange) error) *AuthConfigListener {
	return &AuthConfigListener{
		name:     name,
		logger:   logger,
		callback: callback,
	}
}

// OnConfigChange handles configuration changes for authentication
func (l *AuthConfigListener) OnConfigChange(ctx context.Context, change ConfigChange) error {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	l.logger.Info("Auth configuration change received", 
		"listener", l.name,
		"type", change.Type,
		"key", change.Key)
	
	if l.callback != nil {
		return l.callback(ctx, change)
	}
	
	return nil
}

// GetName returns the listener name
func (l *AuthConfigListener) GetName() string {
	return l.name
}

// CompositeConfigListener combines multiple listeners
type CompositeConfigListener struct {
	name      string
	listeners []ConfigChangeListener
	logger    Logger
	mu        sync.RWMutex
}

// NewCompositeConfigListener creates a new composite configuration listener
func NewCompositeConfigListener(name string, logger Logger) *CompositeConfigListener {
	return &CompositeConfigListener{
		name:      name,
		listeners: make([]ConfigChangeListener, 0),
		logger:    logger,
	}
}

// AddListener adds a listener to the composite
func (l *CompositeConfigListener) AddListener(listener ConfigChangeListener) {
	l.mu.Lock()
	defer l.mu.Unlock()
	
	l.listeners = append(l.listeners, listener)
	l.logger.Info("Listener added to composite", 
		"composite", l.name,
		"listener", listener.GetName())
}

// RemoveListener removes a listener from the composite
func (l *CompositeConfigListener) RemoveListener(listenerName string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	
	for i, listener := range l.listeners {
		if listener.GetName() == listenerName {
			l.listeners = append(l.listeners[:i], l.listeners[i+1:]...)
			l.logger.Info("Listener removed from composite", 
				"composite", l.name,
				"listener", listenerName)
			break
		}
	}
}

// OnConfigChange handles configuration changes by delegating to all listeners
func (l *CompositeConfigListener) OnConfigChange(ctx context.Context, change ConfigChange) error {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	var errors []error
	
	for _, listener := range l.listeners {
		if err := listener.OnConfigChange(ctx, change); err != nil {
			errors = append(errors, fmt.Errorf("listener %s failed: %w", listener.GetName(), err))
		}
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("composite listener errors: %v", errors)
	}
	
	return nil
}

// GetName returns the listener name
func (l *CompositeConfigListener) GetName() string {
	return l.name
}

// AsyncConfigListener wraps another listener to handle changes asynchronously
type AsyncConfigListener struct {
	name     string
	listener ConfigChangeListener
	logger   Logger
	queue    chan ConfigChange
	workers  int
	stopChan chan struct{}
	mu       sync.RWMutex
}

// NewAsyncConfigListener creates a new asynchronous configuration listener
func NewAsyncConfigListener(name string, listener ConfigChangeListener, workers int, logger Logger) *AsyncConfigListener {
	return &AsyncConfigListener{
		name:     name,
		listener: listener,
		logger:   logger,
		queue:    make(chan ConfigChange, 100),
		workers:  workers,
		stopChan: make(chan struct{}),
	}
}

// Start starts the async listener workers
func (l *AsyncConfigListener) Start(ctx context.Context) {
	for i := 0; i < l.workers; i++ {
		go l.worker(ctx, i)
	}
	
	l.logger.Info("Async config listener started", 
		"listener", l.name,
		"workers", l.workers)
}

// Stop stops the async listener
func (l *AsyncConfigListener) Stop() {
	close(l.stopChan)
	l.logger.Info("Async config listener stopped", "listener", l.name)
}

// OnConfigChange queues configuration changes for async processing
func (l *AsyncConfigListener) OnConfigChange(ctx context.Context, change ConfigChange) error {
	select {
	case l.queue <- change:
		return nil
	default:
		return fmt.Errorf("async listener queue is full")
	}
}

// GetName returns the listener name
func (l *AsyncConfigListener) GetName() string {
	return l.name
}

// worker processes configuration changes asynchronously
func (l *AsyncConfigListener) worker(ctx context.Context, workerID int) {
	l.logger.Debug("Async config listener worker started", 
		"listener", l.name,
		"worker", workerID)
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-l.stopChan:
			return
		case change := <-l.queue:
			if err := l.listener.OnConfigChange(ctx, change); err != nil {
				l.logger.Error("Async config listener worker failed", 
					"listener", l.name,
					"worker", workerID,
					"error", err)
			}
		}
	}
}

// FilteredConfigListener filters configuration changes based on criteria
type FilteredConfigListener struct {
	name     string
	listener ConfigChangeListener
	filter   func(change ConfigChange) bool
	logger   Logger
	mu       sync.RWMutex
}

// NewFilteredConfigListener creates a new filtered configuration listener
func NewFilteredConfigListener(name string, listener ConfigChangeListener, filter func(change ConfigChange) bool, logger Logger) *FilteredConfigListener {
	return &FilteredConfigListener{
		name:     name,
		listener: listener,
		filter:   filter,
		logger:   logger,
	}
}

// OnConfigChange handles configuration changes with filtering
func (l *FilteredConfigListener) OnConfigChange(ctx context.Context, change ConfigChange) error {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	if l.filter != nil && !l.filter(change) {
		l.logger.Debug("Configuration change filtered out", 
			"listener", l.name,
			"type", change.Type,
			"section", change.Section,
			"key", change.Key)
		return nil
	}
	
	return l.listener.OnConfigChange(ctx, change)
}

// GetName returns the listener name
func (l *FilteredConfigListener) GetName() string {
	return l.name
}

// RetryConfigListener retries failed configuration changes
type RetryConfigListener struct {
	name       string
	listener   ConfigChangeListener
	maxRetries int
	logger     Logger
	mu         sync.RWMutex
}

// NewRetryConfigListener creates a new retry configuration listener
func NewRetryConfigListener(name string, listener ConfigChangeListener, maxRetries int, logger Logger) *RetryConfigListener {
	return &RetryConfigListener{
		name:       name,
		listener:   listener,
		maxRetries: maxRetries,
		logger:     logger,
	}
}

// OnConfigChange handles configuration changes with retry logic
func (l *RetryConfigListener) OnConfigChange(ctx context.Context, change ConfigChange) error {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	var lastErr error
	
	for attempt := 0; attempt <= l.maxRetries; attempt++ {
		if err := l.listener.OnConfigChange(ctx, change); err != nil {
			lastErr = err
			l.logger.Warn("Configuration change attempt failed", 
				"listener", l.name,
				"attempt", attempt+1,
				"max_retries", l.maxRetries,
				"error", err)
			
			if attempt < l.maxRetries {
				// Add some backoff logic here if needed
				continue
			}
		} else {
			if attempt > 0 {
				l.logger.Info("Configuration change succeeded after retry", 
					"listener", l.name,
					"attempts", attempt+1)
			}
			return nil
		}
	}
	
	return fmt.Errorf("configuration change failed after %d attempts: %w", l.maxRetries+1, lastErr)
}

// GetName returns the listener name
func (l *RetryConfigListener) GetName() string {
	return l.name
}
