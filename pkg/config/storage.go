package config

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// MemoryConfigStorage implements ConfigStorage using in-memory storage
type MemoryConfigStorage struct {
	data     map[string][]byte
	watchers map[string][]chan ConfigEvent
	mu       sync.RWMutex
	logger   Logger
}

// NewMemoryConfigStorage creates a new memory-based configuration storage
func NewMemoryConfigStorage(logger Logger) ConfigStorage {
	return &MemoryConfigStorage{
		data:     make(map[string][]byte),
		watchers: make(map[string][]chan ConfigEvent),
		logger:   logger,
	}
}

// NewMemoryStorage creates a new memory-based configuration storage without logger
func NewMemoryStorage() ConfigStorage {
	return &MemoryConfigStorage{
		data:     make(map[string][]byte),
		watchers: make(map[string][]chan ConfigEvent),
		logger:   nil,
	}
}

// Get retrieves a configuration value by key
func (m *MemoryConfigStorage) Get(key string) ([]byte, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if value, exists := m.data[key]; exists {
		return value, nil
	}
	
	return nil, fmt.Errorf("key %s not found", key)
}

// Set stores a configuration value by key
func (m *MemoryConfigStorage) Set(key string, value []byte) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	oldValue := m.data[key]
	m.data[key] = value
	
	// Notify watchers
	if watchers, exists := m.watchers[key]; exists {
		event := ConfigEvent{
			Type:  "update",
			Key:   key,
			Value: value,
		}
		
		for _, watcher := range watchers {
			select {
			case watcher <- event:
			default:
				// Channel is full, skip
			}
		}
	}
	
	m.logger.Debug("Configuration value updated", "key", key, "size", len(value))
	
	// Log the change if it's different
	if string(oldValue) != string(value) {
		m.logger.Info("Configuration changed", "key", key)
	}
	
	return nil
}

// Delete removes a configuration value by key
func (m *MemoryConfigStorage) Delete(key string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if _, exists := m.data[key]; !exists {
		return fmt.Errorf("key %s not found", key)
	}
	
	delete(m.data, key)
	
	// Notify watchers
	if watchers, exists := m.watchers[key]; exists {
		event := ConfigEvent{
			Type: "delete",
			Key:  key,
		}
		
		for _, watcher := range watchers {
			select {
			case watcher <- event:
			default:
				// Channel is full, skip
			}
		}
	}
	
	m.logger.Info("Configuration value deleted", "key", key)
	
	return nil
}

// List retrieves all configuration values with a given prefix
func (m *MemoryConfigStorage) List(prefix string) (map[string][]byte, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make(map[string][]byte)
	
	for key, value := range m.data {
		if prefix == "" || len(key) >= len(prefix) && key[:len(prefix)] == prefix {
			result[key] = value
		}
	}
	
	return result, nil
}

// Watch creates a watcher for configuration changes on a specific key
func (m *MemoryConfigStorage) Watch(key string) (<-chan ConfigEvent, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	watcher := make(chan ConfigEvent, 10)
	
	if m.watchers[key] == nil {
		m.watchers[key] = make([]chan ConfigEvent, 0)
	}
	
	m.watchers[key] = append(m.watchers[key], watcher)
	
	m.logger.Debug("Configuration watcher created", "key", key)
	
	return watcher, nil
}

// Close closes the storage and cleans up resources
func (m *MemoryConfigStorage) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Close all watchers
	for key, watchers := range m.watchers {
		for _, watcher := range watchers {
			close(watcher)
		}
		delete(m.watchers, key)
	}
	
	m.logger.Info("Memory configuration storage closed")
	
	return nil
}

// RedisConfigStorage implements ConfigStorage using Redis
type RedisConfigStorage struct {
	// Redis client would be here
	// client redis.Client
	logger Logger
}

// NewRedisConfigStorage creates a new Redis-based configuration storage
func NewRedisConfigStorage(address, password string, db int, logger Logger) (ConfigStorage, error) {
	// Implementation would initialize Redis client
	return &RedisConfigStorage{
		logger: logger,
	}, nil
}

// Get retrieves a configuration value by key from Redis
func (r *RedisConfigStorage) Get(key string) ([]byte, error) {
	// Implementation would use Redis GET command
	return nil, fmt.Errorf("Redis storage not implemented")
}

// Set stores a configuration value by key in Redis
func (r *RedisConfigStorage) Set(key string, value []byte) error {
	// Implementation would use Redis SET command
	return fmt.Errorf("Redis storage not implemented")
}

// Delete removes a configuration value by key from Redis
func (r *RedisConfigStorage) Delete(key string) error {
	// Implementation would use Redis DEL command
	return fmt.Errorf("Redis storage not implemented")
}

// List retrieves all configuration values with a given prefix from Redis
func (r *RedisConfigStorage) List(prefix string) (map[string][]byte, error) {
	// Implementation would use Redis SCAN command
	return nil, fmt.Errorf("Redis storage not implemented")
}

// Watch creates a watcher for configuration changes on a specific key in Redis
func (r *RedisConfigStorage) Watch(key string) (<-chan ConfigEvent, error) {
	// Implementation would use Redis pub/sub
	return nil, fmt.Errorf("Redis storage not implemented")
}

// Close closes the Redis connection
func (r *RedisConfigStorage) Close() error {
	// Implementation would close Redis client
	return nil
}

// FileConfigStorage implements ConfigStorage using file system
type FileConfigStorage struct {
	directory string
	logger    Logger
	watchers  map[string][]chan ConfigEvent
	mu        sync.RWMutex
}

// NewFileConfigStorage creates a new file-based configuration storage
func NewFileConfigStorage(directory string, logger Logger) ConfigStorage {
	return &FileConfigStorage{
		directory: directory,
		logger:    logger,
		watchers:  make(map[string][]chan ConfigEvent),
	}
}

// Get retrieves a configuration value by key from file
func (f *FileConfigStorage) Get(key string) ([]byte, error) {
	// Implementation would read from file
	return nil, fmt.Errorf("File storage not implemented")
}

// Set stores a configuration value by key to file
func (f *FileConfigStorage) Set(key string, value []byte) error {
	// Implementation would write to file
	return fmt.Errorf("File storage not implemented")
}

// Delete removes a configuration file by key
func (f *FileConfigStorage) Delete(key string) error {
	// Implementation would delete file
	return fmt.Errorf("File storage not implemented")
}

// List retrieves all configuration files with a given prefix
func (f *FileConfigStorage) List(prefix string) (map[string][]byte, error) {
	// Implementation would scan directory
	return nil, fmt.Errorf("File storage not implemented")
}

// Watch creates a file system watcher for configuration changes
func (f *FileConfigStorage) Watch(key string) (<-chan ConfigEvent, error) {
	// Implementation would use file system watcher
	return nil, fmt.Errorf("File storage not implemented")
}

// Close closes the file storage
func (f *FileConfigStorage) Close() error {
	f.mu.Lock()
	defer f.mu.Unlock()
	
	// Close all watchers
	for key, watchers := range f.watchers {
		for _, watcher := range watchers {
			close(watcher)
		}
		delete(f.watchers, key)
	}
	
	return nil
}

// ConfigStorageFactory creates configuration storage instances
type ConfigStorageFactory struct {
	logger Logger
}

// NewConfigStorageFactory creates a new configuration storage factory
func NewConfigStorageFactory(logger Logger) *ConfigStorageFactory {
	return &ConfigStorageFactory{
		logger: logger,
	}
}

// CreateStorage creates a configuration storage instance based on type
func (f *ConfigStorageFactory) CreateStorage(storageType string, config map[string]interface{}) (ConfigStorage, error) {
	switch storageType {
	case "memory":
		return NewMemoryConfigStorage(f.logger), nil
		
	case "redis":
		address, _ := config["address"].(string)
		password, _ := config["password"].(string)
		db, _ := config["db"].(int)
		
		return NewRedisConfigStorage(address, password, db, f.logger)
		
	case "file":
		directory, _ := config["directory"].(string)
		if directory == "" {
			directory = "/tmp/gateway-config"
		}
		
		return NewFileConfigStorage(directory, f.logger), nil
		
	default:
		return nil, fmt.Errorf("unsupported storage type: %s", storageType)
	}
}

// ConfigChangeNotifier handles configuration change notifications
type ConfigChangeNotifier struct {
	listeners []ConfigChangeListener
	mu        sync.RWMutex
	logger    Logger
}

// NewConfigChangeNotifier creates a new configuration change notifier
func NewConfigChangeNotifier(logger Logger) *ConfigChangeNotifier {
	return &ConfigChangeNotifier{
		listeners: make([]ConfigChangeListener, 0),
		logger:    logger,
	}
}

// AddListener adds a configuration change listener
func (n *ConfigChangeNotifier) AddListener(listener ConfigChangeListener) {
	n.mu.Lock()
	defer n.mu.Unlock()
	
	n.listeners = append(n.listeners, listener)
	n.logger.Info("Configuration change listener added", "listener", listener.GetName())
}

// RemoveListener removes a configuration change listener
func (n *ConfigChangeNotifier) RemoveListener(listenerName string) {
	n.mu.Lock()
	defer n.mu.Unlock()
	
	for i, listener := range n.listeners {
		if listener.GetName() == listenerName {
			n.listeners = append(n.listeners[:i], n.listeners[i+1:]...)
			n.logger.Info("Configuration change listener removed", "listener", listenerName)
			break
		}
	}
}

// NotifyChange notifies all listeners about a configuration change
func (n *ConfigChangeNotifier) NotifyChange(ctx context.Context, change ConfigChange) {
	n.mu.RLock()
	defer n.mu.RUnlock()
	
	for _, listener := range n.listeners {
		go func(l ConfigChangeListener) {
			if err := l.OnConfigChange(ctx, change); err != nil {
				n.logger.Error("Configuration change listener failed", 
					"listener", l.GetName(), 
					"error", err)
			}
		}(listener)
	}
}

// ConfigMetrics tracks configuration-related metrics
type ConfigMetrics struct {
	UpdatesTotal     int64     `json:"updates_total"`
	UpdatesSucceeded int64     `json:"updates_succeeded"`
	UpdatesFailed    int64     `json:"updates_failed"`
	LastUpdate       time.Time `json:"last_update"`
	CurrentVersion   string    `json:"current_version"`
	mu               sync.RWMutex
}

// NewConfigMetrics creates a new configuration metrics tracker
func NewConfigMetrics() *ConfigMetrics {
	return &ConfigMetrics{}
}

// RecordUpdate records a configuration update
func (m *ConfigMetrics) RecordUpdate(success bool, version string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.UpdatesTotal++
	if success {
		m.UpdatesSucceeded++
		m.CurrentVersion = version
	} else {
		m.UpdatesFailed++
	}
	m.LastUpdate = time.Now()
}

// GetMetrics returns the current metrics
func (m *ConfigMetrics) GetMetrics() ConfigMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	return ConfigMetrics{
		UpdatesTotal:     m.UpdatesTotal,
		UpdatesSucceeded: m.UpdatesSucceeded,
		UpdatesFailed:    m.UpdatesFailed,
		LastUpdate:       m.LastUpdate,
		CurrentVersion:   m.CurrentVersion,
	}
}
