package config

// LoggerAdapter 适配器，将任何实现了基本日志方法的类型适配为 Logger 接口
type LoggerAdapter struct {
	info  func(msg string, fields ...interface{})
	error func(msg string, fields ...interface{})
	warn  func(msg string, fields ...interface{})
	debug func(msg string, fields ...interface{})
	with  func(key string, value interface{}) Logger
}

// NewLoggerAdapter 创建日志适配器
func NewLoggerAdapter(
	info func(msg string, fields ...interface{}),
	errorFunc func(msg string, fields ...interface{}),
	warn func(msg string, fields ...interface{}),
	debug func(msg string, fields ...interface{}),
	with func(key string, value interface{}) Logger,
) Logger {
	return &LoggerAdapter{
		info:  info,
		error: errorFunc,
		warn:  warn,
		debug: debug,
		with:  with,
	}
}

// Info 记录信息级别日志
func (l *LoggerAdapter) Info(msg string, fields ...interface{}) {
	if l.info != nil {
		l.info(msg, fields...)
	}
}

// Error 记录错误级别日志
func (l *LoggerAdapter) Error(msg string, fields ...interface{}) {
	if l.error != nil {
		l.error(msg, fields...)
	}
}

// Warn 记录警告级别日志
func (l *LoggerAdapter) Warn(msg string, fields ...interface{}) {
	if l.warn != nil {
		l.warn(msg, fields...)
	}
}

// Debug 记录调试级别日志
func (l *LoggerAdapter) Debug(msg string, fields ...interface{}) {
	if l.debug != nil {
		l.debug(msg, fields...)
	}
}

// With 创建带有额外字段的日志器
func (l *LoggerAdapter) With(key string, value interface{}) Logger {
	if l.with != nil {
		return l.with(key, value)
	}
	return l
}
