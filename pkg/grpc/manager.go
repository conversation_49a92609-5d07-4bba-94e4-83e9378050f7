package grpc

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// Manager gRPC管理器，整合所有gRPC相关功能
type Manager struct {
	config          config.GRPCConfig
	logger          *telemetry.Logger
	
	// 组件
	clientManager   *ClientManager
	proxyHandler    *ProxyHandler
	healthChecker   *HealthChecker
	discoveryManager *DiscoveryManager
	
	// gRPC服务器
	grpcServer      *grpc.Server
	httpServer      *http.Server
	
	// 状态
	running         bool
}

// NewManager 创建新的gRPC管理器
func NewManager(cfg config.GRPCConfig, discoveryCfg config.DiscoveryConfig, logger *telemetry.Logger) *Manager {
	manager := &Manager{
		config: cfg,
		logger: logger.With("component", "grpc-manager"),
	}
	
	// 初始化组件
	manager.initializeComponents(discoveryCfg)
	
	return manager
}

// initializeComponents 初始化所有组件
func (m *Manager) initializeComponents(discoveryCfg config.DiscoveryConfig) {
	// 创建客户端管理器
	m.clientManager = NewClientManager(m.config.Client, m.logger)
	
	// 创建服务发现管理器
	m.discoveryManager = NewDiscoveryManager(discoveryCfg, m.logger)
	
	// 创建健康检查器
	m.healthChecker = NewHealthChecker(m.config.HealthCheck, m.clientManager, m.logger)
	
	// 创建代理处理器
	m.proxyHandler = NewProxyHandler(m.clientManager, m.config.Gateway, m.logger)
	
	// 创建gRPC服务器
	if m.config.Server.Address != "" {
		m.setupGRPCServer()
	}
	
	// 创建HTTP服务器（用于HTTP到gRPC的转换）
	if m.config.Gateway.Enabled {
		m.setupHTTPServer()
	}
}

// setupGRPCServer 设置gRPC服务器
func (m *Manager) setupGRPCServer() {
	var opts []grpc.ServerOption
	
	// 设置消息大小限制
	if m.config.Server.MaxRecvMsgSize > 0 {
		opts = append(opts, grpc.MaxRecvMsgSize(m.config.Server.MaxRecvMsgSize))
	}
	if m.config.Server.MaxSendMsgSize > 0 {
		opts = append(opts, grpc.MaxSendMsgSize(m.config.Server.MaxSendMsgSize))
	}
	
	// 设置Keep-alive参数
	if m.config.Server.KeepAlive.Time > 0 {
		kaParams := keepalive.ServerParameters{
			Time:    time.Duration(m.config.Server.KeepAlive.Time) * time.Second,
			Timeout: time.Duration(m.config.Server.KeepAlive.Timeout) * time.Second,
		}
		opts = append(opts, grpc.KeepaliveParams(kaParams))
		
		kaPolicy := keepalive.EnforcementPolicy{
			MinTime:             time.Duration(m.config.Server.KeepAlive.Time) * time.Second,
			PermitWithoutStream: m.config.Server.KeepAlive.PermitWithoutStream,
		}
		opts = append(opts, grpc.KeepaliveEnforcementPolicy(kaPolicy))
	}
	
	// 创建gRPC服务器
	m.grpcServer = grpc.NewServer(opts...)
	
	// 启用反射（如果配置了）
	if m.config.Server.Reflection {
		reflection.Register(m.grpcServer)
		m.logger.Info("gRPC反射已启用")
	}
}

// setupHTTPServer 设置HTTP服务器
func (m *Manager) setupHTTPServer() {
	mux := http.NewServeMux()
	
	// 注册gRPC代理处理器
	if m.config.Gateway.PathPrefix != "" {
		mux.Handle(m.config.Gateway.PathPrefix, m.proxyHandler)
	} else {
		mux.Handle("/", m.proxyHandler)
	}
	
	// 注册健康检查端点
	mux.HandleFunc("/grpc/health", m.handleHealthCheck)
	
	// 注册统计信息端点
	mux.HandleFunc("/grpc/stats", m.handleStats)
	
	// 注册服务发现端点
	mux.HandleFunc("/grpc/services", m.handleServices)
	
	m.httpServer = &http.Server{
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}
}

// Start 启动gRPC管理器
func (m *Manager) Start() error {
	if !m.config.Enabled {
		m.logger.Info("gRPC支持未启用")
		return nil
	}
	
	m.logger.Info("启动gRPC管理器")
	
	// 启动服务发现管理器
	if err := m.discoveryManager.Start(); err != nil {
		return fmt.Errorf("启动服务发现管理器失败: %w", err)
	}
	
	// 启动健康检查器
	m.healthChecker.Start()
	
	// 启动gRPC服务器
	if m.grpcServer != nil && m.config.Server.Address != "" {
		go m.startGRPCServer()
	}
	
	// 启动HTTP服务器
	if m.httpServer != nil && m.config.Gateway.Enabled {
		go m.startHTTPServer()
	}
	
	m.running = true
	m.logger.Info("gRPC管理器启动完成")
	
	return nil
}

// Stop 停止gRPC管理器
func (m *Manager) Stop() error {
	if !m.running {
		return nil
	}
	
	m.logger.Info("停止gRPC管理器")
	
	// 停止HTTP服务器
	if m.httpServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		m.httpServer.Shutdown(ctx)
	}
	
	// 停止gRPC服务器
	if m.grpcServer != nil {
		m.grpcServer.GracefulStop()
	}
	
	// 停止健康检查器
	m.healthChecker.Stop()
	
	// 停止服务发现管理器
	m.discoveryManager.Stop()
	
	// 关闭所有客户端连接
	m.clientManager.CloseAll()
	
	m.running = false
	m.logger.Info("gRPC管理器已停止")
	
	return nil
}

// startGRPCServer 启动gRPC服务器
func (m *Manager) startGRPCServer() {
	listener, err := net.Listen("tcp", m.config.Server.Address)
	if err != nil {
		m.logger.Error("gRPC服务器监听失败", "address", m.config.Server.Address, "error", err)
		return
	}
	
	m.logger.Info("gRPC服务器启动", "address", m.config.Server.Address)
	
	if err := m.grpcServer.Serve(listener); err != nil {
		m.logger.Error("gRPC服务器运行失败", "error", err)
	}
}

// startHTTPServer 启动HTTP服务器
func (m *Manager) startHTTPServer() {
	// 使用与主HTTP服务器不同的端口
	address := ":8081" // 可以配置化
	
	m.logger.Info("gRPC HTTP网关启动", "address", address)
	
	if err := http.ListenAndServe(address, m.httpServer.Handler); err != nil {
		m.logger.Error("gRPC HTTP网关运行失败", "error", err)
	}
}

// handleHealthCheck 处理健康检查请求
func (m *Manager) handleHealthCheck(w http.ResponseWriter, r *http.Request) {
	serviceName := r.URL.Query().Get("service")
	address := r.URL.Query().Get("address")
	
	if address == "" {
		http.Error(w, "缺少address参数", http.StatusBadRequest)
		return
	}
	
	health := m.healthChecker.CheckService(address, serviceName)
	
	w.Header().Set("Content-Type", "application/json")
	
	response := map[string]interface{}{
		"address":       health.Address,
		"service_name":  health.ServiceName,
		"status":        health.Status.String(),
		"last_check":    health.LastCheck,
		"error_count":   health.ErrorCount,
		"success_count": health.SuccessCount,
	}
	
	if health.LastError != nil {
		response["last_error"] = health.LastError.Error()
	}
	
	// 根据健康状态设置HTTP状态码
	if health.Status != HealthStatusServing {
		w.WriteHeader(http.StatusServiceUnavailable)
	}
	
	// 序列化响应
	if err := writeJSONResponse(w, response); err != nil {
		m.logger.Error("写入健康检查响应失败", "error", err)
	}
}

// handleStats 处理统计信息请求
func (m *Manager) handleStats(w http.ResponseWriter, r *http.Request) {
	stats := map[string]interface{}{
		"client_manager":    m.clientManager.GetStats(),
		"health_checker":    m.healthChecker.GetHealthStats(),
		"grpc_enabled":      m.config.Enabled,
		"gateway_enabled":   m.config.Gateway.Enabled,
		"server_address":    m.config.Server.Address,
		"reflection_enabled": m.config.Server.Reflection,
	}
	
	w.Header().Set("Content-Type", "application/json")
	
	if err := writeJSONResponse(w, stats); err != nil {
		m.logger.Error("写入统计信息响应失败", "error", err)
		http.Error(w, "内部服务器错误", http.StatusInternalServerError)
	}
}

// handleServices 处理服务发现请求
func (m *Manager) handleServices(w http.ResponseWriter, r *http.Request) {
	serviceName := r.URL.Query().Get("service")
	
	if serviceName == "" {
		// 返回所有服务的健康状态
		allHealth := m.healthChecker.GetAllServiceHealth()
		w.Header().Set("Content-Type", "application/json")
		
		if err := writeJSONResponse(w, allHealth); err != nil {
			m.logger.Error("写入服务列表响应失败", "error", err)
			http.Error(w, "内部服务器错误", http.StatusInternalServerError)
		}
		return
	}
	
	// 发现指定服务
	instances, err := m.discoveryManager.DiscoverService(serviceName)
	if err != nil {
		m.logger.Error("服务发现失败", "service", serviceName, "error", err)
		http.Error(w, fmt.Sprintf("服务发现失败: %v", err), http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	
	if err := writeJSONResponse(w, instances); err != nil {
		m.logger.Error("写入服务发现响应失败", "error", err)
		http.Error(w, "内部服务器错误", http.StatusInternalServerError)
	}
}

// GetClientManager 获取客户端管理器
func (m *Manager) GetClientManager() *ClientManager {
	return m.clientManager
}

// GetHealthChecker 获取健康检查器
func (m *Manager) GetHealthChecker() *HealthChecker {
	return m.healthChecker
}

// GetDiscoveryManager 获取服务发现管理器
func (m *Manager) GetDiscoveryManager() *DiscoveryManager {
	return m.discoveryManager
}

// GetProxyHandler 获取代理处理器
func (m *Manager) GetProxyHandler() *ProxyHandler {
	return m.proxyHandler
}

// IsRunning 检查是否正在运行
func (m *Manager) IsRunning() bool {
	return m.running
}

// writeJSONResponse 写入JSON响应
func writeJSONResponse(w http.ResponseWriter, data interface{}) error {
	w.Header().Set("Content-Type", "application/json")

	encoder := json.NewEncoder(w)
	encoder.SetIndent("", "  ")
	return encoder.Encode(data)
}
