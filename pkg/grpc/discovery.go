package grpc

import (
	"fmt"
	"sync"
	"time"

	"google.golang.org/grpc/resolver"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// ServiceInstance 表示服务实例
type ServiceInstance struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Address  string            `json:"address"`
	Port     int               `json:"port"`
	Tags     []string          `json:"tags"`
	Metadata map[string]string `json:"metadata"`
	Health   string            `json:"health"` // passing, warning, critical
}

// ServiceDiscovery gRPC服务发现接口
type ServiceDiscovery interface {
	// 发现服务实例
	DiscoverServices(serviceName string) ([]*ServiceInstance, error)
	
	// 监听服务变化
	WatchServices(serviceName string, callback func([]*ServiceInstance)) error
	
	// 停止监听
	StopWatching(serviceName string) error
	
	// 注册服务
	RegisterService(instance *ServiceInstance) error
	
	// 注销服务
	DeregisterService(serviceID string) error
}

// DiscoveryManager 服务发现管理器
type DiscoveryManager struct {
	config    config.DiscoveryConfig
	logger    *telemetry.Logger
	discovery ServiceDiscovery
	
	// 服务缓存
	serviceCache map[string][]*ServiceInstance
	cacheMutex   sync.RWMutex
	
	// 监听器
	watchers map[string][]func([]*ServiceInstance)
	watchMutex sync.RWMutex
	
	// 停止信号
	stopChan chan struct{}
	wg       sync.WaitGroup
}

// NewDiscoveryManager 创建新的服务发现管理器
func NewDiscoveryManager(cfg config.DiscoveryConfig, logger *telemetry.Logger) *DiscoveryManager {
	dm := &DiscoveryManager{
		config:       cfg,
		logger:       logger.With("component", "grpc-discovery"),
		serviceCache: make(map[string][]*ServiceInstance),
		watchers:     make(map[string][]func([]*ServiceInstance)),
		stopChan:     make(chan struct{}),
	}
	
	// 根据配置类型初始化服务发现
	switch cfg.Type {
	case "consul":
		dm.discovery = NewConsulDiscovery(cfg.Consul, logger)
	case "nacos":
		dm.discovery = NewNacosDiscovery(cfg.Nacos, logger)
	default:
		dm.discovery = NewStaticDiscovery(logger)
	}
	
	return dm
}

// Start 启动服务发现管理器
func (dm *DiscoveryManager) Start() error {
	dm.logger.Info("启动gRPC服务发现管理器", "type", dm.config.Type)
	
	dm.wg.Add(1)
	go dm.cacheRefreshLoop()
	
	return nil
}

// Stop 停止服务发现管理器
func (dm *DiscoveryManager) Stop() error {
	close(dm.stopChan)
	dm.wg.Wait()
	
	dm.logger.Info("gRPC服务发现管理器已停止")
	return nil
}

// DiscoverService 发现服务实例
func (dm *DiscoveryManager) DiscoverService(serviceName string) ([]*ServiceInstance, error) {
	// 先从缓存获取
	dm.cacheMutex.RLock()
	if instances, exists := dm.serviceCache[serviceName]; exists {
		dm.cacheMutex.RUnlock()
		return instances, nil
	}
	dm.cacheMutex.RUnlock()
	
	// 从服务发现获取
	instances, err := dm.discovery.DiscoverServices(serviceName)
	if err != nil {
		return nil, err
	}
	
	// 更新缓存
	dm.cacheMutex.Lock()
	dm.serviceCache[serviceName] = instances
	dm.cacheMutex.Unlock()
	
	return instances, nil
}

// WatchService 监听服务变化
func (dm *DiscoveryManager) WatchService(serviceName string, callback func([]*ServiceInstance)) error {
	dm.watchMutex.Lock()
	defer dm.watchMutex.Unlock()
	
	// 添加监听器
	if _, exists := dm.watchers[serviceName]; !exists {
		dm.watchers[serviceName] = make([]func([]*ServiceInstance), 0)
		
		// 启动服务发现监听
		err := dm.discovery.WatchServices(serviceName, func(instances []*ServiceInstance) {
			// 更新缓存
			dm.cacheMutex.Lock()
			dm.serviceCache[serviceName] = instances
			dm.cacheMutex.Unlock()
			
			// 通知所有监听器
			dm.watchMutex.RLock()
			watchers := dm.watchers[serviceName]
			dm.watchMutex.RUnlock()
			
			for _, watcher := range watchers {
				go watcher(instances)
			}
		})
		
		if err != nil {
			return err
		}
	}
	
	dm.watchers[serviceName] = append(dm.watchers[serviceName], callback)
	return nil
}

// cacheRefreshLoop 缓存刷新循环
func (dm *DiscoveryManager) cacheRefreshLoop() {
	defer dm.wg.Done()
	
	ticker := time.NewTicker(30 * time.Second) // 每30秒刷新一次缓存
	defer ticker.Stop()
	
	for {
		select {
		case <-dm.stopChan:
			return
		case <-ticker.C:
			dm.refreshCache()
		}
	}
}

// refreshCache 刷新服务缓存
func (dm *DiscoveryManager) refreshCache() {
	dm.cacheMutex.RLock()
	serviceNames := make([]string, 0, len(dm.serviceCache))
	for serviceName := range dm.serviceCache {
		serviceNames = append(serviceNames, serviceName)
	}
	dm.cacheMutex.RUnlock()
	
	for _, serviceName := range serviceNames {
		instances, err := dm.discovery.DiscoverServices(serviceName)
		if err != nil {
			dm.logger.Error("刷新服务缓存失败", "service", serviceName, "error", err)
			continue
		}
		
		dm.cacheMutex.Lock()
		dm.serviceCache[serviceName] = instances
		dm.cacheMutex.Unlock()
	}
}

// GetServiceAddress 获取服务地址（负载均衡）
func (dm *DiscoveryManager) GetServiceAddress(serviceName string) (string, error) {
	instances, err := dm.DiscoverService(serviceName)
	if err != nil {
		return "", err
	}
	
	if len(instances) == 0 {
		return "", fmt.Errorf("服务 %s 没有可用实例", serviceName)
	}
	
	// 简单的轮询负载均衡
	// 这里可以实现更复杂的负载均衡算法
	instance := instances[0]
	return fmt.Sprintf("%s:%d", instance.Address, instance.Port), nil
}

// StaticDiscovery 静态服务发现实现
type StaticDiscovery struct {
	logger    *telemetry.Logger
	services  map[string][]*ServiceInstance
	mutex     sync.RWMutex
}

// NewStaticDiscovery 创建静态服务发现
func NewStaticDiscovery(logger *telemetry.Logger) *StaticDiscovery {
	return &StaticDiscovery{
		logger:   logger.With("discovery", "static"),
		services: make(map[string][]*ServiceInstance),
	}
}

// DiscoverServices 发现服务实例
func (sd *StaticDiscovery) DiscoverServices(serviceName string) ([]*ServiceInstance, error) {
	sd.mutex.RLock()
	defer sd.mutex.RUnlock()
	
	if instances, exists := sd.services[serviceName]; exists {
		return instances, nil
	}
	
	return []*ServiceInstance{}, nil
}

// WatchServices 监听服务变化（静态发现不支持）
func (sd *StaticDiscovery) WatchServices(serviceName string, callback func([]*ServiceInstance)) error {
	// 静态发现不支持监听
	return nil
}

// StopWatching 停止监听
func (sd *StaticDiscovery) StopWatching(serviceName string) error {
	return nil
}

// RegisterService 注册服务
func (sd *StaticDiscovery) RegisterService(instance *ServiceInstance) error {
	sd.mutex.Lock()
	defer sd.mutex.Unlock()
	
	if _, exists := sd.services[instance.Name]; !exists {
		sd.services[instance.Name] = make([]*ServiceInstance, 0)
	}
	
	sd.services[instance.Name] = append(sd.services[instance.Name], instance)
	return nil
}

// DeregisterService 注销服务
func (sd *StaticDiscovery) DeregisterService(serviceID string) error {
	sd.mutex.Lock()
	defer sd.mutex.Unlock()
	
	for serviceName, instances := range sd.services {
		for i, instance := range instances {
			if instance.ID == serviceID {
				// 移除实例
				sd.services[serviceName] = append(instances[:i], instances[i+1:]...)
				return nil
			}
		}
	}
	
	return fmt.Errorf("服务实例 %s 未找到", serviceID)
}

// GRPCResolver gRPC解析器实现
type GRPCResolver struct {
	target   resolver.Target
	cc       resolver.ClientConn
	dm       *DiscoveryManager
	logger   *telemetry.Logger
	stopChan chan struct{}
}

// NewGRPCResolver 创建gRPC解析器
func NewGRPCResolver(target resolver.Target, cc resolver.ClientConn, dm *DiscoveryManager, logger *telemetry.Logger) *GRPCResolver {
	return &GRPCResolver{
		target:   target,
		cc:       cc,
		dm:       dm,
		logger:   logger.With("component", "grpc-resolver"),
		stopChan: make(chan struct{}),
	}
}

// ResolveNow 立即解析
func (gr *GRPCResolver) ResolveNow(resolver.ResolveNowOptions) {
	gr.resolve()
}

// Close 关闭解析器
func (gr *GRPCResolver) Close() {
	close(gr.stopChan)
}

// resolve 执行解析
func (gr *GRPCResolver) resolve() {
	serviceName := gr.target.Endpoint()
	instances, err := gr.dm.DiscoverService(serviceName)
	if err != nil {
		gr.logger.Error("解析服务失败", "service", serviceName, "error", err)
		gr.cc.ReportError(err)
		return
	}

	// 转换为gRPC地址
	var addrs []resolver.Address
	for _, instance := range instances {
		addr := resolver.Address{
			Addr: fmt.Sprintf("%s:%d", instance.Address, instance.Port),
		}
		addrs = append(addrs, addr)
	}

	// 更新连接状态
	gr.cc.UpdateState(resolver.State{
		Addresses: addrs,
	})
}

// ConsulDiscovery Consul服务发现实现（占位符）
type ConsulDiscovery struct {
	config config.ConsulConfig
	logger *telemetry.Logger
}

// NewConsulDiscovery 创建Consul服务发现
func NewConsulDiscovery(cfg config.ConsulConfig, logger *telemetry.Logger) *ConsulDiscovery {
	return &ConsulDiscovery{
		config: cfg,
		logger: logger.With("discovery", "consul"),
	}
}

// DiscoverServices 发现服务实例
func (cd *ConsulDiscovery) DiscoverServices(serviceName string) ([]*ServiceInstance, error) {
	// TODO: 实现Consul服务发现
	cd.logger.Info("Consul服务发现暂未实现", "service", serviceName)
	return []*ServiceInstance{}, nil
}

// WatchServices 监听服务变化
func (cd *ConsulDiscovery) WatchServices(serviceName string, callback func([]*ServiceInstance)) error {
	// TODO: 实现Consul服务监听
	return nil
}

// StopWatching 停止监听
func (cd *ConsulDiscovery) StopWatching(serviceName string) error {
	return nil
}

// RegisterService 注册服务
func (cd *ConsulDiscovery) RegisterService(instance *ServiceInstance) error {
	// TODO: 实现Consul服务注册
	return nil
}

// DeregisterService 注销服务
func (cd *ConsulDiscovery) DeregisterService(serviceID string) error {
	// TODO: 实现Consul服务注销
	return nil
}

// NacosDiscovery Nacos服务发现实现（占位符）
type NacosDiscovery struct {
	config config.NacosConfig
	logger *telemetry.Logger
}

// NewNacosDiscovery 创建Nacos服务发现
func NewNacosDiscovery(cfg config.NacosConfig, logger *telemetry.Logger) *NacosDiscovery {
	return &NacosDiscovery{
		config: cfg,
		logger: logger.With("discovery", "nacos"),
	}
}

// DiscoverServices 发现服务实例
func (nd *NacosDiscovery) DiscoverServices(serviceName string) ([]*ServiceInstance, error) {
	// TODO: 实现Nacos服务发现
	nd.logger.Info("Nacos服务发现暂未实现", "service", serviceName)
	return []*ServiceInstance{}, nil
}

// WatchServices 监听服务变化
func (nd *NacosDiscovery) WatchServices(serviceName string, callback func([]*ServiceInstance)) error {
	// TODO: 实现Nacos服务监听
	return nil
}

// StopWatching 停止监听
func (nd *NacosDiscovery) StopWatching(serviceName string) error {
	return nil
}

// RegisterService 注册服务
func (nd *NacosDiscovery) RegisterService(instance *ServiceInstance) error {
	// TODO: 实现Nacos服务注册
	return nil
}

// DeregisterService 注销服务
func (nd *NacosDiscovery) DeregisterService(serviceID string) error {
	// TODO: 实现Nacos服务注销
	return nil
}
