package grpc

import (
	"context"
	"fmt"
	"sync"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/status"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// HealthStatus 表示服务健康状态
type HealthStatus int

const (
	HealthStatusUnknown HealthStatus = iota
	HealthStatusServing
	HealthStatusNotServing
	HealthStatusServiceUnknown
)

func (hs HealthStatus) String() string {
	switch hs {
	case HealthStatusServing:
		return "SERVING"
	case HealthStatusNotServing:
		return "NOT_SERVING"
	case HealthStatusServiceUnknown:
		return "SERVICE_UNKNOWN"
	default:
		return "UNKNOWN"
	}
}

// ServiceHealth 表示单个服务的健康状态
type ServiceHealth struct {
	Address       string
	ServiceName   string
	Status        HealthStatus
	LastCheck     time.Time
	ErrorCount    int
	SuccessCount  int
	LastError     error
}

// HealthChecker gRPC健康检查器
type HealthChecker struct {
	config        config.GRPCHealthConfig
	clientManager *ClientManager
	logger        *telemetry.Logger
	
	// 健康状态缓存
	healthCache map[string]*ServiceHealth
	mutex       sync.RWMutex
	
	// 停止信号
	stopChan chan struct{}
	wg       sync.WaitGroup
}

// NewHealthChecker 创建新的健康检查器
func NewHealthChecker(cfg config.GRPCHealthConfig, clientManager *ClientManager, logger *telemetry.Logger) *HealthChecker {
	return &HealthChecker{
		config:        cfg,
		clientManager: clientManager,
		logger:        logger.With("component", "grpc-health-checker"),
		healthCache:   make(map[string]*ServiceHealth),
		stopChan:      make(chan struct{}),
	}
}

// Start 启动健康检查器
func (hc *HealthChecker) Start() {
	if !hc.config.Enabled {
		hc.logger.Info("gRPC健康检查未启用")
		return
	}

	hc.logger.Info("启动gRPC健康检查器", 
		"interval", hc.config.Interval,
		"timeout", hc.config.Timeout)

	hc.wg.Add(1)
	go hc.healthCheckLoop()
}

// Stop 停止健康检查器
func (hc *HealthChecker) Stop() {
	close(hc.stopChan)
	hc.wg.Wait()
	hc.logger.Info("gRPC健康检查器已停止")
}

// healthCheckLoop 健康检查循环
func (hc *HealthChecker) healthCheckLoop() {
	defer hc.wg.Done()

	ticker := time.NewTicker(time.Duration(hc.config.Interval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-hc.stopChan:
			return
		case <-ticker.C:
			hc.performHealthChecks()
		}
	}
}

// performHealthChecks 执行健康检查
func (hc *HealthChecker) performHealthChecks() {
	// 获取所有连接
	connections := hc.clientManager.ListConnections()
	
	for address := range connections {
		hc.checkServiceHealth(address, "")
	}
}

// CheckService 检查指定服务的健康状态
func (hc *HealthChecker) CheckService(address, serviceName string) *ServiceHealth {
	return hc.checkServiceHealth(address, serviceName)
}

// checkServiceHealth 检查单个服务的健康状态
func (hc *HealthChecker) checkServiceHealth(address, serviceName string) *ServiceHealth {
	key := fmt.Sprintf("%s:%s", address, serviceName)
	
	// 获取或创建健康状态记录
	hc.mutex.Lock()
	health, exists := hc.healthCache[key]
	if !exists {
		health = &ServiceHealth{
			Address:     address,
			ServiceName: serviceName,
			Status:      HealthStatusUnknown,
		}
		hc.healthCache[key] = health
	}
	hc.mutex.Unlock()

	// 执行健康检查
	ctx, cancel := context.WithTimeout(context.Background(), 
		time.Duration(hc.config.Timeout)*time.Second)
	defer cancel()

	status, err := hc.performHealthCheck(ctx, address, serviceName)
	
	// 更新健康状态
	hc.mutex.Lock()
	health.LastCheck = time.Now()
	health.Status = status
	health.LastError = err
	
	if err != nil {
		health.ErrorCount++
		hc.logger.Warn("gRPC健康检查失败", 
			"address", address,
			"service", serviceName,
			"error", err)
	} else {
		health.SuccessCount++
		hc.logger.Debug("gRPC健康检查成功", 
			"address", address,
			"service", serviceName,
			"status", status)
	}
	hc.mutex.Unlock()

	return health
}

// performHealthCheck 执行实际的健康检查
func (hc *HealthChecker) performHealthCheck(ctx context.Context, address, serviceName string) (HealthStatus, error) {
	// 获取gRPC连接
	conn, err := hc.clientManager.GetConnection(address)
	if err != nil {
		return HealthStatusNotServing, fmt.Errorf("获取连接失败: %w", err)
	}

	// 创建健康检查客户端
	client := grpc_health_v1.NewHealthClient(conn)

	// 执行健康检查请求
	req := &grpc_health_v1.HealthCheckRequest{
		Service: serviceName,
	}

	resp, err := client.Check(ctx, req)
	if err != nil {
		// 检查错误类型
		if st, ok := status.FromError(err); ok {
			switch st.Code() {
			case codes.NotFound:
				return HealthStatusServiceUnknown, err
			case codes.Unimplemented:
				// 服务不支持健康检查，假设它是健康的
				return HealthStatusServing, nil
			default:
				return HealthStatusNotServing, err
			}
		}
		return HealthStatusNotServing, err
	}

	// 转换响应状态
	switch resp.Status {
	case grpc_health_v1.HealthCheckResponse_SERVING:
		return HealthStatusServing, nil
	case grpc_health_v1.HealthCheckResponse_NOT_SERVING:
		return HealthStatusNotServing, nil
	case grpc_health_v1.HealthCheckResponse_SERVICE_UNKNOWN:
		return HealthStatusServiceUnknown, nil
	default:
		return HealthStatusUnknown, fmt.Errorf("未知健康状态: %v", resp.Status)
	}
}

// GetServiceHealth 获取服务健康状态
func (hc *HealthChecker) GetServiceHealth(address, serviceName string) *ServiceHealth {
	key := fmt.Sprintf("%s:%s", address, serviceName)
	
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()
	
	if health, exists := hc.healthCache[key]; exists {
		// 返回副本以避免并发修改
		return &ServiceHealth{
			Address:      health.Address,
			ServiceName:  health.ServiceName,
			Status:       health.Status,
			LastCheck:    health.LastCheck,
			ErrorCount:   health.ErrorCount,
			SuccessCount: health.SuccessCount,
			LastError:    health.LastError,
		}
	}
	
	return nil
}

// GetAllServiceHealth 获取所有服务的健康状态
func (hc *HealthChecker) GetAllServiceHealth() map[string]*ServiceHealth {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()
	
	result := make(map[string]*ServiceHealth)
	for key, health := range hc.healthCache {
		result[key] = &ServiceHealth{
			Address:      health.Address,
			ServiceName:  health.ServiceName,
			Status:       health.Status,
			LastCheck:    health.LastCheck,
			ErrorCount:   health.ErrorCount,
			SuccessCount: health.SuccessCount,
			LastError:    health.LastError,
		}
	}
	
	return result
}

// IsServiceHealthy 检查服务是否健康
func (hc *HealthChecker) IsServiceHealthy(address, serviceName string) bool {
	health := hc.GetServiceHealth(address, serviceName)
	if health == nil {
		return false
	}
	
	return health.Status == HealthStatusServing
}

// RemoveService 移除服务健康状态记录
func (hc *HealthChecker) RemoveService(address, serviceName string) {
	key := fmt.Sprintf("%s:%s", address, serviceName)
	
	hc.mutex.Lock()
	defer hc.mutex.Unlock()
	
	delete(hc.healthCache, key)
}

// GetHealthStats 获取健康检查统计信息
func (hc *HealthChecker) GetHealthStats() map[string]interface{} {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()
	
	totalServices := len(hc.healthCache)
	servingCount := 0
	notServingCount := 0
	unknownCount := 0
	
	for _, health := range hc.healthCache {
		switch health.Status {
		case HealthStatusServing:
			servingCount++
		case HealthStatusNotServing:
			notServingCount++
		default:
			unknownCount++
		}
	}
	
	return map[string]interface{}{
		"total_services":     totalServices,
		"serving_services":   servingCount,
		"not_serving_services": notServingCount,
		"unknown_services":   unknownCount,
		"health_check_enabled": hc.config.Enabled,
		"check_interval":     hc.config.Interval,
		"check_timeout":      hc.config.Timeout,
	}
}
