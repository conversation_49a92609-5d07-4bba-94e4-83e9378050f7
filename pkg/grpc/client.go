package grpc

import (
	"context"
	"fmt"
	"sync"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/resolver"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// ClientManager 管理gRPC客户端连接
type ClientManager struct {
	config      config.GRPCClientConfig
	logger      *telemetry.Logger
	connections map[string]*grpc.ClientConn
	mutex       sync.RWMutex
	
	// 连接选项
	dialOptions []grpc.DialOption
}

// NewClientManager 创建新的gRPC客户端管理器
func NewClientManager(cfg config.GRPCClientConfig, logger *telemetry.Logger) *ClientManager {
	manager := &ClientManager{
		config:      cfg,
		logger:      logger.With("component", "grpc-client-manager"),
		connections: make(map[string]*grpc.ClientConn),
	}

	// 设置默认拨号选项
	manager.setupDialOptions()

	return manager
}

// setupDialOptions 设置gRPC拨号选项
func (cm *ClientManager) setupDialOptions() {
	var opts []grpc.DialOption

	// 设置不安全连接（在生产环境中应该使用TLS）
	opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))

	// 设置消息大小限制
	if cm.config.MaxRecvMsgSize > 0 {
		opts = append(opts, grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(cm.config.MaxRecvMsgSize)))
	}
	if cm.config.MaxSendMsgSize > 0 {
		opts = append(opts, grpc.WithDefaultCallOptions(grpc.MaxCallSendMsgSize(cm.config.MaxSendMsgSize)))
	}

	// 设置Keep-alive参数
	if cm.config.KeepAlive.Time > 0 {
		kaParams := keepalive.ClientParameters{
			Time:                time.Duration(cm.config.KeepAlive.Time) * time.Second,
			Timeout:             time.Duration(cm.config.KeepAlive.Timeout) * time.Second,
			PermitWithoutStream: cm.config.KeepAlive.PermitWithoutStream,
		}
		opts = append(opts, grpc.WithKeepaliveParams(kaParams))
	}

	// 设置负载均衡策略
	if cm.config.LoadBalancer != "" {
		opts = append(opts, grpc.WithDefaultServiceConfig(fmt.Sprintf(`{"loadBalancingPolicy":"%s"}`, cm.config.LoadBalancer)))
	}

	// 设置重试策略
	if cm.config.Retry.MaxAttempts > 0 {
		retryPolicy := fmt.Sprintf(`{
			"methodConfig": [{
				"name": [{}],
				"retryPolicy": {
					"MaxAttempts": %d,
					"InitialBackoff": "%dms",
					"MaxBackoff": "%dms",
					"BackoffMultiplier": %f,
					"RetryableStatusCodes": %v
				}
			}]
		}`, cm.config.Retry.MaxAttempts,
			cm.config.Retry.InitialBackoff,
			cm.config.Retry.MaxBackoff,
			cm.config.Retry.BackoffMultiplier,
			cm.config.Retry.RetryableStatusCodes)
		
		opts = append(opts, grpc.WithDefaultServiceConfig(retryPolicy))
	}

	cm.dialOptions = opts
}

// GetConnection 获取到指定地址的gRPC连接
func (cm *ClientManager) GetConnection(address string) (*grpc.ClientConn, error) {
	cm.mutex.RLock()
	if conn, exists := cm.connections[address]; exists {
		// 检查连接状态
		if conn.GetState() != connectivity.Shutdown {
			cm.mutex.RUnlock()
			return conn, nil
		}
	}
	cm.mutex.RUnlock()

	// 需要创建新连接
	return cm.createConnection(address)
}

// createConnection 创建新的gRPC连接
func (cm *ClientManager) createConnection(address string) (*grpc.ClientConn, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 双重检查
	if conn, exists := cm.connections[address]; exists {
		if conn.GetState() != connectivity.Shutdown {
			return conn, nil
		}
		// 清理已关闭的连接
		delete(cm.connections, address)
	}

	cm.logger.Info("创建gRPC连接", "address", address)

	// 设置连接超时
	ctx := context.Background()
	if cm.config.DefaultTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(cm.config.DefaultTimeout)*time.Second)
		defer cancel()
	}

	// 创建连接
	conn, err := grpc.DialContext(ctx, address, cm.dialOptions...)
	if err != nil {
		cm.logger.Error("创建gRPC连接失败", "address", address, "error", err)
		return nil, fmt.Errorf("创建gRPC连接失败: %w", err)
	}

	// 缓存连接
	cm.connections[address] = conn

	cm.logger.Info("gRPC连接创建成功", "address", address)
	return conn, nil
}

// CloseConnection 关闭指定地址的连接
func (cm *ClientManager) CloseConnection(address string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.connections[address]; exists {
		delete(cm.connections, address)
		return conn.Close()
	}

	return nil
}

// CloseAll 关闭所有连接
func (cm *ClientManager) CloseAll() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	var lastErr error
	for address, conn := range cm.connections {
		if err := conn.Close(); err != nil {
			cm.logger.Error("关闭gRPC连接失败", "address", address, "error", err)
			lastErr = err
		}
	}

	// 清空连接映射
	cm.connections = make(map[string]*grpc.ClientConn)

	return lastErr
}

// GetConnectionState 获取连接状态
func (cm *ClientManager) GetConnectionState(address string) connectivity.State {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if conn, exists := cm.connections[address]; exists {
		return conn.GetState()
	}

	return connectivity.Shutdown
}

// ListConnections 列出所有连接及其状态
func (cm *ClientManager) ListConnections() map[string]connectivity.State {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	result := make(map[string]connectivity.State)
	for address, conn := range cm.connections {
		result[address] = conn.GetState()
	}

	return result
}

// HealthCheck 检查连接健康状态
func (cm *ClientManager) HealthCheck(ctx context.Context, address string) error {
	conn, err := cm.GetConnection(address)
	if err != nil {
		return err
	}

	// 等待连接就绪
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	if !conn.WaitForStateChange(ctx, connectivity.Connecting) {
		return fmt.Errorf("连接超时")
	}

	state := conn.GetState()
	if state != connectivity.Ready {
		return fmt.Errorf("连接状态不健康: %v", state)
	}

	return nil
}

// RegisterResolver 注册自定义解析器
func (cm *ClientManager) RegisterResolver(scheme string, builder resolver.Builder) {
	resolver.Register(builder)
	cm.logger.Info("注册gRPC解析器", "scheme", scheme)
}

// SetDialOptions 设置自定义拨号选项
func (cm *ClientManager) SetDialOptions(opts ...grpc.DialOption) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	cm.dialOptions = append(cm.dialOptions, opts...)
}

// GetStats 获取连接统计信息
func (cm *ClientManager) GetStats() map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_connections": len(cm.connections),
		"connections":       make(map[string]interface{}),
	}

	connections := stats["connections"].(map[string]interface{})
	for address, conn := range cm.connections {
		connections[address] = map[string]interface{}{
			"state":  conn.GetState().String(),
			"target": conn.Target(),
		}
	}

	return stats
}
