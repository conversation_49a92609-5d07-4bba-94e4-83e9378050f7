package grpc

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// ProxyHandler 处理HTTP到gRPC的代理请求
type ProxyHandler struct {
	clientManager *ClientManager
	config        config.GRPCGatewayConfig
	logger        *telemetry.Logger
}

// NewProxyHandler 创建新的代理处理器
func NewProxyHandler(clientManager *ClientManager, cfg config.GRPCGatewayConfig, logger *telemetry.Logger) *ProxyHandler {
	return &ProxyHandler{
		clientManager: clientManager,
		config:        cfg,
		logger:        logger.With("component", "grpc-proxy"),
	}
}

// ServeHTTP 处理HTTP请求并转发到gRPC服务
func (ph *ProxyHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// 检查是否启用了gRPC网关
	if !ph.config.Enabled {
		http.Error(w, "gRPC网关未启用", http.StatusServiceUnavailable)
		return
	}

	// 解析请求路径
	servicePath, methodName, err := ph.parseRequestPath(r.URL.Path)
	if err != nil {
		ph.logger.Error("解析请求路径失败", "path", r.URL.Path, "error", err)
		http.Error(w, "无效的请求路径", http.StatusBadRequest)
		return
	}

	// 获取目标服务地址
	targetAddress, err := ph.resolveTargetAddress(servicePath)
	if err != nil {
		ph.logger.Error("解析目标地址失败", "service", servicePath, "error", err)
		http.Error(w, "无法解析目标服务", http.StatusBadGateway)
		return
	}

	// 获取gRPC连接
	conn, err := ph.clientManager.GetConnection(targetAddress)
	if err != nil {
		ph.logger.Error("获取gRPC连接失败", "address", targetAddress, "error", err)
		http.Error(w, "无法连接到目标服务", http.StatusBadGateway)
		return
	}

	// 执行gRPC调用
	err = ph.executeGRPCCall(w, r, conn, servicePath, methodName)
	if err != nil {
		ph.logger.Error("执行gRPC调用失败", "service", servicePath, "method", methodName, "error", err)
		ph.handleGRPCError(w, err)
		return
	}
}

// parseRequestPath 解析HTTP请求路径，提取服务和方法名
func (ph *ProxyHandler) parseRequestPath(path string) (string, string, error) {
	// 移除路径前缀
	if ph.config.PathPrefix != "" {
		if !strings.HasPrefix(path, ph.config.PathPrefix) {
			return "", "", fmt.Errorf("路径不匹配前缀: %s", ph.config.PathPrefix)
		}
		path = strings.TrimPrefix(path, ph.config.PathPrefix)
	}

	// 解析路径格式: /service.name/method
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("无效的路径格式，期望: /service/method")
	}

	return parts[0], parts[1], nil
}

// resolveTargetAddress 解析目标服务地址
func (ph *ProxyHandler) resolveTargetAddress(servicePath string) (string, error) {
	// 这里可以集成服务发现逻辑
	// 目前使用简单的映射
	
	// 从服务名推导地址（这里需要根据实际的服务发现机制实现）
	// 例如: user.service -> user-service:9090
	serviceName := strings.Replace(servicePath, ".", "-", -1)
	return fmt.Sprintf("%s:9090", serviceName), nil
}

// executeGRPCCall 执行gRPC调用
func (ph *ProxyHandler) executeGRPCCall(w http.ResponseWriter, r *http.Request, conn *grpc.ClientConn, servicePath, methodName string) error {
	// 创建gRPC上下文
	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	// 转换HTTP头部到gRPC元数据
	md := ph.convertHTTPHeadersToMetadata(r.Header)
	ctx = metadata.NewOutgoingContext(ctx, md)

	// 构建完整的方法名
	fullMethodName := fmt.Sprintf("/%s/%s", servicePath, methodName)

	// 读取请求体
	var requestData []byte
	if r.Body != nil {
		var err error
		requestData, err = io.ReadAll(r.Body)
		if err != nil {
			return fmt.Errorf("读取请求体失败: %w", err)
		}
	}

	// 执行一元RPC调用
	return ph.executeUnaryCall(ctx, w, conn, fullMethodName, requestData)
}

// executeUnaryCall 执行一元RPC调用
func (ph *ProxyHandler) executeUnaryCall(ctx context.Context, w http.ResponseWriter, conn *grpc.ClientConn, methodName string, requestData []byte) error {
	// 创建动态调用
	var request proto.Message
	var response proto.Message

	// 这里需要根据实际的protobuf定义来创建消息
	// 为了简化，我们使用动态消息处理
	
	// 执行gRPC调用
	err := conn.Invoke(ctx, methodName, request, response)
	if err != nil {
		return err
	}

	// 将响应转换为JSON
	responseJSON, err := protojson.Marshal(response)
	if err != nil {
		return fmt.Errorf("序列化响应失败: %w", err)
	}

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	
	// 写入响应
	_, err = w.Write(responseJSON)
	return err
}

// convertHTTPHeadersToMetadata 将HTTP头部转换为gRPC元数据
func (ph *ProxyHandler) convertHTTPHeadersToMetadata(headers http.Header) metadata.MD {
	md := metadata.New(nil)

	for key, values := range headers {
		// 跳过某些HTTP特定的头部
		if ph.shouldSkipHeader(key) {
			continue
		}

		// 应用元数据映射
		if mappedKey, exists := ph.config.MetadataMapping[key]; exists {
			key = mappedKey
		}

		// 转换为小写（gRPC元数据键必须是小写）
		key = strings.ToLower(key)
		
		for _, value := range values {
			md.Append(key, value)
		}
	}

	return md
}

// shouldSkipHeader 检查是否应该跳过某个HTTP头部
func (ph *ProxyHandler) shouldSkipHeader(header string) bool {
	skipHeaders := []string{
		"content-length",
		"content-encoding",
		"transfer-encoding",
		"connection",
		"upgrade",
		"host",
	}

	header = strings.ToLower(header)
	for _, skip := range skipHeaders {
		if header == skip {
			return true
		}
	}

	return false
}

// handleGRPCError 处理gRPC错误并转换为HTTP响应
func (ph *ProxyHandler) handleGRPCError(w http.ResponseWriter, err error) {
	// 获取gRPC状态
	st, ok := status.FromError(err)
	if !ok {
		// 不是gRPC错误，返回通用错误
		http.Error(w, "内部服务器错误", http.StatusInternalServerError)
		return
	}

	// 将gRPC状态码转换为HTTP状态码
	httpStatus := ph.grpcCodeToHTTPStatus(st.Code())
	
	// 构建错误响应
	errorResponse := map[string]interface{}{
		"error": map[string]interface{}{
			"code":    int(st.Code()),
			"message": st.Message(),
			"details": st.Details(),
		},
	}

	// 序列化错误响应
	responseJSON, err := json.Marshal(errorResponse)
	if err != nil {
		http.Error(w, "序列化错误响应失败", http.StatusInternalServerError)
		return
	}

	// 设置响应头并写入错误
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(httpStatus)
	w.Write(responseJSON)
}

// grpcCodeToHTTPStatus 将gRPC状态码转换为HTTP状态码
func (ph *ProxyHandler) grpcCodeToHTTPStatus(code codes.Code) int {
	switch code {
	case codes.OK:
		return http.StatusOK
	case codes.Canceled:
		return http.StatusRequestTimeout
	case codes.Unknown:
		return http.StatusInternalServerError
	case codes.InvalidArgument:
		return http.StatusBadRequest
	case codes.DeadlineExceeded:
		return http.StatusGatewayTimeout
	case codes.NotFound:
		return http.StatusNotFound
	case codes.AlreadyExists:
		return http.StatusConflict
	case codes.PermissionDenied:
		return http.StatusForbidden
	case codes.ResourceExhausted:
		return http.StatusTooManyRequests
	case codes.FailedPrecondition:
		return http.StatusBadRequest
	case codes.Aborted:
		return http.StatusConflict
	case codes.OutOfRange:
		return http.StatusBadRequest
	case codes.Unimplemented:
		return http.StatusNotImplemented
	case codes.Internal:
		return http.StatusInternalServerError
	case codes.Unavailable:
		return http.StatusServiceUnavailable
	case codes.DataLoss:
		return http.StatusInternalServerError
	case codes.Unauthenticated:
		return http.StatusUnauthorized
	default:
		return http.StatusInternalServerError
	}
}

// createDynamicMessage 创建动态protobuf消息
func (ph *ProxyHandler) createDynamicMessage(messageType protoreflect.MessageType) proto.Message {
	return dynamicpb.NewMessage(messageType.Descriptor())
}

// parseJSONToProto 将JSON数据解析为protobuf消息
func (ph *ProxyHandler) parseJSONToProto(jsonData []byte, message proto.Message) error {
	return protojson.Unmarshal(jsonData, message)
}
