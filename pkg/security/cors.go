package security

import (
	"strings"
	"sync"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// CORSResult represents the result of CORS evaluation
type CORSResult struct {
	Allowed         bool              `json:"allowed"`
	Headers         map[string]string `json:"headers"`
	PreflightResult bool              `json:"preflight_result"`
}

// CORSHandler interface defines CORS handling methods
type CORSHandler interface {
	Handle(origin, method string, headers []string) (*CORSResult, error)
	GetStats() map[string]interface{}
}

// BasicCORSHandler implements basic CORS handling
type BasicCORSHandler struct {
	config config.CORSConfig
	logger *telemetry.Logger
	
	// Statistics
	stats struct {
		totalRequests      int64
		allowedRequests    int64
		blockedRequests    int64
		preflightRequests  int64
		mu                 sync.RWMutex
	}
}

// NewCORSHandler creates a new CORS handler
func NewCORSHandler(cfg config.CORSConfig, logger *telemetry.Logger) (CORSHand<PERSON>, error) {
	handler := &BasicCORSHandler{
		config: cfg,
		logger: logger,
	}

	return handler, nil
}

// <PERSON><PERSON> handles CORS requests
func (c *BasicCORSHandler) Handle(origin, method string, headers []string) (*CORSResult, error) {
	// Update statistics
	c.stats.mu.Lock()
	c.stats.totalRequests++
	c.stats.mu.Unlock()

	result := &CORSResult{
		Headers: make(map[string]string),
	}

	// Check if origin is allowed
	if !c.isOriginAllowed(origin) {
		if c.logger != nil {
			c.logger.Warn("CORS origin not allowed", "origin", origin)
		}
		c.stats.mu.Lock()
		c.stats.blockedRequests++
		c.stats.mu.Unlock()

		result.Allowed = false
		return result, nil
	}

	// Set basic CORS headers
	result.Headers["Access-Control-Allow-Origin"] = c.getAllowedOrigin(origin)
	
	if c.config.AllowCredentials {
		result.Headers["Access-Control-Allow-Credentials"] = "true"
	}

	// Handle preflight request
	if method == "OPTIONS" {
		c.stats.mu.Lock()
		c.stats.preflightRequests++
		c.stats.mu.Unlock()
		
		result.PreflightResult = true
		c.handlePreflight(result, headers)
	}

	// Set exposed headers
	if len(c.config.ExposedHeaders) > 0 {
		result.Headers["Access-Control-Expose-Headers"] = strings.Join(c.config.ExposedHeaders, ", ")
	}

	// Set max age for preflight cache
	if c.config.MaxAge > 0 {
		result.Headers["Access-Control-Max-Age"] = string(rune(c.config.MaxAge))
	}

	result.Allowed = true
	c.stats.mu.Lock()
	c.stats.allowedRequests++
	c.stats.mu.Unlock()

	if c.logger != nil {
		c.logger.Debug("CORS request allowed", "origin", origin, "method", method)
	}
	return result, nil
}

// isOriginAllowed checks if an origin is allowed
func (c *BasicCORSHandler) isOriginAllowed(origin string) bool {
	if origin == "" {
		return true // Allow requests without origin (same-origin)
	}

	// Check for wildcard
	for _, allowedOrigin := range c.config.AllowedOrigins {
		if allowedOrigin == "*" {
			return true
		}
		
		// Exact match
		if allowedOrigin == origin {
			return true
		}
		
		// Wildcard subdomain match (e.g., *.example.com)
		if strings.HasPrefix(allowedOrigin, "*.") {
			domain := strings.TrimPrefix(allowedOrigin, "*.")
			if strings.HasSuffix(origin, "."+domain) || origin == domain {
				return true
			}
		}
	}

	return false
}

// getAllowedOrigin returns the appropriate origin header value
func (c *BasicCORSHandler) getAllowedOrigin(origin string) string {
	// If credentials are allowed, we can't use wildcard
	if c.config.AllowCredentials && origin != "" {
		return origin
	}

	// Check if wildcard is in allowed origins
	for _, allowedOrigin := range c.config.AllowedOrigins {
		if allowedOrigin == "*" {
			return "*"
		}
	}

	return origin
}

// handlePreflight handles CORS preflight requests
func (c *BasicCORSHandler) handlePreflight(result *CORSResult, requestedHeaders []string) {
	// Set allowed methods
	if len(c.config.AllowedMethods) > 0 {
		result.Headers["Access-Control-Allow-Methods"] = strings.Join(c.config.AllowedMethods, ", ")
	}

	// Set allowed headers
	allowedHeaders := c.getAllowedHeaders(requestedHeaders)
	if len(allowedHeaders) > 0 {
		result.Headers["Access-Control-Allow-Headers"] = strings.Join(allowedHeaders, ", ")
	}
}

// getAllowedHeaders returns the headers that are allowed
func (c *BasicCORSHandler) getAllowedHeaders(requestedHeaders []string) []string {
	if len(c.config.AllowedHeaders) == 0 {
		return requestedHeaders // Allow all requested headers if none specified
	}

	var allowedHeaders []string
	
	// Check for wildcard
	for _, allowedHeader := range c.config.AllowedHeaders {
		if allowedHeader == "*" {
			return requestedHeaders // Allow all requested headers
		}
	}

	// Check each requested header
	for _, requestedHeader := range requestedHeaders {
		if c.isHeaderAllowed(requestedHeader) {
			allowedHeaders = append(allowedHeaders, requestedHeader)
		}
	}

	return allowedHeaders
}

// isHeaderAllowed checks if a header is allowed
func (c *BasicCORSHandler) isHeaderAllowed(header string) bool {
	headerLower := strings.ToLower(header)
	
	for _, allowedHeader := range c.config.AllowedHeaders {
		if strings.ToLower(allowedHeader) == headerLower {
			return true
		}
	}

	// Always allow simple headers
	simpleHeaders := []string{
		"accept",
		"accept-language",
		"content-language",
		"content-type",
	}

	for _, simpleHeader := range simpleHeaders {
		if headerLower == simpleHeader {
			return true
		}
	}

	return false
}

// IsMethodAllowed checks if a method is allowed
func (c *BasicCORSHandler) IsMethodAllowed(method string) bool {
	if len(c.config.AllowedMethods) == 0 {
		return true // Allow all methods if none specified
	}

	methodUpper := strings.ToUpper(method)
	
	for _, allowedMethod := range c.config.AllowedMethods {
		if strings.ToUpper(allowedMethod) == methodUpper {
			return true
		}
	}

	return false
}

// GetStats returns CORS statistics
func (c *BasicCORSHandler) GetStats() map[string]interface{} {
	c.stats.mu.RLock()
	defer c.stats.mu.RUnlock()

	return map[string]interface{}{
		"total_requests":     c.stats.totalRequests,
		"allowed_requests":   c.stats.allowedRequests,
		"blocked_requests":   c.stats.blockedRequests,
		"preflight_requests": c.stats.preflightRequests,
	}
}

// UpdateConfig updates the CORS configuration
func (c *BasicCORSHandler) UpdateConfig(cfg config.CORSConfig) {
	c.config = cfg
	if c.logger != nil {
		c.logger.Info("Updated CORS configuration")
	}
}

// GetConfig returns the current CORS configuration
func (c *BasicCORSHandler) GetConfig() config.CORSConfig {
	return c.config
}

// ValidateOrigin validates an origin URL
func ValidateOrigin(origin string) bool {
	if origin == "" {
		return true
	}

	// Basic validation - should start with http:// or https://
	return strings.HasPrefix(origin, "http://") || strings.HasPrefix(origin, "https://")
}

// NormalizeOrigin normalizes an origin URL
func NormalizeOrigin(origin string) string {
	if origin == "" {
		return origin
	}

	// Remove trailing slash
	return strings.TrimSuffix(origin, "/")
}

// GetDefaultCORSConfig returns a default CORS configuration
func GetDefaultCORSConfig() config.CORSConfig {
	return config.CORSConfig{
		Enabled:          true,
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"},
		AllowedHeaders:   []string{"*"},
		ExposedHeaders:   []string{"Content-Length", "Content-Type"},
		AllowCredentials: false,
		MaxAge:           86400, // 24 hours
	}
}
