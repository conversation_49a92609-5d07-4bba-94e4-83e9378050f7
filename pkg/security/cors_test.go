package security

import (
	"testing"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// TestCORSHandlerWithNilLogger 测试当 logger 为 nil 时 CORS 处理器不会 panic
func TestCORSHandlerWithNilLogger(t *testing.T) {
	// 创建 CORS 配置
	cfg := config.CORSConfig{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Content-Type", "Authorization"},
		ExposedHeaders:   []string{"X-Request-ID"},
		AllowCredentials: true,
		MaxAge:           3600,
	}

	// 使用 nil logger 创建 CORS 处理器
	handler, err := NewCORSHandler(cfg, nil)
	if err != nil {
		t.Fatalf("创建 CORS 处理器失败: %v", err)
	}

	// 测试处理请求不会 panic
	t.Run("处理允许的 origin", func(t *testing.T) {
		result, err := handler.Handle("http://example.com", "GET", []string{"Content-Type"})
		if err != nil {
			t.Fatalf("处理 CORS 请求失败: %v", err)
		}
		if !result.Allowed {
			t.Error("期望请求被允许")
		}
	})

	t.Run("处理不允许的 origin", func(t *testing.T) {
		// 更新配置为只允许特定 origin
		restrictiveCfg := config.CORSConfig{
			AllowedOrigins: []string{"http://allowed.com"},
			AllowedMethods: []string{"GET"},
		}
		restrictiveHandler, err := NewCORSHandler(restrictiveCfg, nil)
		if err != nil {
			t.Fatalf("创建限制性 CORS 处理器失败: %v", err)
		}

		result, err := restrictiveHandler.Handle("http://notallowed.com", "GET", []string{})
		if err != nil {
			t.Fatalf("处理 CORS 请求失败: %v", err)
		}
		if result.Allowed {
			t.Error("期望请求被拒绝")
		}
	})

	t.Run("处理 OPTIONS 预检请求", func(t *testing.T) {
		result, err := handler.Handle("http://example.com", "OPTIONS", []string{"Content-Type", "Authorization"})
		if err != nil {
			t.Fatalf("处理预检请求失败: %v", err)
		}
		if !result.Allowed {
			t.Error("期望预检请求被允许")
		}
		if !result.PreflightResult {
			t.Error("期望预检结果为 true")
		}
	})

	t.Run("更新配置", func(t *testing.T) {
		// 测试更新配置不会 panic
		newCfg := config.CORSConfig{
			AllowedOrigins: []string{"http://neworigin.com"},
		}
		handler.(*BasicCORSHandler).UpdateConfig(newCfg)
		
		// 验证配置已更新
		updatedCfg := handler.(*BasicCORSHandler).GetConfig()
		if len(updatedCfg.AllowedOrigins) != 1 || updatedCfg.AllowedOrigins[0] != "http://neworigin.com" {
			t.Error("配置更新失败")
		}
	})
}

// TestCORSHandlerWithLogger 测试当 logger 不为 nil 时的正常功能
func TestCORSHandlerWithLogger(t *testing.T) {
	// 创建 logger
	logger, err := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "json",
		Output: "stdout",
	})
	if err != nil {
		t.Fatalf("创建 logger 失败: %v", err)
	}

	// 创建 CORS 配置
	cfg := config.CORSConfig{
		AllowedOrigins:   []string{"http://example.com"},
		AllowedMethods:   []string{"GET", "POST"},
		AllowedHeaders:   []string{"Content-Type"},
		AllowCredentials: false,
		MaxAge:           1800,
	}

	// 使用 logger 创建 CORS 处理器
	handler, err := NewCORSHandler(cfg, logger)
	if err != nil {
		t.Fatalf("创建 CORS 处理器失败: %v", err)
	}

	// 测试处理请求
	t.Run("处理允许的请求", func(t *testing.T) {
		result, err := handler.Handle("http://example.com", "GET", []string{})
		if err != nil {
			t.Fatalf("处理 CORS 请求失败: %v", err)
		}
		if !result.Allowed {
			t.Error("期望请求被允许")
		}
		
		// 检查响应头
		if result.Headers["Access-Control-Allow-Origin"] != "http://example.com" {
			t.Error("Access-Control-Allow-Origin 头设置错误")
		}
	})

	t.Run("获取统计信息", func(t *testing.T) {
		stats := handler.GetStats()
		if stats["total_requests"] == nil {
			t.Error("期望统计信息包含 total_requests")
		}
	})
}

// TestCORSOriginValidation 测试 origin 验证功能
func TestCORSOriginValidation(t *testing.T) {
	tests := []struct {
		name           string
		allowedOrigins []string
		testOrigin     string
		expected       bool
	}{
		{
			name:           "通配符允许所有",
			allowedOrigins: []string{"*"},
			testOrigin:     "http://any.com",
			expected:       true,
		},
		{
			name:           "精确匹配",
			allowedOrigins: []string{"http://example.com"},
			testOrigin:     "http://example.com",
			expected:       true,
		},
		{
			name:           "不匹配",
			allowedOrigins: []string{"http://example.com"},
			testOrigin:     "http://other.com",
			expected:       false,
		},
		{
			name:           "子域名通配符",
			allowedOrigins: []string{"*.example.com"},
			testOrigin:     "http://sub.example.com",
			expected:       true,
		},
		{
			name:           "空 origin",
			allowedOrigins: []string{"http://example.com"},
			testOrigin:     "",
			expected:       true, // 同源请求
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := config.CORSConfig{
				AllowedOrigins: tt.allowedOrigins,
				AllowedMethods: []string{"GET"},
			}

			handler, err := NewCORSHandler(cfg, nil)
			if err != nil {
				t.Fatalf("创建 CORS 处理器失败: %v", err)
			}

			result, err := handler.Handle(tt.testOrigin, "GET", []string{})
			if err != nil {
				t.Fatalf("处理 CORS 请求失败: %v", err)
			}

			if result.Allowed != tt.expected {
				t.Errorf("期望 %v，得到 %v", tt.expected, result.Allowed)
			}
		})
	}
}
