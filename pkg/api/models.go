package api

// HealthResponse 健康检查响应
// swagger:model HealthResponse
type HealthResponse struct {
	// 服务状态
	// example: healthy
	Status string `json:"status" example:"healthy"`
	
	// 服务版本
	// example: 1.0.0
	Version string `json:"version" example:"1.0.0"`
	
	// 运行时间
	// example: 2小时30分钟15秒
	Uptime string `json:"uptime" example:"2小时30分钟15秒"`
	
	// 时间戳
	// example: 2025-08-17T07:30:14.646Z
	Timestamp string `json:"timestamp" example:"2025-08-17T07:30:14.646Z"`
	
	// 组件状态
	Components map[string]ComponentStatus `json:"components"`
}

// ComponentStatus 组件状态
// swagger:model ComponentStatus
type ComponentStatus struct {
	// 组件状态
	// example: healthy
	Status string `json:"status" example:"healthy"`
	
	// 状态描述
	// example: 认证管理器运行正常
	Description string `json:"description" example:"认证管理器运行正常"`
}

// ErrorResponse 错误响应
// swagger:model ErrorResponse
type ErrorResponse struct {
	// 错误代码
	// example: INVALID_REQUEST
	Code string `json:"code" example:"INVALID_REQUEST"`
	
	// 错误消息
	// example: 请求参数无效
	Message string `json:"message" example:"请求参数无效"`
	
	// 详细信息
	Details interface{} `json:"details,omitempty"`
	
	// 时间戳
	// example: 2025-08-17T07:30:14.646Z
	Timestamp string `json:"timestamp" example:"2025-08-17T07:30:14.646Z"`
}

// ConfigReloadRequest 配置重载请求
// swagger:model ConfigReloadRequest
type ConfigReloadRequest struct {
	// 配置文件路径
	// example: configs/gateway.yaml
	ConfigFile string `json:"config_file,omitempty" example:"configs/gateway.yaml"`
}

// ConfigReloadResponse 配置重载响应
// swagger:model ConfigReloadResponse
type ConfigReloadResponse struct {
	// 响应消息
	// example: 配置重载成功
	Message string `json:"message" example:"配置重载成功"`
	
	// 配置文件路径
	// example: configs/gateway.yaml
	ConfigFile string `json:"config_file" example:"configs/gateway.yaml"`
	
	// 时间戳
	// example: 2025-08-17T07:30:14.646Z
	Timestamp string `json:"timestamp" example:"2025-08-17T07:30:14.646Z"`
}

// RouteInfo 路由信息
// swagger:model RouteInfo
type RouteInfo struct {
	// 路由名称
	// example: protected-api
	Name string `json:"name" example:"protected-api"`
	
	// 路径模式
	// example: /api/v1/protected/*
	Path string `json:"path" example:"/api/v1/protected/*"`
	
	// HTTP方法
	// example: GET
	Method string `json:"method" example:"GET"`
	
	// 上游配置
	Upstream UpstreamInfo `json:"upstream"`
	
	// 认证配置
	Auth *AuthInfo `json:"auth,omitempty"`
	
	// 超时时间(秒)
	// example: 30
	Timeout int `json:"timeout" example:"30"`
	
	// 重试次数
	// example: 3
	Retries int `json:"retries" example:"3"`
}

// UpstreamInfo 上游服务信息
// swagger:model UpstreamInfo
type UpstreamInfo struct {
	// 类型
	// example: static
	Type string `json:"type" example:"static"`
	
	// 负载均衡算法
	// example: round_robin
	LoadBalancer string `json:"load_balancer" example:"round_robin"`
	
	// 服务器列表
	Servers []ServerInfo `json:"servers"`
}

// ServerInfo 服务器信息
// swagger:model ServerInfo
type ServerInfo struct {
	// 主机地址
	// example: backend1.example.com
	Host string `json:"host" example:"backend1.example.com"`
	
	// 端口
	// example: 8081
	Port int `json:"port" example:"8081"`
	
	// 权重
	// example: 100
	Weight int `json:"weight,omitempty" example:"100"`
	
	// 健康状态
	// example: healthy
	Status string `json:"status,omitempty" example:"healthy"`
}

// AuthInfo 认证信息
// swagger:model AuthInfo
type AuthInfo struct {
	// 是否需要认证
	// example: true
	Required bool `json:"required" example:"true"`
	
	// 支持的认证方法
	// example: ["jwt", "api_key"]
	Methods []string `json:"methods" example:"jwt,api_key"`
	
	// 需要的角色
	// example: ["user", "admin"]
	Roles []string `json:"roles,omitempty" example:"user,admin"`
}

// MetricsResponse 指标响应
// swagger:model MetricsResponse
type MetricsResponse struct {
	// 指标数据(Prometheus格式)
	Data string `json:"data"`
	
	// 内容类型
	// example: text/plain
	ContentType string `json:"content_type" example:"text/plain"`
}

// OIDCAuthRequest OIDC认证请求
// swagger:model OIDCAuthRequest
type OIDCAuthRequest struct {
	// 状态参数(CSRF保护)
	// example: random-state-string
	State string `json:"state,omitempty" example:"random-state-string"`
}

// OIDCAuthResponse OIDC认证响应
// swagger:model OIDCAuthResponse
type OIDCAuthResponse struct {
	// 授权URL
	// example: https://accounts.google.com/oauth/authorize?client_id=...
	AuthorizationURL string `json:"authorization_url" example:"https://accounts.google.com/oauth/authorize?client_id=..."`
}

// OIDCCallbackRequest OIDC回调请求
// swagger:model OIDCCallbackRequest
type OIDCCallbackRequest struct {
	// 授权码
	// example: auth-code-from-provider
	Code string `json:"code" form:"code" example:"auth-code-from-provider"`
	
	// 状态参数
	// example: random-state-string
	State string `json:"state" form:"state" example:"random-state-string"`
}

// OIDCTokenResponse OIDC Token响应
// swagger:model OIDCTokenResponse
type OIDCTokenResponse struct {
	// 访问令牌
	// example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
	AccessToken string `json:"access_token" example:"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."`
	
	// ID令牌
	// example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
	IDToken string `json:"id_token" example:"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."`
	
	// 刷新令牌
	// example: refresh-token-string
	RefreshToken string `json:"refresh_token,omitempty" example:"refresh-token-string"`
	
	// 令牌类型
	// example: Bearer
	TokenType string `json:"token_type" example:"Bearer"`
	
	// 过期时间(秒)
	// example: 3600
	ExpiresIn int `json:"expires_in" example:"3600"`
}

// RefreshTokenRequest 刷新令牌请求
// swagger:model RefreshTokenRequest
type RefreshTokenRequest struct {
	// 刷新令牌
	// example: refresh-token-string
	RefreshToken string `json:"refresh_token" example:"refresh-token-string"`
}

// RevokeTokenRequest 撤销令牌请求
// swagger:model RevokeTokenRequest
type RevokeTokenRequest struct {
	// 要撤销的令牌
	// example: token-to-revoke
	Token string `json:"token" example:"token-to-revoke"`
}

// SuccessResponse 成功响应
// swagger:model SuccessResponse
type SuccessResponse struct {
	// 响应消息
	// example: 操作成功
	Message string `json:"message" example:"操作成功"`
	
	// 时间戳
	// example: 2025-08-17T07:30:14.646Z
	Timestamp string `json:"timestamp" example:"2025-08-17T07:30:14.646Z"`
}
