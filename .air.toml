# Air 热重载配置文件
# 用于 API Gateway 开发环境的自动重载

# 工作目录
root = "."

# 测试目录
testdata_dir = "testdata"

# 临时目录
tmp_dir = "tmp"

[build]
  # 要监听的命令，这里是构建 API Gateway 的命令
  cmd = "go build -o ./tmp/main cmd/gateway/main.go"
  
  # 构建后执行的二进制文件
  bin = "tmp/main"
  
  # 构建时的参数，包含版本信息和构建时间
  args_bin = ["--config", "configs/gateway.yaml"]
  
  # 要监听的文件扩展名
  include_ext = ["go", "tpl", "tmpl", "html", "yaml", "yml", "json"]
  
  # 要排除的文件扩展名
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "build", "logs", ".git", ".idea", ".vscode"]
  
  # 要包含的目录
  include_dir = []
  
  # 要排除的文件
  exclude_file = []
  
  # 要排除的正则表达式
  exclude_regex = ["_test.go"]
  
  # 排除不变的文件
  exclude_unchanged = false
  
  # 在执行命令之前是否跟随符号链接
  follow_symlink = false
  
  # 完整构建标志
  full_bin = ""
  
  # 监听这些文件名的变化
  include_file = []
  
  # 杀死延迟（毫秒）
  kill_delay = "0s"
  
  # 日志
  log = "build-errors.log"
  
  # 发送中断信号前的延迟时间
  send_interrupt = false
  
  # 停止运行旧的二进制文件的延迟时间
  stop_on_root = false

[color]
  # 自定义每个部分显示的颜色
  main = "magenta"
  watcher = "cyan"
  build = "yellow"
  runner = "green"

[log]
  # 显示日志时间
  time = false

[misc]
  # 删除每次构建时的临时目录
  clean_on_exit = false

[screen]
  # 清屏
  clear_on_rebuild = false
  
  # 保持滚动
  keep_scroll = true
