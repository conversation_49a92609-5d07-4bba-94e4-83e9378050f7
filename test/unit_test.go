package test

import (
	"context"
	"testing"
	"time"

	"api-gateway/pkg/config"
)

// testLogger 实现 config.Logger 接口用于测试
type testLogger struct{}

func (l *testLogger) Info(msg string, fields ...interface{})  {}
func (l *testLogger) Error(msg string, fields ...interface{}) {}
func (l *testLogger) Warn(msg string, fields ...interface{})  {}
func (l *testLogger) Debug(msg string, fields ...interface{}) {}
func (l *testLogger) With(key string, value interface{}) config.Logger {
	return l
}

// TestMemoryStorage 测试内存存储
func TestMemoryStorage(t *testing.T) {
	storage := config.NewMemoryStorage()
	
	// 测试存储和获取
	t.Run("存储和获取配置", func(t *testing.T) {
		testConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
		}
		
		err := storage.Store("test-key", testConfig)
		if err != nil {
			t.Fatalf("存储配置失败: %v", err)
		}
		
		retrievedConfig, err := storage.Get("test-key")
		if err != nil {
			t.Fatalf("获取配置失败: %v", err)
		}
		
		if retrievedConfig == nil {
			t.Fatal("获取的配置为空")
		}
		
		// 验证配置内容
		if retrievedConfig.Server.Host != "localhost" {
			t.Errorf("期望主机 'localhost', 得到 '%s'", retrievedConfig.Server.Host)
		}
		
		if retrievedConfig.Server.Port != 8080 {
			t.Errorf("期望端口 8080, 得到 %d", retrievedConfig.Server.Port)
		}
	})
	
	// 测试不存在的键
	t.Run("获取不存在的配置", func(t *testing.T) {
		_, err := storage.Get("non-existent-key")
		if err == nil {
			t.Error("期望获取不存在的键时返回错误")
		}
	})
	
	// 测试删除
	t.Run("删除配置", func(t *testing.T) {
		testConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "test",
				Port: 9000,
			},
		}
		
		err := storage.Store("delete-test", testConfig)
		if err != nil {
			t.Fatalf("存储配置失败: %v", err)
		}
		
		err = storage.Delete("delete-test")
		if err != nil {
			t.Fatalf("删除配置失败: %v", err)
		}
		
		_, err = storage.Get("delete-test")
		if err == nil {
			t.Error("期望删除后获取配置时返回错误")
		}
	})
	
	// 测试列出所有键
	t.Run("列出所有键", func(t *testing.T) {
		// 清理存储
		storage.Close()
		storage = config.NewMemoryStorage()
		
		// 添加一些测试数据
		testConfigs := map[string]*config.Config{
			"key1": {Server: config.ServerConfig{Host: "host1", Port: 8001}},
			"key2": {Server: config.ServerConfig{Host: "host2", Port: 8002}},
			"key3": {Server: config.ServerConfig{Host: "host3", Port: 8003}},
		}
		
		for key, cfg := range testConfigs {
			err := storage.Store(key, cfg)
			if err != nil {
				t.Fatalf("存储配置 %s 失败: %v", key, err)
			}
		}
		
		keys, err := storage.List()
		if err != nil {
			t.Fatalf("列出键失败: %v", err)
		}
		
		if len(keys) != 3 {
			t.Errorf("期望 3 个键, 得到 %d", len(keys))
		}
		
		// 验证所有键都存在
		keyMap := make(map[string]bool)
		for _, key := range keys {
			keyMap[key] = true
		}
		
		for expectedKey := range testConfigs {
			if !keyMap[expectedKey] {
				t.Errorf("期望的键 '%s' 未找到", expectedKey)
			}
		}
	})
}

// TestConfigValidator 测试配置验证器
func TestConfigValidator(t *testing.T) {
	validator := config.NewConfigValidator()
	
	// 测试有效配置
	t.Run("验证有效配置", func(t *testing.T) {
		validConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
			Routes: []config.Route{
				{
					ID:      "valid-route",
					Path:    "/api/v1/test",
					Method:  "GET",
					Backend: "http://backend:8080",
					Timeout: 30 * time.Second,
					Retries: 3,
					Enabled: true,
				},
			},
			Auth: config.AuthConfig{
				Enabled: true,
				JWT: config.JWTConfig{
					Secret:     "test-secret",
					Expiration: 24 * time.Hour,
				},
			},
		}
		
		err := validator.Validate(validConfig)
		if err != nil {
			t.Errorf("有效配置验证失败: %v", err)
		}
	})
	
	// 测试无效配置 - 空路由ID
	t.Run("验证无效配置 - 空路由ID", func(t *testing.T) {
		invalidConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
			Routes: []config.Route{
				{
					ID:      "", // 空ID
					Path:    "/api/v1/test",
					Method:  "GET",
					Backend: "http://backend:8080",
					Timeout: 30 * time.Second,
					Retries: 3,
					Enabled: true,
				},
			},
		}
		
		err := validator.Validate(invalidConfig)
		if err == nil {
			t.Error("期望无效配置验证失败")
		}
	})
	
	// 测试无效配置 - 无效端口
	t.Run("验证无效配置 - 无效端口", func(t *testing.T) {
		invalidConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "localhost",
				Port: -1, // 无效端口
			},
		}
		
		err := validator.Validate(invalidConfig)
		if err == nil {
			t.Error("期望无效端口配置验证失败")
		}
	})
	
	// 测试无效配置 - 无效后端URL
	t.Run("验证无效配置 - 无效后端URL", func(t *testing.T) {
		invalidConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
			Routes: []config.Route{
				{
					ID:      "invalid-backend-route",
					Path:    "/api/v1/test",
					Method:  "GET",
					Backend: "invalid-url", // 无效URL
					Timeout: 30 * time.Second,
					Retries: 3,
					Enabled: true,
				},
			},
		}
		
		err := validator.Validate(invalidConfig)
		if err == nil {
			t.Error("期望无效后端URL配置验证失败")
		}
	})
}

// TestConfigChangeListener 测试配置变更监听器
func TestConfigChangeListener(t *testing.T) {
	// 创建测试监听器
	changeReceived := false
	var receivedChange config.ConfigChange
	
	listener := config.NewRouteConfigListener(
		"test-listener",
		&testLogger{}, // 使用测试日志器
		func(ctx context.Context, change config.ConfigChange) error {
			changeReceived = true
			receivedChange = change
			return nil
		},
	)
	
	// 测试监听器基本信息
	t.Run("监听器基本信息", func(t *testing.T) {
		if listener.GetID() != "test-listener" {
			t.Errorf("期望监听器ID 'test-listener', 得到 '%s'", listener.GetID())
		}
		
		if listener.GetType() != "routes" {
			t.Errorf("期望监听器类型 'routes', 得到 '%s'", listener.GetType())
		}
	})
	
	// 测试配置变更通知
	t.Run("配置变更通知", func(t *testing.T) {
		change := config.ConfigChange{
			Type:      config.ConfigChangeTypeUpdate,
			Key:       "test-route",
			OldValue:  nil,
			NewValue:  &config.Route{ID: "test-route", Path: "/test"},
			Timestamp: time.Now(),
		}
		
		err := listener.OnChange(context.Background(), change)
		if err != nil {
			t.Fatalf("配置变更通知失败: %v", err)
		}
		
		if !changeReceived {
			t.Error("期望接收到配置变更通知")
		}
		
		if receivedChange.Type != config.ConfigChangeTypeUpdate {
			t.Errorf("期望变更类型 'update', 得到 '%s'", receivedChange.Type)
		}
		
		if receivedChange.Key != "test-route" {
			t.Errorf("期望变更键 'test-route', 得到 '%s'", receivedChange.Key)
		}
	})
}

// TestConfigVersion 测试配置版本管理
func TestConfigVersion(t *testing.T) {
	// 创建配置版本
	version := config.ConfigVersion{
		Version:   "v1.0.0",
		Timestamp: time.Now(),
		Author:    "test-user",
		Comment:   "测试版本",
		Config: &config.Config{
			Server: config.ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
		},
	}
	
	// 测试版本信息
	t.Run("版本信息", func(t *testing.T) {
		if version.Version != "v1.0.0" {
			t.Errorf("期望版本 'v1.0.0', 得到 '%s'", version.Version)
		}
		
		if version.Author != "test-user" {
			t.Errorf("期望作者 'test-user', 得到 '%s'", version.Author)
		}
		
		if version.Comment != "测试版本" {
			t.Errorf("期望注释 '测试版本', 得到 '%s'", version.Comment)
		}
		
		if version.Config == nil {
			t.Error("配置不应为空")
		}
	})
	
	// 测试版本比较
	t.Run("版本比较", func(t *testing.T) {
		version1 := config.ConfigVersion{
			Version:   "v1.0.0",
			Timestamp: time.Now().Add(-time.Hour),
		}
		
		version2 := config.ConfigVersion{
			Version:   "v1.0.1",
			Timestamp: time.Now(),
		}
		
		if !version2.Timestamp.After(version1.Timestamp) {
			t.Error("期望版本2的时间戳晚于版本1")
		}
	})
}

// TestDynamicConfigManager 测试动态配置管理器
func TestDynamicConfigManager(t *testing.T) {
	// 创建内存存储
	storage := config.NewMemoryStorage()

	// 创建配置管理器
	cfg := createTestConfig()
	manager := config.NewDynamicConfigManager(cfg, storage, &testLogger{})

	// 启动管理器
	ctx := context.Background()
	err := manager.Start(ctx)
	if err != nil {
		t.Fatalf("启动配置管理器失败: %v", err)
	}
	defer manager.Stop()

	// 测试配置更新
	t.Run("配置更新", func(t *testing.T) {
		// 更新路由配置
		newRoute := config.Route{
			ID:      "new-test-route",
			Path:    "/new-test",
			Method:  "GET",
			Backend: "http://new-backend:8080",
			Timeout: 30 * time.Second,
			Retries: 3,
			Enabled: true,
		}

		err := manager.UpdateRouteConfig("new-test-route", newRoute)
		if err != nil {
			t.Fatalf("更新路由配置失败: %v", err)
		}

		// 验证配置是否更新
		updatedConfig := manager.GetConfig()
		found := false
		for _, route := range updatedConfig.Routes {
			if route.ID == "new-test-route" {
				found = true
				if route.Path != "/new-test" {
					t.Errorf("期望路径 '/new-test', 得到 '%s'", route.Path)
				}
				break
			}
		}

		if !found {
			t.Error("新路由未找到")
		}
	})

	// 测试配置版本
	t.Run("配置版本", func(t *testing.T) {
		version := manager.GetCurrentVersion()
		if version == "" {
			t.Error("配置版本不应为空")
		}

		t.Logf("当前配置版本: %s", version)
	})

	// 测试配置历史
	t.Run("配置历史", func(t *testing.T) {
		history := manager.GetVersionHistory()
		if len(history) == 0 {
			t.Error("配置历史不应为空")
		}

		t.Logf("配置历史记录数: %d", len(history))
	})
}
