package test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"api-gateway/internal/core"
	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// TestDynamicConfigurationIntegration 测试动态配置集成功能
func TestDynamicConfigurationIntegration(t *testing.T) {
	// 创建测试配置
	cfg := createTestConfig()
	
	// 创建测试日志器和指标收集器
	logger := createTestLogger()
	metrics := createTestMetrics()
	
	// 创建网关实例
	gateway, err := core.NewGateway(cfg, logger, metrics, nil)
	if err != nil {
		t.Fatalf("创建网关失败: %v", err)
	}
	defer gateway.Shutdown(context.Background())
	
	// 启动网关
	server := httptest.NewServer(gateway.Handler())
	defer server.Close()
	
	// 测试基本健康检查
	t.Run("健康检查", func(t *testing.T) {
		resp, err := http.Get(server.URL + "/health")
		if err != nil {
			t.Fatalf("健康检查请求失败: %v", err)
		}
		defer resp.Body.Close()
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", resp.StatusCode)
		}
	})
	
	// 测试管理API - 获取配置
	t.Run("获取当前配置", func(t *testing.T) {
		resp, err := http.Get(server.URL + "/admin/config")
		if err != nil {
			t.Fatalf("获取配置请求失败: %v", err)
		}
		defer resp.Body.Close()
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", resp.StatusCode)
		}
	})
	
	// 测试动态配置API - 获取路由配置
	t.Run("获取路由配置", func(t *testing.T) {
		resp, err := http.Get(server.URL + "/admin/api/v1/config/routes")
		if err != nil {
			t.Fatalf("获取路由配置请求失败: %v", err)
		}
		defer resp.Body.Close()
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", resp.StatusCode)
		}
	})
	
	// 测试动态配置更新
	t.Run("更新路由配置", func(t *testing.T) {
		// 创建新的路由配置
		newRoute := config.Route{
			ID:       "test-route-dynamic",
			Path:     "/test-dynamic",
			Method:   "GET",
			Backend:  "http://test-backend:8080",
			Timeout:  30 * time.Second,
			Retries:  3,
			Enabled:  true,
		}
		
		routeJSON, err := json.Marshal(newRoute)
		if err != nil {
			t.Fatalf("序列化路由配置失败: %v", err)
		}
		
		// 发送PUT请求更新路由配置
		req, err := http.NewRequest("PUT", 
			server.URL+"/admin/api/v1/config/routes/test-route-dynamic", 
			strings.NewReader(string(routeJSON)))
		if err != nil {
			t.Fatalf("创建更新请求失败: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")
		
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("更新路由配置请求失败: %v", err)
		}
		defer resp.Body.Close()
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", resp.StatusCode)
		}
		
		// 等待配置生效
		time.Sleep(100 * time.Millisecond)
		
		// 验证新路由是否生效
		testResp, err := http.Get(server.URL + "/test-dynamic")
		if err != nil {
			t.Fatalf("测试新路由请求失败: %v", err)
		}
		defer testResp.Body.Close()
		
		// 由于后端不存在，期望得到502或503错误，这表明路由已经生效
		if testResp.StatusCode != http.StatusBadGateway && 
		   testResp.StatusCode != http.StatusServiceUnavailable {
			t.Logf("新路由状态码: %d (期望 502 或 503，表明路由已生效)", testResp.StatusCode)
		}
	})
	
	// 测试配置版本管理
	t.Run("配置版本管理", func(t *testing.T) {
		// 获取当前版本
		resp, err := http.Get(server.URL + "/admin/api/v1/config/version")
		if err != nil {
			t.Fatalf("获取配置版本请求失败: %v", err)
		}
		defer resp.Body.Close()
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", resp.StatusCode)
		}
	})
	
	// 测试配置历史记录
	t.Run("配置历史记录", func(t *testing.T) {
		resp, err := http.Get(server.URL + "/admin/api/v1/config/history")
		if err != nil {
			t.Fatalf("获取配置历史请求失败: %v", err)
		}
		defer resp.Body.Close()
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", resp.StatusCode)
		}
	})
}

// createTestConfig 创建测试配置
func createTestConfig() *config.Config {
	return &config.Config{
		Server: config.ServerConfig{
			Host: "localhost",
			Port: 0, // 使用随机端口
		},
		Routes: []config.Route{
			{
				ID:      "test-route-1",
				Path:    "/test1",
				Method:  "GET",
				Backend: "http://test-backend-1:8080",
				Timeout: 30 * time.Second,
				Retries: 3,
				Enabled: true,
			},
			{
				ID:      "test-route-2",
				Path:    "/test2",
				Method:  "POST",
				Backend: "http://test-backend-2:8080",
				Timeout: 30 * time.Second,
				Retries: 3,
				Enabled: true,
			},
		},
		Auth: config.AuthConfig{
			Enabled: false,
		},
		Security: config.SecurityConfig{
			RateLimit: config.RateLimitConfig{
				Enabled: false,
			},
		},
		Plugins: []config.PluginConfig{},
	}
}

// createTestLogger 创建测试日志器
func createTestLogger() *telemetry.Logger {
	// 这里应该创建一个真实的日志器，但为了简化测试，我们返回nil
	// 在实际实现中，需要创建一个适合测试的日志器
	return nil
}

// createTestMetrics 创建测试指标收集器
func createTestMetrics() *telemetry.Metrics {
	// 这里应该创建一个真实的指标收集器，但为了简化测试，我们返回nil
	// 在实际实现中，需要创建一个适合测试的指标收集器
	return nil
}

// TestDynamicConfigManager 测试动态配置管理器
func TestDynamicConfigManager(t *testing.T) {
	// 创建内存存储
	storage := config.NewMemoryStorage()
	
	// 创建配置管理器
	cfg := createTestConfig()
	manager := config.NewDynamicConfigManager(cfg, storage, nil)
	
	// 启动管理器
	ctx := context.Background()
	err := manager.Start(ctx)
	if err != nil {
		t.Fatalf("启动配置管理器失败: %v", err)
	}
	defer manager.Stop()
	
	// 测试配置更新
	t.Run("配置更新", func(t *testing.T) {
		// 更新路由配置
		newRoute := config.Route{
			ID:      "new-test-route",
			Path:    "/new-test",
			Method:  "GET",
			Backend: "http://new-backend:8080",
			Timeout: 30 * time.Second,
			Retries: 3,
			Enabled: true,
		}
		
		err := manager.UpdateRouteConfig("new-test-route", newRoute)
		if err != nil {
			t.Fatalf("更新路由配置失败: %v", err)
		}
		
		// 验证配置是否更新
		updatedConfig := manager.GetConfig()
		found := false
		for _, route := range updatedConfig.Routes {
			if route.ID == "new-test-route" {
				found = true
				if route.Path != "/new-test" {
					t.Errorf("期望路径 '/new-test', 得到 '%s'", route.Path)
				}
				break
			}
		}
		
		if !found {
			t.Error("新路由未找到")
		}
	})
	
	// 测试配置版本
	t.Run("配置版本", func(t *testing.T) {
		version := manager.GetCurrentVersion()
		if version == "" {
			t.Error("配置版本不应为空")
		}
		
		fmt.Printf("当前配置版本: %s\n", version)
	})
	
	// 测试配置历史
	t.Run("配置历史", func(t *testing.T) {
		history := manager.GetVersionHistory()
		if len(history) == 0 {
			t.Error("配置历史不应为空")
		}
		
		fmt.Printf("配置历史记录数: %d\n", len(history))
	})
}
