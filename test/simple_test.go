package test

import (
	"testing"
	"time"

	"api-gateway/pkg/config"
)

// TestBasicConfig 测试基本配置功能
func TestBasicConfig(t *testing.T) {
	// 测试配置结构体创建
	t.Run("创建基本配置", func(t *testing.T) {
		cfg := &config.Config{
			Server: config.ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
			Routes: []config.Route{
				{
					ID:      "test-route",
					Path:    "/api/test",
					Method:  "GET",
					Backend: "http://backend:8080",
					Timeout: 30 * time.Second,
					Retries: 3,
					Enabled: true,
				},
			},
		}
		
		if cfg.Server.Host != "localhost" {
			t.<PERSON>rf("期望主机 'localhost', 得到 '%s'", cfg.Server.Host)
		}
		
		if cfg.Server.Port != 8080 {
			t.Errorf("期望端口 8080, 得到 %d", cfg.Server.Port)
		}
		
		if len(cfg.Routes) != 1 {
			t.<PERSON><PERSON>("期望 1 个路由, 得到 %d", len(cfg.Routes))
		}
		
		route := cfg.Routes[0]
		if route.ID != "test-route" {
			t.Errorf("期望路由ID 'test-route', 得到 '%s'", route.ID)
		}
		
		if route.Path != "/api/test" {
			t.Errorf("期望路径 '/api/test', 得到 '%s'", route.Path)
		}
		
		if route.Method != "GET" {
			t.Errorf("期望方法 'GET', 得到 '%s'", route.Method)
		}
		
		if route.Backend != "http://backend:8080" {
			t.Errorf("期望后端 'http://backend:8080', 得到 '%s'", route.Backend)
		}
		
		if route.Timeout != 30*time.Second {
			t.Errorf("期望超时 30s, 得到 %v", route.Timeout)
		}
		
		if route.Retries != 3 {
			t.Errorf("期望重试次数 3, 得到 %d", route.Retries)
		}
		
		if !route.Enabled {
			t.Error("期望路由启用")
		}
	})
	
	// 测试配置变更类型
	t.Run("配置变更类型", func(t *testing.T) {
		change := config.ConfigChange{
			Type:      config.ConfigChangeTypeUpdate,
			Key:       "test-key",
			OldValue:  "old-value",
			NewValue:  "new-value",
			Timestamp: time.Now(),
		}
		
		if change.Type != config.ConfigChangeTypeUpdate {
			t.Errorf("期望变更类型 'update', 得到 '%s'", change.Type)
		}
		
		if change.Key != "test-key" {
			t.Errorf("期望键 'test-key', 得到 '%s'", change.Key)
		}
		
		if change.OldValue != "old-value" {
			t.Errorf("期望旧值 'old-value', 得到 '%v'", change.OldValue)
		}
		
		if change.NewValue != "new-value" {
			t.Errorf("期望新值 'new-value', 得到 '%v'", change.NewValue)
		}
	})
	
	// 测试配置版本
	t.Run("配置版本", func(t *testing.T) {
		version := config.ConfigVersion{
			Version:   "v1.0.0",
			Timestamp: time.Now(),
			Author:    "test-user",
			Comment:   "测试版本",
			Config: &config.Config{
				Server: config.ServerConfig{
					Host: "localhost",
					Port: 8080,
				},
			},
		}
		
		if version.Version != "v1.0.0" {
			t.Errorf("期望版本 'v1.0.0', 得到 '%s'", version.Version)
		}
		
		if version.Author != "test-user" {
			t.Errorf("期望作者 'test-user', 得到 '%s'", version.Author)
		}
		
		if version.Comment != "测试版本" {
			t.Errorf("期望注释 '测试版本', 得到 '%s'", version.Comment)
		}
		
		if version.Config == nil {
			t.Error("配置不应为空")
		}
		
		if version.Config.Server.Host != "localhost" {
			t.Errorf("期望配置主机 'localhost', 得到 '%s'", version.Config.Server.Host)
		}
	})
}

// TestMemoryStorageBasic 测试内存存储基本功能
func TestMemoryStorageBasic(t *testing.T) {
	storage := config.NewMemoryStorage()
	defer storage.Close()
	
	// 测试存储和获取
	t.Run("存储和获取", func(t *testing.T) {
		testConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "test-host",
				Port: 9000,
			},
		}
		
		err := storage.Store("test-key", testConfig)
		if err != nil {
			t.Fatalf("存储失败: %v", err)
		}
		
		retrievedConfig, err := storage.Get("test-key")
		if err != nil {
			t.Fatalf("获取失败: %v", err)
		}
		
		if retrievedConfig == nil {
			t.Fatal("获取的配置为空")
		}
		
		if retrievedConfig.Server.Host != "test-host" {
			t.Errorf("期望主机 'test-host', 得到 '%s'", retrievedConfig.Server.Host)
		}
		
		if retrievedConfig.Server.Port != 9000 {
			t.Errorf("期望端口 9000, 得到 %d", retrievedConfig.Server.Port)
		}
	})
	
	// 测试删除
	t.Run("删除", func(t *testing.T) {
		testConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "delete-test",
				Port: 8888,
			},
		}
		
		err := storage.Store("delete-key", testConfig)
		if err != nil {
			t.Fatalf("存储失败: %v", err)
		}
		
		err = storage.Delete("delete-key")
		if err != nil {
			t.Fatalf("删除失败: %v", err)
		}
		
		_, err = storage.Get("delete-key")
		if err == nil {
			t.Error("期望删除后获取失败")
		}
	})
	
	// 测试列出键
	t.Run("列出键", func(t *testing.T) {
		// 清理存储
		storage.Close()
		storage = config.NewMemoryStorage()
		
		// 添加测试数据
		configs := map[string]*config.Config{
			"key1": {Server: config.ServerConfig{Host: "host1", Port: 8001}},
			"key2": {Server: config.ServerConfig{Host: "host2", Port: 8002}},
		}
		
		for key, cfg := range configs {
			err := storage.Store(key, cfg)
			if err != nil {
				t.Fatalf("存储 %s 失败: %v", key, err)
			}
		}
		
		keys, err := storage.List()
		if err != nil {
			t.Fatalf("列出键失败: %v", err)
		}
		
		if len(keys) != 2 {
			t.Errorf("期望 2 个键, 得到 %d", len(keys))
		}
		
		keyMap := make(map[string]bool)
		for _, key := range keys {
			keyMap[key] = true
		}
		
		if !keyMap["key1"] {
			t.Error("键 'key1' 未找到")
		}
		
		if !keyMap["key2"] {
			t.Error("键 'key2' 未找到")
		}
	})
}

// TestConfigValidator 测试配置验证器
func TestConfigValidator(t *testing.T) {
	validator := config.NewConfigValidator()
	
	// 测试有效配置
	t.Run("有效配置", func(t *testing.T) {
		validConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
			Routes: []config.Route{
				{
					ID:      "valid-route",
					Path:    "/api/v1/test",
					Method:  "GET",
					Backend: "http://backend:8080",
					Timeout: 30 * time.Second,
					Retries: 3,
					Enabled: true,
				},
			},
		}
		
		err := validator.Validate(validConfig)
		if err != nil {
			t.Errorf("有效配置验证失败: %v", err)
		}
	})
	
	// 测试无效配置
	t.Run("无效配置 - 空路由ID", func(t *testing.T) {
		invalidConfig := &config.Config{
			Server: config.ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
			Routes: []config.Route{
				{
					ID:      "", // 空ID
					Path:    "/api/v1/test",
					Method:  "GET",
					Backend: "http://backend:8080",
					Timeout: 30 * time.Second,
					Retries: 3,
					Enabled: true,
				},
			},
		}
		
		err := validator.Validate(invalidConfig)
		if err == nil {
			t.Error("期望无效配置验证失败")
		}
	})
}
