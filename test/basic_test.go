package main

import (
	"testing"
	"time"
)

// 基本配置结构体，不依赖外部包
type ServerConfig struct {
	Host string `yaml:"host" json:"host"`
	Port int    `yaml:"port" json:"port"`
}

type Route struct {
	ID      string        `yaml:"id" json:"id"`
	Path    string        `yaml:"path" json:"path"`
	Method  string        `yaml:"method" json:"method"`
	Backend string        `yaml:"backend" json:"backend"`
	Timeout time.Duration `yaml:"timeout" json:"timeout"`
	Retries int           `yaml:"retries" json:"retries"`
	Enabled bool          `yaml:"enabled" json:"enabled"`
}

type Config struct {
	Server ServerConfig `yaml:"server" json:"server"`
	Routes []Route      `yaml:"routes" json:"routes"`
}

// TestBasicStructures 测试基本结构体
func TestBasicStructures(t *testing.T) {
	t.Run("创建服务器配置", func(t *testing.T) {
		server := ServerConfig{
			Host: "localhost",
			Port: 8080,
		}
		
		if server.Host != "localhost" {
			t.Errorf("期望主机 'localhost', 得到 '%s'", server.Host)
		}
		
		if server.Port != 8080 {
			t.Errorf("期望端口 8080, 得到 %d", server.Port)
		}
	})
	
	t.Run("创建路由配置", func(t *testing.T) {
		route := Route{
			ID:      "test-route",
			Path:    "/api/test",
			Method:  "GET",
			Backend: "http://backend:8080",
			Timeout: 30 * time.Second,
			Retries: 3,
			Enabled: true,
		}
		
		if route.ID != "test-route" {
			t.Errorf("期望路由ID 'test-route', 得到 '%s'", route.ID)
		}
		
		if route.Path != "/api/test" {
			t.Errorf("期望路径 '/api/test', 得到 '%s'", route.Path)
		}
		
		if route.Method != "GET" {
			t.Errorf("期望方法 'GET', 得到 '%s'", route.Method)
		}
		
		if route.Backend != "http://backend:8080" {
			t.Errorf("期望后端 'http://backend:8080', 得到 '%s'", route.Backend)
		}
		
		if route.Timeout != 30*time.Second {
			t.Errorf("期望超时 30s, 得到 %v", route.Timeout)
		}
		
		if route.Retries != 3 {
			t.Errorf("期望重试次数 3, 得到 %d", route.Retries)
		}
		
		if !route.Enabled {
			t.Error("期望路由启用")
		}
	})
	
	t.Run("创建完整配置", func(t *testing.T) {
		config := Config{
			Server: ServerConfig{
				Host: "0.0.0.0",
				Port: 9090,
			},
			Routes: []Route{
				{
					ID:      "api-v1",
					Path:    "/api/v1/*",
					Method:  "ANY",
					Backend: "http://api-service:8080",
					Timeout: 60 * time.Second,
					Retries: 2,
					Enabled: true,
				},
				{
					ID:      "health",
					Path:    "/health",
					Method:  "GET",
					Backend: "http://health-service:8080",
					Timeout: 10 * time.Second,
					Retries: 1,
					Enabled: true,
				},
			},
		}
		
		if config.Server.Host != "0.0.0.0" {
			t.Errorf("期望服务器主机 '0.0.0.0', 得到 '%s'", config.Server.Host)
		}
		
		if config.Server.Port != 9090 {
			t.Errorf("期望服务器端口 9090, 得到 %d", config.Server.Port)
		}
		
		if len(config.Routes) != 2 {
			t.Errorf("期望 2 个路由, 得到 %d", len(config.Routes))
		}
		
		// 验证第一个路由
		route1 := config.Routes[0]
		if route1.ID != "api-v1" {
			t.Errorf("期望第一个路由ID 'api-v1', 得到 '%s'", route1.ID)
		}
		
		if route1.Path != "/api/v1/*" {
			t.Errorf("期望第一个路由路径 '/api/v1/*', 得到 '%s'", route1.Path)
		}
		
		// 验证第二个路由
		route2 := config.Routes[1]
		if route2.ID != "health" {
			t.Errorf("期望第二个路由ID 'health', 得到 '%s'", route2.ID)
		}
		
		if route2.Path != "/health" {
			t.Errorf("期望第二个路由路径 '/health', 得到 '%s'", route2.Path)
		}
	})
}

// TestConfigValidation 测试配置验证逻辑
func TestConfigValidation(t *testing.T) {
	t.Run("验证有效配置", func(t *testing.T) {
		config := Config{
			Server: ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
			Routes: []Route{
				{
					ID:      "valid-route",
					Path:    "/api/test",
					Method:  "GET",
					Backend: "http://backend:8080",
					Timeout: 30 * time.Second,
					Retries: 3,
					Enabled: true,
				},
			},
		}
		
		// 基本验证逻辑
		if config.Server.Port <= 0 || config.Server.Port > 65535 {
			t.Error("无效的端口号")
		}
		
		if config.Server.Host == "" {
			t.Error("主机不能为空")
		}
		
		for _, route := range config.Routes {
			if route.ID == "" {
				t.Error("路由ID不能为空")
			}
			
			if route.Path == "" {
				t.Error("路由路径不能为空")
			}
			
			if route.Method == "" {
				t.Error("路由方法不能为空")
			}
			
			if route.Backend == "" {
				t.Error("路由后端不能为空")
			}
			
			if route.Timeout <= 0 {
				t.Error("路由超时必须大于0")
			}
			
			if route.Retries < 0 {
				t.Error("路由重试次数不能为负数")
			}
		}
	})
	
	t.Run("验证无效配置", func(t *testing.T) {
		// 测试无效端口
		config1 := Config{
			Server: ServerConfig{
				Host: "localhost",
				Port: -1, // 无效端口
			},
		}
		
		if config1.Server.Port > 0 && config1.Server.Port <= 65535 {
			t.Error("应该检测到无效端口")
		}
		
		// 测试空路由ID
		config2 := Config{
			Server: ServerConfig{
				Host: "localhost",
				Port: 8080,
			},
			Routes: []Route{
				{
					ID:      "", // 空ID
					Path:    "/test",
					Method:  "GET",
					Backend: "http://backend:8080",
					Timeout: 30 * time.Second,
					Retries: 3,
					Enabled: true,
				},
			},
		}
		
		hasEmptyID := false
		for _, route := range config2.Routes {
			if route.ID == "" {
				hasEmptyID = true
				break
			}
		}
		
		if !hasEmptyID {
			t.Error("应该检测到空路由ID")
		}
	})
}

// TestMemoryStorage 测试内存存储功能
func TestMemoryStorage(t *testing.T) {
	// 简单的内存存储实现
	type MemoryStorage struct {
		data map[string]interface{}
	}
	
	storage := &MemoryStorage{
		data: make(map[string]interface{}),
	}
	
	t.Run("存储和获取", func(t *testing.T) {
		testConfig := Config{
			Server: ServerConfig{
				Host: "test-host",
				Port: 9000,
			},
		}
		
		// 存储
		storage.data["test-key"] = testConfig
		
		// 获取
		if retrieved, exists := storage.data["test-key"]; exists {
			if config, ok := retrieved.(Config); ok {
				if config.Server.Host != "test-host" {
					t.Errorf("期望主机 'test-host', 得到 '%s'", config.Server.Host)
				}
				
				if config.Server.Port != 9000 {
					t.Errorf("期望端口 9000, 得到 %d", config.Server.Port)
				}
			} else {
				t.Error("类型转换失败")
			}
		} else {
			t.Error("配置未找到")
		}
	})
	
	t.Run("删除", func(t *testing.T) {
		storage.data["delete-key"] = "test-value"
		
		// 验证存在
		if _, exists := storage.data["delete-key"]; !exists {
			t.Error("键应该存在")
		}
		
		// 删除
		delete(storage.data, "delete-key")
		
		// 验证删除
		if _, exists := storage.data["delete-key"]; exists {
			t.Error("键应该已被删除")
		}
	})
	
	t.Run("列出所有键", func(t *testing.T) {
		// 清理
		storage.data = make(map[string]interface{})
		
		// 添加测试数据
		storage.data["key1"] = "value1"
		storage.data["key2"] = "value2"
		storage.data["key3"] = "value3"
		
		// 获取所有键
		keys := make([]string, 0, len(storage.data))
		for key := range storage.data {
			keys = append(keys, key)
		}
		
		if len(keys) != 3 {
			t.Errorf("期望 3 个键, 得到 %d", len(keys))
		}
		
		// 验证键存在
		keyMap := make(map[string]bool)
		for _, key := range keys {
			keyMap[key] = true
		}
		
		expectedKeys := []string{"key1", "key2", "key3"}
		for _, expectedKey := range expectedKeys {
			if !keyMap[expectedKey] {
				t.Errorf("期望的键 '%s' 未找到", expectedKey)
			}
		}
	})
}
